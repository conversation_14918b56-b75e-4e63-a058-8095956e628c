import unzipper from "unzipper";
import { Logger } from "../utils/Logger";
import axios from "axios";
import PDFParser from "pdf-parse";
import WordExtractor from "word-extractor";
export async function extractTextFromDocx(buffer: Buffer): Promise<string> {
  try {
    Logger.debug("extractTextFromDocx: buffer length:", buffer.length);

    // Validate buffer
    if (!buffer || buffer.length === 0) {
      throw new Error("Empty buffer provided");
    }

    // Use a Promise wrapper to handle potential stream errors
    const directory = await new Promise<unzipper.CentralDirectory>(
      (resolve, reject) => {
        unzipper.Open.buffer(buffer)
          .then(resolve)
          .catch((error) => {
            Logger.error("Failed to open DOCX buffer:", error);
            reject(error);
          });
      }
    );

    Logger.debug(
      "DOCX ZIP directory files:",
      directory.files.map((f) => f.path)
    );

    const docFile = directory.files.find(
      (file) => file.path === "word/document.xml"
    );

    if (!docFile) {
      throw new Error("word/document.xml not found in DOCX.");
    }

    // Use a Promise wrapper for buffer extraction
    const xmlBuf = await new Promise<Buffer>((resolve, reject) => {
      docFile
        .buffer()
        .then(resolve)
        .catch((error) => {
          Logger.error("Failed to extract XML buffer:", error);
          reject(error);
        });
    });

    const xmlContent = xmlBuf.toString("utf8");
    Logger.debug("XML content length:", xmlContent.length);

    // More robust regex to handle different types of text elements
    const textMatches =
      xmlContent.match(/<w:t[^>]*>([^<]*)<\/w:t>|<w:t>([^<]*)<\/w:t>/g) || [];
    Logger.debug("Number of <w:t> matches found:", textMatches.length);

    let extracted = textMatches
      .map((match) => {
        // Remove XML tags and decode HTML entities
        return match
          .replace(/<\/?w:t[^>]*>/g, "")
          .replace(/&amp;/g, "&")
          .replace(/&lt;/g, "<")
          .replace(/&gt;/g, ">")
          .replace(/&quot;/g, '"')
          .replace(/&apos;/g, "'");
      })
      .join(" ");

    // Clean up whitespace
    extracted = extracted
      .replace(/\s+/g, " ")
      .replace(/\n\s*\n/g, "\n")
      .trim();

    Logger.debug("Final extracted text length:", extracted.length);
    
    return extracted;

    
  } catch (err: unknown) {
    Logger.error("extractTextFromDocx error:", err);

    // Type checking before accessing error properties
    if (err instanceof Error && err.message === "FILE_ENDED") {
      try {
        return await extractTextFromDocxFallback(buffer);
      } catch (fallbackErr) {
        Logger.error("Fallback extraction also failed:", fallbackErr);
        throw fallbackErr;
      }
    }

    throw err;
  }
}

// Fallback method using a more basic approach
async function extractTextFromDocxFallback(buffer: Buffer): Promise<string> {
  try {
    // Look for text content directly in the buffer
    const content = buffer.toString("utf8");
    const textMatches = content.match(/<w:t[^>]*>([^<]*)<\/w:t>/g) || [];

    let extracted = textMatches
      .map((match) => match.replace(/<\/?w:t[^>]*>/g, ""))
      .join(" ")
      .trim();

    if (extracted.length === 0) {
      throw new Error("No text content found in fallback method");
    }

    return extracted;
  } catch (err) {
    Logger.error("Fallback extraction error:", err);
    throw new Error("Failed to extract text using fallback method");
  }
}


/**
 * Downloads a Word document (DOC or DOCX) from a URL and extracts its text.
 * @param url The URL of the Word document
 * @returns Cleaned text extracted from the document
 */
export async function extractTextFromWordURL(url: string): Promise<string> {
  try {    
    // Download the file
    const response = await axios.get(url, { responseType: "arraybuffer" });
    const buffer = Buffer.from(response.data);
    
    // Determine file type from URL
    const isDocx = url.toLowerCase().endsWith(".docx");
    
    // Extract text based on file type
    let extractedText = "";
    if (isDocx) {
      extractedText = await extractTextFromDocx(buffer);
    } else {
      extractedText = await extractTextFromDoc(buffer);
    }
        
    // Clean the text (remove XML tags and non-ASCII characters except newline)
    let cleanedText = extractedText.replace(/<[^>]+>/g, "");
    cleanedText = cleanedText.replace(/[^\n\x20-\x7E]+/g, "");
    
    return cleanedText;
  } catch (error) {
    Logger.error("Error in extracting text from word blob url: ", url);
    Logger.error("Error details: ", error);
    // throw error;
    return ""
  }
}

export async function extractTextFromPdfBuffer(buffer: Buffer): Promise<string> {
  try {
    const data = await PDFParser(buffer);
    return data.text;
  } catch (error) {
    console.error("Error in extractTextFromPdf:", error);
    throw error;
  }
}

/**
 * Extract text from a classic DOC (application/msword) file buffer.
 * This function uses a more robust approach to handle the binary structure of DOC files.
 */
// export async function extractTextFromDoc(buffer: Buffer): Promise<string> {
//   try {
//     Logger.debug("extractTextFromDoc: using heuristic text extraction");

//     // Convert buffer to string for initial processing
//     const content = buffer.toString("binary");

//     // Step 1: Skip file header and locate text sections
//     // Most DOC files have text content after specific markers
//     const textMarkers = [
//       "MSWordDoc",
//       "Word.Document",
//       "Microsoft Word",
//       "Times New Roman",
//       "Arial",
//       "Calibri",
//       "Cambria",
//     ];

//     let startPos = 0;
//     for (const marker of textMarkers) {
//       const pos = content.indexOf(marker);
//       if (pos > 0) {
//         startPos = Math.max(startPos, pos + marker.length);
//         Logger.debug(`Found marker "${marker}" at position ${pos}`);
//       }
//     }

//     // Step 2: Extract text using multiple approaches and combine results
//     const results: string[] = [];

//     // Approach 1: Extract ASCII sequences (improved)
//     const minSequenceLength = 2; // Even shorter to catch more fragments
//     const sequences: string[] = [];
//     let currentSequence = "";

//     // Process from the identified start position
//     for (let i = startPos; i < buffer.length; i++) {
//       const byte = buffer[i];

//       // More permissive character detection
//       if (
//         (byte >= 32 && byte <= 126) || // ASCII printable
//         (byte >= 192 && byte <= 255) || // Latin extended
//         [9, 10, 13, 32].includes(byte)
//       ) {
//         // Control chars we want to keep
//         currentSequence += String.fromCharCode(byte);
//       } else if (byte === 0 && buffer[i + 1] >= 32 && buffer[i + 1] <= 126) {
//         // Handle Unicode text (common in newer DOC files)
//         currentSequence += String.fromCharCode(buffer[i + 1]);
//         i++; // Skip the next byte as we've already processed it
//       } else {
//         if (currentSequence.length >= minSequenceLength) {
//           const cleaned = currentSequence.trim();
//           if (cleaned.length >= minSequenceLength) {
//             sequences.push(cleaned);
//           }
//         }
//         currentSequence = "";
//       }
//     }

//     // Don't forget the last sequence
//     if (currentSequence.length >= minSequenceLength) {
//       sequences.push(currentSequence.trim());
//     }

//     // Join sequences with proper spacing
//     const sequenceResult = sequences
//       .join(" ")
//       .replace(/\s{2,}/g, " ")
//       .trim();

//     results.push(sequenceResult);

//     // Approach 2: Look for Unicode text patterns (16-bit characters)
//     const unicodeTexts: string[] = [];
//     for (let i = 0; i < buffer.length - 1; i += 2) {
//       // Check for potential Unicode text sections
//       if (buffer[i] === 0 && buffer[i + 1] >= 32 && buffer[i + 1] <= 126) {
//         let unicodeSeq = "";
//         let j = i;
//         while (
//           j < buffer.length - 1 &&
//           buffer[j] === 0 &&
//           buffer[j + 1] >= 32 &&
//           buffer[j + 1] <= 126
//         ) {
//           unicodeSeq += String.fromCharCode(buffer[j + 1]);
//           j += 2;
//         }

//         if (unicodeSeq.length >= 5) {
//           // Only keep longer sequences
//           unicodeTexts.push(unicodeSeq);
//           i = j - 2; // Adjust index to continue after this sequence
//         }
//       }
//     }

//     if (unicodeTexts.length > 0) {
//       results.push(unicodeTexts.join(" "));
//     }

//     // Combine results, prioritizing the approach that yielded more text
//     let finalText = results.sort((a, b) => b.length - a.length)[0];

//     // Final cleanup
//     finalText = finalText
//       .replace(/[^\x20-\x7E\n\r\t\u00A0-\u00FF]+/g, " ") // Remove non-printable chars
//       .replace(/\s{2,}/g, " ") // Normalize whitespace
//       .trim();

//     Logger.debug(
//       "extractTextFromDoc: extracted text length:",
//       finalText.length
//     );
//     Logger.info("extractTextFromDoc:"+finalText);
//     return finalText;
//   } catch (err) {
//     Logger.error("extractTextFromDoc error:", err);
//     throw err;
//   }
// }

export async function extractTextFromDoc(buffer: Buffer): Promise<string> {
  const extractor = new WordExtractor();
  
  try {
    Logger.debug("extractTextFromDoc: Using 'word-extractor' library.");
    
    // extractor.extract() returns a Promise that resolves with the document object
    const doc = await extractor.extract(buffer);
    
    const text = doc.getBody();

    // The same cleanup logic you had before is still useful here.
    const cleanedText = text
      .replace(/\s{2,}/g, " ") // Replace multiple spaces/newlines with a single space
      .trim();

    Logger.debug(
      `extractTextFromDoc: Extracted text length: ${cleanedText.length}`
    );
    Logger.info("extractTextFromDoc: Successfully extracted text.");
    
    return cleanedText;

  } catch (err: any) { // It's good practice to type the catch variable
    Logger.error("extractTextFromDoc error with word-extractor:", err);
    throw new Error(
      `Failed to extract text using word-extractor: ${err.message}`
    );
  }
}

/**
 * Extract text from a TXT file buffer.
 * This function handles different encodings and normalizes line endings.
 */
export async function extractTextFromTxt(buffer: Buffer): Promise<string> {
  try {
    Logger.debug(
      "extractTextFromTxt: starting text extraction from TXT buffer."
    );

    // First try UTF-8 encoding (most common)
    let text = buffer.toString("utf8");

    // Check if the text contains mostly readable characters
    const readableCharRatio =
      text.replace(/[^\x20-\x7E\n\r\t\u00A0-\u00FF]/g, "").length / text.length;

    // If less than 80% readable characters, try other encodings
    if (readableCharRatio < 0.8) {
      Logger.debug(
        "UTF-8 encoding produced low readability, trying other encodings"
      );

      // Try Latin1 (ISO-8859-1) encoding
      const textLatin1 = buffer.toString("latin1");
      const readableCharRatioLatin1 =
        textLatin1.replace(/[^\x20-\x7E\n\r\t\u00A0-\u00FF]/g, "").length /
        textLatin1.length;

      // Try Windows-1252 encoding (approximated via latin1 + replacements)
      const textWin1252 = buffer
        .toString("latin1")
        .replace(/\x80/g, "€")
        .replace(/\x82/g, "‚")
        .replace(/\x83/g, "ƒ")
        .replace(/\x84/g, "„")
        .replace(/\x85/g, "…")
        .replace(/\x86/g, "†")
        .replace(/\x87/g, "‡")
        .replace(/\x88/g, "ˆ")
        .replace(/\x89/g, "‰")
        .replace(/\x8A/g, "Š")
        .replace(/\x8B/g, "‹")
        .replace(/\x8C/g, "Œ")
        .replace(/\x8E/g, "Ž")
        .replace(/\x91/g, "'")
        .replace(/\x92/g, "'")
        .replace(/\x93/g, '"')
        .replace(/\x94/g, '"')
        .replace(/\x95/g, "•")
        .replace(/\x96/g, "–")
        .replace(/\x97/g, "—")
        .replace(/\x98/g, "˜")
        .replace(/\x99/g, "™")
        .replace(/\x9A/g, "š")
        .replace(/\x9B/g, "›")
        .replace(/\x9C/g, "œ")
        .replace(/\x9E/g, "ž")
        .replace(/\x9F/g, "Ÿ");

      const readableCharRatioWin1252 =
        textWin1252.replace(/[^\x20-\x7E\n\r\t\u00A0-\u00FF]/g, "").length /
        textWin1252.length;

      // Use the encoding that produced the highest readability
      if (
        readableCharRatioLatin1 > readableCharRatio &&
        readableCharRatioLatin1 > readableCharRatioWin1252
      ) {
        text = textLatin1;
        Logger.debug("Using Latin1 encoding for better readability");
      } else if (readableCharRatioWin1252 > readableCharRatio) {
        text = textWin1252;
        Logger.debug("Using Windows-1252 encoding for better readability");
      }
    }

    // Normalize line endings (convert all to \n)
    text = text.replace(/\r\n/g, "\n").replace(/\r/g, "\n");

    // Remove any remaining control characters except tabs and newlines
    text = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F]/g, "");

    // Remove BOM (Byte Order Mark) if present
    text = text.replace(/^\uFEFF/, "");

    Logger.debug("extractTextFromTxt: extracted text length:", text.length);

    return text;
  } catch (err) {
    Logger.error("extractTextFromTxt error:", err);
    throw err;
  }
}
