// import { Vertex<PERSON><PERSON> } from "@google-cloud/vertexai";
import { Content, ContentListUnion, GenerateContentConfig, GoogleGenAI } from '@google/genai'
import { Logger } from '@/lib/utils/Logger'
import { getGooglePalmApiKey } from '../utils/apiKeyRotation'
import { logServerEvent } from '../analytics/events-server'
import { ServerErrorEvent } from '../analytics/event-types'
import { serializeError } from '../utils/errorUtils'

export type GoogleServiceAccount = {
    type: 'service_account'
    project_id: string
    private_key_id: string
    private_key: string
    client_email: string
    client_id: string
    auth_uri: string
    token_uri: string
    auth_provider_x509_cert_url: string
    client_x509_cert_url: string
}

export class GoogleAIService {
    // Model configuration map
    static readonly MODEL_CONFIG = {
        primary_model: process.env.GOOGLE_PRIMARY_MODEL || 'gemini-2.5-pro',
        secondary_model: process.env.GOOGLE_SECONDARY_MODEL || 'gemini-2.5-flash',
        lite_model: process.env.GOOGLE_LITE_MODEL || 'gemini-2.0-flash-lite'
    };

    // Private static instance variables
    private static vertexAIInstance: GoogleGenAI | null = null;
    private static googleAIInstance: GoogleGenAI | null = null;

    // Lazy initialization for Vertex AI
    private static getVertexAI(): GoogleGenAI {
        if (!this.vertexAIInstance) {
            try {
                // Parse credentials from environment variable
                let credentials: GoogleServiceAccount = JSON.parse(
                    process.env.VERTEXAI_CREDENTIALS_JSON!,
                )
                if (!credentials || !credentials.project_id) {
                    throw new Error('Invalid Vertex AI credentials')
                }

                this.vertexAIInstance = new GoogleGenAI({
                    vertexai: true,
                    project: credentials.project_id,
                    location: process.env.VERTEX_LOCATION || 'us-central1',
                    googleAuthOptions: {
                        credentials,
                        scopes: ['https://www.googleapis.com/auth/cloud-platform'],
                    },
                })
                Logger.info('Vertex AI instance initialized successfully')
            } catch (error) {
                Logger.error('Error initializing Vertex AI instance:', error)
                throw error
            }
        }
        return this.vertexAIInstance;
    }

    // Lazy initialization for Google AI
    private static getGoogleAI(): GoogleGenAI {
        if (!this.googleAIInstance) {
            try {
                const apiKey = getGooglePalmApiKey()
                this.googleAIInstance = new GoogleGenAI({
                    vertexai: false,
                    apiKey,
                })
                Logger.info('Google AI instance initialized successfully')
            } catch (error) {
                Logger.error('Error initializing Google AI instance:', error)
                throw error
            }
        }
        return this.googleAIInstance;
    }

    // Method to reset instances (useful for testing or when credentials change)
    public static resetInstances(): void {
        this.vertexAIInstance = null;
        this.googleAIInstance = null;
        Logger.info('GoogleAI instances have been reset')
    }

    /**
     * Generates content using either Vertex AI or Google AI based on the vertexAi flag
     */
    static async generateContent(
        model: string = this.MODEL_CONFIG.lite_model,
        contents: ContentListUnion,
        config?: GenerateContentConfig,
        vertexAi: boolean = true
    ) {
        const ai = vertexAi ? this.getVertexAI() : this.getGoogleAI()

        try {
            const response = await ai.models.generateContent({
                model: model,
                contents,
                config,
            })

            return { response }
        } catch (error) {
            Logger.error('Error in AI generateContent:', error)
            throw error
        }
    }

    /**
     * Generates streaming content using either Vertex AI or Google AI based on the vertexAi flag
     */
    static async generateContentStream(
        model: string = this.MODEL_CONFIG.primary_model,
        contents: ContentListUnion,
        config?: GenerateContentConfig,
        vertexAi: boolean = true
    ) {
        const ai = vertexAi ? this.getVertexAI() : this.getGoogleAI()

        try {
            return await ai.models.generateContentStream({
                model: model,
                contents,
                config,
            })
        } catch (error) {
            // Logger.warn('Error in AI generateContentStream:', error)
            throw error
        }
    }

    /**
     * Generates streaming content with built-in fallback handling
     */
    static async generateContentStreamWithFallback(
        model: string = this.MODEL_CONFIG.primary_model,
        contents: ContentListUnion,
        config?: GenerateContentConfig,
        analyticsTags?: {
            [key: string]: string | number | undefined;
        }
    ) {
        // Define fallback strategies
        const strategies = [
            {
                name: 'Primary Model (Vertex AI)',
                model,
                vertexAi: true
            },
            {
                name: 'Fallback Model (Vertex AI)',
                // model: 'gemini-2.0-flash-thinking-exp',
                model: 'gemini-2.5-flash',
                vertexAi: true,
                delay: 0
            },
            {
                name: 'Primary Model (Google AI Studio)',
                model,
                vertexAi: false,
                delay: 0
            },
        ];

        // Try each strategy in sequence
        let firstError;
        for (let i = 0; i < strategies.length; i++) {
            const strategy = strategies[i];
            const attemptNum = i + 1;
            const provider = strategy.vertexAi ? 'Vertex AI' : 'Google AI Studio';
            
            try {
                // Apply delay if specified
                if (strategy.delay) {
                    await new Promise(resolve => setTimeout(resolve, strategy.delay));
                }
                
                Logger.info(`Attempting stream initialization: ${provider} with model ${strategy.model} (Attempt ${attemptNum})`);
                
                const result = await this.generateContentStream(
                    strategy.model,
                    contents,
                    config,
                    strategy.vertexAi
                );
                
                Logger.info(`Successfully generated stream with ${strategy.name} (Attempt ${attemptNum})`);
                
                // Add metadata about which attempt succeeded
                return {
                    stream: result,
                    _metadata: {
                        attemptNum: attemptNum,
                        strategyName: strategy.name,
                        model: strategy.model,
                        provider
                    }
                };
            } catch (error) {
                // Store the first error we encounter
                if (!firstError) {
                    firstError = error;
                }
                
                Logger.warn(`${strategy.name} failed (Attempt ${attemptNum}):`, error);
                
                // Log error with analytics
                this.logModelError(
                    provider,
                    error,
                    strategy.model,
                    analyticsTags,
                    `Attempt ${attemptNum}: ${strategy.name}`
                );
                
                // Continue to next strategy
            }
        }
        
        // If all strategies failed, throw the first error
        Logger.warn('All fallback attempts failed');
        throw firstError;
    }

    /**
     * Generates content with built-in fallback handling
     */
    static async generateContentWithFallback(
        model: string = this.MODEL_CONFIG.primary_model,
        contents: ContentListUnion,
        config?: GenerateContentConfig,
        analyticsTags?: {
            [key: string]: string | number | undefined;
        }
    ) {
        // Define fallback strategies
        const strategies = [
            {
                name: 'Primary Model (Vertex AI)',
                model,
                vertexAi: true
            },
            {
                name: 'Primary Model (Google AI Studio)',
                model,
                vertexAi: false,
                delay: 0
            },
        ];

        // Try each strategy in sequence
        let firstError;
        for (let i = 0; i < strategies.length; i++) {
            const strategy = strategies[i];
            const attemptNum = i + 1;
            const provider = strategy.vertexAi ? 'Vertex AI' : 'Google AI Studio';
            
            try {
                // Apply delay if specified
                if (strategy.delay) {
                    await new Promise(resolve => setTimeout(resolve, strategy.delay));
                }
                
                Logger.info(`Attempting content generation: ${provider} with model ${strategy.model} (Attempt ${attemptNum})`);
                
                const response = await this.generateContent(
                    strategy.model,
                    contents,
                    config,
                    strategy.vertexAi
                );
                
                Logger.info(`Successfully generated content with ${strategy.name} (Attempt ${attemptNum})`);
                return response
                // // Add metadata about which attempt succeeded
                // return {
                //     ...result,
                //     _metadata: {
                //         attemptNum: attemptNum,
                //         strategyName: strategy.name,
                //         model: strategy.model,
                //         provider
                //     }
                // };
            } catch (error) {
                // Store the first error we encounter
                if (!firstError) {
                    firstError = error;
                }
                
                Logger.warn(`${strategy.name} failed (Attempt ${attemptNum}):`, error);
                
                // Log error with analytics - commented as requested
                /*
                this.logModelError(
                    provider,
                    error,
                    strategy.model,
                    analyticsTags,
                    `Attempt ${attemptNum}: ${strategy.name}`
                );
                */
                
                // Continue to next strategy
            }
        }
        
        // If all strategies failed, throw the first error
        Logger.warn('All fallback attempts failed');
        throw firstError;
    }

    /**
     * Helper function to log model errors consistently
     */
    private static logModelError(
        provider: string,
        error: any,
        modelIdentifier: string,
        analyticsTags?: {
            [key: string]: string | number | undefined;
        },
        attempt?: string
    ) {
        try {
            const errorDetails: Record<string, any> = {
                error: serializeError(error),
                modelIdentifier,
                context: attempt,
            };

            // Add analytics tags if available
            if (analyticsTags) {
                Object.entries(analyticsTags).forEach(([key, value]) => {
                    if (value !== undefined) {
                        errorDetails[key] = value;
                    }
                });
            }

            // Try to extract status code and message
            if (error instanceof Error) {
                errorDetails.errorMessage = error.message;
            }

            // Extract status code if available
            const status =
                error?.status ||
                error?.response?.status ||
                (typeof error === "object" && "code" in error ? error.code : null);

            if (status) {
                errorDetails.statusCode = status;
            }

            // Log the server error event
            logServerEvent(
                provider === "Google AI Studio"
                    ? ServerErrorEvent.GEMINI_PALM_API
                    : ServerErrorEvent.GEMINI_VERTEX_API,
                errorDetails,
                false
            );
        }
        catch (error) {
            Logger.warn('Error logging model error:', error);
        }
    }

}
