import { CitationValidationService } from "./CitationValidationService";
import { CITATION_TEST_CASES, CitationTestCase } from "./test-cases";
import { Logger } from "../../utils/Logger";
import * as dotenv from "dotenv";

interface TestResult {
  testCase: CitationTestCase;
  anthropicResult: {
    status: "Valid" | "Need Review";
    details: string;
    success: boolean;
  };
  perplexityResult: {
    status: "Valid" | "Need Review";
    details: string;
    success: boolean;
  };
}

// Batch size for processing citations
const BATCH_SIZE = 8;

async function runCitationValidationTests() {
  const results: TestResult[] = [];
  
  // Create a simple conversation context for testing
  const conversationSummary = "Discussion about important legal precedents and their implications in modern law.";
  const userQuery = "What are some landmark cases in American law and their significance?";
  
  console.log("Starting citation validation tests...");
  
  // Shuffle the test cases to avoid clumping similar types together
  const shuffledTestCases = shuffleArray([...CITATION_TEST_CASES]);
  
  // Split into batches
  const batches = createBatches(shuffledTestCases, BATCH_SIZE);
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i+1}/${batches.length} with ${batch.length} citations`);
    
    // Create a generic response text that includes all citations in the batch
    const responseText = createBatchResponseText(batch);
    
    try {
      // Test with Anthropic
      const anthropicResult = await CitationValidationService.validateCitations(
        conversationSummary,
        userQuery,
        responseText
      );
      
      // Test with Perplexity
      const perplexityResult = await CitationValidationService.validateCitationsWithPerplexity(
        conversationSummary,
        userQuery,
        responseText
      );
      
      // Process results for each citation in the batch
      for (const testCase of batch) {
        // Find the corresponding citation in the results
        const anthropicCitation = findCitationInResults(anthropicResult.citations, testCase.citation);
        const perplexityCitation = findCitationInResults(perplexityResult.citations, testCase.citation);
        
        // Determine if the validation was successful
        const anthropicSuccess = 
          (testCase.validity === "Valid" && anthropicCitation?.status === "Valid") ||
          (testCase.validity === "Invalid" && anthropicCitation?.status === "Need Review");
          
        const perplexitySuccess = 
          (testCase.validity === "Valid" && perplexityCitation?.status === "Valid") ||
          (testCase.validity === "Invalid" && perplexityCitation?.status === "Need Review");
        
        results.push({
          testCase,
          anthropicResult: {
            status: anthropicCitation?.status || "Need Review",
            details: anthropicCitation?.details || "No details provided",
            success: anthropicSuccess
          },
          perplexityResult: {
            status: perplexityCitation?.status || "Need Review",
            details: perplexityCitation?.details || "No details provided",
            success: perplexitySuccess
          }
        });
      }
      
    } catch (error) {
      Logger.error(`Error testing batch ${i+1}:`, error);
      
      // Add failed tests to results
      for (const testCase of batch) {
        results.push({
          testCase,
          anthropicResult: {
            status: "Need Review",
            details: `Test error: ${(error as Error).message}`,
            success: false
          },
          perplexityResult: {
            status: "Need Review",
            details: `Test error: ${(error as Error).message}`,
            success: false
          }
        });
      }
    }
    
    // Add a delay between batches to avoid rate limiting
    if (i < batches.length - 1) {
      console.log("Waiting between batches...");
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // Output results
  outputResults(results);
  
  return results;
}

// Helper function to shuffle an array (Fisher-Yates algorithm)
function shuffleArray<T>(array: T[]): T[] {
  const result = [...array];
  for (let i = result.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [result[i], result[j]] = [result[j], result[i]];
  }
  return result;
}

// Helper function to create batches from an array
function createBatches<T>(array: T[], batchSize: number): T[][] {
  const batches: T[][] = [];
  for (let i = 0; i < array.length; i += batchSize) {
    batches.push(array.slice(i, i + batchSize));
  }
  return batches;
}

// Helper function to create a generic response text containing all citations in a batch
function createBatchResponseText(batch: CitationTestCase[]): string {
  let responseText = "Here are several landmark legal cases that have shaped American jurisprudence:\n\n";
  
  batch.forEach((testCase, index) => {
    responseText += `${index + 1}. ${testCase.citation}\n`;
    responseText += "   This case established important legal principles that continue to influence modern law.\n\n";
  });
  
  responseText += "\nThese cases represent foundational precedents in their respective areas of law. They demonstrate how the judicial system has evolved over time and continues to shape our understanding of legal rights and responsibilities.";
  
  return responseText;
}

// Helper function to find a citation in the results array
function findCitationInResults(citations: any[], citationText: string): any {
  // First try exact match
  let match = citations.find(c => c.citation === citationText);
  
  // If no exact match, try to find a citation that contains the key parts
  if (!match) {
    const caseName = citationText.split(',')[0].trim();
    const reporter = citationText.match(/\d+\s+[A-Za-z\.]+\s+\d+/)?.[0];
    
    match = citations.find(c => 
      c.citation.includes(caseName) && 
      (reporter ? c.citation.includes(reporter) : true)
    );
  }
  
  return match;
}

function outputResults(results: TestResult[]) {
  console.log("\n=== CITATION VALIDATION TEST RESULTS ===\n");
  
  // Calculate overall statistics
  const totalTests = results.length;
  const anthropicSuccesses = results.filter(r => r.anthropicResult.success).length;
  const perplexitySuccesses = results.filter(r => r.perplexityResult.success).length;
  
  console.log(`Total test cases: ${totalTests}`);
  console.log(`Anthropic success rate: ${anthropicSuccesses}/${totalTests} (${(anthropicSuccesses/totalTests*100).toFixed(2)}%)`);
  console.log(`Perplexity success rate: ${perplexitySuccesses}/${totalTests} (${(perplexitySuccesses/totalTests*100).toFixed(2)}%)`);
  
  // Calculate success rates by citation validity
  const validCases = results.filter(r => r.testCase.validity === "Valid");
  const invalidCases = results.filter(r => r.testCase.validity === "Invalid");
  
  const anthropicValidSuccesses = validCases.filter(r => r.anthropicResult.success).length;
  const anthropicInvalidSuccesses = invalidCases.filter(r => r.anthropicResult.success).length;
  
  const perplexityValidSuccesses = validCases.filter(r => r.perplexityResult.success).length;
  const perplexityInvalidSuccesses = invalidCases.filter(r => r.perplexityResult.success).length;
  
  console.log("\n=== SUCCESS RATES BY CITATION TYPE ===\n");
  console.log(`Valid citations (${validCases.length} total):`);
  console.log(`  Anthropic: ${anthropicValidSuccesses}/${validCases.length} (${(anthropicValidSuccesses/validCases.length*100).toFixed(2)}%)`);
  console.log(`  Perplexity: ${perplexityValidSuccesses}/${validCases.length} (${(perplexityValidSuccesses/validCases.length*100).toFixed(2)}%)`);
  
  console.log(`Invalid citations (${invalidCases.length} total):`);
  console.log(`  Anthropic: ${anthropicInvalidSuccesses}/${invalidCases.length} (${(anthropicInvalidSuccesses/invalidCases.length*100).toFixed(2)}%)`);
  console.log(`  Perplexity: ${perplexityInvalidSuccesses}/${invalidCases.length} (${(perplexityInvalidSuccesses/invalidCases.length*100).toFixed(2)}%)`);
  
  console.log("\n=== DETAILED RESULTS ===\n");
  
  results.forEach((result, index) => {
    console.log(`Test #${index + 1}: ${result.testCase.citation}`);
    console.log(`Expected: ${result.testCase.validity}`);
    console.log(`Type: ${result.testCase.type}`);
    console.log(`Notes: ${result.testCase.notes}`);
    console.log("\nAnthropic Result:");
    console.log(`Status: ${result.anthropicResult.status}`);
    console.log(`Details: ${result.anthropicResult.details}`);
    console.log(`Success: ${result.anthropicResult.success ? "✅" : "❌"}`);
    console.log("\nPerplexity Result:");
    console.log(`Status: ${result.perplexityResult.status}`);
    console.log(`Details: ${result.perplexityResult.details}`);
    console.log(`Success: ${result.perplexityResult.success ? "✅" : "❌"}`);
    console.log("\n" + "-".repeat(80) + "\n");
  });
}

// Export the function to run the tests
export { runCitationValidationTests };

// If this file is run directly, execute the tests
if (require.main === module) {
  dotenv.config();
  runCitationValidationTests()
    .then(() => {
      console.log("Tests completed");
      process.exit(0);
    })
    .catch(error => {
      Logger.error("Test execution failed:", error);
      process.exit(1);
    });
}