import { Logger } from "../../utils/Logger";
import Anthropic from "@anthropic-ai/sdk";
// import fs from 'fs';
// import path from 'path';

// Define interfaces for structured data
export interface Citation {
  id: string;
  citation: string;
  url?: string;
  status: "Valid" | "Need Review";
  details: string;
}

export interface CitationValidationResult {
  citations: Citation[];
  overallAssessment: string;
  validCitationsCount: number;
  needReviewCount: number;
  rawResponse?: string;
}

export class CitationValidationService {
  static async validateCitations(
    conversationSummary: string,
    userQuery: string,
    responseText: string,
    researcherData?: {
      mainContent: string;
      citations: string[];
    }
  ): Promise<CitationValidationResult> {
    try {
      const anthropicClient = new Anthropic({
        apiKey: process.env.ANTHROPIC_API_KEY,
      });

      // // Format researcher citations if available
      // const researcherResultsText = researcherData?.citations?.length 
      //   ? `\nRESEARCHER RESULTS (used as reference for the final answer):\n${researcherData.mainContent}\n\nREFERENCE SOURCES:\n${researcherData.citations.map((citation, index) => `${index + 1}. ${citation}`).join('\n')}`
      //   : '';

      // Define the citation validation tool
      // Expected structured response format:
      // {
      //   citations: [{ id: "1", citation: "text", url: "optional-url", status: "Valid|Need Review", details: "explanation" }, ...],
      //   overallAssessment: "text summary of all citations",
      //   validCitationsCount: number,
      //   needReviewCount: number
      // }
      const tools = [
        {
          name: "validate_citations",
          description: "Validates citations in a text and returns structured assessment data",
          input_schema: {
            type: "object" as const,
            properties: {
              citations: {
                type: "array" as const,
                items: {
                  type: "object" as const,
                  properties: {
                    id: {
                      type: "string" as const,
                      description: "Unique identifier for the citation (e.g., '1', '2', etc.)"
                    },
                    citation: {
                      type: "string" as const,
                      description: "The full text of the citation as it appears in the response"
                    },
                    url: {
                      type: "string" as const,
                      description: "URL of the citation source if available"
                    },
                    status: {
                      type: "string" as const,
                      enum: ["Valid", "Need Review"],
                      description: "Assessment of the citation's validity"
                    },
                    details: {
                      type: "string" as const,
                      description: "Explanation of the assessment, including any issues found"
                    }
                  },
                  required: ["id", "citation", "status", "details"]
                }
              },
              overallAssessment: {
                type: "string" as const,
                description: "Overall assessment of all citations in the response"
              }
            },
            required: ["citations", "overallAssessment"]
          }
        }
      ];

      const systemPrompt = `You are an expert citation validator for legal and academic content. Your task is to identify and validate citations in the provided response.

For each citation:
1. Extract the citation text
2. Determine if it's valid based on the provided conversation summary and user query
3. Provide details about your assessment

Use the validate_citations tool to return structured data about your findings.`;

      const userPrompt = `I need you to validate the citations in this response.

CONVERSATION SUMMARY:
${conversationSummary}

USER QUERY:
${userQuery}

RESPONSE TO VALIDATE:
${responseText}

Please identify all citations in the response and assess their validity. For each citation:
1. Assign a unique ID
2. Extract the full citation text
3. Include the URL if available
4. Mark as "Valid" if it accurately represents the source, or "Need Review" if there are issues
5. Provide details explaining your assessment

Then give an overall assessment of the citation quality.`;

      // First message to get Claude to use the tool
      const response = await anthropicClient.messages.create({
        // model: "claude-3-opus-20240229",
        model: "claude-opus-4-20250514",
        max_tokens: 4000,
        system: systemPrompt,
        messages: [{ role: "user", content: userPrompt }],
        tools: tools,
      });

      // Check if Claude used the tool
      const toolUse = response.content.find(block => block.type === "tool_use");
      
      if (!toolUse || toolUse.type !== "tool_use" || toolUse.name !== "validate_citations") {
        // If Claude didn't use the tool, fall back to text parsing
        Logger.warn("Claude didn't use the validation tool, falling back to text parsing");
        return this.parseValidationFromText(response);
      }
      
      // Extract the structured data from the tool use
      const validationData = toolUse.input as {
        citations: Citation[];
        overallAssessment: string;
      };
      
      // Get the raw text response for debugging/display purposes
      let rawResponse = "";
      for (const block of response.content) {
        if (block.type === "text") {
          rawResponse += block.text;
        }
      }
      
      // Calculate counts from the citations array
      let validCitationsCount = 0;
      let needReviewCount = 0;
      try {
      validCitationsCount = validationData.citations.filter(c => c.status === "Valid").length;
      needReviewCount = validationData.citations.filter(c => c.status === "Need Review").length;
      }
      catch (error) {
        Logger.error("Error calculating citation counts:", error);
        console.log(validationData)
        throw error;
      }
      
      return {
        citations: validationData.citations,
        overallAssessment: validationData.overallAssessment,
        validCitationsCount,
        needReviewCount,
        rawResponse
      };
    } catch (error) {
      Logger.error("Error validating citations:", error);
      
      // Return a fallback result
      return {
        citations: [],
        overallAssessment: "Citation validation failed due to an error.",
        validCitationsCount: 0,
        needReviewCount: 0,
        rawResponse: (error as Error).message
      };
    }
  }
  
  // Fallback method to parse validation from text response
  private static parseValidationFromText(response: Anthropic.Message): CitationValidationResult {
    try {
      let rawResponse = "";
      for (const block of response.content) {
        if (block.type === "text") {
          rawResponse += block.text;
        }
      }
      
      // Extract citations using regex patterns
      const citations: Citation[] = [];
      
      // Look for citation patterns in the text
      // Pattern 1: Numbered citations with assessment
      const citationPattern = /(?:Citation|Reference)\s+(\d+):\s*(?:"([^"]+)")?([^\n]+)?(?:Status|Assessment):\s*(Valid|Need Review|Invalid|Questionable)[^\n]*(?:Details|Reason):\s*([^\n]+)/gi;
      
      let match;
      while ((match = citationPattern.exec(rawResponse)) !== null) {
        citations.push({
          id: match[1],
          citation: match[2] || match[3] || "Unknown citation",
          status: match[4] === "Valid" ? "Valid" : "Need Review",
          details: match[5] || "No details provided"
        });
      }
      
      // If no citations found with the pattern, try to extract from bullet points
      if (citations.length === 0) {
        const bulletPoints = rawResponse.split(/\n\s*[-•*]\s+/);
        bulletPoints.forEach((point, index) => {
          if (point.includes("citation") || point.includes("reference")) {
            citations.push({
              id: String(index + 1),
              citation: point.substring(0, 100) + "...",
              status: point.toLowerCase().includes("valid") ? "Valid" : "Need Review",
              details: point
            });
          }
        });
      }
      
      // Extract overall assessment
      let overallAssessment = "No overall assessment provided";
      const overallPattern = /(?:Overall Assessment|Overall|Summary):\s*([^\n]+)/i;
      const overallMatch = rawResponse.match(overallPattern);
      if (overallMatch) {
        overallAssessment = overallMatch[1];
      }
      
      // Calculate counts from the citations array
      const validCitationsCount = citations.filter(c => c.status === "Valid").length;
      const needReviewCount = citations.filter(c => c.status === "Need Review").length;
      
      return {
        citations,
        overallAssessment,
        validCitationsCount,
        needReviewCount,
        rawResponse
      };
    } catch (error) {
      Logger.error("Error parsing validation text:", error);
      return {
        citations: [],
        overallAssessment: "Failed to parse citation validation results.",
        validCitationsCount: 0,
        needReviewCount: 0,
        rawResponse: ""
      };
    }
  }

  static async validateCitationsWithPerplexity(
    conversationSummary: string,
    userQuery: string,
    responseText: string,
    researcherData?: {
      mainContent: string;
      citations: string[];
    }
  ): Promise<CitationValidationResult> {
    try {
      // Construct the prompt for Perplexity
      const prompt = `You are an expert citation validator for legal and academic content. Your task is to identify and validate citations in the provided response.

CONVERSATION SUMMARY:
${conversationSummary}

USER QUERY:
${userQuery}

RESPONSE TO VALIDATE:
${responseText}

Identify all citations and assess their validity. For each citation:
1. Assign a unique ID
2. Extract ONLY the exact citation reference (e.g., case name, statute, or reference) without surrounding context
3. Include the URL if available
4. Mark as "Valid" if it accurately represents the source, or "Need Review" if there are issues
5. Provide details explaining your assessment

Format your response as a JSON object with this structure:
{
  "citations": [
    {
      "id": "1",
      "citation": "citation reference",
      "url": "optional url",
      "status": "Valid or Need Review",
      "details": "explanation"
    }
  ],
  "overallAssessment": "summary of all citations"
}`;

      // Call Perplexity API
      const response = await fetch(process.env.PERPLEXITY_API_URL || "https://api.perplexity.ai/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.PERPLEXITY_API_KEY}`
        },
        body: JSON.stringify({
          model: "sonar-pro",
          messages: [
            { role: "system", content: "You are an expert citation validator for legal and academic content." },
            { role: "user", content: prompt }
          ],
          response_format: { 
            type: "json_schema", 
            json_schema: {
              schema: {
                type: "object",
                properties: {
                  citations: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        citation: { type: "string" },
                        url: { type: "string" },
                        status: { type: "string", enum: ["Valid", "Need Review"] },
                        details: { type: "string" }
                      },
                      required: ["id", "citation", "status", "details"]
                    }
                  },
                  overallAssessment: { type: "string" }
                },
                required: ["citations", "overallAssessment"]
              }
            }
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Perplexity API error: ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content;
      
      if (!content) {
        throw new Error("No content returned from Perplexity API");
      }

      // // Write the response to a JSON file for debugging
      // try {
      //   const debugDir = path.join(process.cwd(), 'debug');
      //   if (!fs.existsSync(debugDir)) {
      //     fs.mkdirSync(debugDir, { recursive: true });
      //   }
        
      //   const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      //   const filePath = path.join(debugDir, `perplexity-response-${timestamp}.json`);
        
      //   fs.writeFileSync(filePath, JSON.stringify({
      //     prompt,
      //     response: data,
      //     content
      //   }, null, 2));
        
      //   Logger.info(`Perplexity response saved to ${filePath}`);
      // } catch (fileError) {
      //   Logger.error("Error saving Perplexity response to file:", fileError);
      // }

      // Parse the JSON response
      let validationData: {
        citations: Citation[];
        overallAssessment: string;
      };
      
      try {
        validationData = JSON.parse(content);
        
        // Ensure the response has the expected structure
        if (!validationData.citations || !Array.isArray(validationData.citations)) {
          throw new Error("Invalid response format: citations array missing");
        }
        
        // Calculate counts from the citations array
        const validCitationsCount = validationData.citations.filter(c => c.status === "Valid").length;
        const needReviewCount = validationData.citations.filter(c => c.status === "Need Review").length;
        
        return {
          citations: validationData.citations,
          overallAssessment: validationData.overallAssessment,
          validCitationsCount,
          needReviewCount,
          rawResponse: content
        };
        
      } catch (parseError) {
        Logger.error("Error parsing Perplexity response:", parseError);
        return this.parseValidationFromText({
          content: [{ type: "text", text: content }]
        } as any);
      }
    } catch (error) {
      Logger.error("Error validating citations with Perplexity:", error);
      
      // Return a fallback result
      return {
        citations: [],
        overallAssessment: "Citation validation failed due to an error.",
        validCitationsCount: 0,
        needReviewCount: 0,
        rawResponse: (error as Error).message
      };
    }
  }
}
