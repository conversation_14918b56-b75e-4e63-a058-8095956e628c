import { ragDebug } from "@/lib/utils/debug";
import { db } from "@/lib/db";
import { sourceDocuments } from "@/lib/db/schema";
import { getChatById, saveChat } from "@/lib/db/queries";
import { Logger } from "../utils/Logger";
import { expandMimeType } from "@/lib/utils";
import { generateAndStoreEmbeddingsVoyage } from "./ragV1";
import { ProcessedChunk } from "@/lib/types";

export async function processFileForRAG(
  chunks: ProcessedChunk[],
  filename: string,
  url: string,
  userId: string,
  chatId: string,
  fileType: string
) {
  if (!chatId) {
    throw new Error("chatId is required for attachment file processing");
  }

  console.log("📝 Starting file processing for:", filename);

  try {
    // Check if chat exists
    const chat = await getChatById({ id: chatId });

    // Create chat if it doesn't exist
    if (!chat) {
      try {
        const title = `${filename}`;
        await saveChat({
          id: chatId,
          userId,
          title
        });
        console.log("📝 Created new chat for File", { chatId, title });
      } catch (error) {
        // If error is due to duplicate key, ignore it (another concurrent upload already created it)
        if (error instanceof Error && error.message.includes('duplicate key')) {
          console.log("📝 Chat already exists (created by another upload)");
        } else {
          // For other errors, rethrow
          throw error;
        }
      }
    }

    // Create source document
    const [sourceDoc] = await db
      .insert(sourceDocuments)
      .values({
        filename,
        url,
        userId,
        chatId,
      })
      .returning();

    console.log("📝 Created source document record", {
      id: sourceDoc.id,
      filename: sourceDoc.filename,
      chatId,
    });

    // Expand normalized MIME type back to full form for processing
    const expandedFileType = expandMimeType(fileType);

    if (expandedFileType === "application/pdf") {
      Logger.debug("Processing PDF chunks for embeddings");
      Logger.debug(`Chunks count: ${chunks.length}`);
      // Generate and store embeddings with the processed chunks
      await generateAndStoreEmbeddingsVoyage(chunks, sourceDoc.id);
    }
    if (expandedFileType.startsWith("image")) {
      // Can add image processing logic here later
    }

    return {
      sourceDocumentId: sourceDoc.id,
    };
  } catch (error) {
    Logger.error("❌ File processing failed:", error);
    ragDebug("File processing error: %O", error);
    throw error;
  }
}

export function fixMessageAttachments(messages: any[]): any[] {
  return messages.map((message) => {
    if (!message.experimental_attachments) {
      return message;
    }

    return {
      ...message,
      experimental_attachments: message.experimental_attachments.map(
        (attachment: any) => ({
          ...attachment,
          contentType:
            attachment.contentType || inferContentType(attachment.name),
        })
      ),
    };
  });
}

// Add helper function to infer content type
function inferContentType(filename: string): string {
  if (filename.endsWith(".pdf")) return "application/pdf";
  if (filename.match(/\.(jpg|jpeg|png|gif)$/i)) return "image/*";
  return "application/octet-stream";
}
