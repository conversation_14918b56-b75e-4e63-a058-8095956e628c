import { <PERSON>A<PERSON> } from "openai";
import type { CreateEmbeddingResponse } from "openai/resources/embeddings";
import { ragDebug } from "@/lib/utils/debug";
import { db } from "@/lib/db";
import { resources, embeddings, sourceDocuments } from "@/lib/db/schema";
import { sql } from "drizzle-orm";
import { Logger } from "../utils/Logger";
import { RAGPipeline } from "../ai/rag";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Optimized constants
const BATCH_SIZE = 20; // Increased for better throughput
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

const SIMILARITY_THRESHOLD = 0.75;
const MAX_RESULTS = 5;
const MAX_TOKENS_PER_RESPONSE = 8192;

interface EmbeddingBatch {
  texts: string[];
  startIdx: number;
}

interface BatchTiming {
  total: string;
  embedding: string;
  storage: string;
}

interface BatchStats {
  batch: number;
  total: number;
  chunks: number;
  timing: BatchTiming;
}

interface CompletionStats {
  totalChunks: number;
  batches: number;
  timing: {
    total: string;
    embedding: string;
    storage: string;
    avgPerBatch: string;
  };
}

export async function generateAndStoreEmbeddings(
  chunks: readonly string[],
  sourceDocumentId: string,
  chatId: string
): Promise<void> {
  const startTime = Date.now();
  Logger.debug("Starting embedding generation", {
    totalChunks: chunks.length,
    sourceDocumentId,
    chatId,
    startedAt: new Date().toISOString(),
  });

  // Use your existing RAG pipeline for all embedding logic.
  const pipeline = new RAGPipeline();

  // Split the input chunks into batches
  const numBatches = Math.ceil(chunks.length / BATCH_SIZE);
  let totalEmbeddingTime = 0;
  let totalStorageTime = 0;

  for (let batchIndex = 0; batchIndex < numBatches; batchIndex++) {
    const batchChunks = chunks.slice(
      batchIndex * BATCH_SIZE,
      (batchIndex + 1) * BATCH_SIZE
    );
    let retries = 0;
    let success = false;

    while (!success && retries < MAX_RETRIES) {
      try {
        const batchStartTime = Date.now();

        // Generate embeddings using the RAG pipeline's embedText method
        const embeddingStartTime = Date.now();
        const embeddingVectors = await Promise.all(
          batchChunks.map((text) => pipeline.embedText(text))
        );
        const embeddingTime = Date.now() - embeddingStartTime;
        totalEmbeddingTime += embeddingTime;

        // Store the chunk and its embedding in the database.
        const storageStartTime = Date.now();
        await db.transaction(async (tx) => {
          // Insert each chunk into the resources table.
          const insertedResources = await tx
            .insert(resources)
            .values(
              batchChunks.map((content) => ({
                content,
                sourceDocumentId,
                chatId, // if your table has a column for chatId
              }))
            )
            .returning();

          // Now insert the corresponding embeddings referencing the resource IDs.
          await tx.insert(embeddings).values(
            insertedResources.map((resource, idx) => ({
              resourceId: resource.id,
              content: batchChunks[idx],
              embedding: embeddingVectors[idx],
            }))
          );
        });
        const storageTime = Date.now() - storageStartTime;
        totalStorageTime += storageTime;

        const batchDuration = Date.now() - batchStartTime;
        Logger.debug("Completed batch", {
          batch: batchIndex + 1,
          totalBatches: numBatches,
          chunks: batchChunks.length,
          timing: {
            total: `${batchDuration}ms`,
            embedding: `${embeddingTime}ms`,
            storage: `${storageTime}ms`,
          },
        });

        success = true;
      } catch (error) {
        retries++;
        if (retries >= MAX_RETRIES) {
          Logger.error("Embedding batch failed after max retries", {
            batch: batchIndex + 1,
            error: error instanceof Error ? error.message : "Unknown error",
          });
          throw error;
        }
        Logger.warn("Batch failed, retrying...", {
          batch: batchIndex + 1,
          retry: retries,
          error: error instanceof Error ? error.message : "Unknown error",
        });
        await new Promise((resolve) =>
          setTimeout(resolve, RETRY_DELAY * retries)
        );
      }
    }
  }

  const totalDuration = Date.now() - startTime;
  Logger.debug("Embedding generation complete", {
    totalChunks: chunks.length,
    batches: numBatches,
    timing: {
      total: `${totalDuration}ms`,
      embedding: `${totalEmbeddingTime}ms`,
      storage: `${totalStorageTime}ms`,
      avgPerBatch: `${Math.round(totalDuration / numBatches)}ms`,
    },
  });
}

export async function searchEmbeddings(query: string, chatId: string) {
  const startTime = Date.now();

  try {
    const embedding = await openai.embeddings.create({
      model: "text-embedding-ada-002",
      input: query,
    });

    const searchStartTime = Date.now();

    try {
      // Modified query to include chatId filter
      const results = await db
        .select({
          content: sql`${embeddings.content}::text`,
          similarity: sql`cosine_similarity(${embeddings.embedding}, ${sql.raw(
            `'[${embedding.data[0].embedding.join(",")}]'::vector`
          )})`,
          resourceId: embeddings.resourceId,
          filename: sourceDocuments.filename, // Include filename in the result
        })
        .from(embeddings)
        .innerJoin(resources, sql`${embeddings.resourceId} = ${resources.id}`)
        .innerJoin(
          sourceDocuments,
          sql`${resources.sourceDocumentId} = ${sourceDocuments.id}`
        )
        .where(
          sql`${sourceDocuments.chatId} = ${chatId} AND 
              cosine_similarity(${embeddings.embedding}, ${sql.raw(
            `'[${embedding.data[0].embedding.join(",")}]'::vector`
          )}) > ${SIMILARITY_THRESHOLD}`
        )
        .orderBy(
          sql`cosine_similarity(${embeddings.embedding}, ${sql.raw(
            `'[${embedding.data[0].embedding.join(",")}]'::vector`
          )}) DESC`
        )
        .limit(MAX_RESULTS);

      // Logging results with chatId context
      Logger.debug(`=== RAG SEARCH RESULTS ===
        ${JSON.stringify(
          {
            type: "rag_results",
            chatId,
            resultsFound: results.length,
            results: results.map((r: any) => ({
              preview: r.content.substring(0, 150) + "...",
              contentLength: r.content.length,
              similarity: r.similarity,
              resourceId: r.resourceId,
              filename: r.filename, // Add filename to the logged output
            })),
            searchTime: `${Date.now() - searchStartTime}ms`,
            totalTime: `${Date.now() - startTime}ms`,
          },
          null,
          2
        )}
    `);

      return results;
    } catch (searchError) {
      Logger.error("RAG Search Error:", {
        error:
          searchError instanceof Error ? searchError.message : "Unknown error",
        chatId,
        timestamp: new Date().toISOString(),
      });
      return [];
    }
  } catch (error) {
    Logger.error("RAG Embedding Error:", {
      error: error instanceof Error ? error.message : "Unknown error",
      chatId,
      timestamp: new Date().toISOString(),
    });
    return [];
  }
}

// Improved results processor
export function processResultsWithContext(
  results: Array<{ content: string; similarity?: number }>,
  maxTokens: number = MAX_TOKENS_PER_RESPONSE
): string {
  if (!results?.length) {
    return "No relevant content found.";
  }

  // Cleaning and formatting results
  const cleanedResults = results.map((result) => {
    let content = result.content
      .replace(/(?:\\[nr]|\r?\n|\r)+/g, "\n") // Normalize all newlines
      .replace(/\s+/g, " ") // Normalize whitespace
      .trim();

    // Extract section if present
    const sectionMatch = content.match(
      /(?:Section\s*)?(\d+(?:\.\d+)*)[.\s]+(.*)/i
    );
    const section = sectionMatch ? sectionMatch[1] : "";
    const text = sectionMatch ? sectionMatch[2] : content;

    return {
      section,
      content: text,
      similarity: result.similarity,
    };
  });

  // Group by section if sections exist
  const sections = new Map<string, string>();
  cleanedResults.forEach(({ section, content }) => {
    if (section) {
      sections.set(section, content);
    }
  });

  // Format output
  let output = "";
  if (sections.size > 0) {
    output = Array.from(sections.entries())
      .sort(([a], [b]) => a.localeCompare(b, undefined, { numeric: true }))
      .map(([section, content]) => `Section ${section}:\n${content}`)
      .join("\n\n");
  } else {
    output = cleanedResults
      .map(({ content }, i) => `Result ${i + 1}:\n${content}`)
      .join("\n\n");
  }

  return output;
}
