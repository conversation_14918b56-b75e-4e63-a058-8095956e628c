import { db } from "../db/db";
import { document, documentAuditLog, user, artifact, documentFolder } from "../db/schema";
import { Logger } from "../utils/Logger";
import { eq, and, desc, sql, inArray, count, ilike } from "drizzle-orm";
import { orderBy, uniqBy } from "lodash-es";

// Types for audit log operations
export type AuditAction = "VIEW" | "DOWNLOAD" | "SHARE" | "UNSHARE" | "EDIT" | "DELETE" | "CHAT";
export type EntityType = "DOCUMENT" | "FOLDER";
export type TargetType = "USER" | "ORGANIZATION" | "PROJECT";
export type OperationResult = "SUCCESS" | "FAILED";

// Interface for audit log entry
export interface DocumentAuditEntry {
  entityId: string;
  entityType: EntityType;
  action: AuditAction;
  operatorId: string;
  targetId: string;
  targetType: TargetType;
  result?: OperationResult;
  errorMessage?: string;
  additionalInfo?: Record<string, any>;
}
/**
 * Record document audit events in batch. Each entry can have its own documentId, documentUrl, fileSize, etc.
 */
export async function recordDocumentAudit({
  entries,
  userId,
  targetId,
  targetType = "USER",
  additionalInfo,
  result = "SUCCESS",
  errorMessage,
  action = "DOWNLOAD",
}: {
  entries: Array<{
    documentId: string;
    documentUrl?: string;
    fileSize?: number;
    additionalInfo?: Record<string, any>;
    targetId?: string;
    targetType?: TargetType;
  }>;
  userId: string;
  targetId?: string;
  targetType?: TargetType;
  additionalInfo?: Record<string, any>; // global additional info, merged into each entry
  result?: OperationResult;
  errorMessage?: string;
  action?: AuditAction;
}): Promise<void> {
  if (!entries || entries.length === 0) {
    Logger.error("No entries provided for audit log");
    return;
  }
  try {
    const now = new Date().toISOString();
    const values = entries.map((entry) => ({
      entityId: entry.documentId,
      entityType: "DOCUMENT",
      action,
      operatorId: userId,
      targetId: entry.targetId || targetId || "",
      targetType: entry.targetType || targetType,
      result,
      errorMessage,
      additionalInfo: {
        timestamp: now,
        documentUrl: entry.documentUrl,
        fileSize: entry.fileSize,
        ...additionalInfo,
        ...entry.additionalInfo,
      },
    }));
    await db.insert(documentAuditLog).values(values);
    Logger.info("Batch document audit logs recorded successfully", {
      documentIds: entries.map(e => e.documentId),
      userId,
      targetId,
      targetType,
      count: entries.length,
    });
  } catch (error) {
    Logger.error("Failed to record batch document audit logs", {
      documentIds: entries.map(e => e.documentId),
      userId,
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Get document audit history
 */
export async function getDocumentAuditHistory({
  documentId,
  userId,
  action,
  limit = 50,
  offset = 0,
}: {
  documentId?: string;
  userId?: string;
  action?: AuditAction;
  limit?: number;
  offset?: number;
}) {
  try {
    let query: any = db
      .select({
        id: documentAuditLog.id,
        entityId: documentAuditLog.entityId,
        entityType: documentAuditLog.entityType,
        action: documentAuditLog.action,
        operatorId: documentAuditLog.operatorId,
        targetId: documentAuditLog.targetId,
        targetType: documentAuditLog.targetType,
        result: documentAuditLog.result,
        errorMessage: documentAuditLog.errorMessage,
        additionalInfo: documentAuditLog.additionalInfo,
        createdAt: documentAuditLog.createdAt,
        operatorEmail: user.email,
      })
      .from(documentAuditLog)
      .leftJoin(user, eq(documentAuditLog.operatorId, user.id))
      .orderBy(desc(documentAuditLog.createdAt))
      .limit(limit)
      .offset(offset);

    // Add filters based on parameters
    const conditions = [];
    if (documentId) {
      conditions.push(eq(documentAuditLog.entityId, documentId));
    }
    if (userId) {
      conditions.push(eq(documentAuditLog.operatorId, userId));
    }
    if (action) {
      conditions.push(eq(documentAuditLog.action, action));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    const results = await query;

    Logger.info("Document audit history retrieved successfully", {
      documentId,
      userId,
      action,
      count: results.length,
    });

    return results;
  } catch (error) {
    Logger.error("Failed to retrieve document audit history", {
      documentId,
      userId,
      action,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

export async function getRecentlyDocumentsByUser({
  userId,
  fileName,
}: {
  userId: string;
  limit?: number;
  offset?: number;
  fileName?: string;
}) {
  try {
    // Retrieve these entity ids and key information to query the folders and artifact contents where these ids are located
    const documentAuditLogsUnique = await db.execute(
      sql`
        SELECT
        DISTINCT ON (entity_id) entity_id, created_at
        FROM document_audit_log
        WHERE operator_id = ${userId}
        AND action = 'CHAT'
        AND entity_type = 'DOCUMENT'
        AND result = 'SUCCESS'
        ORDER BY entity_id,created_at DESC
      `
    )
    console.log("🚀 ~ documentAuditLogsUnique:", documentAuditLogsUnique)
    const documentIds: string[] = documentAuditLogsUnique.map((log: any) => log.entity_id)
    const documentCreatedAtMap:Record<string, string> = {}
    documentAuditLogsUnique.forEach((log: any) => {
      documentCreatedAtMap[log.entity_id] = log.created_at
    })

    // Count the occurrences of actions per entity id
    const documentActions = await db
      .select({
        entityId: documentAuditLog.entityId,
        action: documentAuditLog.action,
        count: count(documentAuditLog.entityId),
      })
      .from(documentAuditLog)
      .where(and(
        eq(documentAuditLog.operatorId, userId),
        eq(documentAuditLog.action, "CHAT"),
        eq(documentAuditLog.entityType, "DOCUMENT"),
        eq(documentAuditLog.result, "SUCCESS"),
        inArray(documentAuditLog.entityId, documentIds),
      ))
      .groupBy(documentAuditLog.entityId, documentAuditLog.action)

      const documentActionsMap:Record<string, { CHAT: number, createdAt: string }> = {}
      documentActions.forEach(item => {
        if (!documentActionsMap[item.entityId]) {
          documentActionsMap[item.entityId] = {
            CHAT: 0,
            createdAt: documentCreatedAtMap[item.entityId],
          }
        }
        documentActionsMap[item.entityId][item.action as "CHAT"] = item.count
      })

    const documentsUsedInChat = await db
      .select({
        id: document.id,
        originalName: document.originalName,
        storageKey: artifact.storageKey,
        mime: document.mime,
        size: artifact.sizeByte,
        folderId: document.folderId,
        folderName: documentFolder.name,
        isFavorite: document.isFavorite,
      })
      .from(document)
      .leftJoin(artifact, eq(document.artifactId, artifact.id))
      .leftJoin(documentFolder, eq(document.folderId, documentFolder.id))
      .where(
        and(
          eq(document.uploadedBy, userId),
          inArray(document.id, documentIds),
          eq(document.artifactStatus, "AVAILABLE"),
          fileName ? ilike(document.originalName, `%${fileName}%`) : undefined,
        )
      )

    const documentsRecentlyUploaded = await db
      .select({
        id: document.id,
        originalName: document.originalName,
        storageKey: artifact.storageKey,
        mime: document.mime,
        size: artifact.sizeByte,
        folderId: document.folderId,
        folderName: documentFolder.name,
        isFavorite: document.isFavorite,
        createdAt: document.createdAt,
      })
      .from(document)
      .leftJoin(artifact, eq(document.artifactId, artifact.id))
      .leftJoin(documentFolder, eq(document.folderId, documentFolder.id))
      .where(
        and(
          eq(document.uploadedBy, userId),
          eq(document.artifactSource, "CHAT"),
          eq(document.artifactStatus, "AVAILABLE"),
          fileName ? ilike(document.originalName, `%${fileName}%`) : undefined,
        )
      )

    const results = [...documentsUsedInChat, ...documentsRecentlyUploaded].map(doc => {
      const actionData = documentActionsMap[doc.id]
      return {
        usedCount: actionData?.CHAT || 0,
        createdAt: actionData?.createdAt,
        ...doc,
      }
    })

    return {
      documents: orderBy(
        uniqBy(results, 'id'),
        [(it) => new Date(it.createdAt).getTime()], ['desc']
      ),
      documentActionsMap,
    };

  } catch (error) {
    Logger.error("Failed to retrieve recently used documents", {
      userId,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}