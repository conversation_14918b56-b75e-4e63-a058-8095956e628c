import { Logger } from "../../utils/Logger";
import { GoogleAIService } from "../../services/GoogleAIService";
import { Content, GenerateContentConfig, Part } from "@google/genai";

export class QueryRewriteService {
  static async rewriteQuery(
    query: string,
    chatHistory: string = "",
    processedImages: Array<{ mime_type: string; data: string, url: string }> = [],
    filesText: string = ""
  ): Promise<string> {
    try {
      const rewrittenQuery = await this.rewrite(
        chatHistory,
        processedImages,
        query,
        filesText
      );

      Logger.debug("Query rewrite result:", rewrittenQuery);

      return rewrittenQuery;
    } catch (error) {
      Logger.error("Error in query rewrite process:", error);
      return query;
    }
  }

  static async rewrite(
    history: string,
    processedImages: Array<{ mime_type: string; data: string }>,
    query: string,
    filesText: string = ""
  ): Promise<string> {
    //Rewrite the query to include prior context and dooucment to make it much more contextual.
    const modelIdentifier = GoogleAIService.MODEL_CONFIG.lite_model;

    const promptText = ` You are Iqidis Research Assistant Iq-mini. 
      You have to help me rewrite the given user query to be more detailed and dense. 
      Your output will be fed to our online research assistant which will search the internet for relevant case law and
      legal analysis to help the your users (lawyers and other legal professionals) to more detailed drafts and conduct deeper legal analysis. 
  
      To help you write the most comprehensive question possible, you will be given the following- 
      1. The chat history upto this point. 
      2. Relevant chunks from documents (if any are selected by the user).
      3. The actual query by the user. 
  
      With this context you will do the following- 
      1. Summarize all the important pieces of information in the context so that the online research tool has the relevant background information. 
      2. Rewrite the query to ask the Research tool to explore all legally significant angles pertaining to the given query and user documents. 
  
      For example when given access to a matter and asked to come up with counter argument to a motion- your output will contain an analysis of the 
      kinds of cases ("cases dealing with unfair detention of healthy person", "cases showing that drunk people can't sign important contracts" etc.) that will be helpful to your user. DO NOT actually give case names, only the desired content of the cases/legal material that will
      help your user fight the cases. The actual citations and names should be left to the main research tool.   
  
  
      Your output will be structured as following- 
  
      Information about the matter- Summary of chat so far, uploaded documents, and what the user wants. 
  
      Important angles to explore- the kinds of cases and legal citations that will help the user accomplish their goals.
  
      Both the above sections are important. So think deeply and in a detailed step by step manner to create your final output. 
  
      Now feeding you the context- 
      Previous conversation with the user:
      ${history}
  
      Current Question asked by the user: ${query}

      Relevant information from the document:
      ${filesText ? `${filesText}` : ""}
      `;

    // Create parts array for the content
    const parts: Part[] = [];
    parts.push({ text: promptText });

    // Adding processed images to parts if they exist
    for (const img of processedImages) {
      parts.push({
        inlineData: {
          mimeType: img.mime_type,
          data: img.data,
        },
      });
    }

    // Create a properly formatted Content object
    const content: Content = { 
      role: 'user',
      parts,
    };

    const config: GenerateContentConfig = {
      temperature: 0.0,
      topP: 0.2,
      topK: 32,
      maxOutputTokens: 2048,
      responseMimeType: "text/plain",
    };

    // Pass the content directly - it will be handled by GoogleAIService
    const { response } = await GoogleAIService.generateContentWithFallback(
      modelIdentifier,
      content,
      config
    );

    Logger.debug("Gemini response:", response);

    return response.text ?? '';
  }
}
