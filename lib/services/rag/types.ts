export type EmbeddingContentType = "text" | "image_base64";

export interface TextContent {
  type: "text";
  text: string;
}

export interface ImageContent {
  type: "image_base64";
  image_base64: string;
}

export type EmbeddingContent = TextContent | ImageContent;

export interface EmbeddingInput {
  content: EmbeddingContent[];
}

export interface VoyageEmbeddingResponse {
  data: Array<{
    embedding: number[];
  }>;
}

export interface PerplexityMessage {
  role: 'system' | 'user';
  content: string;
}

export interface PerplexityResponse {
  choices: Array<{ message: { content: string } }>;
  citations?: Array<unknown>;
}

export interface SearchResult {
  content: string;
  similarity: number;
  image_data?: {
    mime_type: string;
    data: string;
  };
}
