import { db } from "../../db";
import { embeddings, resources, sourceDocuments } from "../../db/schema";
import { SearchResult } from "./types";
import { Logger } from "../../utils/Logger";
import { sql } from "drizzle-orm";
import { VoyageEmbeddingService } from "./VoyageEmbeddingService";
import { CONFIG } from "./config";

// Define the expected result type from the database query
// Using Record<string, unknown> to satisfy Drizzle's constraint
interface QueryResult extends Record<string, unknown> {
  content: string;
  resourceId: string;
  filename: string;
  sourceDocumentId: string;
  createdAt: Date;
  similarity: number;
}

export class SearchService {
  static async searchEmbeddings(
    query: string,
    chatId: string
  ): Promise<SearchResult[]> {
    try {
      const startTime = Date.now();
      Logger.debug(`Searching embeddings for query: ${query}`);

      // Get query embedding from Voyage
      const queryEmbedding: number[] =
        await VoyageEmbeddingService.generateQueryEmbedding(query);

      const embeddingTime = Date.now();
      Logger.debug(
        `Query embedding generated in ${embeddingTime - startTime}ms`
      );

      const results1 = await db.execute<QueryResult>(sql`
        SELECT 
          ${embeddings.content}::text as content,
          ${embeddings.resourceId} as "resourceId",
          ${sourceDocuments.filename} as filename,
          ${resources.sourceDocumentId} as "sourceDocumentId",
          ${embeddings.createdAt} as "createdAt",
          1 - (${embeddings.embedding} <=> ${sql.raw(
        `'[${queryEmbedding.join(",")}]'::vector`
      )}) as similarity
        FROM ${embeddings}
        JOIN ${resources} ON ${embeddings.resourceId} = ${resources.id}
        JOIN ${sourceDocuments} ON ${resources.sourceDocumentId} = ${
        sourceDocuments.id
      }
        WHERE ${sourceDocuments.chatId} = ${chatId}
        ORDER BY ${embeddings.embedding} <=> ${sql.raw(
        `'[${queryEmbedding.join(",")}]'::vector`
      )} 
        LIMIT ${CONFIG.SIMILARITY.MAX_RESULTS * 2};
      `);

      // Filter after retrieval
      const results = results1
        .filter((r) => (r.similarity as number) > CONFIG.SIMILARITY.THRESHOLD)
        .slice(0, CONFIG.SIMILARITY.MAX_RESULTS);
      const queryTime = Date.now();
      Logger.debug(
        `Database query completed in ${queryTime - embeddingTime}ms`
      );
      Logger.debug(`Found ${results.length} similar chunks`);

      // Transform results into SearchResult type
      const searchResults: SearchResult[] = results.map((result) => ({
        content: result.content as string,
        similarity: result.similarity as number,
        resourceId: result.resourceId as string,
        filename: result.filename as string,
        sourceDocumentId: result.sourceDocumentId as string,
        createdAt: result.createdAt as Date,
        // Parse content to check if it's an image
        image_data: (result.content as string).startsWith("data:image")
          ? {
              mime_type: (result.content as string).split(";")[0].split(":")[1],
              data: (result.content as string).split(",")[1],
            }
          : {
              mime_type: "image/jpeg",
              data: result.content as string,
            },
      }));

      Logger.debug(`Total search time: ${Date.now() - startTime}ms`);
      return searchResults;
    } catch (error) {
      Logger.error("Error in searchEmbeddings:", error);
      throw error;
    }
  }
}
