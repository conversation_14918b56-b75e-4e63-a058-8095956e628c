import { Logger } from "@/lib/utils/Logger";
import { GoogleAIService } from "./GoogleAIService";

export class SummaryService {
  /**
   * Generates a summary of a conversation using a basic Gemini model
   * 
   * @param conversationText The conversation text to summarize
   * @returns A markdown-formatted summary of the conversation
   */
  static async generateSummary(conversationText: string): Promise<string> {
    try {
      Logger.info("Generating conversation summary with <PERSON>");
      
      // Create the summary prompt
      const summaryPrompt = `
Please create a concise summary of the following conversation. 
Focus on the main topics, key questions, information exchanged, and important conclusions.
Format the summary with markdown, using headers, bullet points, and brief paragraphs.
The summary should be comprehensive enough to continue the conversation but brief enough to be readable.

CONVERSATION:
${conversationText}

SUMMARY:`;

      // Generate the summary using GoogleAIService
      const { response } = await GoogleAIService.generateContentWithFallback(
        GoogleAIService.MODEL_CONFIG.lite_model,
        summaryPrompt
      );

      const summary = response.text || 'No summary generated';
      
      Logger.info("Successfully generated conversation summary");
      return summary;
    } catch (error) {
      Logger.error("Error generating summary with <PERSON>:", error);
      return this.generateFallbackSummary(conversationText);
    }
  }

  /**
   * Generates a fallback summary when the model fails
   * 
   * @param conversationText The conversation text to summarize
   * @returns A simple markdown-formatted summary
   */
  private static generateFallbackSummary(conversationText: string): string {
    try {
      // Extract user messages for a basic summary
      const lines = conversationText.split('\n');
      const userMessages: string[] = [];
      
      for (const line of lines) {
        if (line.startsWith('USER:')) {
          const message = line.substring(5).trim();
          if (message) {
            userMessages.push(message);
          }
        }
      }
      
      // Create a simple summary with the first few user messages
      return `# Chat Summary

## Key Topics Discussed:
${userMessages.slice(0, 5).map((msg, i) => {
  const content = msg.substring(0, 100);
  return `- ${content}${content.length >= 100 ? '...' : ''}`;
}).join('\n')}

This is a continuation of our previous conversation. I've summarized the key points above.`;
    } catch (fallbackError) {
      Logger.error("Error generating fallback summary:", fallbackError);
      return "# Chat Summary\n\nThis is a continuation of our previous conversation.";
    }
  }
}
