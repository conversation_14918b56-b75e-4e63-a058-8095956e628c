import { db } from "./db";
import { message } from "./schema";
import { eq, and, sql } from "drizzle-orm";
import { Logger } from "../utils/Logger";

/**
 * Update a message with citation validation results
 */
export async function updateMessageWithCitationValidation({
  messageId,
  userId,
  citationValidation,
}: {
  messageId: string;
  userId: string;
  citationValidation: string;
}) {
  try {
    // First verify the message exists and belongs to the user
    const result = await db
      .update(message)
      .set({
        metadata: sql`
          CASE
            WHEN ${message.metadata} IS NULL THEN jsonb_build_object('citationValidation', ${citationValidation}::jsonb)
            ELSE jsonb_set(${message.metadata}::jsonb, '{citationValidation}', ${citationValidation}::jsonb)
          END
        `,
      })
      .where(
        and(
          eq(message.id, messageId),
          // We don't directly check userId here since messages don't have userId
          // The API will verify chat ownership separately
        )
      )
      .returning();

    if (result.length === 0) {
      throw new Error("Message not found or update failed");
    }

    return result[0];
  } catch (error) {
    Logger.error("Error updating message with citation validation:", error);
    throw error;
  }
}




