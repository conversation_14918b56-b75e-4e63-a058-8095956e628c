import type { InferSelectModel } from "drizzle-orm";
import {
  pgTable,
  varchar,
  timestamp,
  json,
  uuid,
  text,
  primaryKey,
  foreignKey,
  boolean,
  customType,
  integer,
  index,
  jsonb,
  serial,
  unique,
  numeric,
  decimal,
  type AnyPgColumn,
  bigint,
} from "drizzle-orm/pg-core";
import { sql, relations } from "drizzle-orm";

// Reusable timestamp mixin for consistent timestamp fields
// This doesn't change existing tables but provides a pattern for new ones
const timestampFields = {
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
};

// Reusable soft delete mixin
const softDeleteField = {
  isDeleted: boolean("is_deleted").default(false).notNull(),
};

// Reusable audit fields mixin
const auditFields = {
  createdById: uuid("created_by_id").references(() => user.id),
  updatedById: uuid("updated_by_id").references(() => user.id),
};

export const user = pgTable(
  "User",
  {
    id: uuid("id").notNull().defaultRandom(),
    email: varchar("email", { length: 64 }).notNull(),
    status: varchar("status", { length: 20 }).default("active"),
    password: varchar("password", { length: 64 }),
    isAdmin: boolean("isAdmin").notNull().default(false),
    firstname: varchar("firstname", { length: 64 }),
    lastname: varchar("lastname", { length: 64 }),
    company: varchar("company", { length: 100 }),
    teamsize: varchar("teamsize", { length: 20 }),
    description: varchar("description", { length: 255 }),
    isEmailVerified: boolean("isEmailVerified").notNull().default(false),
    stripeCustomerId: varchar("stripe_customer_id", { length: 100 }),
    invoiceStatus: varchar("invoice_status", { length: 20 }),
    subscriptionTier: varchar("subscriptionTier", { length: 20 }).default(
      "free"
    ),
    nextSubscriptionTier: varchar("next_subscription_tier", { length: 20 }),
    avatarUrl: varchar("avatarUrl", { length: 255 }),
    referralCode: varchar("referral_code", { length: 36 }).unique(), // nullable due to existing user's don't have this code
    // .notNull(),
    referrerId: varchar("referrer_id", { length: 36 }),
    totalReferralCredits: integer("total_referral_credits")
      .notNull()
      .default(0),
    successfulReferrals: integer("successful_referrals").notNull().default(0),
    heardFrom: varchar("heard_from", { length: 50 }),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
  })
);

export type User = InferSelectModel<typeof user>;

export const chat = pgTable("Chat", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  title: text("title").notNull(),
  userId: uuid("userId")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  visibility: varchar("visibility", { enum: ["public", "private"] })
    .notNull()
    .default("public"),
  modelId: text(),
});

export type Chat = InferSelectModel<typeof chat>;

// Updated tag type with color
export type Tag = {
  name: string;
  color: string;
};

// Updated ChatWithTags type
export type ChatWithTags = Chat & {
  tags: Tag[];
  isLoading?: boolean; // Add this property
};

export const message = pgTable("Message", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  chatId: uuid("chatId")
    .notNull()
    .references(() => chat.id, { onDelete: "cascade" }),
  role: varchar("role").notNull(),
  content: json("content").notNull(),
  createdAt: timestamp("createdAt").notNull(),
  metadata: json("metadata"), // New optional field
});

export type Message = InferSelectModel<typeof message> & {
  experimental_attachments?: Array<{
    id: string;
    name: string;
    url: string;
    filetype?: number;
    document_id?: string; // Add document_id field
  }>;
  metadata?: {
    internetResults?: {
      mainContent: string;
      citations: string[];
      formattedContent: string;
    };
    relevantImages?: Array<{ mime_type: string; url: string }>;
    // Can add other metadata fields here in the future
    [key: string]: any;
  };
};

export const vote = pgTable(
  "Vote",
  {
    chatId: uuid("chatId")
      .notNull()
      .references(() => chat.id, { onDelete: "cascade" }),
    messageId: uuid("messageId")
      .notNull()
      .references(() => message.id, { onDelete: "cascade" }),
    isUpvoted: boolean("isUpvoted").notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  }
);

export type Vote = InferSelectModel<typeof vote>;

export const Document = pgTable(
  "Document",
  {
    id: uuid("id").notNull().defaultRandom(),
    createdAt: timestamp("createdAt").notNull(),
    title: text("title").notNull(),
    content: text("content"),
    kind: varchar("text", { enum: ["text", "code"] })
      .notNull()
      .default("text"),
    userId: uuid("userId")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] }),
    };
  }
);

export type DocumentType = InferSelectModel<typeof Document>;

export const suggestion = pgTable(
  "Suggestion",
  {
    id: uuid("id").notNull().defaultRandom(),
    documentId: uuid("documentId").notNull(),
    documentCreatedAt: timestamp("documentCreatedAt").notNull(),
    originalText: text("originalText").notNull(),
    suggestedText: text("suggestedText").notNull(),
    description: text("description"),
    isResolved: boolean("isResolved").notNull().default(false),
    userId: uuid("userId")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    createdAt: timestamp("createdAt").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
    documentRef: foreignKey({
      columns: [table.documentId, table.documentCreatedAt],
      foreignColumns: [Document.id, Document.createdAt],
    }),
  })
);

export type Suggestion = InferSelectModel<typeof suggestion>;

// First, enable pgvector extension
export const pgvectorExtension = sql`CREATE EXTENSION IF NOT EXISTS vector;`;

// Add source_documents table to track uploaded PDFs
export const sourceDocuments = pgTable(
  "source_documents",
  {
    id: uuid("id").notNull().defaultRandom(),
    filename: text("filename").notNull(),
    url: text("url").notNull(),
    fileSize: integer("file_size"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    userId: text("user_id").notNull(),
    extractedText: text("extractedText"),
    chatId: uuid("chat_id")
      .notNull()
      .references(() => chat.id, { onDelete: "cascade" }),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
  })
);

// Modify resources table to match column names
export const resources = pgTable(
  "resources",
  {
    id: uuid("id").notNull().defaultRandom(),
    content: text("content").notNull(),
    sourceDocumentId: uuid("source_document_id")
      .notNull()
      .references(() => sourceDocuments.id, { onDelete: "cascade" }),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
  })
);

const pgVector = customType<{ data: number[]; notNull: true; default: false }>({
  dataType() {
    return "vector(1024)"; // Change from 1536 to 1024 to match Voyage embeddings
  },
  toDriver(value: number[] | unknown): string {
    if (!Array.isArray(value)) {
      throw new Error(
        `Expected array for vector embedding, got ${typeof value}`
      );
    }
    return `[${value.join(",")}]`;
  },
});

// Embeddings table with UUID and vector support
export const embeddings = pgTable(
  "embeddings",
  {
    id: uuid("id").notNull().defaultRandom(),
    resourceId: uuid("resource_id")
      .notNull()
      .references(() => resources.id, { onDelete: "cascade" }),
    content: text("content").notNull(),
    embedding: pgVector("embedding").notNull(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
  })
);

// Join table for linking messages with documents
export const messageDocuments = pgTable(
  "message_documents",
  {
    messageId: uuid("message_id")
      .notNull()
      .references(() => message.id, { onDelete: "cascade" }),
    sourceDocumentId: uuid("source_document_id")
      .notNull()
      .references(() => sourceDocuments.id, { onDelete: "cascade" }),
    createdAt: timestamp("created_at").notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.messageId, table.sourceDocumentId] }),
  })
);

// Types
export type SourceDocument = InferSelectModel<typeof sourceDocuments>;
export type Resource = InferSelectModel<typeof resources>;
export type Embedding = InferSelectModel<typeof embeddings>;
export type MessageDocument = InferSelectModel<typeof messageDocuments>;

export const userPreferences = pgTable(
  "UserPreferences",
  {
    id: uuid("id").notNull().defaultRandom(),
    userId: uuid("userId")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" })
      .unique(),
    // Personal Details - all optional
    fullName: varchar("fullName", { length: 100 }),
    roleTitle: varchar("roleTitle", { length: 100 }),
    firmName: varchar("firmName", { length: 100 }),
    barId: varchar("barId", { length: 50 }),
    officePhone: varchar("officePhone", { length: 20 }),
    directPhone: varchar("directPhone", { length: 20 }),
    email: varchar("email", { length: 100 }),
    firmAddress: text("firmAddress"),

    // Practice Areas & Jurisdictions
    practiceAreas: text("practiceAreas"),
    jurisdictions: text("jurisdictions"),

    // Style Preferences - keeping these required with defaults
    formalityScale: integer("formalityScale").notNull().default(50),
    riskToleranceScale: integer("riskToleranceScale").notNull().default(50),
    detailLevel: integer("detailLevel").notNull().default(50),

    // References and formatting - keeping these required
    preferredReferences: text("preferredReferences"),
    documentFormatting: text("documentFormatting"),

    // Optional fields
    miscInformation: text("miscInformation"),
    aiGeneratedProfile: text("aiGeneratedProfile"),

    updatedAt: timestamp("updatedAt").notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
    userIdx: index("user_preferences_idx").on(table.userId),
  })
);

export type UserPreferences = InferSelectModel<typeof userPreferences>;

// Error tracking table
export const errorEvents = pgTable("error_events", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  eventType: varchar("event_type", { length: 255 }).notNull(),
  userId: uuid("user_id").references(() => user.id, { onDelete: "set null" }),
  chatId: uuid("chat_id").references(() => chat.id, { onDelete: "set null" }),
  errorMessage: text("error_message"),
  errorDetails: jsonb("error_details"),
  source: varchar("source", { length: 100 }).notNull(), // 'client' or 'server'
  url: text("url"), // For client-side errors
  userAgent: text("user_agent"), // Browser/device info
  severity: varchar("severity", { length: 50 }).default("error").notNull(), // 'error', 'warning', 'critical'
  resolved: boolean("resolved").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const plan = pgTable("Plan", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  name: varchar("name", { length: 100 }).notNull(),
  type: varchar("type", { length: 50 }),
  description: text("description"),
  stripePriceId: varchar("stripe_price_id", { length: 100 }),
  price: decimal("price", { precision: 10, scale: 2 }),
  interval: varchar("interval", { enum: ["month", "year"] }),
  features: jsonb("features"),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const subscription = pgTable("Subscription", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  userId: uuid("user_id")
    .references(() => user.id, { onDelete: "cascade" })
    .notNull(),
  planId: uuid("plan_id")
    .references(() => plan.id)
    .notNull(),
  status: varchar("status", { length: 50 }).notNull().default("active"),
  startDate: timestamp("start_date").defaultNow().notNull(),
  endDate: timestamp("end_date"),
  billingCycle: varchar("billing_cycle", { length: 50 }),
  paymentMethod: varchar("payment_method", { length: 100 }),
  paymentId: varchar("payment_id", { length: 100 }),
  autoRenew: boolean("auto_renew").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  stripeSubscriptionId: varchar("stripe_subscription_id", { length: 100 }),
  stripeCustomerId: varchar("stripe_customer_id", { length: 100 }),
  stripePriceId: varchar("stripe_price_id", { length: 100 }),
  stripeSubscriptionItemId: varchar("stripe_subscription_item_id", {
    length: 100,
  }),
  stripeSubscriptionStatus: varchar("stripe_subscription_status", {
    length: 100,
  }),
  isTrialUsed: boolean("is_trial_used").default(false),
  trialEndsAt: timestamp("trial_ends_at"),
  isAdminManaged: boolean("is_admin_managed").default(false),
  extendedTrial: boolean("extended_trial").default(false),
});

export type Plan = InferSelectModel<typeof plan>;
export type Subscription = InferSelectModel<typeof subscription>;
export type ErrorEvent = InferSelectModel<typeof errorEvents>;

// Prompt Folders table for organizing prompts
// Supports system-defined starter pack folders (shared across users) and user-created folders
export const promptFolders = pgTable(
  "prompt_folders",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    userId: uuid("user_id").references(() => user.id, { onDelete: "cascade" }), // NULL for system folders
    name: text("name").notNull(),
    isSystem: boolean("is_system").notNull().default(false), // true for starter-pack folders
    ...timestampFields, // Using the reusable timestamp mixin
  },
  (table) => ({
    // Ensure folder names are unique per user (or unique among system folders)
    uniqueFolderNamePerUser: unique().on(table.name, table.userId ?? sql`NULL`),
    userIdIdx: index("prompt_folders_user_id_idx").on(table.userId),
  })
);

export type PromptFolder = typeof promptFolders.$inferSelect;

// Prompts table for storing prompt cards
export const prompts = pgTable(
  "prompts",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    folderId: uuid("folder_id")
      .notNull()
      .references(() => promptFolders.id, { onDelete: "cascade" }),
    title: text("title").notNull(),
    content: text("content").notNull(),
    lastUsed: timestamp("last_used"),
    isFavorite: boolean("is_favorite").notNull().default(false),
    ...timestampFields, // Using the reusable timestamp mixin
  },
  (table) => ({
    folderIdIdx: index("prompts_folder_id_idx").on(table.folderId),
  })
);

export type Prompt = typeof prompts.$inferSelect;

// PromptFiles table for storing links to cloud-hosted files associated with prompts
export const promptFiles = pgTable(
  "prompt_files",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    promptId: uuid("prompt_id")
      .notNull()
      .references(() => prompts.id, { onDelete: "cascade" }),
    fileName: text("file_name").notNull(),
    fileUrl: text("file_url").notNull(),
    fileType: varchar("file_type", { length: 100 }).notNull(),
    fileSize: integer("file_size"), // Size in bytes (optional)
    uploadedAt: timestamp("uploaded_at").defaultNow().notNull(),
    ...timestampFields, // Using the reusable timestamp mixin
  },
  (table) => ({
    promptIdIdx: index("prompt_files_prompt_id_idx").on(table.promptId),
  })
);

export type PromptFile = typeof promptFiles.$inferSelect;

// Add a new table to track user-specific prompt favorites
export const userFavorites = pgTable(
  "user_favorites",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    promptId: uuid("prompt_id")
      .notNull()
      .references(() => prompts.id, { onDelete: "cascade" }),
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    uniqueUserPrompt: unique().on(table.userId, table.promptId),
    userIdIdx: index("user_favorites_user_id_idx").on(table.userId),
    promptIdIdx: index("user_favorites_prompt_id_idx").on(table.promptId),
  })
);

export type UserFavorite = typeof userFavorites.$inferSelect;

export const userFavoritesRelations = relations(userFavorites, ({ one }) => ({
  user: one(user, {
    fields: [userFavorites.userId],
    references: [user.id],
  }),
  prompt: one(prompts, {
    fields: [userFavorites.promptId],
    references: [prompts.id],
  }),
}));

// Define relations between tables
export const promptFoldersRelations = relations(
  promptFolders,
  ({ one, many }) => ({
    user: one(user, {
      fields: [promptFolders.userId],
      references: [user.id],
    }),
    prompts: many(prompts),
  })
);

export const promptsRelations = relations(prompts, ({ one, many }) => ({
  folder: one(promptFolders, {
    fields: [prompts.folderId],
    references: [promptFolders.id],
  }),
  files: many(promptFiles),
}));

export const promptFilesRelations = relations(promptFiles, ({ one }) => ({
  prompt: one(prompts, {
    fields: [promptFiles.promptId],
    references: [prompts.id],
  }),
}));

export const pendingDocumentAssociations = pgTable(
  "pending_document_associations",
  {
    id: serial("id").primaryKey(),
    messageId: text("message_id").notNull(),
    documentIds: text("document_ids").notNull(), // JSON string of document IDs
    createdAt: timestamp("created_at").defaultNow().notNull(),
    lastRetryAt: timestamp("last_retry_at"),
    retryCount: integer("retry_count").default(0).notNull(),
  }
);
const organizationField = {
  organizationId: varchar('organization_id', { length: 36 }),
  projectId: varchar('project_id', { length: 36 }),
}

export const document = pgTable("document", {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  isDerived: boolean('is_derived').notNull().default(false),
  artifactSource: varchar('artifact_source', { length: 10 , enum: ['UPLOAD', 'SHARED', 'GENERATED', 'CHAT']}).notNull(),
  artifactStatus: varchar('artifact_status', { length: 10 , enum: ['PENDING', 'AVAILABLE', 'PROCESSING', 'HIDDEN', 'BANNED']}).notNull(),  
  parentId: uuid('parent_id'),
  uploadedBy: uuid('uploaded_by').references(() => user.id),
  folderId: uuid('folder_id').references(() => documentFolder.id),
  metadata: jsonb('metadata'),
  artifactId: uuid('artifact_id').references(() => artifact.id),
  originalName: varchar('original_name', { length: 255 }),
  mime: varchar('mime', { length: 100 }),
  sizeByte: bigint('size_byte', { mode: 'number' }),
  isFavorite: boolean('is_favorite').default(false),
  ...timestampFields,
  ...organizationField
});

export const artifact = pgTable("artifact", {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  metadata: jsonb('metadata'),
  storageKey: varchar('storage_key', { length: 100 }).notNull().unique(),
  contentHash: varchar('content_hash', { length: 64 }).notNull(),
  sizeByte: bigint('size_byte', { mode: 'number' }).notNull(),
  mime: varchar('mime', { length: 100 }).notNull(),
})

export type Artifact = typeof artifact.$inferSelect;
export type Documents = typeof document.$inferSelect;

export const documentRelations = relations(document, ({ one }) => ({
  user: one(user, {
    fields: [document.uploadedBy],
    references: [user.id],
  }),
  artifact: one(artifact, {
    fields: [document.artifactId],
    references: [artifact.id],
  }),
}));

export const documentFolder = pgTable(
  "document_folder",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    name: varchar("name", { length: 255 }).notNull(),
    description: text("description"),
    status: varchar("status", { length: 10, enum: ['NORMAL', 'HIDDEN', 'BANNED'] }).default("NORMAL"),
    userId: uuid("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    parentFolderId: uuid("parent_id"),
    color: varchar("color", { length: 7 }).default("#808080"),
    icon: varchar("icon", { length: 50 }),
    parentPath: varchar("parent_path", { length: 255 }).default("/"),
    ...timestampFields,
    ...organizationField,
  }
);

export const documentFoldersRelations = relations(documentFolder, ({ one, many }) => ({
  user: one(user, {
    fields: [documentFolder.userId],
    references: [user.id],
  }),
  documents: many(document),
}));

// export const documentShare = pgTable("document_share", {
//   id: uuid("id").primaryKey().notNull().defaultRandom(),
//   status: varchar('status', { length: 10, enum: ['SHARED', 'DISABLED'] }).notNull(),
//   sharedById: varchar('shared_by_id', { length: 32 }).notNull().references(() => user.id),
//   sharedId: varchar('shared_id', { length: 32 }).notNull().references(() => documents.id),
//   ...timestampFields,
//   artifactId: varchar('artifact_id', { length: 32 }).notNull().references(() => artifact.id),
//   targetId: varchar('target_id', { length: 32 }).notNull(), // user or organization
//   targetType: varchar('target_type', { length: 32 }).notNull(), // user or organization
//   permission: varchar('permission', { length: 10 }).notNull(), // read or write
// });

// export const folderShare = pgTable("folder_share", {
//   id: uuid("id").primaryKey().notNull().defaultRandom(),
//   status: varchar('status', { length: 10, enum: ['SHARED', 'DISABLED'] }).notNull(),
//   sharedById: varchar('shared_by_id', { length: 32 }).notNull().references(() => user.id),
//   sharedId: varchar('shared_id', { length: 32 }).notNull().references(() => documentFolder.id),
//   ...timestampFields,
//   artifactId: varchar('artifact_id', { length: 32 }).notNull().references(() => artifact.id),
//   targetId: varchar('target_id', { length: 32 }).notNull(), // user or organization
//   targetType: varchar('target_type', { length: 32 }).notNull(), // user or organization
//   permission: varchar('permission', { length: 10 }).notNull(), // read or write
// });

// CHAT ORGANIZATION TABLES
// Chat folders (matters)
export const chatFolders = pgTable(
  "chat_folders",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    name: text("name").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    uniqueFolderNamePerUser: unique().on(table.name, table.userId),
    userIdIdx: index("chat_folders_user_id_idx").on(table.userId),
  })
);

export type ChatFolder = InferSelectModel<typeof chatFolders>;

// Chat to folder mapping
export const chatToFolder = pgTable(
  "chat_to_folder",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    chatId: uuid("chat_id")
      .notNull()
      .references(() => chat.id, { onDelete: "cascade" }),
    folderId: uuid("folder_id")
      .notNull()
      .references(() => chatFolders.id, { onDelete: "cascade" }),
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    uniqueChatFolder: unique().on(table.chatId, table.folderId),
    chatIdIdx: index("chat_to_folder_chat_id_idx").on(table.chatId),
    folderIdIdx: index("chat_to_folder_folder_id_idx").on(table.folderId),
  })
);

export type ChatToFolder = InferSelectModel<typeof chatToFolder>;

// Pinned chats
export const pinnedChats = pgTable(
  "pinned_chats",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    chatId: uuid("chat_id")
      .notNull()
      .references(() => chat.id, { onDelete: "cascade" }),
    position: integer("position").notNull(), // For ordering pinned chats
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    uniqueUserChat: unique().on(table.userId, table.chatId),
    userIdIdx: index("pinned_chats_user_id_idx").on(table.userId),
    chatIdIdx: index("pinned_chats_chat_id_idx").on(table.chatId),
  })
);

export type PinnedChat = InferSelectModel<typeof pinnedChats>;

// Chat tags with color support
export const chatTags = pgTable(
  "chat_tags",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    chatId: uuid("chat_id")
      .notNull()
      .references(() => chat.id, { onDelete: "cascade" }),
    tagName: text("tag_name").notNull(),
    color: text("color").notNull().default("#3B82F6"), // Default blue color
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    uniqueUserChatTag: unique().on(table.userId, table.chatId, table.tagName),
    userIdIdx: index("chat_tags_user_id_idx").on(table.userId),
    chatIdIdx: index("chat_tags_chat_id_idx").on(table.chatId),
  })
);

export type ChatTag = InferSelectModel<typeof chatTags>;

// Define relations
export const chatFoldersRelations = relations(chatFolders, ({ one, many }) => ({
  user: one(user, {
    fields: [chatFolders.userId],
    references: [user.id],
  }),
  chats: many(chatToFolder),
}));

export const chatToFolderRelations = relations(chatToFolder, ({ one }) => ({
  chat: one(chat, {
    fields: [chatToFolder.chatId],
    references: [chat.id],
  }),
  folder: one(chatFolders, {
    fields: [chatToFolder.folderId],
    references: [chatFolders.id],
  }),
}));

export const pinnedChatsRelations = relations(pinnedChats, ({ one }) => ({
  user: one(user, {
    fields: [pinnedChats.userId],
    references: [user.id],
  }),
  chat: one(chat, {
    fields: [pinnedChats.chatId],
    references: [chat.id],
  }),
}));

export const chatTagsRelations = relations(chatTags, ({ one }) => ({
  user: one(user, {
    fields: [chatTags.userId],
    references: [user.id],
  }),
  chat: one(chat, {
    fields: [chatTags.chatId],
    references: [chat.id],
  }),
}));

export const referral = pgTable(
  "Referral",
  {
    id: uuid("id").notNull().defaultRandom(),

    referrerId: uuid("referrer_id").notNull(),
    referredUserId: uuid("referred_user_id").notNull(),

    isRewarded: text("is_rewarded").notNull().default("pending"),

    createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
  })
);

export type Referral = InferSelectModel<typeof referral>;

export const referralCredit = pgTable(
  "ReferralCredit",
  {
    id: uuid("id").notNull().defaultRandom(),
    referralId: varchar("referral_id").notNull().unique(),
    referrerId: uuid("referrer_id").notNull(),
    amount: integer("amount").notNull(),
    appliedToStripe: boolean("applied_to_stripe").notNull().default(false),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
  })
);

export type ReferralCredit = InferSelectModel<typeof referralCredit>;

// User feedback table
export const feedback = pgTable(
  "feedback",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    userEmail: text("user_email").notNull(),
    featureType: varchar("feature_type", { length: 50 }).notNull(), // e.g., "cite-check", "chat", etc.
    rating: integer("rating").notNull(), // 1 for positive, 0 for negative
    feedbackText: text("feedback_text"),
    metadata: json("metadata"), // For feature-specific data (chatId, messageId, etc.)
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    userIdIdx: index("feedback_user_id_idx").on(table.userId),
    featureTypeIdx: index("feedback_feature_type_idx").on(table.featureType),
  })
);

export type Feedback = InferSelectModel<typeof feedback>;
export const documentShare = pgTable("document_share", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  status: varchar("status", { length: 10, enum: ["SHARED", "DISABLED"] }).notNull(),
  sharedById: uuid("shared_by_id").notNull().references(() => user.id),
  documentId: uuid("document_id").notNull().references(() => document.id),
  expiresAt: timestamp("expires_at"),
  ...timestampFields,
});

export const documentShareUser = pgTable("document_share_user", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  status: varchar("status", { length: 10, enum: ["SHARED", "DISABLED"] }).notNull(),
  documentShareId: uuid("document_share_id").notNull().references(() => documentShare.id),
  sharedToId: uuid("shared_to_id").notNull().references(() => user.id),
  sharedToType: varchar("shared_to_type", { length: 10, enum: ["USER", "ORGANIZATION", "PROJECT"] }).notNull(),
  sharedById: uuid("shared_by_id").notNull().references(() => user.id),
  documentId: uuid("document_id").notNull().references(() => document.id),
  permission: varchar("permission", { length: 10, enum: ["READ", "WRITE"] }).notNull().default("READ"),
  ...timestampFields,
});

export const folderShare = pgTable("folder_share", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  status: varchar("status", { length: 10, enum: ["SHARED", "DISABLED"] }).notNull(),
  sharedById: uuid("shared_by_id").notNull().references(() => user.id),
  folderId: uuid("folder_id").notNull().references(() => documentFolder.id),
  expiresAt: timestamp("expires_at"),
  ...timestampFields,
});

export const folderShareUser = pgTable("folder_share_user", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  status: varchar("status", { length: 10, enum: ["SHARED", "DISABLED"] }).notNull(),
  folderShareId: uuid("folder_share_id").notNull().references(() => folderShare.id),
  sharedToId: uuid("shared_to_id").notNull().references(() => user.id),
  sharedToType: varchar("shared_to_type", { length: 10, enum: ["USER", "ORGANIZATION", "PROJECT"] }).notNull(),
  sharedById: uuid("shared_by_id").notNull().references(() => user.id),
  folderId: uuid("folder_id").notNull().references(() => documentFolder.id),  
  permission: varchar("permission", { length: 10, enum: ["READ", "WRITE"] }).notNull().default("READ"),
  ...timestampFields,
});

export const documentAuditLog = pgTable("document_audit_log", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  entityId: uuid("entity_id").notNull(),
  entityType: varchar("entity_type", { length: 32}).notNull(), // DOCUMENT or FOLDER and so on...
  action: varchar("action", { length: 32}).notNull(), // VIEW DOWNLOAD SHARE UNSHARE EDIT DELETE  and so on...
  operatorId: uuid("operator_id").notNull().references(() => user.id),
  targetId: uuid("target_id").notNull(),
  targetType: varchar("target_type", { length: 32}).notNull(), // USER or ORGANIZATION or PROJECT and so on...
  result: varchar("result", { length: 32, enum: ["SUCCESS", "FAILED"] }).notNull().default("SUCCESS"),
  errorMessage: text("error_message"),
  additionalInfo: jsonb("additional_info"),
  createdAt: timestampFields.createdAt
});

export const notifications = pgTable('notifications', {
  id: uuid('id').defaultRandom().primaryKey(),
  type: text('type').notNull(),
  title: text('title').notNull(),
  link: text('link'),
  description: text('description').notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const notificationTargets = pgTable('notification_targets', {
  id: uuid('id').defaultRandom().primaryKey(),
  notificationId: uuid('notification_id')
    .notNull()
    .references(() => notifications.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => user.id),
  isRead: boolean('is_read').default(false),
  readAt: timestamp('read_at', { withTimezone: true }),
});

// Optional: add relations if needed
export const notificationRelations = relations(notifications, ({ many }) => ({
  targets: many(notificationTargets),
}));

export const notificationTargetRelations = relations(notificationTargets, ({ one }) => ({
  notification: one(notifications, {
    fields: [notificationTargets.notificationId],
    references: [notifications.id],
  }),
}));

export const stream = pgTable(
  "stream",
  {
    id: uuid("id").primaryKey().notNull().defaultRandom(),
    chatId: uuid("chat_id").references(() => chat.id, { onDelete: "set null" }),
    messageId: uuid("message_id").references(() => message.id, { onDelete: "set null" }),
    userId: uuid("user_id").references(() => user.id, { onDelete: "set null" }),
    status: varchar("status", { length: 50 }).notNull().default("PENDING"),
    metadata: json("metadata"),
    ...timestampFields,
  },
  (table) => ({
    chatIdIdx: index("stream_chat_id_idx").on(table.chatId),
    messageIdIdx: index("stream_message_id_idx").on(table.messageId),
    userIdIdx: index("stream_user_id_idx").on(table.userId),
    statusIdx: index("stream_status_idx").on(table.status),
  })
);

export type Stream = InferSelectModel<typeof stream>;

// Define the status enum in TypeScript only (not enforced at DB level)
export enum StreamStatus {
  PENDING = "PENDING",
  STREAMING = "STREAMING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  INTERRUPTED = "INTERRUPTED",
  TIMEOUT = "TIMEOUT"
}
