import { db } from "./db";
import { chatFolders, chatToFolder, chat, pinnedChats, chatTags } from "./schema";
import { eq, and, asc, not, sql, desc, inArray } from "drizzle-orm";
import { Logger } from "@/lib/utils/Logger";
import { DEFAULT_TAG_COLOR } from "@/lib/constants/tag-colors";

// Folder-related queries

/**
 * Get all folders for a user
 */
export async function getFoldersByUserId(userId: string) {
  try {
    return await db
      .select()
      .from(chatFolders)
      .where(eq(chatFolders.userId, userId))
      .orderBy(asc(chatFolders.name));
  } catch (error) {
    Logger.error("Failed to get chat folders from database", error);
    throw error;
  }
}

/**
 * Get a specific folder by ID
 */
export async function getFolderById(id: string) {
  try {
    const folders = await db
      .select()
      .from(chatFolders)
      .where(eq(chatFolders.id, id));
    return folders.length > 0 ? folders[0] : null;
  } catch (error) {
    Logger.error("Failed to get chat folder from database", error);
    throw error;
  }
}

/**
 * Create a new folder
 */
export async function createFolder({
  name,
  userId,
}: {
  name: string;
  userId: string;
}) {
  try {
    // Check if a folder with the same name already exists for this user
    const existingFolders = await db
      .select()
      .from(chatFolders)
      .where(
        and(
          eq(chatFolders.name, name),
          eq(chatFolders.userId, userId)
        )
      );

    if (existingFolders.length > 0) {
      throw new Error("DUPLICATE_FOLDER_NAME");
    }

    const [folder] = await db
      .insert(chatFolders)
      .values({
        name,
        userId,
      })
      .returning();
    return folder;
  } catch (error) {
    Logger.error("Failed to create chat folder in database", error);
    throw error;
  }
}

/**
 * Update a folder's name
 */
export async function updateFolder({
  oldName,
  newName,
  userId,
}: {
  oldName: string;
  newName: string;
  userId: string;
}) {
  try {
    // Check if a folder with the new name already exists for this user
    const existingFolders = await db
      .select()
      .from(chatFolders)
      .where(
        and(
          eq(chatFolders.name, newName),
          eq(chatFolders.userId, userId),
          not(eq(chatFolders.name, oldName))
        )
      );

    if (existingFolders.length > 0) {
      throw new Error("DUPLICATE_FOLDER_NAME");
    }

    // Update the folder name in a single query
    const [folder] = await db
      .update(chatFolders)
      .set({
        name: newName,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(chatFolders.name, oldName),
          eq(chatFolders.userId, userId)
        )
      )
      .returning();
    
    return folder;
  } catch (error) {
    Logger.error("Failed to update chat folder in database", error);
    throw error;
  }
}

/**
 * Delete a folder
 */
export async function deleteFolder({
  name,
  userId,
}: {
  name: string;
  userId: string;
}) {
  try {
    // First get the folder ID
    const folder = await db
      .select()
      .from(chatFolders)
      .where(
        and(
          eq(chatFolders.name, name),
          eq(chatFolders.userId, userId)
        )
      )
      .limit(1);
    
    if (folder.length === 0) {
      return null;
    }
    
    const folderId = folder[0].id;
    
    // Delete all chat-to-folder mappings
    await db
      .delete(chatToFolder)
      .where(eq(chatToFolder.folderId, folderId));

    // Then delete the folder
    const [deletedFolder] = await db
      .delete(chatFolders)
      .where(
        and(
          eq(chatFolders.id, folderId),
          eq(chatFolders.userId, userId)
        )
      )
      .returning();
    
    return deletedFolder;
  } catch (error) {
    Logger.error("Failed to delete chat folder from database", error);
    throw error;
  }
}

/**
 * Get all chats in a folder by folder name
 */
export async function getChatsByFolderName({
  folderName,
  userId,
}: {
  folderName: string;
  userId: string;
}) {
  try {
    // Get the folder ID in a single query with the chats
    const result = await db
      .select({
        chat: chat,
      })
      .from(chatFolders)
      .innerJoin(chatToFolder, eq(chatToFolder.folderId, chatFolders.id))
      .innerJoin(chat, eq(chatToFolder.chatId, chat.id))
      .where(
        and(
          eq(chatFolders.name, folderName),
          eq(chatFolders.userId, userId)
        )
      );

    if (result.length === 0) {
      // Check if the folder exists
      const folder = await db
        .select()
        .from(chatFolders)
        .where(
          and(
            eq(chatFolders.name, folderName),
            eq(chatFolders.userId, userId)
          )
        )
        .limit(1);
      
      if (folder.length === 0) {
        throw new Error("FOLDER_NOT_FOUND");
      }
      
      // Folder exists but has no chats
      return [];
    }

    return result.map(r => r.chat);
  } catch (error) {
    Logger.error("Failed to get chats by folder name from database", error);
    throw error;
  }
}

/**
 * Add a chat to a folder by folder name
 */
export async function addChatToFolder({
  chatId,
  folderName,
  userId,
}: {
  chatId: string;
  folderName: string;
  userId: string;
}) {
  try {
    // Get the folder ID
    const folder = await db
      .select()
      .from(chatFolders)
      .where(
        and(
          eq(chatFolders.name, folderName),
          eq(chatFolders.userId, userId)
        )
      )
      .limit(1);
    
    if (folder.length === 0) {
      throw new Error("FOLDER_NOT_FOUND");
    }
    
    const folderId = folder[0].id;

    // Check if the mapping already exists
    const existingMappings = await db
      .select()
      .from(chatToFolder)
      .where(
        and(
          eq(chatToFolder.chatId, chatId),
          eq(chatToFolder.folderId, folderId)
        )
      );

    if (existingMappings.length > 0) {
      // Mapping already exists, return it
      return existingMappings[0];
    }

    // Create the mapping
    const [mapping] = await db
      .insert(chatToFolder)
      .values({
        chatId,
        folderId,
      })
      .returning();
    
    return mapping;
  } catch (error) {
    Logger.error("Failed to add chat to folder in database", error);
    throw error;
  }
}

/**
 * Remove a chat from a folder by folder name
 */
export async function removeChatFromFolder({
  chatId,
  folderName,
  userId,
}: {
  chatId: string;
  folderName: string;
  userId: string;
}) {
  try {
    // Get the folder ID
    const folder = await db
      .select()
      .from(chatFolders)
      .where(
        and(
          eq(chatFolders.name, folderName),
          eq(chatFolders.userId, userId)
        )
      )
      .limit(1);
    
    if (folder.length === 0) {
      throw new Error("FOLDER_NOT_FOUND");
    }
    
    const folderId = folder[0].id;

    // Delete the mapping
    const [mapping] = await db
      .delete(chatToFolder)
      .where(
        and(
          eq(chatToFolder.chatId, chatId),
          eq(chatToFolder.folderId, folderId)
        )
      )
      .returning();
    
    return mapping;
  } catch (error) {
    Logger.error("Failed to remove chat from folder in database", error);
    throw error;
  }
}

/**
 * Reorder chats in a folder by folder name
 */
export async function reorderChatsInFolder({
  chatIds,
  folderName,
  userId,
}: {
  chatIds: string[];
  folderName: string;
  userId: string;
}) {
  try {
    // Get the folder ID
    const folder = await db
      .select()
      .from(chatFolders)
      .where(
        and(
          eq(chatFolders.name, folderName),
          eq(chatFolders.userId, userId)
        )
      )
      .limit(1);
    
    if (folder.length === 0) {
      throw new Error("FOLDER_NOT_FOUND");
    }
    
    // For now, we'll just return success
    // In a real implementation, you would update the order in the database
    return { success: true, chatIds };
  } catch (error) {
    Logger.error("Failed to reorder chats in folder in database", error);
    throw error;
  }
}

// Pinned chats queries

/**
 * Get all pinned chats for a user
 */
export async function getPinnedChatsByUserId(userId: string) {
  try {
    const result = await db
      .select({
        pinnedChat: pinnedChats,
        chat: chat,
      })
      .from(pinnedChats)
      .innerJoin(chat, eq(pinnedChats.chatId, chat.id))
      .where(eq(pinnedChats.userId, userId))
      .orderBy(asc(pinnedChats.position));

    return result.map(r => ({
      ...r.pinnedChat,
      chat: r.chat
    }));
  } catch (error) {
    Logger.error("Failed to get pinned chats from database", error);
    throw error;
  }
}

/**
 * Pin a chat
 */
export async function pinChat({
  chatId,
  userId,
}: {
  chatId: string;
  userId: string;
}) {
  try {
    // Check if the chat is already pinned
    const existingPins = await db
      .select()
      .from(pinnedChats)
      .where(
        and(
          eq(pinnedChats.chatId, chatId),
          eq(pinnedChats.userId, userId)
        )
      );

    if (existingPins.length > 0) {
      // Chat is already pinned, return it
      return existingPins[0];
    }

    // Get the highest position to add the new pin at the end
    const highestPositionResult = await db
      .select({ maxPosition: sql`MAX(${pinnedChats.position})` })
      .from(pinnedChats)
      .where(eq(pinnedChats.userId, userId));

    const position = (highestPositionResult[0]?.maxPosition as number || 0) + 1;

    // Create the pin
    const [pin] = await db
      .insert(pinnedChats)
      .values({
        chatId,
        userId,
        position,
      })
      .returning();
    return pin;
  } catch (error) {
    Logger.error("Failed to pin chat in database", error);
    throw error;
  }
}

/**
 * Unpin a chat
 */
export async function unpinChat({
  chatId,
  userId,
}: {
  chatId: string;
  userId: string;
}) {
  try {
    // Delete the pin
    const [pin] = await db
      .delete(pinnedChats)
      .where(
        and(
          eq(pinnedChats.chatId, chatId),
          eq(pinnedChats.userId, userId)
        )
      )
      .returning();

    if (!pin) {
      return null;
    }

    // Reorder remaining pins to close the gap
    await db.execute(sql`
      UPDATE ${pinnedChats}
      SET position = position - 1
      WHERE ${pinnedChats.userId} = ${userId}
        AND ${pinnedChats.position} > ${pin.position}
    `);

    return pin;
  } catch (error) {
    Logger.error("Failed to unpin chat in database", error);
    throw error;
  }
}

/**
 * Reorder pinned chats
 */
export async function reorderPinnedChats({
  userId,
  chatIds,
}: {
  userId: string;
  chatIds: string[];
}) {
  try {
    // Start a transaction to ensure all updates are atomic
    return await db.transaction(async (tx) => {
      // Update each pin's position based on the order in chatIds
      for (let i = 0; i < chatIds.length; i++) {
        await tx
          .update(pinnedChats)
          .set({ position: i + 1 })
          .where(
            and(
              eq(pinnedChats.chatId, chatIds[i]),
              eq(pinnedChats.userId, userId)
            )
          );
      }

      // Return the updated pins
      const result = await tx
        .select({
          pinnedChat: pinnedChats,
          chat: chat,
        })
        .from(pinnedChats)
        .innerJoin(chat, eq(pinnedChats.chatId, chat.id))
        .where(eq(pinnedChats.userId, userId))
        .orderBy(asc(pinnedChats.position));

      return result.map(r => ({
        ...r.pinnedChat,
        chat: r.chat
      }));
    });
  } catch (error) {
    Logger.error("Failed to reorder pinned chats in database", error);
    throw error;
  }
}

// Chat tags queries

/**
 * Get tags for a specific chat
 */
export async function getTagsForChat({
  chatId,
  userId,
}: {
  chatId: string;
  userId: string;
}) {
  try {
    const result = await db
      .select({
        id: chatTags.id,
        tagName: chatTags.tagName,
        color: chatTags.color,
      })
      .from(chatTags)
      .where(
        and(
          eq(chatTags.chatId, chatId),
          eq(chatTags.userId, userId)
        )
      )
      .orderBy(asc(chatTags.tagName));

    return result;
  } catch (error) {
    Logger.error("Failed to get tags for chat from database", error);
    throw error;
  }
}

/**
 * Get all unique tags used by a user
 */
export async function getAllTagsByUserId(userId: string) {
  try {
    // Get all unique tags with their colors
    const result = await db
      .select({
        tagName: chatTags.tagName,
        color: chatTags.color,
      })
      .from(chatTags)
      .where(eq(chatTags.userId, userId))
      .orderBy(asc(chatTags.tagName));

    // Deduplicate tags (keeping the first occurrence of each tag name)
    const uniqueTags = result.reduce((acc, tag) => {
      if (!acc.some(t => t.tagName === tag.tagName)) {
        acc.push(tag);
      }
      return acc;
    }, [] as { tagName: string; color: string }[]);

    return uniqueTags;
  } catch (error) {
    Logger.error("Failed to get all tags from database", error);
    throw error;
  }
}

/**
 * Add a tag to a chat
 */
export async function addTagToChat({
  chatId,
  userId,
  tagName,
  color = DEFAULT_TAG_COLOR,
}: {
  chatId: string;
  userId: string;
  tagName: string;
  color?: string;
}) {
  try {
    // Check if tag already exists for this chat
    const existingTag = await db
      .select()
      .from(chatTags)
      .where(
        and(
          eq(chatTags.chatId, chatId),
          eq(chatTags.userId, userId),
          eq(chatTags.tagName, tagName)
        )
      )
      .limit(1);

    if (existingTag.length > 0) {
      // Tag already exists, update its color if different
      if (existingTag[0].color !== color) {
        const [updated] = await db
          .update(chatTags)
          .set({
            color,
            updatedAt: new Date(),
          })
          .where(eq(chatTags.id, existingTag[0].id))
          .returning();
        return updated;
      }
      return existingTag[0];
    }

    // Create new tag
    const [created] = await db
      .insert(chatTags)
      .values({
        chatId,
        userId,
        tagName,
        color,
      })
      .returning();
    return created;
  } catch (error) {
    Logger.error("Failed to add tag to chat in database", error);
    throw error;
  }
}

/**
 * Update a tag's color
 */
export async function updateTagColor({
  userId,
  tagName,
  color,
}: {
  userId: string;
  tagName: string;
  color: string;
}) {
  try {
    // Update color for all instances of this tag for this user
    const result = await db
      .update(chatTags)
      .set({
        color,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(chatTags.userId, userId),
          eq(chatTags.tagName, tagName)
        )
      )
      .returning();
    
    return result;
  } catch (error) {
    Logger.error("Failed to update tag color in database", error);
    throw error;
  }
}

/**
 * Save multiple tags for a chat
 */
export async function saveTagsForChat({
  chatId,
  userId,
  tags,
}: {
  chatId: string;
  userId: string;
  tags: { tagName: string; color: string }[];
}) {
  try {
    // Start a transaction
    return await db.transaction(async (tx) => {
      // Get existing tags for this chat
      const existingTags = await tx
        .select()
        .from(chatTags)
        .where(
          and(
            eq(chatTags.chatId, chatId),
            eq(chatTags.userId, userId)
          )
        );
      
      // Delete tags that are not in the new list
      const newTagNames = tags.map(t => t.tagName);
      const tagsToDelete = existingTags.filter(
        tag => !newTagNames.includes(tag.tagName)
      );
      
      if (tagsToDelete.length > 0) {
        await tx
          .delete(chatTags)
          .where(inArray(chatTags.id, tagsToDelete.map(t => t.id)));
      }
      
      // Add or update tags
      const results = [];
      for (const tag of tags) {
        const existingTag = existingTags.find(t => t.tagName === tag.tagName);
        
        if (existingTag) {
          // Update if color changed
          if (existingTag.color !== tag.color) {
            const [updated] = await tx
              .update(chatTags)
              .set({
                color: tag.color,
                updatedAt: new Date(),
              })
              .where(eq(chatTags.id, existingTag.id))
              .returning();
            results.push(updated);
          } else {
            results.push(existingTag);
          }
        } else {
          // Create new tag
          const [created] = await tx
            .insert(chatTags)
            .values({
              chatId,
              userId,
              tagName: tag.tagName,
              color: tag.color,
            })
            .returning();
          results.push(created);
        }
      }
      
      return results;
    });
  } catch (error) {
    Logger.error("Failed to save tags for chat in database", error);
    throw error;
  }
}

/**
 * Remove a tag from a chat
 */
export async function removeTagFromChat({
  chatId,
  userId,
  tagName,
}: {
  chatId: string;
  userId: string;
  tagName: string;
}) {
  try {
    const [deleted] = await db
      .delete(chatTags)
      .where(
        and(
          eq(chatTags.chatId, chatId),
          eq(chatTags.userId, userId),
          eq(chatTags.tagName, tagName)
        )
      )
      .returning();
    
    return deleted;
  } catch (error) {
    Logger.error("Failed to remove tag from chat in database", error);
    throw error;
  }
}

/**
 * Get all chats with a specific tag
 */
export async function getChatsByTag({
  userId,
  tagName,
}: {
  userId: string;
  tagName: string;
}) {
  try {
    const result = await db
      .select({
        chat: chat,
      })
      .from(chatTags)
      .innerJoin(chat, eq(chatTags.chatId, chat.id))
      .where(
        and(
          eq(chatTags.userId, userId),
          eq(chatTags.tagName, tagName)
        )
      )
      .orderBy(desc(chat.updatedAt));
    
    return result.map(r => r.chat);
  } catch (error) {
    Logger.error("Failed to get chats by tag from database", error);
    throw error;
  }
}

/**
 * Get all data needed for the sidebar
 * This includes:
 * - Chats with their tags
 * - Folders with their associated chats
 * - Pinned chats
 */
export async function getSidebarData(userId: string) {
  try {
    // Get all chats for the user
    const chatsResult = await db
      .select()
      .from(chat)
      .where(eq(chat.userId, userId))
      .orderBy(desc(chat.updatedAt));

    // Get all tags for the user's chats
    const tagsResult = await db
      .select({
        chatId: chatTags.chatId,
        tagName: chatTags.tagName,
        color: chatTags.color,
      })
      .from(chatTags)
      .where(eq(chatTags.userId, userId));

    // Create a map of chatId to tags
    const chatTagsMap = new Map();
    tagsResult.forEach(tag => {
      if (!chatTagsMap.has(tag.chatId)) {
        chatTagsMap.set(tag.chatId, []);
      }
      chatTagsMap.get(tag.chatId).push({
        name: tag.tagName,
        color: tag.color,
      });
    });

    // Add tags to each chat
    const chatsWithTags = chatsResult.map(chatRecord => ({
      ...chatRecord,
      tags: chatTagsMap.get(chatRecord.id) || []
    }));

    // Get all folders for the user
    const foldersResult = await db
      .select()
      .from(chatFolders)
      .where(eq(chatFolders.userId, userId))
      .orderBy(asc(chatFolders.name));

    // Get all chat-to-folder mappings for the user
    const chatToFolderResult = await db
      .select({
        folderId: chatToFolder.folderId,
        chatId: chatToFolder.chatId
      })
      .from(chatToFolder)
      .innerJoin(chatFolders, eq(chatToFolder.folderId, chatFolders.id))
      .where(eq(chatFolders.userId, userId));

    // Create a map of folderId to chats
    const folderChatsMap = new Map();
    chatToFolderResult.forEach(record => {
      if (!folderChatsMap.has(record.folderId)) {
        folderChatsMap.set(record.folderId, []);
      }
      folderChatsMap.get(record.folderId).push(record.chatId);
    });

    // Add chats to each folder
    const foldersWithChats = foldersResult.map(folder => {
      const folderChatIds = folderChatsMap.get(folder.id) || [];
      const folderChats = chatsWithTags.filter(chat => folderChatIds.includes(chat.id));
      return {
        ...folder,
        chats: folderChats
      };
    });

    // Get all pinned chats for the user
    const pinnedChatsResult = await db
      .select({
        chatId: pinnedChats.chatId,
        position: pinnedChats.position,
        chat: chat,
      })
      .from(pinnedChats)
      .innerJoin(chat, eq(pinnedChats.chatId, chat.id))
      .where(eq(pinnedChats.userId, userId))
      .orderBy(asc(pinnedChats.position));

    // Add tags to pinned chats
    const pinnedChatsWithTags = pinnedChatsResult.map(record => ({
      chatId: record.chatId,
      position: record.position,
      chat: {
        ...record.chat,
        tags: chatTagsMap.get(record.chat.id) || []
      }
    }));

    return {
      chats: chatsWithTags,
      folders: foldersWithChats,
      pinnedChats: pinnedChatsWithTags
    };
  } catch (error) {
    Logger.error("Failed to get sidebar data from database", error);
    throw error;
  }
}




