import { and, eq, gte, sql, sum } from 'drizzle-orm'
import { db } from './index'
import { chat, stream } from './schema'
import { Logger } from '../utils/Logger'
import { TOTAL_MEMORY_LIMIT_MB, TOTAL_CHAT_LIMIT, DAILY_MESSAGE_LIMIT } from '@/lib/constants'

/**
 * Gets the count of all chats created by a user
 */
export async function getAllChatsCreatedByUser(userId: string): Promise<number> {
  try {
    const result = await db
      .select({ count: sql`count(*)` })
      .from(chat)
      .where(eq(chat.userId, userId));
    
    // Add debug logging
    console.log(`Chat count query result for user ${userId}:`, result);
    
    // Ensure we're properly parsing the count
    const count = Number(result[0]?.count || 0);
    console.log(`Parsed chat count: ${count}`);
    
    return count;
  } catch (error) {
    console.error('Failed to get all chats count', error);
    return 0;
  }
}

/**
 * Gets the count of assistant messages sent to a user today
 */
export async function getAssistantMessagesSentTodayForUser(userId: string): Promise<number> {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const result = await db
      .select({ count: sql`count(*)` })
      .from(stream)
      .where(
        and(
          eq(stream.userId, userId),
          gte(stream.createdAt, today),
          // Count only completed streams (successful message generations)
          eq(stream.status, 'COMPLETED')
        )
      );
    return Number(result[0]?.count || 0);
  } catch (error) {
    Logger.error('Failed to get assistant messages sent today count', error);
    return 0;
  }
}

/**
 * Checks if a user has exceeded the total chat creation limit
 */
export async function checkChatCreationLimit(userId: string): Promise<boolean> {
  const totalChatsCreated = await getAllChatsCreatedByUser(userId);
  return totalChatsCreated < TOTAL_CHAT_LIMIT;
}

/**
 * Checks if a user has exceeded the daily message limit
 */
export async function checkChatMessageLimit(userId: string): Promise<boolean> {
  const assistantMessagesSentToday = await getAssistantMessagesSentTodayForUser(userId);
  return assistantMessagesSentToday < DAILY_MESSAGE_LIMIT;
}

/**
 * Gets the total memory usage for a user in bytes
 * This includes message content size
 */
export async function getUserTotalMemoryUsage(userId: string): Promise<number> {
  try {
    const result = await db.execute(sql`
      SELECT 
        (SELECT COALESCE(SUM(LENGTH(CAST(content AS TEXT))), 0)
         FROM "Message" m
         JOIN "Chat" c ON m."chatId" = c.id
         WHERE c."userId" = ${userId}) +
        (SELECT COALESCE(SUM(COALESCE(file_size, 0)), 0)
         FROM "source_documents"
         WHERE "user_id" = ${userId}) AS total_size
    `);
    
    const totalSizeBytes = Number(result[0]?.total_size || 0);
    
    return totalSizeBytes;
  } catch (error) {
    Logger.error('Failed to get user total memory usage', error);
    return 0;
  }
}

/**
 * Checks if a user has exceeded their total memory limit
 */
export async function checkMemoryUsageLimit(userId: string): Promise<boolean> {
  const totalUsageBytes = await getUserTotalMemoryUsage(userId);
  const totalUsageMB = totalUsageBytes / (1024 * 1024);
  return totalUsageMB < TOTAL_MEMORY_LIMIT_MB;
}

/**
 * Calculates minutes until daily message limit reset (midnight)
 * @returns Number of minutes until midnight
 */
export function getMinutesUntilReset(): number {
  const now = new Date();
  const resetTime = new Date();
  resetTime.setHours(24, 0, 0, 0); // Set to midnight tonight
  
  // Calculate minutes until reset
  return Math.floor((resetTime.getTime() - now.getTime()) / (1000 * 60));
}

/**
 * Type for usage limit check result
 */
export type UsageLimitResult = {
  restricted: boolean;
  errorMessage?: string;
  resetTime?: Date;
  // Additional information for free tier users
  totalChats?: number;
  dailyMessageCount?: number;
  timeUntilReset?: string; // ISO string format
};

/**
 * Checks usage limits based on user and chat
 * Returns whether usage should be restricted and an error message if applicable
 */
export async function checkUsageLimits(
  user: { id: string; subscriptionTier?: string; isAdmin?: boolean },
  chat: any | null | undefined,
  chatId: string
): Promise<UsageLimitResult> {
  // Extract user properties
  const userId = user.id;
  
  // If admin or not on free tier, no restrictions
  if (user.isAdmin || user.subscriptionTier !== 'free') {
    return { restricted: false };
  }
  
  // Check memory usage limit for all users on free tier
  const withinMemoryLimit = await checkMemoryUsageLimit(userId);
  if (!withinMemoryLimit) {
    return {  
      restricted: true,
      errorMessage: `You've reached your storage limit of ${TOTAL_MEMORY_LIMIT_MB} MB. Upgrade for more storage or delete old chats that may have large attachments.`
    };
  }
  
  // Always check daily message limit regardless of chat status
  const canSendMessage = await checkChatMessageLimit(userId);
  if (!canSendMessage) {
    return {
      restricted: true,
      errorMessage: `You've reached your daily limit of ${DAILY_MESSAGE_LIMIT} messages. Upgrade for unlimited messages.`,
    };
  }
  
  // Check total chat limit only for new chats
  if (!chat) {
    const canCreateChat = await checkChatCreationLimit(userId);
    if (!canCreateChat) {
      return {
        restricted: true,
        errorMessage: `You've reached your limit of ${TOTAL_CHAT_LIMIT} total chats. Upgrade for unlimited usage or delete old chats.`
      };
    }
  }
  
  // No restrictions apply
  return { restricted: false };
}

/**
 * Gets usage statistics for free tier users
 * Returns total chats, daily message count, and time until reset
 */
export async function getFreeTierUsageStats(userId: string): Promise<{
  totalChats: number;
  dailyMessageCount: number;
  timeUntilReset: number;
  memoryUsageMB: number;
}> {
  console.log(`Getting usage stats for user: ${userId}`);
  
  try {
    // Get total chats, daily message count, and memory usage using existing functions
    const [totalChats, dailyMessageCount, totalMemoryBytes] = await Promise.all([
      getAllChatsCreatedByUser(userId),
      getAssistantMessagesSentTodayForUser(userId),
      getUserTotalMemoryUsage(userId)
    ]);
    
  
    
    // Calculate minutes until reset
    const timeUntilReset = getMinutesUntilReset();
    
    // Convert memory usage from bytes to MB
    const memoryUsageMB = Math.round((totalMemoryBytes / (1024 * 1024)) * 10) / 10;
    
    const result = {
      totalChats,
      dailyMessageCount,
      timeUntilReset,
      memoryUsageMB
    };
    
 
    
    return result;
  } catch (error) {
    console.error('Error in getFreeTierUsageStats:', error);
    return {
      totalChats: 0,
      dailyMessageCount: 0,
      timeUntilReset: 0,
      memoryUsageMB: 0
    };
  }
}

