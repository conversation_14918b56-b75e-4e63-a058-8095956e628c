import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";
import * as dotenv from "dotenv";
import { sql } from "drizzle-orm";
import { Logger } from "../utils/Logger";
import { getPostgresUrl } from "../utils/env";

dotenv.config();

const postgresUrl = getPostgresUrl();

if (!postgresUrl) {
  throw new Error("No POSTGRES_URL provided in .env");
}

const migrationClient = postgres(postgresUrl, { max: 1 });

async function runMigrate() {
  try {
    const db = drizzle(migrationClient);

    // First enable pgvector extension and setup similarity function
    await db.execute(sql`
      CREATE EXTENSION IF NOT EXISTS vector;

      CREATE OR REPLACE FUNCTION cosine_similarity(a vector, b vector)
      RETURNS float
      LANGUAGE plpgsql
      AS $$
      BEGIN
          -- Ensure vectors are normalized
          RETURN 1 - (a <=> b);
      END;
      $$;
    `);

    // Then run the regular migrations
    await migrate(db, {
      migrationsFolder: `./lib/db/migrations/${
        process.env.VERCEL_ENV || "development"
      }`,
    });

    // Finally create the vector search index
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS embeddings_embedding_idx 
      ON "embeddings" 
      USING ivfflat (embedding vector_cosine_ops)
      WITH (lists = 100);
    `);

    await migrationClient.end();
    Logger.info("Migration completed");
    process.exit(0);
  } catch (error) {
    Logger.error("Migration failed:", error);
    await migrationClient.end().catch(console.error);
    process.exit(1);
  }
}

runMigrate();
