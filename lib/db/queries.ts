import { genSaltSync, hashSync } from "bcrypt-ts";
import {
	aliasedTable,
	and,
	asc,
	desc,
	eq,
	gt,
	gte,
	ilike,
	inArray,
	lt,
	or,
	sql,
} from "drizzle-orm";
import { drizzle } from "drizzle-orm/postgres-js";
import { sync } from "framer-motion";
import { get } from "http";
import postgres from "postgres";
import { BlockKind } from "@/components/block";
import { stripe } from "@/lib/stripe";
import { DEFAULT_CHAT_VISIBILITY } from "@/lib/types";
import { getSignedUrlToDownload } from "@/lib/utils/s3Object";
import { Logger } from "../utils/Logger";
import { db } from "./db";
import type {
	Chat,
	DocumentType,
	Message,
	Prompt,
	PromptFile,
	PromptFolder,
	Referral,
	ReferralCredit,
	Suggestion,
	User,
	UserPreferences,
	Vote,
} from "./schema";
import {
	artifact,
	chat,
	Document,
	document,
	embeddings,
	errorEvents,
	message,
	messageDocuments,
	notificationTargets,
	Plan,
	pendingDocumentAssociations,
	plan,
	promptFiles,
	promptFolders,
	prompts,
	referral,
	referralCredit,
	resources,
	Subscription,
	sourceDocuments,
	subscription,
	suggestion,
	user,
	userFavorites,
	userPreferences,
	vote,
} from "./schema";

/**
 * Update user's password securely
 */
export async function updateUserPassword(
	email: string,
	hashedPassword: string,
) {
	try {
		const result = await db
			.update(user)
			.set({ password: hashedPassword })
			.where(sql`LOWER(${user.email}) = LOWER(${email})`)
			.returning();

		if (result.length === 0) {
			throw new Error("User not found or password not updated.");
		}

		return { success: true, message: "Password updated successfully" };
	} catch (error) {
		Logger.error("Failed to update password in database", error);
		throw error;
	}
}

/**
 * Update user's email verification
 */
export async function updateUserEmailVerification(email: string) {
	try {
		const result = await db
			.update(user)
			.set({ isEmailVerified: true })
			.where(sql`LOWER(${user.email}) = LOWER(${email})`)
			.returning();

		if (result.length === 0) {
			throw new Error("User not found or email verify not updated.");
		}

		return { success: true, message: "Email verified successfully" };
	} catch (error) {
		Logger.error("Failed to update email verification in database", error);
		throw error;
	}
}

/**
 * Add initial user preferences
 */
export async function addInitialPreferences(user: User) {
	try {
		await db.insert(userPreferences).values({
			userId: user.id,
			fullName: `${user.firstname} ${user.lastname}`.trim(),
			email: user.email,
		});
	} catch (error) {
		Logger.error("Failed to add initial user preferences", error);
		throw error;
	}
}

// Remove these lines as they're duplicates
// const client = postgres(getPostgresUrl()!);
// export const db = drizzle(client, { schema });

export async function getUser(email: string): Promise<Array<User>> {
	try {
		return await db
			.select()
			.from(user)
			.where(sql`LOWER(${user.email}) = LOWER(${email})`);
	} catch (error) {
		Logger.error(`Failed to get user from database ${error}`);
		throw error;
	}
}

export async function createUser(
	email: string,
	referralCode?: string,
	password?: string,
	referrerId?: string,
	firstname?: string,
	lastname?: string,
	company?: string,
	teamsize?: string,
	description?: string,
	isAdmin: boolean = false,
	heardFrom?: string,
) {
	const salt = genSaltSync(10);
	let hash = "";
	if (password) {
		hash = hashSync(password, salt);
	}

	try {
		return await db
			.insert(user)
			.values({
				email,
				password: hash,
				isAdmin,
				firstname,
				lastname,
				company,
				teamsize,
				description,
				referralCode,
				referrerId: referrerId ?? null,
				heardFrom,
			})
			.returning();
	} catch (error) {
		Logger.error("Failed to create user in database");
		throw error;
	}
}

export async function getAllUsers(): Promise<Array<User>> {
	try {
		return await db.select().from(user);
	} catch (error) {
		Logger.error("Failed to get users from database");
		throw error;
	}
}

export async function deleteUser(userId: string) {
	try {
		return await db.transaction(async (tx) => {
			// Fetching all chats and source docs owned by the user in parallel
			const [userChats, userSourceDocs] = await Promise.all([
				tx.select({ id: chat.id }).from(chat).where(eq(chat.userId, userId)),
				tx
					.select({ id: sourceDocuments.id })
					.from(sourceDocuments)
					.where(eq(sourceDocuments.userId, userId)),
			]);

			// Deleting chat-related data in parallel
			await Promise.all(
				userChats.map(async (userChat) => {
					await Promise.all([
						tx
							.delete(messageDocuments)
							.where(
								eq(
									messageDocuments.messageId,
									tx
										.select({ id: message.id })
										.from(message)
										.where(eq(message.chatId, userChat.id))
										.limit(1),
								),
							),
						tx.delete(vote).where(eq(vote.chatId, userChat.id)),
						tx.delete(message).where(eq(message.chatId, userChat.id)),
					]);
				}),
			);

			// Deleting source document-related data in parallel
			await Promise.all(
				userSourceDocs.map(async (doc) => {
					await Promise.all([
						tx
							.delete(embeddings)
							.where(
								eq(
									embeddings.resourceId,
									tx
										.select({ id: resources.id })
										.from(resources)
										.where(eq(resources.sourceDocumentId, doc.id))
										.limit(1),
								),
							),
						tx.delete(resources).where(eq(resources.sourceDocumentId, doc.id)),
					]);
				}),
			);

			// Delete notification targets for this user
			await tx
				.delete(notificationTargets)
				.where(eq(notificationTargets.userId, userId));

			// Final cleanup in parallel
			await Promise.all([
				tx.delete(chat).where(eq(chat.userId, userId)),
				tx.delete(sourceDocuments).where(eq(sourceDocuments.userId, userId)),
				tx.delete(Document).where(eq(Document.userId, userId)),
				tx.delete(suggestion).where(eq(suggestion.userId, userId)),
				tx.delete(user).where(eq(user.id, userId)),
			]);
		});
	} catch (error) {
		Logger.error("Failed to delete user from database with error:", error);
		throw error;
	}
}

export async function saveChat({
	id,
	userId,
	title,
	modelId,
	visibility = "public",
}: {
	id?: string;
	userId: string;
	title: string;
	modelId?: string;
	visibility?: "public" | "private";
}) {
	try {
		const values = {
			...(id && { id }), // Only include id if it's provided
			createdAt: new Date(),
			userId,
			title,
			visibility: DEFAULT_CHAT_VISIBILITY,
		};
		return await db.insert(chat).values(values);
	} catch (error) {
		Logger.error("Failed to save chat in database");
		throw error;
	}
}

export async function updateChatTitleById({
	id,
	title,
}: {
	id: string;
	title: string;
}) {
	try {
		return await db.update(chat).set({ title }).where(eq(chat.id, id));
	} catch (error) {
		Logger.error("Failed to update chat title in database", error);
		throw error;
	}
}

export async function deleteChatById({ id }: { id: string }) {
	try {
		await db.delete(vote).where(eq(vote.chatId, id));
		await db.delete(message).where(eq(message.chatId, id));

		return await db.delete(chat).where(eq(chat.id, id));
	} catch (error) {
		Logger.error("Failed to delete chat by id from database");
		throw error;
	}
}

export async function getChatsByUserId({ id }: { id: string }) {
	try {
		const chats = await db
			.select({
				id: chat.id,
				title: chat.title,
				createdAt: chat.createdAt,
				updatedAt: chat.updatedAt,
				visibility: chat.visibility,
				userId: chat.userId,
			})
			.from(chat)
			.where(eq(chat.userId, id))
			.orderBy(desc(chat.updatedAt));

		return chats;
	} catch (error) {
		Logger.error("Failed to get chats from database:", error);
		return [];
	}
}

export async function getChatById({ id }: { id: string }) {
	try {
		const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
		return selectedChat;
	} catch (error) {
		Logger.error("Failed to get chat by id from database");
		throw error;
	}
}

export async function saveMessages({ messages }: { messages: Array<Message> }) {
	try {
		await db.insert(message).values(messages);

		// Update the chat timestamp if there are messages
		if (messages.length > 0) {
			const chatId = messages[0].chatId;
			await updateChatTimestamp({ id: chatId });
			Logger.info("Updated chat timestamp after saving messages", {
				chatId,
			});
		}

		return true;
	} catch (error) {
		Logger.error("Failed to save messages in database", error);
		throw error;
	}
}

export async function getMessagesByChatId({ id }: { id: string }) {
	try {
		return await db
			.select()
			.from(message)
			.where(eq(message.chatId, id))
			.orderBy(asc(message.createdAt));
	} catch (error) {
		Logger.error("Failed to get messages by chat id from database", error);
		throw error;
	}
}

export async function saveMessageDocuments({
	messageId,
	documentIds,
	retryCount = 0,
}: {
	messageId: string;
	documentIds: string[];
	retryCount?: number;
}): Promise<void> {
	if (!documentIds.length) {
		return;
	}

	Logger.info("Saving message-document links", {
		messageId,
		documentCount: documentIds.length,
		documentIds,
	});

	try {
		// Check if the message exists
		const messageExists = await db
			.select({ id: message.id })
			.from(message)
			.where(eq(message.id, messageId))
			.limit(1);

		if (messageExists.length === 0) {
			Logger.warn("Message does not exist in database", { messageId });

			// If this is a retry attempt, give up after 3 tries
			if (retryCount >= 3) {
				throw new Error(
					`Message with ID ${messageId} does not exist in database after ${retryCount} retries`,
				);
			}

			// Schedule a retry after a delay
			Logger.info(
				`Scheduling retry #${retryCount + 1} for message-document association`,
				{ messageId },
			);
			setTimeout(
				() => {
					saveMessageDocuments({
						messageId,
						documentIds,
						retryCount: retryCount + 1,
					}).catch((err) => {
						Logger.error(
							`Retry #${retryCount + 1} failed for message-document association`,
							{
								messageId,
								error: err,
							},
						);
					});
				},
				2000 * (retryCount + 1),
			); // Exponential backoff

			return;
		}

		// Insert links one by one to avoid failing the entire batch if one document is invalid
		const results = await Promise.allSettled(
			documentIds.map(async (docId) => {
				try {
					// First check if the document exists
					const docExists = await db
						.select({ id: sourceDocuments.id })
						.from(sourceDocuments)
						.where(eq(sourceDocuments.id, docId))
						.limit(1);

					if (docExists.length === 0) {
						Logger.warn("Document does not exist in database", { docId });
						return null;
					}

					await db
						.insert(messageDocuments)
						.values({
							messageId,
							sourceDocumentId: docId,
						})
						.onConflictDoNothing();
					return docId;
				} catch (error) {
					Logger.error("Error inserting message-document link", {
						messageId,
						documentId: docId,
						error,
					});
					throw error;
				}
			}),
		);

		const successful = results.filter(
			(r) => r.status === "fulfilled" && r.value !== null,
		).length;
		Logger.info("Successfully saved message-document links", {
			messageId,
			count: successful,
		});
	} catch (error) {
		Logger.error("Failed to save message-document links in database", {
			error,
			messageId,
			documentIds,
		});
		throw error;
	}
}

export async function getMessagesWithAttachmentsByChatId({
	id,
}: {
	id: string;
}) {
	try {
		const result = await db
			.select({
				message: message,
				documents: {
					id: sourceDocuments.id,
					filename: sourceDocuments.filename,
					url: sourceDocuments.url,
				},
			})
			.from(message)
			.leftJoin(messageDocuments, eq(message.id, messageDocuments.messageId))
			.leftJoin(
				sourceDocuments,
				eq(messageDocuments.sourceDocumentId, sourceDocuments.id),
			)
			.where(eq(message.chatId, id))
			.orderBy(asc(message.createdAt));

		// Group attachments by message
		const messagesWithAttachments = result.reduce(
			(acc, row) => {
				const messageId = row.message.id;
				if (!acc[messageId]) {
					acc[messageId] = {
						...row.message,
						createdAt: row.message.createdAt, // Explicitly include createdAt
						metadata: row.message.metadata || undefined,
						experimental_attachments: row.documents?.id
							? [
									{
										id: row.documents.id,
										name: row.documents.filename,
										url: row.documents.url,
										document_id: row.documents.id, // Set document_id to match the document's ID
									},
								]
							: [],
					};
				} else if (row.documents?.id) {
					if (!acc[messageId].experimental_attachments) {
						acc[messageId].experimental_attachments = [];
					}
					acc[messageId].experimental_attachments.push({
						id: row.documents.id,
						name: row.documents.filename,
						url: row.documents.url,
						document_id: row.documents.id, // Set document_id to match the document's ID
					});
				}

				return acc;
			},
			{} as Record<string, Message>,
		);

		return Object.values(messagesWithAttachments);
	} catch (error) {
		Logger.error(
			"Failed to get messages with attachments by chat id from database",
			error,
		);
		throw error;
	}
}

export async function voteMessage({
	chatId,
	messageId,
	type,
}: {
	chatId: string;
	messageId: string;
	type: "up" | "down" | "neutral";
}) {
	try {
		const [existingVote] = await db
			.select()
			.from(vote)
			.where(and(eq(vote.messageId, messageId)));

		// If type is neutral, delete the vote if it exists
		if (type === "neutral") {
			if (existingVote) {
				return await db
					.delete(vote)
					.where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
			}
			return; // No vote to remove
		}

		// Otherwise update or insert the vote
		if (existingVote) {
			return await db
				.update(vote)
				.set({ isUpvoted: type === "up" })
				.where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
		}
		return await db.insert(vote).values({
			chatId,
			messageId,
			isUpvoted: type === "up",
		});
	} catch (error) {
		Logger.error("Failed to vote message in database", error);
		throw error;
	}
}

export async function getVotesByChatId({ id }: { id: string }) {
	try {
		return await db.select().from(vote).where(eq(vote.chatId, id));
	} catch (error) {
		Logger.error("Failed to get votes by chat id from database", error);
		throw error;
	}
}

export async function saveDocument({
	id,
	title,
	kind,
	content,
	userId,
}: {
	id: string;
	title: string;
	kind: BlockKind;
	content: string;
	userId: string;
}) {
	try {
		return await db.insert(Document).values({
			id,
			title,
			kind,
			content,
			userId,
			createdAt: new Date(),
		});
	} catch (error) {
		Logger.error("Failed to save document in database");
		throw error;
	}
}

export async function getDocumentsById({ id }: { id: string }) {
	try {
		if (!id || id === "undefined") {
			Logger.error("Invalid document ID provided:", id);
			return []; // Return empty array instead of null
		}

		const documents = await db
			.select()
			.from(Document)
			.where(eq(Document.id, id))
			.orderBy(asc(Document.createdAt));

		return documents;
	} catch (error) {
		Logger.error("Failed to get document by id from database");
		throw error;
	}
}

export async function getDocumentById({ id }: { id: string }) {
	try {
		if (!id || id === "undefined") {
			Logger.error("Invalid document ID provided:", id);
			return null;
		}

		const [selectedDocument] = await db
			.select()
			.from(Document)
			.where(eq(Document.id, id))
			.orderBy(desc(Document.createdAt));

		return selectedDocument;
	} catch (error) {
		Logger.error("Failed to get document by id from database:", {
			id,
			error,
		});
		throw error;
	}
}

export async function deleteDocumentsByIdAfterTimestamp({
	id,
	timestamp,
}: {
	id: string;
	timestamp: Date;
}) {
	try {
		await db
			.delete(suggestion)
			.where(
				and(
					eq(suggestion.documentId, id),
					gt(suggestion.documentCreatedAt, timestamp),
				),
			);

		return await db
			.delete(Document)
			.where(and(eq(Document.id, id), gt(Document.createdAt, timestamp)));
	} catch (error) {
		Logger.error(
			"Failed to delete documents by id after timestamp from database",
		);
		throw error;
	}
}

export async function saveSuggestions({
	suggestions,
}: {
	suggestions: Array<Suggestion>;
}) {
	try {
		return await db.insert(suggestion).values(suggestions);
	} catch (error) {
		Logger.error("Failed to save suggestions in database");
		throw error;
	}
}

export async function getSuggestionsByDocumentId({
	documentId,
}: {
	documentId: string;
}) {
	try {
		return await db
			.select()
			.from(suggestion)
			.where(and(eq(suggestion.documentId, documentId)));
	} catch (error) {
		Logger.error("Failed to get suggestions by document version from database");
		throw error;
	}
}

export async function getMessageById({ id }: { id: string }) {
	try {
		return await db.select().from(message).where(eq(message.id, id));
	} catch (error) {
		Logger.error("Failed to get message by id from database");
		throw error;
	}
}

export async function deleteMessagesByChatIdAfterTimestamp({
	chatId,
	timestamp,
}: {
	chatId: string;
	timestamp: Date;
}) {
	try {
		return await db
			.delete(message)
			.where(
				and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),
			);
	} catch (error) {
		Logger.error(
			"Failed to delete messages by id after timestamp from database",
		);
		throw error;
	}
}

export async function deleteMessagesByChatId({ chatId }: { chatId: string }) {
	try {
		return await db.delete(message).where(eq(message.chatId, chatId));
	} catch (error) {
		Logger.error("Failed to delete messages by chat id from database");
		throw error;
	}
}

export async function deleteMessageById({ id }: { id: string }) {
	try {
		return await db.delete(message).where(eq(message.id, id));
	} catch (error) {
		Logger.error("Failed to delete message by id from database");
		throw error;
	}
}

export async function updateChatVisibilityById({
	chatId,
	visibility,
}: {
	chatId: string;
	visibility: "private" | "public";
}) {
	try {
		return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
	} catch (error) {
		Logger.error("Failed to update chat visibility in database");
		throw error;
	}
}

export async function getSourceDocumentsByChatId({
	chatId,
}: {
	chatId: string;
}) {
	try {
		return await db
			.select()
			.from(sourceDocuments)
			.where(eq(sourceDocuments.chatId, chatId))
			.orderBy(desc(sourceDocuments.createdAt));
	} catch (error) {
		Logger.error("Failed to get source documents by chat id from database");
		throw error;
	}
}

export async function deleteSourceDocumentIfOwner({
	sourceDocId,
	userId,
}: {
	sourceDocId: string;
	userId: string;
}) {
	try {
		const result = await db
			.delete(sourceDocuments)
			.where(
				and(
					eq(sourceDocuments.id, sourceDocId),
					eq(sourceDocuments.userId, userId),
				),
			)
			.returning();

		// Return the deleted document if successful, or null if nothing was deleted
		return result[0] || null;
	} catch (error) {
		Logger.error("Failed to delete source document from database", error);
		throw error;
	}
}

type SearchResult = {
	chatId: string;
	chatTitle: string;
	messageId?: string;
	messagePreview?: string;
	timestamp: Date;
	matchType: "title" | "message";
	content?: string;
};

const extractCommandText = (content: any): string => {
	if (typeof content === "string") {
		return content.trim();
	} else if (Array.isArray(content)) {
		return content
			.filter((c: any) => c.type === "text")
			.map((c: any) => c.text)
			.join(" ")
			.trim();
	}
	return ""; // Return empty string for null, undefined, or other types
};

export async function searchChatsAndMessages(
	userId: string,
	query: string,
): Promise<SearchResult[]> {
	try {
		// Search in chat titles
		const titleResults = await db
			.select({
				chatId: chat.id,
				chatTitle: chat.title,
				timestamp: chat.createdAt,
			})
			.from(chat)
			.where(
				sql`${chat.title} ILIKE ${`%${query}%`} AND ${chat.userId} = ${userId}`,
			)
			.orderBy(desc(chat.createdAt))
			.limit(100);

		// Search in messages
		const messageResults = await db
			.select({
				chatId: message.chatId,
				messageId: message.id,
				content: message.content,
				timestamp: message.createdAt,
				chatTitle: chat.title,
			})
			.from(message)
			.innerJoin(chat, sql`${message.chatId} = ${chat.id}`)
			.where(
				sql`CAST(${message.content} AS TEXT) ILIKE ${`%${query}%`} AND ${
					chat.userId
				} = ${userId}`,
			)
			.orderBy(desc(chat.createdAt))
			.limit(100);
		// Filter messages to only include those with matching command text (exclude document json matches)
		const filteredMessageResults = messageResults.filter((r) => {
			const commandText = extractCommandText(r.content);
			// Mimic ILIKE (case-insensitive substring match)
			return commandText.toLowerCase().includes(query.toLowerCase());
		});

		const results = [
			...titleResults.map((r) => ({
				...r,
				matchType: "title" as const,
			})),
			...filteredMessageResults.map((r) => ({
				chatId: r.chatId,
				chatTitle: r.chatTitle,
				messageId: r.messageId,
				content: extractCommandText(r.content),
				matchType: "message" as const,
				timestamp: r.timestamp,
			})),
		].sort(
			(a, b) =>
				new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
		);

		// Remove duplicate chats (if a chat matches both title and message)
		const uniqueResults = results.filter(
			(result, index, self) =>
				index === self.findIndex((r) => r.chatId === result.chatId),
		);

		return uniqueResults;
	} catch (error) {
		Logger.error("Failed to search chats and messages in database", error);
		throw error;
	}
}

// Prompt Folder Functions

/**
 * Get all prompt folders for a user, including system folders
 */
export async function getPromptFolders(userId: string) {
	try {
		return await db
			.select()
			.from(promptFolders)
			.where(
				// Get both user folders and system folders
				sql`${promptFolders.userId} = ${userId} OR ${promptFolders.isSystem} = true`,
			)
			.orderBy(asc(promptFolders.name));
	} catch (error) {
		Logger.error("Failed to get prompt folders from database", error);
		throw error;
	}
}

/**
 * Get system prompt folders (starter pack)
 */
export async function getSystemPromptFolders() {
	try {
		return await db
			.select()
			.from(promptFolders)
			.where(eq(promptFolders.isSystem, true))
			.orderBy(asc(promptFolders.name));
	} catch (error) {
		Logger.error("Failed to get system prompt folders from database", error);
		throw error;
	}
}

/**
 * Get user prompt folders (playbook)
 */
export async function getUserPromptFolders(userId: string) {
	try {
		return await db
			.select()
			.from(promptFolders)
			.where(eq(promptFolders.userId, userId))
			.orderBy(asc(promptFolders.name));
	} catch (error) {
		Logger.error("Failed to get user prompt folders from database", error);
		throw error;
	}
}

/**
 * Create a new prompt folder
 */
export async function createPromptFolder({
	name,
	userId,
	isSystem = false,
}: {
	name: string;
	userId: string;
	isSystem?: boolean;
}) {
	try {
		// For non-system folders, verify that the user exists first
		if (!isSystem) {
			const users = await db.select().from(user).where(eq(user.id, userId));

			if (users.length === 0) {
				Logger.error(`User with ID ${userId} not found when creating folder`);
				throw new Error("USER_NOT_FOUND");
			}
		}

		// Check if a folder with the same name already exists for this user
		let conditions = [
			eq(promptFolders.name, name),
			eq(promptFolders.isSystem, isSystem),
		];

		// Add user ID condition based on whether it's a system folder
		if (isSystem) {
			conditions.push(sql`${promptFolders.userId} IS NULL`);
		} else {
			conditions.push(eq(promptFolders.userId, userId));
		}

		const existingFolders = await db
			.select()
			.from(promptFolders)
			.where(and(...conditions));

		if (existingFolders.length > 0) {
			// Folder with this name already exists
			throw new Error("DUPLICATE_FOLDER_NAME");
		}

		// Create the folder if no duplicate exists
		const [folder] = await db
			.insert(promptFolders)
			.values({
				name,
				userId: isSystem ? null : userId,
				isSystem,
			})
			.returning();
		return folder;
	} catch (error) {
		Logger.error("Failed to create prompt folder in database", error);
		throw error;
	}
}

/**
 * Update a prompt folder
 */
export async function updatePromptFolder({
	id,
	name,
	userId,
}: {
	id: string;
	name: string;
	userId: string;
}) {
	try {
		const [folder] = await db
			.update(promptFolders)
			.set({ name })
			.where(
				and(
					eq(promptFolders.id, id),
					eq(promptFolders.userId, userId),
					eq(promptFolders.isSystem, false),
				),
			)
			.returning();
		return folder;
	} catch (error) {
		Logger.error("Failed to update prompt folder in database", error);
		throw error;
	}
}

/**
 * Delete a prompt folder and all its prompts
 */
export async function deletePromptFolder({
	id,
	userId,
}: {
	id: string;
	userId: string;
}) {
	try {
		// Only allow deletion of user folders, not system folders
		const [folder] = await db
			.delete(promptFolders)
			.where(
				and(
					eq(promptFolders.id, id),
					eq(promptFolders.userId, userId),
					eq(promptFolders.isSystem, false),
				),
			)
			.returning();
		return folder;
	} catch (error) {
		Logger.error("Failed to delete prompt folder from database", error);
		throw error;
	}
}

// Prompt Functions

/**
 * Get all prompts in a folder
 */
export async function getPromptsByFolderId(folderId: string) {
	try {
		return await db
			.select()
			.from(prompts)
			.where(eq(prompts.folderId, folderId))
			.orderBy(asc(prompts.title));
	} catch (error) {
		Logger.error("Failed to get prompts by folder id from database", error);
		throw error;
	}
}

/**
 * Get a prompt by ID
 */
export async function getPromptById(id: string) {
	try {
		const [prompt] = await db.select().from(prompts).where(eq(prompts.id, id));
		return prompt;
	} catch (error) {
		Logger.error("Failed to get prompt by id from database", error);
		throw error;
	}
}

/**
 * Get favorite prompts for a user
 * Includes both user's own favorited prompts and system prompts marked as favorites
 */
export async function getFavoritePrompts(userId: string) {
	try {
		Logger.info(`Getting favorite prompts for user ${userId}`);

		// Get user's own prompts that are marked as favorites
		const userPrompts = await db
			.select({
				id: prompts.id,
				folderId: prompts.folderId,
				title: prompts.title,
				content: prompts.content,
				lastUsed: prompts.lastUsed,
				isFavorite: prompts.isFavorite,
				createdAt: prompts.createdAt,
				updatedAt: prompts.updatedAt,
				folderName: promptFolders.name,
				isSystemFolder: promptFolders.isSystem,
			})
			.from(prompts)
			.innerJoin(promptFolders, eq(prompts.folderId, promptFolders.id))
			.where(
				and(eq(promptFolders.userId, userId), eq(prompts.isFavorite, true)),
			);

		// Get system prompts that the user has marked as favorites
		const systemPrompts = await db
			.select({
				id: prompts.id,
				folderId: prompts.folderId,
				title: prompts.title,
				content: prompts.content,
				lastUsed: prompts.lastUsed,
				isFavorite: sql`true`, // Override to show as favorite
				createdAt: prompts.createdAt,
				updatedAt: prompts.updatedAt,
				folderName: promptFolders.name,
				isSystemFolder: promptFolders.isSystem,
			})
			.from(userFavorites)
			.innerJoin(prompts, eq(userFavorites.promptId, prompts.id))
			.innerJoin(promptFolders, eq(prompts.folderId, promptFolders.id))
			.where(eq(userFavorites.userId, userId));

		// Combine and return both sets of prompts
		const allFavorites = [...userPrompts, ...systemPrompts];

		Logger.info(
			`Found ${allFavorites.length} favorite prompts for user ${userId}`,
		);

		return allFavorites;
	} catch (error) {
		Logger.error("Failed to get favorite prompts from database", error);
		throw error;
	}
}

/**
 * Get recently used prompts for a user
 */
export async function getRecentPrompts(userId: string, limit = 5) {
	try {
		Logger.info(
			`Getting recent prompts for user ${userId} with limit ${limit}`,
		);

		// Get prompts from user's folders and system folders where lastUsed is not null
		const results = await db
			.select({
				id: prompts.id,
				folderId: prompts.folderId,
				title: prompts.title,
				content: prompts.content,
				lastUsed: prompts.lastUsed,
				isFavorite: prompts.isFavorite,
				createdAt: prompts.createdAt,
				updatedAt: prompts.updatedAt,
				folderName: promptFolders.name,
				isSystemFolder: promptFolders.isSystem,
			})
			.from(prompts)
			.innerJoin(promptFolders, eq(prompts.folderId, promptFolders.id))
			.where(
				and(
					// Only get prompts from user's folders
					eq(promptFolders.userId, userId),
					// Only include prompts with non-null lastUsed
					sql`${prompts.lastUsed} IS NOT NULL`,
				),
			)
			.orderBy(desc(prompts.lastUsed))
			.limit(limit);

		Logger.info(`Found ${results.length} recent prompts for user ${userId}`);

		// Now fetch the attachments for these prompts
		const promptIds = results.map((prompt) => prompt.id);
		const attachments =
			promptIds.length > 0
				? await db
						.select()
						.from(promptFiles)
						.where(inArray(promptFiles.promptId, promptIds))
				: [];

		// Map attachments to their respective prompts
		return results.map((prompt) => {
			const promptAttachments = attachments
				.filter((file) => file.promptId === prompt.id)
				.map((file) => ({
					name: file.fileName,
					url: file.fileUrl,
					contentType: file.fileType,
					size: file.fileSize,
					document_id: file.id,
				}));

			return {
				...prompt,
				attachments: promptAttachments,
			};
		});
	} catch (error) {
		Logger.error("Failed to get recent prompts from database", error);
		throw error;
	}
}

/**
 * Get or create a default folder for a user
 */
export async function getOrCreateDefaultFolder(userId: string) {
	try {
		// Check if the user already has a default folder
		const [defaultFolder] = await db
			.select()
			.from(promptFolders)
			.where(
				and(
					eq(promptFolders.userId, userId),
					eq(promptFolders.name, "General"),
				),
			);

		if (defaultFolder) {
			return defaultFolder;
		}

		// Create a default folder if it doesn't exist
		const [newFolder] = await db
			.insert(promptFolders)
			.values({
				name: "General",
				userId,
				isSystem: false,
			})
			.returning();

		return newFolder;
	} catch (error) {
		Logger.error("Failed to get or create default folder", error);
		throw error;
	}
}

/**
 * Check if a prompt title already exists in a folder for a user
 */
export async function checkPromptTitleExists({
	title,
	folderId,
	userId,
	excludePromptId,
}: {
	title: string;
	folderId: string;
	userId: string;
	excludePromptId?: string;
}) {
	try {
		// Get the folder to check if it's a system folder
		const [folder] = await db
			.select()
			.from(promptFolders)
			.where(eq(promptFolders.id, folderId));

		if (!folder) {
			throw new Error("FOLDER_NOT_FOUND");
		}

		// Build conditions for the query
		let conditions = [
			eq(prompts.folderId, folderId),
			sql`LOWER(${prompts.title}) = LOWER(${title})`, // Case-insensitive comparison
		];

		// For system folders, check all prompts in that folder
		// For user folders, only check prompts accessible to this user
		if (!folder.isSystem) {
			// For user folders, ensure the folder belongs to the user
			if (folder.userId !== userId) {
				throw new Error("FOLDER_ACCESS_DENIED");
			}
		}

		// Exclude the current prompt if updating
		if (excludePromptId) {
			conditions.push(sql`${prompts.id} != ${excludePromptId}`);
		}

		const existingPrompts = await db
			.select({ id: prompts.id })
			.from(prompts)
			.where(and(...conditions))
			.limit(1);

		return existingPrompts.length > 0;
	} catch (error) {
		Logger.error("Failed to check prompt title existence", error);
		throw error;
	}
}

/**
 * Create a new prompt
 */
export async function createPrompt({
	title,
	content,
	folderId,
	userId,
	isFavorite = false,
}: {
	title: string;
	content: string;
	folderId?: string;
	userId: string;
	isFavorite?: boolean;
}) {
	try {
		// If no folderId is provided, use the default folder
		let actualFolderId = folderId;
		if (!actualFolderId) {
			const defaultFolder = await getOrCreateDefaultFolder(userId);
			actualFolderId = defaultFolder.id;
		}

		// Check for duplicate title in the folder
		const titleExists = await checkPromptTitleExists({
			title,
			folderId: actualFolderId,
			userId,
		});

		if (titleExists) {
			throw new Error("DUPLICATE_PROMPT_TITLE");
		}

		const [prompt] = await db
			.insert(prompts)
			.values({
				title,
				content,
				folderId: actualFolderId,
				isFavorite,
			})
			.returning();
		return prompt;
	} catch (error) {
		Logger.error("Failed to create prompt in database", error);
		throw error;
	}
}

/**
 * Update a prompt
 */
export async function updatePrompt({
	id,
	title,
	content,
	folderId,
	isFavorite,
	userId,
}: {
	id: string;
	title?: string;
	content?: string;
	folderId?: string;
	isFavorite?: boolean;
	userId?: string;
}) {
	try {
		const updateData: Partial<Prompt> = {};
		if (title !== undefined) updateData.title = title;
		if (content !== undefined) updateData.content = content;
		if (folderId !== undefined) updateData.folderId = folderId;
		if (isFavorite !== undefined) updateData.isFavorite = isFavorite;

		// If title or folderId is being updated, check for duplicates
		if ((title !== undefined || folderId !== undefined) && userId) {
			// Get the current prompt to determine the folder to check
			const [currentPrompt] = await db
				.select()
				.from(prompts)
				.where(eq(prompts.id, id));

			if (!currentPrompt) {
				throw new Error("PROMPT_NOT_FOUND");
			}

			const targetFolderId =
				folderId !== undefined ? folderId : currentPrompt.folderId;
			const targetTitle = title !== undefined ? title : currentPrompt.title;

			// Check for duplicate title in the target folder
			const titleExists = await checkPromptTitleExists({
				title: targetTitle,
				folderId: targetFolderId,
				userId,
				excludePromptId: id, // Exclude current prompt from check
			});

			if (titleExists) {
				throw new Error("DUPLICATE_PROMPT_TITLE");
			}
		}

		const [prompt] = await db
			.update(prompts)
			.set(updateData)
			.where(eq(prompts.id, id))
			.returning();
		return prompt;
	} catch (error) {
		Logger.error("Failed to update prompt in database", error);
		throw error;
	}
}

/**
 * Delete a prompt
 */
export async function deletePrompt(id: string) {
	try {
		const [prompt] = await db
			.delete(prompts)
			.where(eq(prompts.id, id))
			.returning();
		return prompt;
	} catch (error) {
		Logger.error("Failed to delete prompt from database", error);
		throw error;
	}
}

/**
 * Mark a prompt as used (update lastUsed timestamp) for a specific user
 * For user-specific prompts, we update the prompt directly
 * For system prompts, we create a copy in the user's default folder
 */
export async function markPromptAsUsed(id: string, userId: string) {
	try {
		// Validate parameters
		if (!id) {
			throw new Error("Prompt ID is required");
		}
		if (!userId) {
			throw new Error("User ID is required");
		}

		Logger.info(`Marking prompt ${id} as used by user ${userId}`);

		// Get the current prompt to verify it exists
		const prompt = await getPromptById(id);
		if (!prompt) {
			Logger.error(
				`Prompt with id ${id} not found when trying to mark as used`,
			);
			throw new Error(`Prompt with id ${id} not found`);
		}

		// Get the folder to check if it's a system prompt
		const [folder] = await db
			.select()
			.from(promptFolders)
			.where(eq(promptFolders.id, prompt.folderId));

		if (!folder) {
			Logger.error(`Folder not found for prompt ${id}`);
			throw new Error(`Folder not found for prompt ${id}`);
		}

		const now = new Date();
		Logger.info(`Setting lastUsed to ${now.toISOString()} for prompt ${id}`);

		// If it's a user's own prompt, simply update the lastUsed timestamp
		if (folder.userId === userId) {
			Logger.info(`Updating lastUsed for user's own prompt ${id}`);
			const [updated] = await db
				.update(prompts)
				.set({ lastUsed: now })
				.where(eq(prompts.id, id))
				.returning();
			return updated;
		}
		// If it's a system prompt or another user's prompt
		else {
			// Check if the user already has a copy of this prompt
			const [existingCopy] = await db
				.select({
					id: prompts.id,
					title: prompts.title,
					content: prompts.content,
					folderId: prompts.folderId,
					lastUsed: prompts.lastUsed,
					isFavorite: prompts.isFavorite,
				})
				.from(prompts)
				.innerJoin(promptFolders, eq(prompts.folderId, promptFolders.id))
				.where(
					and(
						eq(promptFolders.userId, userId),
						eq(prompts.title, `${prompt.title} (Copy)`),
					),
				);

			if (existingCopy) {
				// Update the existing copy
				Logger.info(`Updating lastUsed for existing copy of prompt ${id}`);
				const [updated] = await db
					.update(prompts)
					.set({ lastUsed: now })
					.where(eq(prompts.id, existingCopy.id))
					.returning();
				return updated;
			} else {
				// Get or create the user's default folder
				const defaultFolder = await getOrCreateDefaultFolder(userId);

				// Create a copy of the prompt in the user's default folder
				Logger.info(
					`Creating a copy of system prompt ${id} for user ${userId}`,
				);
				const [newPrompt] = await db
					.insert(prompts)
					.values({
						title: `${prompt.title} (Copy)`,
						content: prompt.content,
						folderId: defaultFolder.id,
						lastUsed: now,
						isFavorite: false,
					})
					.returning();

				// Copy the attachments from the original prompt
				const originalAttachments = await db
					.select()
					.from(promptFiles)
					.where(eq(promptFiles.promptId, id));

				if (originalAttachments.length > 0) {
					Logger.info(
						`Copying ${originalAttachments.length} attachments for prompt copy`,
					);

					for (const attachment of originalAttachments) {
						await db.insert(promptFiles).values({
							promptId: newPrompt.id,
							fileName: attachment.fileName,
							fileUrl: attachment.fileUrl,
							fileType: attachment.fileType,
							fileSize: attachment.fileSize,
							uploadedAt: new Date(),
						});
					}
				}

				return newPrompt;
			}
		}
	} catch (error) {
		Logger.error("Failed to mark prompt as used in database", error);
		throw error;
	}
}

/**
 * Remove a prompt from a user's recently used list
 */
export async function removeFromRecentPrompts(
	promptId: string,
	userId: string,
) {
	try {
		Logger.info(
			`Removing prompt ${promptId} from recently used list for user ${userId}`,
		);

		// First, verify the prompt belongs to this user
		const [prompt] = await db
			.select()
			.from(prompts)
			.innerJoin(promptFolders, eq(prompts.folderId, promptFolders.id))
			.where(and(eq(prompts.id, promptId), eq(promptFolders.userId, userId)));

		if (!prompt) {
			Logger.error(
				`Prompt ${promptId} not found or doesn't belong to user ${userId}`,
			);
			return null;
		}

		// Set lastUsed to null to remove from recently used list
		const [updated] = await db
			.update(prompts)
			.set({ lastUsed: null })
			.where(eq(prompts.id, promptId))
			.returning();

		return updated;
	} catch (error) {
		Logger.error("Failed to remove prompt from recently used list", error);
		throw error;
	}
}

/**
 * Toggle favorite status for a prompt
 * For user-specific prompts, we update the prompt directly
 * For system prompts, we use the userFavorites table
 */
export async function togglePromptFavorite(id: string, userId: string) {
	try {
		if (!id) {
			throw new Error("Prompt ID is required");
		}
		if (!userId) {
			throw new Error("User ID is required");
		}

		Logger.info(`Toggling favorite status for prompt ${id} by user ${userId}`);

		// Get the current prompt to verify it exists
		const prompt = await getPromptById(id);
		if (!prompt) {
			Logger.error(
				`Prompt with id ${id} not found when trying to toggle favorite`,
			);
			throw new Error(`Prompt with id ${id} not found`);
		}

		// Get the folder to check if it's a system prompt
		const [folder] = await db
			.select()
			.from(promptFolders)
			.where(eq(promptFolders.id, prompt.folderId));

		if (!folder) {
			Logger.error(`Folder not found for prompt ${id}`);
			throw new Error(`Folder not found for prompt ${id}`);
		}

		// If it's a user's own prompt, simply toggle the isFavorite flag
		if (folder.userId === userId) {
			Logger.info(`Toggling isFavorite for user's own prompt ${id}`);
			const [updated] = await db
				.update(prompts)
				.set({
					isFavorite: !prompt.isFavorite,
					updatedAt: new Date(),
				})
				.where(eq(prompts.id, id))
				.returning();
			return updated;
		}
		// If it's a system prompt or another user's prompt
		else {
			// Check if the user already has this prompt in their favorites
			const [existingFavorite] = await db
				.select()
				.from(userFavorites)
				.where(
					and(eq(userFavorites.promptId, id), eq(userFavorites.userId, userId)),
				);

			if (existingFavorite) {
				// Remove from favorites
				Logger.info(
					`Removing system prompt ${id} from favorites for user ${userId}`,
				);
				await db
					.delete(userFavorites)
					.where(eq(userFavorites.id, existingFavorite.id));

				// Return the prompt with updated isFavorite status
				return {
					...prompt,
					isFavorite: false,
				};
			} else {
				// Add to favorites
				Logger.info(
					`Adding system prompt ${id} to favorites for user ${userId}`,
				);
				await db.insert(userFavorites).values({
					promptId: id,
					userId: userId,
				});

				// Return the prompt with updated isFavorite status
				return {
					...prompt,
					isFavorite: true,
				};
			}
		}
	} catch (error) {
		Logger.error("Failed to toggle prompt favorite status in database", error);
		throw error;
	}
}

/**
 * Check if a prompt is favorited by a user
 * Used to determine the favorite status of system prompts in the UI
 */
export async function isPromptFavoritedByUser(
	promptId: string,
	userId: string,
) {
	try {
		// First check if it's a user's own prompt
		const [userPrompt] = await db
			.select()
			.from(prompts)
			.innerJoin(promptFolders, eq(prompts.folderId, promptFolders.id))
			.where(
				and(
					eq(prompts.id, promptId),
					eq(promptFolders.userId, userId),
					eq(prompts.isFavorite, true),
				),
			);

		if (userPrompt) {
			return true;
		}

		// Then check if it's a system prompt that the user has favorited
		const [systemFavorite] = await db
			.select()
			.from(userFavorites)
			.where(
				and(
					eq(userFavorites.promptId, promptId),
					eq(userFavorites.userId, userId),
				),
			);

		return !!systemFavorite;
	} catch (error) {
		Logger.error("Failed to check if prompt is favorited by user", error);
		return false;
	}
}

export async function updateChatModelById({
	id,
	modelId,
}: {
	id: string;
	modelId: string;
}) {
	try {
		return await db.update(chat).set({ modelId }).where(eq(chat.id, id));
	} catch (error) {
		Logger.error("Failed to update chat model in database");
		throw error;
	}
}

export async function getUserPreferences(
	userId: string,
): Promise<UserPreferences | null> {
	try {
		const [preferences] = await db
			.select()
			.from(userPreferences)
			.where(eq(userPreferences.userId, userId));

		return preferences || null;
	} catch (error) {
		Logger.error("Failed to get user preferences from database", error);
		return null;
	}
}

export async function createSourceDocumentIfNotExists({
	documentId,
	filename,
	url,
	fileSize,
	userId,
	chatId,
}: {
	documentId: string;
	filename: string;
	url: string;
	fileSize?: number;
	userId: string;
	chatId: string;
}) {
	try {
		// If fileSize is not a valid number, default to 0
		const safeFileSize = typeof fileSize === "number" ? fileSize : 0;

		// Use raw SQL for the upsert operation with the safe file size
		const result = await db.execute(sql`
            INSERT INTO "source_documents" ("id", "filename", "url", "file_size", "user_id", "chat_id", "created_at")
            VALUES (${documentId}, ${filename}, ${url}, ${safeFileSize}, ${userId}, ${chatId}, NOW())
            ON CONFLICT ("id") DO NOTHING
            RETURNING "id"
        `);
		// Check if a row was inserted or if it already existed
		if (result.length === 0) {
			Logger.info("Source document already exists", { documentId });
			return { id: documentId, isNew: false, success: true };
		}
		Logger.info("Created new source document", { documentId });
		return { id: documentId, isNew: true, success: true };
	} catch (error) {
		Logger.error("Failed to create source document", error);
		return { id: documentId, isNew: true, success: false };
	}
}

export async function updateChatTimestamp({ id }: { id: string }) {
	try {
		Logger.info("Updating chat timestamp", { chatId: id });
		const result = await db
			.update(chat)
			.set({ updatedAt: new Date() })
			.where(eq(chat.id, id));
		Logger.info("Chat timestamp updated", { chatId: id, result });
		return result;
	} catch (error) {
		Logger.error("Failed to update chat timestamp in database", error);
		throw error;
	}
}

export async function sourceDocumentExists({
	documentId,
}: {
	documentId: string;
}) {
	try {
		const result = await db
			.select({ id: sourceDocuments.id })
			.from(sourceDocuments)
			.where(eq(sourceDocuments.id, documentId))
			.limit(1);

		return result.length > 0;
	} catch (error) {
		Logger.error("Failed to check if source document exists", error);
		return false;
	}
}

export async function subscribe(userId: string, planId: string) {
	try {
		return await db.insert(subscription).values({
			userId,
			planId,
			status: "active",
			startDate: new Date(),
			autoRenew: true,
		});
	} catch (error) {
		Logger.error("Failed to create user subscription in database", error);
		throw error;
	}
}

export async function getPlanByName(name: string): Promise<Array<Plan>> {
	try {
		return await db.select().from(plan).where(eq(plan.name, name));
	} catch (error) {
		Logger.error("Failed to get plan from database");
		throw error;
	}
}

export async function getUserSubcriptionByUserId(
	userId: string,
): Promise<Array<Subscription>> {
	try {
		return await db
			.select()
			.from(subscription)
			.where(eq(subscription.userId, userId));
	} catch (error) {
		Logger.error(
			"Failed to get subscription from database while calling getUserSubcriptionByUserId",
		);
		throw error;
	}
}

/**
 * Check if a user has already used their free trial
 */
export async function hasUserUsedTrial(userId: string): Promise<boolean> {
	try {
		const [userSubscription] = await db
			.select()
			.from(subscription)
			.where(eq(subscription.userId, userId))
			.limit(1);

		return userSubscription?.isTrialUsed || false;
	} catch (error) {
		Logger.error("Failed to check if user has used trial", error);
		return false;
	}
}

export async function getPlanById(planId: string): Promise<Plan | null> {
	try {
		const [currentPlan] = await db
			.select()
			.from(plan)
			.where(eq(plan.id, planId))
			.limit(1);
		return currentPlan || null;
	} catch (error) {
		Logger.error("Failed to get plan from database while calling getPlanById");
		throw error;
	}
}

export async function getPlanByStripePriceId(
	priceId: string,
): Promise<Array<Plan>> {
	try {
		return await db
			.select()
			.from(plan)
			.where(eq(plan.stripePriceId, priceId))
			.limit(1);
	} catch (error) {
		Logger.error(
			"Failed to get plan from database while calling getPlanByStripePriceId",
		);
		throw error;
	}
}

export async function getActivePlans(): Promise<Array<Plan>> {
	try {
		return await db.select().from(plan).where(eq(plan.isActive, true));
	} catch (error) {
		Logger.error(
			"Failed to get plan from database while calling getActivePlans",
		);
		throw error;
	}
}

export async function getUserActiveSubscription(
	userId: string,
): Promise<Array<{ subscription: Subscription; plan: Plan }>> {
	try {
		const [activeSubscription] = await db
			.select({
				subscription: subscription,
				plan: plan,
			})
			.from(subscription)
			.innerJoin(plan, eq(subscription.planId, plan.id))
			.where(
				and(eq(subscription.status, "active"), eq(subscription.userId, userId)),
			)
			.orderBy(desc(subscription.createdAt)) // Order by creation date descending to get the latest
			.limit(1);
		return [activeSubscription];
	} catch (error) {
		Logger.error(
			"Failed to get plan from database while calling getUserActiveSubscription",
		);
		throw error;
	}
}

export async function getFirstActiveSubscription(
	userId: string,
): Promise<Subscription | null> {
	try {
		const [activeSubscription] = await db
			.select()
			.from(subscription)
			.where(
				and(eq(subscription.status, "active"), eq(subscription.userId, userId)),
			)
			.limit(1);
		return activeSubscription || null;
	} catch (error) {
		Logger.error(
			"Failed to get plan from database while calling getFirstActiveSubscription",
		);
		throw error;
	}
}

/**
 * Get user's subscription tier and end date
 */
export async function getUserSubscriptionInfo(userId: string): Promise<{
	subscriptionTier: string;
	subscriptionEndDate?: Date;
}> {
	try {
		// Get active subscription using existing query
		const [activeSubscription] = await getUserActiveSubscription(userId);

		// Default to free tier
		let subscriptionTier = "free";
		let subscriptionEndDate: Date | undefined = undefined;

		if (activeSubscription) {
			// Get plan details
			const userPlan = await getPlanById(activeSubscription.plan.id);

			if (userPlan) {
				// Set subscription tier based on plan name
				subscriptionTier = userPlan.name;

				// Check if it's a trial
				if (
					activeSubscription.subscription.isTrialUsed &&
					activeSubscription.subscription.trialEndsAt &&
					activeSubscription.subscription.trialEndsAt > new Date()
				) {
					// subscriptionTier = 'premium-trial';
					subscriptionEndDate = activeSubscription.subscription.trialEndsAt;
				} else {
					// Regular subscription end date
					subscriptionEndDate =
						activeSubscription.subscription.endDate || undefined;
				}
			}
		}

		return { subscriptionTier, subscriptionEndDate };
	} catch (error) {
		Logger.error("Failed to get user subscription info", error);
		return { subscriptionTier: "free" };
	}
}

/**
 * Create a new prompt file (document attachment for a prompt)
 */
export async function createPromptFile({
	promptId,
	fileName,
	fileUrl,
	fileType,
	fileSize,
}: {
	promptId: string;
	fileName: string;
	fileUrl: string;
	fileType: string;
	fileSize?: number;
}) {
	try {
		const [promptFile] = await db
			.insert(promptFiles)
			.values({
				promptId,
				fileName,
				fileUrl,
				fileType,
				fileSize,
			})
			.returning();
		return promptFile;
	} catch (error) {
		Logger.error("Failed to create prompt file in database", error);
		throw error;
	}
}

/**
 * Get all files associated with a prompt
 */
export async function getPromptFilesByPromptId(promptId: string) {
	try {
		return await db
			.select()
			.from(promptFiles)
			.where(eq(promptFiles.promptId, promptId))
			.orderBy(asc(promptFiles.uploadedAt));
	} catch (error) {
		Logger.error(
			"Failed to get prompt files by prompt id from database",
			error,
		);
		throw error;
	}
}

/**
 * Delete all files associated with a prompt
 */
export async function deletePromptFilesByPromptId(promptId: string) {
	try {
		Logger.info(`Deleting all files for prompt ${promptId}`);
		const result = await db
			.delete(promptFiles)
			.where(eq(promptFiles.promptId, promptId))
			.returning();

		Logger.info(
			`Successfully deleted ${result.length} files for prompt ${promptId}`,
		);
		return result;
	} catch (error) {
		Logger.error(
			"Failed to delete prompt files by prompt id from database",
			error,
		);
		throw error;
	}
}

/**
 * Ensures a user has at least a free plan subscription
 * @param userId The user ID to check and create subscription for if needed
 */
export async function ensureUserHasSubscription(userId: string) {
	try {
		// Check if user already has any subscription
		const existingSubscription = await db
			.select()
			.from(subscription)
			.where(eq(subscription.userId, userId))
			.limit(1);

		if (existingSubscription.length > 0) {
			return; // User already has a subscription
		}

		const [fetchedUser] = await db
			.select()
			.from(user)
			.where(eq(user.id, userId))
			.limit(1);

		if (fetchedUser.isAdmin) {
			const [premiumPlan] = await db
				.select()
				.from(plan)
				.where(eq(plan.name, "premium"))
				.limit(1);

			if (!premiumPlan) {
				Logger.error("Premium plan not found in database");
				return;
			}

			// Create a premium subscription for the user
			await db.insert(subscription).values({
				userId: userId,
				planId: premiumPlan.id,
				status: "active",
				startDate: new Date(),
				autoRenew: true,
			});
			await db
				.update(user)
				.set({
					subscriptionTier: "premium",
				})
				.where(eq(user.id, userId));

			Logger.info(`Created premium subscription for user ${userId}`);
			return;
		} else {
			// Get the free plan
			const [freePlan] = await db
				.select()
				.from(plan)
				.where(eq(plan.name, "free"))
				.limit(1);

			if (!freePlan) {
				Logger.error("Free plan not found in database");
				return;
			}

			// Create a free subscription for the user
			await db.insert(subscription).values({
				userId: userId,
				planId: freePlan.id,
				status: "active",
				startDate: new Date(),
				autoRenew: true,
			});

			Logger.info(`Created free subscription for user ${userId}`);
		}
	} catch (error) {
		Logger.error("Failed to ensure user has subscription", error);
	}
}

/**
 * Stores pending document associations for retry
 */
export async function storePendingDocumentAssociation({
	messageId,
	documentIds,
}: {
	messageId: string;
	documentIds: string[];
}): Promise<void> {
	try {
		// Create a table for pending associations if it doesn't exist yet
		// This is a simplified example - in production, you'd use a proper queue or table
		await db.insert(pendingDocumentAssociations).values({
			messageId,
			documentIds: JSON.stringify(documentIds),
			createdAt: new Date(),
			retryCount: 0,
		});

		Logger.info("Stored pending document association for retry", {
			messageId,
			documentCount: documentIds.length,
		});
	} catch (error) {
		Logger.error("Failed to store pending document association", {
			error,
			messageId,
			documentCount: documentIds.length,
		});
	}
}

/**
 * Processes pending document associations
 * This would be called by a cron job or background worker
 */
export async function processPendingDocumentAssociations(): Promise<void> {
	try {
		// Get pending associations with retry count < 3
		const pendingAssociations = await db
			.select()
			.from(pendingDocumentAssociations)
			.where(
				and(
					lt(pendingDocumentAssociations.retryCount, 3),
					gt(
						pendingDocumentAssociations.createdAt,
						new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
					),
				),
			)
			.limit(100);

		for (const association of pendingAssociations) {
			try {
				await saveMessageDocuments({
					messageId: association.messageId,
					documentIds: JSON.parse(association.documentIds),
				});

				// Delete the pending association if successful
				await db
					.delete(pendingDocumentAssociations)
					.where(eq(pendingDocumentAssociations.id, association.id));

				Logger.info("Successfully processed pending document association", {
					messageId: association.messageId,
				});
			} catch (error) {
				// Increment retry count
				await db
					.update(pendingDocumentAssociations)
					.set({
						retryCount: association.retryCount + 1,
						lastRetryAt: new Date(),
					})
					.where(eq(pendingDocumentAssociations.id, association.id));

				Logger.error("Failed to process pending document association", {
					error,
					messageId: association.messageId,
					retryCount: association.retryCount + 1,
				});
			}
		}
	} catch (error) {
		Logger.error("Error processing pending document associations", { error });
	}
}

export async function deleteChatsByIds({
	ids,
	userId,
}: {
	ids: string[];
	userId: string;
}) {
	try {
		// First verify all chats belong to the user
		const chatsToDelete = await db
			.select({ id: chat.id })
			.from(chat)
			.where(and(eq(chat.userId, userId), inArray(chat.id, ids)));

		const validIds = chatsToDelete.map((c) => c.id);

		if (validIds.length === 0) {
			return { count: 0 };
		}

		// Delete related records first
		await db.delete(vote).where(inArray(vote.chatId, validIds));
		await db.delete(message).where(inArray(message.chatId, validIds));

		// Delete the chats
		const _result = await db.delete(chat).where(inArray(chat.id, validIds));
		return { count: validIds.length };
	} catch (error) {
		Logger.error("Failed to bulk delete chats from database", error);
		throw error;
	}
}

export async function getUserByReferralCode(
	referralCode: string,
): Promise<string | undefined> {
	try {
		const [userMatch] = await db
			.select({ id: user.id })
			.from(user)
			.where(eq(user.referralCode, referralCode))
			.limit(1);
		console.log("for refererral user -==> ", referralCode, userMatch);

		return userMatch?.id ?? undefined;
	} catch (error) {
		Logger.error(`Failed to get user from database ${error}`);
		throw error;
	}
}

export async function addReferralEntry(
	referrerId: string,
	referredUserId: string,
): Promise<Referral> {
	try {
		const [existingReferral] = await db
			.select()
			.from(referral)
			.where(eq(referral.referredUserId, referredUserId))
			.limit(1);

		if (existingReferral) {
			return existingReferral;
		} else {
			const [referralEntry] = await db
				.insert(referral)
				.values({
					referrerId,
					referredUserId,
				})
				.returning();
			return referralEntry;
		}
	} catch (error) {
		console.error("Failed to insert referral entry:", error);
		throw error;
	}
}

export async function markReferralAsRewarded({
	referrerId,
	referredUserId,
}: {
	referrerId: string;
	referredUserId: string;
}): Promise<Referral> {
	try {
		const [updatedReferral] = await db
			.update(referral)
			.set({ isRewarded: "converted" })
			.where(
				and(
					eq(referral.referrerId, referrerId),
					eq(referral.referredUserId, referredUserId),
				),
			)
			.returning();

		return updatedReferral;
	} catch (error) {
		console.error("Failed to mark referral as rewarded:", error);
		throw error;
	}
}

export async function createReferralCredit({
	referralId,
	referrerId,
	amount,
}: {
	referralId: string;
	referrerId: string;
	amount: number;
}): Promise<ReferralCredit> {
	try {
		// Get the referrer's details
		const [referrer] = await db
			.select()
			.from(user)
			.where(eq(user.id, referrerId))
			.limit(1);

		if (!referrer) {
			throw new Error(`Referrer with ID ${referrerId} not found`);
		}

		// Create database entry first to track the credit regardless of Stripe status
		const [referralCreditEntry] = await db
			.insert(referralCredit)
			.values({
				referralId,
				referrerId,
				amount,
				appliedToStripe: false, // Track whether it's been applied to Stripe
			})
			.returning();

		let stripeCustomerId = referrer.stripeCustomerId;

		// If user doesn't have a Stripe customer ID, create one
		if (!stripeCustomerId) {
			try {
				// Create a new Stripe customer
				const customer = await stripe.customers.create({
					email: referrer.email,
					name: `${referrer.firstname} ${referrer.lastname}`,
					metadata: { userId: referrer.id, email: referrer.email },
				});

				// Update user with new Stripe customer ID
				await db
					.update(user)
					.set({ stripeCustomerId: customer.id })
					.where(eq(user.id, referrer.id));

				stripeCustomerId = customer.id;
				Logger.info(
					`Created Stripe customer for referrer ${referrer.id}: ${customer.id}`,
				);
			} catch (error) {
				Logger.error(
					`Failed to create Stripe customer for referrer ${referrer.id}:`,
					error,
				);
				// Continue without applying credit to Stripe
				return referralCreditEntry;
			}
		}

		// Apply the credit to Stripe
		if (stripeCustomerId) {
			try {
				await stripe.customers.createBalanceTransaction(stripeCustomerId, {
					amount: -amount, // Negative amount creates a credit
					currency: "usd",
					description: "Referral credit - paid subscription",
				});

				// Update the credit as applied
				await db
					.update(referralCredit)
					.set({ appliedToStripe: true })
					.where(eq(referralCredit.id, referralCreditEntry.id));

				Logger.info(
					`Applied referral credit of $${
						amount / 100
					} to customer ${stripeCustomerId}`,
				);
			} catch (error) {
				Logger.error(
					`Failed to apply credit to Stripe for customer ${stripeCustomerId}:`,
					error,
				);
				// Credit is still tracked in our database even if Stripe application fails
			}
		}

		return referralCreditEntry;
	} catch (error) {
		console.error("Failed to insert referral credit entry:", error);
		throw error;
	}
}

/**
 * Update user's referralCode
 */
export async function updateUserReferralCode(
	email: string,
	referralCode: string,
) {
	try {
		const result = await db
			.update(user)
			.set({ referralCode })
			.where(sql`LOWER(${user.email}) = LOWER(${email})`)
			.returning();

		if (result.length === 0) {
			throw new Error("User not found or referral code not updated.");
		}

		return { success: true, message: "Referral code updated successfully" };
	} catch (error) {
		Logger.error("Failed to update referral code in database", error);
		throw error;
	}
}

/**
 * Update user's referrerId
 */
export async function updateUserReferrerId(id: string, referrerId: string) {
	try {
		const result = await db
			.update(user)
			.set({ referrerId })
			.where(eq(user.id, id))
			.returning();

		if (result.length === 0) {
			throw new Error("User not found or referrer ID not updated.");
		}

		return { success: true, message: "Referrer ID updated successfully" };
	} catch (error) {
		Logger.error("Failed to update referrer ID in database", error);
		throw error;
	}
}

/**
 * Preview artifact
 */
export async function getArtifactByDocumentId(
	documentId: string,
	userId: string,
) {
	try {
		const docs = await db
			.select({
				document,
				storageKey: artifact.storageKey,
			})
			.from(document)
			.leftJoin(artifact, eq(document.artifactId, artifact.id))
			.where(and(eq(document.id, documentId), eq(document.uploadedBy, userId)))
			.limit(1);

		const doc = docs[0];
		if (!doc || !doc.storageKey) {
			return { success: false, message: "Document not found" };
		}

		const signedUrl = await getSignedUrlToDownload(
			{
				key: doc.storageKey,
				bucket: "iqidis-artifact",
				mimeType: doc.document.mime || "application/pdf",
				otherOptions: {
					ResponseContentDisposition: "inline",
				},
			},
			{ expiresIn: 60 },
		);

		return {
			success: true,
			document: {
				...doc.document,
				downloadUrl: signedUrl,
			},
		};
	} catch (error) {
		Logger.error("Failed to get artifact by document id", error);
		throw error;
	}
}

/**
 * Search user Email or user name
 */
export async function searchUser(query: string) {
	try {
		const users = await db
			.select()
			.from(user)
			.where(
				or(
					ilike(user.email, `%${query}%`),
					ilike(user.firstname, `%${query}%`),
					ilike(user.lastname, `%${query}%`),
				),
			)
			.limit(20);

		return users.map((user) => ({
			id: user.id,
			email: user.email,
			firstname: user.firstname,
			lastname: user.lastname,
		}));
	} catch (error) {
		Logger.error("Failed to search user", error);
		throw error;
	}
}

export async function getReferralHistory(referrerId: string) {
	try {
		const referredUser = aliasedTable(user, "referredUser");

		const results = await db
			.select({
				firstname: referredUser.firstname,
				lastname: referredUser.lastname,
				email: referredUser.email,
				referralDate: referral.createdAt,
				isRewarded: referral.isRewarded,
				credit: referralCredit.amount,
			})
			.from(referral)
			.innerJoin(referredUser, eq(referral.referredUserId, referredUser.id))
			.leftJoin(
				referralCredit,
				eq(referralCredit.referralId, referredUser.referralCode),
			)
			.where(eq(referral.referrerId, referrerId));

		let totalRewardCents = 0;
		let convertedReferrals = 0;
		let pendingReferrals = 0;

		const referrals = results.map((entry) => {
			const status = entry?.isRewarded as "pending" | "converted" | "expired";

			if (status === "converted") {
				convertedReferrals++;
				if (entry.credit) totalRewardCents += entry.credit;
			}

			if (status === "pending") {
				pendingReferrals++;
			}

			return {
				name: `${entry.firstname ?? ""} ${entry.lastname ?? ""}`.trim(),
				email: entry.email,
				date: entry.referralDate ?? "",
				status: entry.isRewarded,
				reward:
					entry.credit != null
						? `$${(entry.credit / 100).toFixed(2)} credit`
						: "Pending",
			};
		});
		return {
			referrals,
			totalReferrals: referrals?.length,
			convertedReferrals,
			pendingReferrals,
			totalRewardEarned: `${(totalRewardCents / 100).toFixed(2)}`,
		};
	} catch (error) {
		console.error("Error fetching referral history:", error);
		throw error;
	}
}

export async function applyPendingReferralCredits(
	userId: string,
	stripeCustomerId: string,
): Promise<void> {
	try {
		// Get all unapplied credits for this user
		const pendingCredits = await db
			.select()
			.from(referralCredit)
			.where(
				and(
					eq(referralCredit.referrerId, userId),
					eq(referralCredit.appliedToStripe, false),
				),
			);

		if (pendingCredits.length === 0) {
			return;
		}

		Logger.info(
			`Applying ${pendingCredits.length} pending referral credits for user ${userId}`,
		);

		// Apply each credit to Stripe
		for (const credit of pendingCredits) {
			try {
				await stripe.customers.createBalanceTransaction(stripeCustomerId, {
					amount: -credit.amount, // Negative amount creates a credit
					currency: "usd",
					description: "Referral credit",
				});

				// Mark as applied
				await db
					.update(referralCredit)
					.set({
						appliedToStripe: true,
					})
					.where(eq(referralCredit.id, credit.id));

				Logger.info(
					`Applied referral credit ${credit.id} of $${
						credit.amount / 100
					} to customer ${stripeCustomerId}`,
				);
			} catch (error) {
				Logger.error(`Failed to apply referral credit ${credit.id}:`, error);
				// Continue with other credits even if one fails
			}
		}
	} catch (error) {
		Logger.error(
			`Error applying pending referral credits for user ${userId}:`,
			error,
		);
		throw error;
	}
}

export async function userhasChat(userId: string) {
	try {
		const hasChats = await db
			.select({ id: chat.id })
			.from(chat)
			.where(eq(chat.userId, userId))
			.limit(1);

		const userHasChat = hasChats.length > 0;
		return userHasChat;
	} catch (error) {
		console.error("Error fetching chat:", error);
	}
}
