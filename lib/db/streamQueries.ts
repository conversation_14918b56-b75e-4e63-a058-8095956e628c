import { db } from "./db";
import { stream, StreamStatus } from "./schema";
import { Logger } from "@/lib/utils/Logger";
import { eq, desc } from "drizzle-orm";

/**
 * Initialize a new stream record with PENDING status
 */
export function createStreamRecordInDb({
  chatId,
  messageId,
  userId,
  metadata = {},
}: {
  chatId?: string;
  messageId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}) {
  // Fire and forget - no async/await here
  (async () => {
    try {
      const [newStream] = await db
        .insert(stream)
        .values({
          chatId,
          messageId,
          userId,
          status: StreamStatus.PENDING,
          metadata,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      Logger.info("Initialized new stream", {
        streamId: newStream.id,
        chatId,
        messageId,
        userId,
      });
    } catch (error) {
      Logger.warn("Failed to initialize stream in database (non-blocking)", {
        error,
        chatId,
        messageId,
        userId,
      });
      // Error is caught here and won't propagate
    }
  })();
  
  // Return immediately without waiting
  return;
}

/**
 * Update the most recent stream for a chat to given status
 */
export async function updateLatestStreamForChat(chatId: string, status: StreamStatus) {
  try {
    // Find the most recent stream for this chat
    const [latestStream] = await db
      .select()
      .from(stream)
      .where(eq(stream.chatId, chatId))
      .orderBy(desc(stream.createdAt))
      .limit(1);

    if (!latestStream) {
      Logger.warn("No stream found to complete for chat", { chatId });
      return null;
    }

    // Update the stream status
    const [updatedStream] = await db
      .update(stream)
      .set({
        status,
        updatedAt: new Date(),
      })
      .where(eq(stream.id, latestStream.id))
      .returning();

    Logger.info("Updated latest stream for chat", {
      chatId,
      streamId: updatedStream.id,
    });

    return updatedStream;
  } catch (error) {
    Logger.error("Failed to complete latest stream for chat", {
      error,
      chatId,
    });
    // Don't throw error to maintain fire-and-forget pattern
    return null;
  }
}

/**
 * Update stream status by ID
 */
export async function updateStreamStatus({
  streamId,
  messageId,
  status,
  metadata,
}: {
  streamId?: string;
  messageId?: string;
  status: StreamStatus;
  metadata?: Record<string, any>;
}) {
  try {
    const updateValues: any = {
      status,
      updatedAt: new Date(),
    };

    if (metadata) {
      updateValues.metadata = metadata;
    }

    // If streamId is provided, update by streamId
    if (streamId) {
      const [updatedStream] = await db
        .update(stream)
        .set(updateValues)
        .where(eq(stream.id, streamId))
        .returning();
      
      Logger.info("Updated stream status by streamId", {
        streamId,
        status,
      });
      
      return updatedStream;
    }
    
    // If messageId is provided, update by messageId
    if (messageId) {
      const [updatedStream] = await db
        .update(stream)
        .set(updateValues)
        .where(eq(stream.messageId, messageId))
        .returning();
      
      Logger.info("Updated stream status by messageId", {
        messageId,
        status,
      });
      
      return updatedStream;
    }
    
    throw new Error("Either streamId or messageId must be provided");
  } catch (error) {
    Logger.error("Failed to update stream status in database", error);
    throw error;
  }
}

/**
 * Get stream by ID
 */
export async function getStreamById(streamId: string) {
  try {
    const [foundStream] = await db
      .select()
      .from(stream)
      .where(eq(stream.id, streamId));

    return foundStream;
  } catch (error) {
    Logger.error("Failed to get stream from database", error);
    throw error;
  }
}


