CREATE TABLE IF NOT EXISTS "document_audit_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"entity_id" uuid NOT NULL,
	"entity_type" varchar(32) NOT NULL,
	"action" varchar(32) NOT NULL,
	"operator_id" uuid NOT NULL,
	"target_id" uuid NOT NULL,
	"target_type" varchar(32) NOT NULL,
	"result" varchar(32) DEFAULT 'SUCCESS' NOT NULL,
	"error_message" text,
	"additional_info" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "document_share" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"status" varchar(10) NOT NULL,
	"shared_by_id" uuid NOT NULL,
	"document_id" uuid NOT NULL,
	"expires_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "document_share_user" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"status" varchar(10) NOT NULL,
	"document_share_id" uuid NOT NULL,
	"shared_to_id" uuid NOT NULL,
	"shared_to_type" varchar(10) NOT NULL,
	"shared_by_id" uuid NOT NULL,
	"document_id" uuid NOT NULL,
	"permission" varchar(10) DEFAULT 'READ' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "feedback" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"user_email" text NOT NULL,
	"feature_type" varchar(50) NOT NULL,
	"rating" integer NOT NULL,
	"feedback_text" text,
	"metadata" json,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "folder_share" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"status" varchar(10) NOT NULL,
	"shared_by_id" uuid NOT NULL,
	"folder_id" uuid NOT NULL,
	"expires_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "folder_share_user" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"status" varchar(10) NOT NULL,
	"folder_share_id" uuid NOT NULL,
	"shared_to_id" uuid NOT NULL,
	"shared_to_type" varchar(10) NOT NULL,
	"shared_by_id" uuid NOT NULL,
	"folder_id" uuid NOT NULL,
	"permission" varchar(10) DEFAULT 'READ' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "extended_trial" boolean DEFAULT false;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "document_audit_log" ADD CONSTRAINT "document_audit_log_operator_id_User_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "document_share" ADD CONSTRAINT "document_share_shared_by_id_User_id_fk" FOREIGN KEY ("shared_by_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "document_share" ADD CONSTRAINT "document_share_document_id_document_id_fk" FOREIGN KEY ("document_id") REFERENCES "public"."document"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "document_share_user" ADD CONSTRAINT "document_share_user_document_share_id_document_share_id_fk" FOREIGN KEY ("document_share_id") REFERENCES "public"."document_share"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "document_share_user" ADD CONSTRAINT "document_share_user_shared_to_id_User_id_fk" FOREIGN KEY ("shared_to_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "document_share_user" ADD CONSTRAINT "document_share_user_shared_by_id_User_id_fk" FOREIGN KEY ("shared_by_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "document_share_user" ADD CONSTRAINT "document_share_user_document_id_document_id_fk" FOREIGN KEY ("document_id") REFERENCES "public"."document"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "feedback" ADD CONSTRAINT "feedback_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "folder_share" ADD CONSTRAINT "folder_share_shared_by_id_User_id_fk" FOREIGN KEY ("shared_by_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "folder_share" ADD CONSTRAINT "folder_share_folder_id_document_folder_id_fk" FOREIGN KEY ("folder_id") REFERENCES "public"."document_folder"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "folder_share_user" ADD CONSTRAINT "folder_share_user_folder_share_id_folder_share_id_fk" FOREIGN KEY ("folder_share_id") REFERENCES "public"."folder_share"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "folder_share_user" ADD CONSTRAINT "folder_share_user_shared_to_id_User_id_fk" FOREIGN KEY ("shared_to_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "folder_share_user" ADD CONSTRAINT "folder_share_user_shared_by_id_User_id_fk" FOREIGN KEY ("shared_by_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "folder_share_user" ADD CONSTRAINT "folder_share_user_folder_id_document_folder_id_fk" FOREIGN KEY ("folder_id") REFERENCES "public"."document_folder"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "feedback_user_id_idx" ON "feedback" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "feedback_feature_type_idx" ON "feedback" USING btree ("feature_type");