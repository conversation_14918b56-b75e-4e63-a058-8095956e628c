{"id": "0fb77699-dd36-4868-a06a-a88788b9c9e5", "prevId": "ace3f883-c098-4f68-b79e-fa90ec89d0b8", "version": "7", "dialect": "postgresql", "tables": {"public.Document": {"name": "Document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'text'"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Document_userId_User_id_fk": {"name": "Document_userId_User_id_fk", "tableFrom": "Document", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Document_id_createdAt_pk": {"name": "Document_id_createdAt_pk", "columns": ["id", "createdAt"]}}, "uniqueConstraints": {}}, "public.artifact": {"name": "artifact", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "storage_key": {"name": "storage_key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "content_hash": {"name": "content_hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "size_byte": {"name": "size_byte", "type": "bigint", "primaryKey": false, "notNull": true}, "mime": {"name": "mime", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"artifact_storage_key_unique": {"name": "artifact_storage_key_unique", "nullsNotDistinct": false, "columns": ["storage_key"]}}}, "public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'public'"}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Chat_userId_User_id_fk": {"name": "Chat_userId_User_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_folders": {"name": "chat_folders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chat_folders_user_id_idx": {"name": "chat_folders_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_folders_user_id_User_id_fk": {"name": "chat_folders_user_id_User_id_fk", "tableFrom": "chat_folders", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"chat_folders_name_user_id_unique": {"name": "chat_folders_name_user_id_unique", "nullsNotDistinct": false, "columns": ["name", "user_id"]}}}, "public.chat_tags": {"name": "chat_tags", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tag_name": {"name": "tag_name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#3B82F6'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chat_tags_user_id_idx": {"name": "chat_tags_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_tags_chat_id_idx": {"name": "chat_tags_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_tags_user_id_User_id_fk": {"name": "chat_tags_user_id_User_id_fk", "tableFrom": "chat_tags", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chat_tags_chat_id_Chat_id_fk": {"name": "chat_tags_chat_id_Chat_id_fk", "tableFrom": "chat_tags", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"chat_tags_user_id_chat_id_tag_name_unique": {"name": "chat_tags_user_id_chat_id_tag_name_unique", "nullsNotDistinct": false, "columns": ["user_id", "chat_id", "tag_name"]}}}, "public.chat_to_folder": {"name": "chat_to_folder", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "folder_id": {"name": "folder_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chat_to_folder_chat_id_idx": {"name": "chat_to_folder_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_to_folder_folder_id_idx": {"name": "chat_to_folder_folder_id_idx", "columns": [{"expression": "folder_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_to_folder_chat_id_Chat_id_fk": {"name": "chat_to_folder_chat_id_Chat_id_fk", "tableFrom": "chat_to_folder", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chat_to_folder_folder_id_chat_folders_id_fk": {"name": "chat_to_folder_folder_id_chat_folders_id_fk", "tableFrom": "chat_to_folder", "tableTo": "chat_folders", "columnsFrom": ["folder_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"chat_to_folder_chat_id_folder_id_unique": {"name": "chat_to_folder_chat_id_folder_id_unique", "nullsNotDistinct": false, "columns": ["chat_id", "folder_id"]}}}, "public.document": {"name": "document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "is_derived": {"name": "is_derived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "artifact_source": {"name": "artifact_source", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "artifact_status": {"name": "artifact_status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "folder_id": {"name": "folder_id", "type": "uuid", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "artifact_id": {"name": "artifact_id", "type": "uuid", "primaryKey": false, "notNull": false}, "original_name": {"name": "original_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "mime": {"name": "mime", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "size_byte": {"name": "size_byte", "type": "bigint", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"document_uploaded_by_User_id_fk": {"name": "document_uploaded_by_User_id_fk", "tableFrom": "document", "tableTo": "User", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_folder_id_document_folder_id_fk": {"name": "document_folder_id_document_folder_id_fk", "tableFrom": "document", "tableTo": "document_folder", "columnsFrom": ["folder_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_artifact_id_artifact_id_fk": {"name": "document_artifact_id_artifact_id_fk", "tableFrom": "document", "tableTo": "artifact", "columnsFrom": ["artifact_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.document_audit_log": {"name": "document_audit_log", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "operator_id": {"name": "operator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "target_id": {"name": "target_id", "type": "uuid", "primaryKey": false, "notNull": true}, "target_type": {"name": "target_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "default": "'SUCCESS'"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "additional_info": {"name": "additional_info", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"document_audit_log_operator_id_User_id_fk": {"name": "document_audit_log_operator_id_User_id_fk", "tableFrom": "document_audit_log", "tableTo": "User", "columnsFrom": ["operator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.document_folder": {"name": "document_folder", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'NORMAL'"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false, "default": "'#808080'"}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "parent_path": {"name": "parent_path", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "'/'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"document_folder_user_id_User_id_fk": {"name": "document_folder_user_id_User_id_fk", "tableFrom": "document_folder", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.document_share": {"name": "document_share", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "shared_by_id": {"name": "shared_by_id", "type": "uuid", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"document_share_shared_by_id_User_id_fk": {"name": "document_share_shared_by_id_User_id_fk", "tableFrom": "document_share", "tableTo": "User", "columnsFrom": ["shared_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_share_document_id_document_id_fk": {"name": "document_share_document_id_document_id_fk", "tableFrom": "document_share", "tableTo": "document", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.document_share_user": {"name": "document_share_user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "document_share_id": {"name": "document_share_id", "type": "uuid", "primaryKey": false, "notNull": true}, "shared_to_id": {"name": "shared_to_id", "type": "uuid", "primaryKey": false, "notNull": true}, "shared_to_type": {"name": "shared_to_type", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "shared_by_id": {"name": "shared_by_id", "type": "uuid", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "uuid", "primaryKey": false, "notNull": true}, "permission": {"name": "permission", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'READ'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"document_share_user_document_share_id_document_share_id_fk": {"name": "document_share_user_document_share_id_document_share_id_fk", "tableFrom": "document_share_user", "tableTo": "document_share", "columnsFrom": ["document_share_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_share_user_shared_to_id_User_id_fk": {"name": "document_share_user_shared_to_id_User_id_fk", "tableFrom": "document_share_user", "tableTo": "User", "columnsFrom": ["shared_to_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_share_user_shared_by_id_User_id_fk": {"name": "document_share_user_shared_by_id_User_id_fk", "tableFrom": "document_share_user", "tableTo": "User", "columnsFrom": ["shared_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_share_user_document_id_document_id_fk": {"name": "document_share_user_document_id_document_id_fk", "tableFrom": "document_share_user", "tableTo": "document", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.embeddings": {"name": "embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "resource_id": {"name": "resource_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1024)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"embeddings_resource_id_resources_id_fk": {"name": "embeddings_resource_id_resources_id_fk", "tableFrom": "embeddings", "tableTo": "resources", "columnsFrom": ["resource_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"embeddings_id_pk": {"name": "embeddings_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.error_events": {"name": "error_events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "error_details": {"name": "error_details", "type": "jsonb", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "severity": {"name": "severity", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'error'"}, "resolved": {"name": "resolved", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"error_events_user_id_User_id_fk": {"name": "error_events_user_id_User_id_fk", "tableFrom": "error_events", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "error_events_chat_id_Chat_id_fk": {"name": "error_events_chat_id_Chat_id_fk", "tableFrom": "error_events", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.feedback": {"name": "feedback", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_email": {"name": "user_email", "type": "text", "primaryKey": false, "notNull": true}, "feature_type": {"name": "feature_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "feedback_text": {"name": "feedback_text", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"feedback_user_id_idx": {"name": "feedback_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "feedback_feature_type_idx": {"name": "feedback_feature_type_idx", "columns": [{"expression": "feature_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"feedback_user_id_User_id_fk": {"name": "feedback_user_id_User_id_fk", "tableFrom": "feedback", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.folder_share": {"name": "folder_share", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "shared_by_id": {"name": "shared_by_id", "type": "uuid", "primaryKey": false, "notNull": true}, "folder_id": {"name": "folder_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"folder_share_shared_by_id_User_id_fk": {"name": "folder_share_shared_by_id_User_id_fk", "tableFrom": "folder_share", "tableTo": "User", "columnsFrom": ["shared_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "folder_share_folder_id_document_folder_id_fk": {"name": "folder_share_folder_id_document_folder_id_fk", "tableFrom": "folder_share", "tableTo": "document_folder", "columnsFrom": ["folder_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.folder_share_user": {"name": "folder_share_user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "folder_share_id": {"name": "folder_share_id", "type": "uuid", "primaryKey": false, "notNull": true}, "shared_to_id": {"name": "shared_to_id", "type": "uuid", "primaryKey": false, "notNull": true}, "shared_to_type": {"name": "shared_to_type", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "shared_by_id": {"name": "shared_by_id", "type": "uuid", "primaryKey": false, "notNull": true}, "folder_id": {"name": "folder_id", "type": "uuid", "primaryKey": false, "notNull": true}, "permission": {"name": "permission", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'READ'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"folder_share_user_folder_share_id_folder_share_id_fk": {"name": "folder_share_user_folder_share_id_folder_share_id_fk", "tableFrom": "folder_share_user", "tableTo": "folder_share", "columnsFrom": ["folder_share_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "folder_share_user_shared_to_id_User_id_fk": {"name": "folder_share_user_shared_to_id_User_id_fk", "tableFrom": "folder_share_user", "tableTo": "User", "columnsFrom": ["shared_to_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "folder_share_user_shared_by_id_User_id_fk": {"name": "folder_share_user_shared_by_id_User_id_fk", "tableFrom": "folder_share_user", "tableTo": "User", "columnsFrom": ["shared_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "folder_share_user_folder_id_document_folder_id_fk": {"name": "folder_share_user_folder_id_document_folder_id_fk", "tableFrom": "folder_share_user", "tableTo": "document_folder", "columnsFrom": ["folder_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Message_chatId_Chat_id_fk": {"name": "Message_chatId_Chat_id_fk", "tableFrom": "Message", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.message_documents": {"name": "message_documents", "schema": "", "columns": {"message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": true}, "source_document_id": {"name": "source_document_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"message_documents_message_id_Message_id_fk": {"name": "message_documents_message_id_Message_id_fk", "tableFrom": "message_documents", "tableTo": "Message", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "message_documents_source_document_id_source_documents_id_fk": {"name": "message_documents_source_document_id_source_documents_id_fk", "tableFrom": "message_documents", "tableTo": "source_documents", "columnsFrom": ["source_document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"message_documents_message_id_source_document_id_pk": {"name": "message_documents_message_id_source_document_id_pk", "columns": ["message_id", "source_document_id"]}}, "uniqueConstraints": {}}, "public.pending_document_associations": {"name": "pending_document_associations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true}, "document_ids": {"name": "document_ids", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_retry_at": {"name": "last_retry_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.pinned_chats": {"name": "pinned_chats", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"pinned_chats_user_id_idx": {"name": "pinned_chats_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pinned_chats_chat_id_idx": {"name": "pinned_chats_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pinned_chats_user_id_User_id_fk": {"name": "pinned_chats_user_id_User_id_fk", "tableFrom": "pinned_chats", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pinned_chats_chat_id_Chat_id_fk": {"name": "pinned_chats_chat_id_Chat_id_fk", "tableFrom": "pinned_chats", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pinned_chats_user_id_chat_id_unique": {"name": "pinned_chats_user_id_chat_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "chat_id"]}}}, "public.Plan": {"name": "Plan", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "stripe_price_id": {"name": "stripe_price_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "interval": {"name": "interval", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.prompt_files": {"name": "prompt_files", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "prompt_id": {"name": "prompt_id", "type": "uuid", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "uploaded_at": {"name": "uploaded_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"prompt_files_prompt_id_idx": {"name": "prompt_files_prompt_id_idx", "columns": [{"expression": "prompt_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"prompt_files_prompt_id_prompts_id_fk": {"name": "prompt_files_prompt_id_prompts_id_fk", "tableFrom": "prompt_files", "tableTo": "prompts", "columnsFrom": ["prompt_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.prompt_folders": {"name": "prompt_folders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"prompt_folders_user_id_idx": {"name": "prompt_folders_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"prompt_folders_user_id_User_id_fk": {"name": "prompt_folders_user_id_User_id_fk", "tableFrom": "prompt_folders", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"prompt_folders_name_user_id_unique": {"name": "prompt_folders_name_user_id_unique", "nullsNotDistinct": false, "columns": ["name", "user_id"]}}}, "public.prompts": {"name": "prompts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "folder_id": {"name": "folder_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_favorite": {"name": "is_favorite", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"prompts_folder_id_idx": {"name": "prompts_folder_id_idx", "columns": [{"expression": "folder_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"prompts_folder_id_prompt_folders_id_fk": {"name": "prompts_folder_id_prompt_folders_id_fk", "tableFrom": "prompts", "tableTo": "prompt_folders", "columnsFrom": ["folder_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Referral": {"name": "Referral", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "referrer_id": {"name": "referrer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "referred_user_id": {"name": "referred_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_rewarded": {"name": "is_rewarded", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"Referral_id_pk": {"name": "Referral_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.ReferralCredit": {"name": "ReferralCredit", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "referral_id": {"name": "referral_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "referrer_id": {"name": "referrer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "applied_to_stripe": {"name": "applied_to_stripe", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"ReferralCredit_id_pk": {"name": "ReferralCredit_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"ReferralCredit_referral_id_unique": {"name": "ReferralCredit_referral_id_unique", "nullsNotDistinct": false, "columns": ["referral_id"]}}}, "public.resources": {"name": "resources", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "source_document_id": {"name": "source_document_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"resources_source_document_id_source_documents_id_fk": {"name": "resources_source_document_id_source_documents_id_fk", "tableFrom": "resources", "tableTo": "source_documents", "columnsFrom": ["source_document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"resources_id_pk": {"name": "resources_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.source_documents": {"name": "source_documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "extractedText": {"name": "extractedText", "type": "text", "primaryKey": false, "notNull": false}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"source_documents_chat_id_Chat_id_fk": {"name": "source_documents_chat_id_Chat_id_fk", "tableFrom": "source_documents", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"source_documents_id_pk": {"name": "source_documents_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.Subscription": {"name": "Subscription", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'active'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "billing_cycle": {"name": "billing_cycle", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "payment_id": {"name": "payment_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "auto_renew": {"name": "auto_renew", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "stripe_price_id": {"name": "stripe_price_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "stripe_subscription_item_id": {"name": "stripe_subscription_item_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "stripe_subscription_status": {"name": "stripe_subscription_status", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "is_trial_used": {"name": "is_trial_used", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_admin_managed": {"name": "is_admin_managed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "extended_trial": {"name": "extended_trial", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"Subscription_user_id_User_id_fk": {"name": "Subscription_user_id_User_id_fk", "tableFrom": "Subscription", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "Subscription_plan_id_Plan_id_fk": {"name": "Subscription_plan_id_Plan_id_fk", "tableFrom": "Subscription", "tableTo": "Plan", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Suggestion": {"name": "Suggestion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "documentId": {"name": "documentId", "type": "uuid", "primaryKey": false, "notNull": true}, "documentCreatedAt": {"name": "documentCreatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "originalText": {"name": "originalText", "type": "text", "primaryKey": false, "notNull": true}, "suggestedText": {"name": "suggestedText", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isResolved": {"name": "isResolved", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Suggestion_userId_User_id_fk": {"name": "Suggestion_userId_User_id_fk", "tableFrom": "Suggestion", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk": {"name": "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk", "tableFrom": "Suggestion", "tableTo": "Document", "columnsFrom": ["documentId", "documentCreatedAt"], "columnsTo": ["id", "createdAt"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Suggestion_id_pk": {"name": "Suggestion_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "isAdmin": {"name": "isAdmin", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "firstname": {"name": "firstname", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "lastname": {"name": "lastname", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "teamsize": {"name": "teamsize", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "isEmailVerified": {"name": "isEmailVerified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "invoice_status": {"name": "invoice_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "subscriptionTier": {"name": "subscriptionTier", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'free'"}, "next_subscription_tier": {"name": "next_subscription_tier", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "avatarUrl": {"name": "avatarUrl", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "referral_code": {"name": "referral_code", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "referrer_id": {"name": "referrer_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "total_referral_credits": {"name": "total_referral_credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "successful_referrals": {"name": "successful_referrals", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"User_id_pk": {"name": "User_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"User_referral_code_unique": {"name": "User_referral_code_unique", "nullsNotDistinct": false, "columns": ["referral_code"]}}}, "public.user_favorites": {"name": "user_favorites", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "prompt_id": {"name": "prompt_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_favorites_user_id_idx": {"name": "user_favorites_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_favorites_prompt_id_idx": {"name": "user_favorites_prompt_id_idx", "columns": [{"expression": "prompt_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_favorites_user_id_User_id_fk": {"name": "user_favorites_user_id_User_id_fk", "tableFrom": "user_favorites", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_favorites_prompt_id_prompts_id_fk": {"name": "user_favorites_prompt_id_prompts_id_fk", "tableFrom": "user_favorites", "tableTo": "prompts", "columnsFrom": ["prompt_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_favorites_user_id_prompt_id_unique": {"name": "user_favorites_user_id_prompt_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "prompt_id"]}}}, "public.UserPreferences": {"name": "UserPreferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "fullName": {"name": "fullName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "roleTitle": {"name": "role<PERSON>itle", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "firmName": {"name": "firmName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "barId": {"name": "barId", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "officePhone": {"name": "officePhone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "directPhone": {"name": "directPhone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "firmAddress": {"name": "firmAddress", "type": "text", "primaryKey": false, "notNull": false}, "practiceAreas": {"name": "practiceAreas", "type": "text", "primaryKey": false, "notNull": false}, "jurisdictions": {"name": "jurisdictions", "type": "text", "primaryKey": false, "notNull": false}, "formalityScale": {"name": "formalityScale", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "riskToleranceScale": {"name": "riskToleranceScale", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "detailLevel": {"name": "detailLevel", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "preferredReferences": {"name": "preferredReferences", "type": "text", "primaryKey": false, "notNull": false}, "documentFormatting": {"name": "documentFormatting", "type": "text", "primaryKey": false, "notNull": false}, "miscInformation": {"name": "miscInformation", "type": "text", "primaryKey": false, "notNull": false}, "aiGeneratedProfile": {"name": "aiGeneratedProfile", "type": "text", "primaryKey": false, "notNull": false}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_preferences_idx": {"name": "user_preferences_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"UserPreferences_userId_User_id_fk": {"name": "UserPreferences_userId_User_id_fk", "tableFrom": "UserPreferences", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"UserPreferences_id_pk": {"name": "UserPreferences_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"UserPreferences_userId_unique": {"name": "UserPreferences_userId_unique", "nullsNotDistinct": false, "columns": ["userId"]}}}, "public.Vote": {"name": "Vote", "schema": "", "columns": {"chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "isUpvoted": {"name": "isUpvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Vote_chatId_Chat_id_fk": {"name": "Vote_chatId_Chat_id_fk", "tableFrom": "Vote", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "Vote_messageId_Message_id_fk": {"name": "Vote_messageId_Message_id_fk", "tableFrom": "Vote", "tableTo": "Message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Vote_chatId_messageId_pk": {"name": "Vote_chatId_messageId_pk", "columns": ["chatId", "messageId"]}}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}