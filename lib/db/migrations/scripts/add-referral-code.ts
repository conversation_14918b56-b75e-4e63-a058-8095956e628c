import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addReferralCodeColumn() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    console.log("Adding referral-related columns to User table...");

    // Check and add referral_code column
    const hasReferralCode = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'User' AND column_name = 'referral_code'
    `);

    if (hasReferralCode.length === 0) {
      console.log("Adding referral_code column...");
      await db.execute(sql`
        ALTER TABLE "User" 
        ADD COLUMN IF NOT EXISTS "referral_code" varchar(36) UNIQUE
      `);
    }

    // Check and add referrer_id column
    const hasReferrerId = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'User' AND column_name = 'referrer_id'
    `);

    if (hasReferrerId.length === 0) {
      console.log("Adding referrer_id column...");
      await db.execute(sql`
        ALTER TABLE "User" 
        ADD COLUMN IF NOT EXISTS "referrer_id" varchar(36)
      `);
    }

    // Check and add total_referral_credits column
    const hasTotalReferralCredits = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'User' AND column_name = 'total_referral_credits'
    `);

    if (hasTotalReferralCredits.length === 0) {
      console.log("Adding total_referral_credits column...");
      await db.execute(sql`
        ALTER TABLE "User" 
        ADD COLUMN IF NOT EXISTS "total_referral_credits" integer NOT NULL DEFAULT 0
      `);
    }

    // Check and add successful_referrals column
    const hasSuccessfulReferrals = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'User' AND column_name = 'successful_referrals'
    `);

    if (hasSuccessfulReferrals.length === 0) {
      console.log("Adding successful_referrals column...");
      await db.execute(sql`
        ALTER TABLE "User" 
        ADD COLUMN IF NOT EXISTS "successful_referrals" integer NOT NULL DEFAULT 0
      `);
    }

    console.log("All referral-related columns added successfully");
    return true;
  } catch (error) {
    console.error("Error adding referral-related columns:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}
