import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addFeedbackTable() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    console.log("Creating feedback table...");

    // Create feedback table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "feedback" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "user_id" uuid NOT NULL,
        "user_email" text NOT NULL,
        "feature_type" varchar(50) NOT NULL,
        "rating" integer NOT NULL,
        "feedback_text" text,
        "metadata" json,
        "created_at" timestamp DEFAULT now() NOT NULL
      )
    `);

    // Add foreign key constraints
    console.log("Adding foreign key constraints...");
    await db.execute(sql`
      DO $$ BEGIN
        ALTER TABLE "feedback" ADD CONSTRAINT "feedback_user_id_User_id_fk" 
        FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create indexes
    console.log("Creating indexes...");
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "feedback_user_id_idx" ON "feedback" USING btree ("user_id")
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "feedback_feature_type_idx" ON "feedback" USING btree ("feature_type")
    `);

    console.log("Feedback table created successfully");
    return true;
  } catch (error) {
    console.error("Error creating feedback table:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}