import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";
import { Logger } from "../../../utils/Logger";

dotenv.config();

export async function addExtendedTrialColumn() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    Logger.info("Checking if extended_trial column exists in Subscription table...");

    // Check if the column already exists to avoid errors
    const checkColumnExists = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'Subscription' AND column_name = 'extended_trial'
    `);

    if (checkColumnExists.length === 0) {
      Logger.info("Adding extended_trial column to Subscription table...");

      // Add the column if it doesn't exist
      await db.execute(sql`
        ALTER TABLE "Subscription" 
        ADD COLUMN IF NOT EXISTS "extended_trial" boolean DEFAULT false
      `);

      Logger.info("extended_trial column added successfully");
    } else {
      Logger.info("extended_trial column already exists, skipping");
    }

    return true;
  } catch (error) {
    Logger.error("Error adding extended_trial column:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}