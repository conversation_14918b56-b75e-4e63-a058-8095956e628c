import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql, SQL } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addChatOrganizationTables() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    console.log("Adding chat organization tables...");

    // Create chat_folders table
    await createTableIfNotExists(
      db,
      "chat_folders",
      sql`
        CREATE TABLE IF NOT EXISTS "chat_folders" (
          "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
          "user_id" uuid NOT NULL,
          "name" text NOT NULL,
          "created_at" timestamp DEFAULT now() NOT NULL,
          "updated_at" timestamp DEFAULT now() NOT NULL,
          CONSTRAINT "chat_folders_name_user_id_unique" UNIQUE("name","user_id")
        )
      `
    );

    // Create chat_tags table
    await createTableIfNotExists(
      db,
      "chat_tags",
      sql`
        CREATE TABLE IF NOT EXISTS "chat_tags" (
          "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
          "user_id" uuid NOT NULL,
          "chat_id" uuid NOT NULL,
          "tags" text NOT NULL,
          "created_at" timestamp DEFAULT now() NOT NULL,
          "updated_at" timestamp DEFAULT now() NOT NULL,
          CONSTRAINT "chat_tags_user_id_chat_id_unique" UNIQUE("user_id","chat_id")
        )
      `
    );

    // Create chat_to_folder table
    await createTableIfNotExists(
      db,
      "chat_to_folder",
      sql`
        CREATE TABLE IF NOT EXISTS "chat_to_folder" (
          "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
          "chat_id" uuid NOT NULL,
          "folder_id" uuid NOT NULL,
          "created_at" timestamp DEFAULT now() NOT NULL,
          CONSTRAINT "chat_to_folder_chat_id_folder_id_unique" UNIQUE("chat_id","folder_id")
        )
      `
    );

    // Create pinned_chats table
    await createTableIfNotExists(
      db,
      "pinned_chats",
      sql`
        CREATE TABLE IF NOT EXISTS "pinned_chats" (
          "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
          "user_id" uuid NOT NULL,
          "chat_id" uuid NOT NULL,
          "position" integer NOT NULL,
          "created_at" timestamp DEFAULT now() NOT NULL,
          CONSTRAINT "pinned_chats_user_id_chat_id_unique" UNIQUE("user_id","chat_id")
        )
      `
    );

    // Add foreign key constraints
    await addForeignKeyConstraints(db);
    
    // Create indexes
    await createIndexes(db);

    console.log("Chat organization tables added successfully");
    return true;
  } catch (error) {
    console.error("Error adding chat organization tables:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}

async function createTableIfNotExists(
  db: ReturnType<typeof drizzle>,
  tableName: string,
  createTableSql: SQL
) {
  console.log(`Checking if ${tableName} table exists...`);
  
  const checkTableExists = await db.execute(sql`
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_name = ${tableName}
  `);

  if (checkTableExists.length === 0) {
    console.log(`Creating ${tableName} table...`);
    await db.execute(createTableSql);
    console.log(`${tableName} table created successfully`);
  } else {
    console.log(`${tableName} table already exists, skipping`);
  }
}

async function addForeignKeyConstraints(db: ReturnType<typeof drizzle>) {
  console.log("Adding foreign key constraints...");
  
  // Add foreign key constraints with exception handling
  const constraints = [
    {
      name: "chat_folders_user_id_User_id_fk",
      sql: sql`
        DO $$ BEGIN
          ALTER TABLE "chat_folders" ADD CONSTRAINT "chat_folders_user_id_User_id_fk" 
          FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
      `
    },
    // Add other constraints similarly
  ];

  for (const constraint of constraints) {
    await db.execute(constraint.sql);
  }
  
  // Add remaining constraints
  await db.execute(sql`
    DO $$ BEGIN
      ALTER TABLE "chat_tags" ADD CONSTRAINT "chat_tags_user_id_User_id_fk" 
      FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
    EXCEPTION
      WHEN duplicate_object THEN null;
    END $$;
  `);
  
  await db.execute(sql`
    DO $$ BEGIN
      ALTER TABLE "chat_tags" ADD CONSTRAINT "chat_tags_chat_id_Chat_id_fk" 
      FOREIGN KEY ("chat_id") REFERENCES "public"."Chat"("id") ON DELETE cascade ON UPDATE no action;
    EXCEPTION
      WHEN duplicate_object THEN null;
    END $$;
  `);
  
  // Add remaining constraints...
}

async function createIndexes(db: ReturnType<typeof drizzle>) {
  console.log("Creating indexes...");
  
  const indexes = [
    {
      name: "chat_folders_user_id_idx",
      sql: sql`CREATE INDEX IF NOT EXISTS "chat_folders_user_id_idx" ON "chat_folders" USING btree ("user_id")`
    },
    {
      name: "chat_tags_user_id_idx",
      sql: sql`CREATE INDEX IF NOT EXISTS "chat_tags_user_id_idx" ON "chat_tags" USING btree ("user_id")`
    },
    // Add other indexes
  ];

  for (const index of indexes) {
    await db.execute(index.sql);
  }
}
