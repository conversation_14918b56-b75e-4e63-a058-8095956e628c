import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addHeardFromColumn() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    console.log("Checking if heard_from column exists...");

    // Check if the column already exists to avoid errors
    const checkColumnExists = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'User' AND column_name = 'heard_from'
    `);

    if (checkColumnExists.length === 0) {
      console.log("Adding heard_from column to User table...");

      // Add the column if it doesn't exist
      await db.execute(sql`
        ALTER TABLE "User" 
        ADD COLUMN IF NOT EXISTS "heard_from" varchar(255)
      `);

      console.log("heard_from column added successfully");
    } else {
      console.log("heard_from column already exists, skipping");
    }

    return true;
  } catch (error) {
    console.error("Error adding heard_from column:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}
