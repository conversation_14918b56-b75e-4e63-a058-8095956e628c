import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addStreamTable() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Create stream table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS stream (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        chat_id UUID REFERENCES "Chat"(id) ON DELETE SET NULL,
        message_id UUID REFERENCES "Message"(id) ON DELETE SET NULL,
        user_id UUID REFERENCES "User"(id) ON DELETE SET NULL,
        status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
        metadata JSON,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);

    // Add indexes for better query performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS stream_chat_id_idx ON stream(chat_id);
      CREATE INDEX IF NOT EXISTS stream_message_id_idx ON stream(message_id);
      CREATE INDEX IF NOT EXISTS stream_user_id_idx ON stream(user_id);
      CREATE INDEX IF NOT EXISTS stream_status_idx ON stream(status);
    `);

    console.log("✅ Successfully created stream table");
    return true;
  } catch (error) {
    console.error("❌ Error creating stream table:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}

export async function removeStreamTable() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Drop table
    await db.execute(sql`
      DROP TABLE IF EXISTS stream;
    `);

    console.log("✅ Successfully dropped stream table");
    return true;
  } catch (error) {
    console.error("❌ Error dropping stream table:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}