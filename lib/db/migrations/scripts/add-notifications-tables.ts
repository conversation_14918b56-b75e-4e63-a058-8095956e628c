import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addNotificationTables() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Create notifications table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS notifications (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        link TEXT,
        description TEXT NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);

    // Create notification_targets table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS notification_targets (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES "User"(id),
        is_read BOOLEAN NOT NULL DEFAULT false,
        read_at TIMESTAMP WITH TIME ZONE
      );
    `);

    // Add indexes for better query performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_notification_targets_user_id ON notification_targets(user_id);
      CREATE INDEX IF NOT EXISTS idx_notification_targets_notification_id ON notification_targets(notification_id);
      CREATE INDEX IF NOT EXISTS idx_notification_targets_is_read ON notification_targets(is_read);
    `);

    console.log("✅ Successfully created notifications tables");
    return true;
  } catch (error) {
    console.error("❌ Error creating notifications tables:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}

export async function removeNotificationTables() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Drop tables in reverse order
    await db.execute(sql`
      DROP TABLE IF EXISTS notification_targets;
      DROP TABLE IF EXISTS notifications;
    `);

    console.log("✅ Successfully dropped notifications tables");
    return true;
  } catch (error) {
    console.error("❌ Error dropping notifications tables:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
} 