import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function makeDocumentFolderIdNullable() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    console.log("Checking if document.folder_id allows NULL values...");

    // Check if the column is already nullable
    const columnInfo = await db.execute(sql`
      SELECT is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'document' AND column_name = 'folder_id'
    `);

    if (columnInfo.length > 0 && columnInfo[0].is_nullable === "YES") {
      console.log("document.folder_id already allows NULL values, skipping");
    } else {
      console.log("Making document.folder_id nullable...");

      // Alter the column to allow NULL values
      await db.execute(sql`
        ALTER TABLE "document" ALTER COLUMN "folder_id" DROP NOT NULL
      `);

      console.log(
        "document.folder_id successfully modified to allow NULL values"
      );
    }

    return true;
  } catch (error) {
    console.error("Error modifying document.folder_id:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}
