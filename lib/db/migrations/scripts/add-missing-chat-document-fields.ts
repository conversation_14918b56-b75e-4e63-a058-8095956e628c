import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addMissingChatDocumentFields() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    console.log("Adding missing fields to Chat and Document tables...");

    // Check and add missing fields to Chat table
    await addMissingChatFields(db);
    
    // Check and add missing fields to Document table
    await addMissingDocumentFields(db);

    console.log("All missing fields added successfully");
    return true;
  } catch (error) {
    console.error("Error adding missing fields:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}

async function addMissingChatFields(db: ReturnType<typeof drizzle>) {
  console.log("Checking missing fields in Chat table...");
  
  // Add visibility field if missing
  const hasVisibility = await db.execute(sql`
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_name = 'Chat' AND column_name = 'visibility'
  `);

  if (hasVisibility.length === 0) {
    console.log("Adding visibility column to Chat table...");
    await db.execute(sql`
      ALTER TABLE "Chat" 
      ADD COLUMN IF NOT EXISTS "visibility" varchar(20) DEFAULT 'public' NOT NULL
    `);
  }

  // Add modelId field if missing
  const hasModelId = await db.execute(sql`
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_name = 'Chat' AND column_name = 'modelId'
  `);

  if (hasModelId.length === 0) {
    console.log("Adding modelId column to Chat table...");
    await db.execute(sql`
      ALTER TABLE "Chat" 
      ADD COLUMN IF NOT EXISTS "modelId" text
    `);
  }
}

async function addMissingDocumentFields(db: ReturnType<typeof drizzle>) {
  console.log("Checking missing fields in Document table...");
  
  // Add kind field if missing
  const hasKind = await db.execute(sql`
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_name = 'Document' AND column_name = 'kind'
  `);

  if (hasKind.length === 0) {
    console.log("Adding kind column to Document table...");
    await db.execute(sql`
      ALTER TABLE "Document" 
      ADD COLUMN IF NOT EXISTS "kind" varchar(20) DEFAULT 'text' NOT NULL
    `);
  }
  
  // Add is_favorite field if missing
  const hasFavorite = await db.execute(sql`
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_name = 'document' AND column_name = 'is_favorite'
  `);

  if (hasFavorite.length === 0) {
    console.log("Adding is_favorite column to document table...");
    await db.execute(sql`
      ALTER TABLE "document" 
      ADD COLUMN IF NOT EXISTS "is_favorite" boolean DEFAULT false
    `);
  }
}
