import { DAILY_MESSAGE_LIMIT, TOTAL_CHAT_LIMIT, TOTAL_MEMORY_LIMIT_MB } from "@/lib/constants";
import { User } from "next-auth";

/**
 * Checks if a feature is available for the given user
 * @param feature - The feature to check (optional)
 * @param user - The user object
 * @returns boolean indicating if the feature is available
 */
export function isFeatureAvailable(feature?: string, user?: User): boolean {
  // Giving everyone access for now
  return true;

  // // If no user is provided, default to free tier
  // const subscriptionTier = user?.subscriptionTier || 'free';
  
  // // Define feature availability by subscription tier
  // const featureAvailability: Record<string, string[]> = {
  //   'free': ['research-mode',],
  //   'premium': ['enhanced-doc-reader', 'research-mode','document-export','advanced-search'],
  //   'premium-trial': ['enhanced-doc-reader', 'research-mode','document-export','advanced-search'],
  // };
  
  // // If no feature specified, check if user has any premium features
  // if (!feature) {
  //   return subscriptionTier !== 'free';
  // }
  
  // // Get available features for the subscription tier
  // const availableFeatures = featureAvailability[subscriptionTier] || featureAvailability['free'];
  // return availableFeatures.includes(feature);
}

/**
 * Checks if user can create a new chat based on their total chat count
 */
export function canCreateNewChat(user?: User): boolean {
  // Allow if user is not on free tier or if user data is not available
  if (!user || user.subscriptionTier !== 'free' || !user.totalChats) {
    return true;
  }
  
  return user.totalChats < TOTAL_CHAT_LIMIT;
}

/**
 * Checks if user can send a message based on their daily message count
 */
export function canSendMessage(user?: User): boolean {
  // Allow if user is not on free tier or if user data is not available
  if (!user || user.subscriptionTier !== 'free' || !user.dailyMessageCount === undefined) {
    return true;
  }
  
  return (user.dailyMessageCount ?? 0) < DAILY_MESSAGE_LIMIT;
}

/**
 * Formats time until reset in a readable format
 */
function formatTimeUntilReset(minutesUntilReset?: number): string {
  if (!minutesUntilReset) return '';
  
  const hours = Math.floor(minutesUntilReset / 60);
  const minutes = minutesUntilReset % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes} minutes`;
}

/**
 * Checks for usage limits and returns appropriate UI messages
 */
export function checkUsageLimits(user?: User, isNewChat?: boolean): {
  isLimited: boolean;
  limitMessage: string;
  minutesUntilReset?: number;
} {
  // First check if user is admin, and if so, bypass all limits
  if (!user || user.isAdmin || user.subscriptionTier !== 'free') {
    return { isLimited: false, limitMessage: '' };
  }
  // Check if user has reached memory limit
  if (user.memoryUsageMB !== undefined && TOTAL_MEMORY_LIMIT_MB >= 0 && user.memoryUsageMB >= TOTAL_MEMORY_LIMIT_MB) {
    return {
      isLimited: true,
      limitMessage: `You've reached the storage limit of ${TOTAL_MEMORY_LIMIT_MB} MB on the free plan. To continue using, delete old chats or upgrade to a premium plan.`
    };
  }
  
  // Check if user has reached total chat limit
  if (isNewChat && !canCreateNewChat(user)) {
    return {
      isLimited: true,
      limitMessage: `You've reached the limit of ${TOTAL_CHAT_LIMIT} chats on the free plan. To continue using, delete old chats or upgrade to a premium plan.`
    };
  }
  
  // Check if user has reached daily message limit
  if (!canSendMessage(user)) {
    const timeUntilReset = formatTimeUntilReset(user.minutesUntilReset);
    return {
      isLimited: true,
      limitMessage: `You've reached the daily limit of ${DAILY_MESSAGE_LIMIT} messages. Your limit will reset in ${timeUntilReset}.`,
      minutesUntilReset: user.minutesUntilReset
    };
  }
  
  return { isLimited: false, limitMessage: '' };
}



