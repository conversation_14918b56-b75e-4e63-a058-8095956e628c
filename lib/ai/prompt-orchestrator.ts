import {
  sendRequestToAnthropic,
  sendRequestToOpenAI,
} from "@/lib/services/llmService";
import { Logger } from "@/lib/utils/Logger";

/**
 * Evaluates the user command for vagueness and returns a clarifying supplement
 * to append to the base prompt. If the command is clear, returns an empty string.
 */
export async function getClarifyingInstructions(
  userCommand: string
): Promise<string> {
  const metaPrompt = `
You are an expert prompt engineer. Evaluate the following user command and determine if it is vague.
If the command is vague, provide clear, concise clarifying instructions that can be appended to a system prompt in order to help guide a language model's response.
If the command is sufficiently clear, simply return an empty string.

User command: "${userCommand}"

Clarifying instructions (if any):
`;
  try {
    const instructions = await sendRequestToOpenAI(
      metaPrompt,
      undefined,
      "gpt-4o-mini"
    );
    return instructions && instructions.trim() ? instructions.trim() : "";
  } catch (error) {
    Logger.error("Error getting clarifying instructions using LLM:", error);
    return "";
  }
}

/**
 * Uses an LLM call to decide whether external document context should be used.
 * Returns true if RAG is needed, or false otherwise.
 */
export async function shouldUseRAG(userCommand: string): Promise<boolean> {
  const metaPrompt = `
You are an expert in document retrieval and retrieval-augmented generation (RAG). 
Evaluate the following user command. Determine if relevant documents should be used 
to provide a better answer. If the command clearly indicates that document context is needed, 
respond with "YES". Otherwise, respond with "NO".

User command: "${userCommand}"

Answer:
`;
  try {
    const response = await sendRequestToOpenAI(
      metaPrompt,
      undefined,
      "gpt-4o-mini"
    );
    const lowerResponse = response?.toLowerCase();
    if (lowerResponse?.includes("yes")) {
      return true;
    } else if (lowerResponse?.includes("no")) {
      return false;
    } else {
      // Default to using RAG if the output is ambiguous
      return true;
    }
  } catch (error) {
    Logger.error("Error determining whether to use RAG:", error);
    return true;
  }
}

/**
 * Generates the final system prompt by starting with the base prompt, appending any clarifying
 * instructions, and (if indicated) appending the retrieved RAG context.
 *
 * @param basePrompt The constant base prompt.
 * @param userCommand The user's command text.
 * @param ragContext The context retrieved from documents.
 * @returns The final system prompt.
 */
export async function generateFinalSystemPrompt(
  basePrompt: string,
  userCommand: string,
  ragContext: string
): Promise<string> {
  const clarifyingInstructions = await getClarifyingInstructions(userCommand);
  const useRag = await shouldUseRAG(userCommand);
  let finalPrompt = basePrompt;
  if (clarifyingInstructions) {
    finalPrompt += "\n\n" + clarifyingInstructions;
  }
  if (useRag && ragContext) {
    finalPrompt += "\n\nContext from files:\n" + ragContext;
  }
  return finalPrompt;
}
