// Define your models here.
import { generateSystemPromptAdvanced, generateSystemPromptEveryday } from "./prompts";

export interface Model {
  id: string;
  label: string;
  apiIdentifier: string;
  description: string;
  provider: "openai" | "anthropic" | "google";
  hidden?: boolean;
  appendPrompt?: string;
  getSystemPrompt?: (preferences: any) => string;
}

export const models: Array<Model> = [
  {
    id: "claude-3-sonnet",
    label: "Claude",
    apiIdentifier: "claude-3-5-sonnet-20241022",
    description: "Ideal balance of intelligence and speed",
    provider: "anthropic",
    hidden: true,
  },
  {
    id: "gpt-4o-mini",
    label: "GPT 4o mini",
    apiIdentifier: "gpt-4o-mini",
    description: "Small model for fast, lightweight tasks",
    provider: "openai",
    hidden: true,
  },
  {
    id: "gpt-4o",
    label: "GPT 4o",
    apiIdentifier: "gpt-4o",
    description: "For complex, multi-step tasks",
    provider: "openai",
    hidden: true,
  },
  {
    id: "gpt-4-1-concise",
    label: "GPT 4 point 1",
    apiIdentifier: "gpt-4.1-2025-04-14",
    description: "Latest GPT Model",
    provider: "openai",
    hidden: true,
    getSystemPrompt: generateSystemPromptEveryday,
  },
  {
    id: "gpt-4-1-advanced",
    label: "GPT 4 point 1",
    apiIdentifier: "gpt-4.1-2025-04-14",
    description: "Latest GPT Model",
    provider: "openai",
    hidden: true,
    getSystemPrompt: generateSystemPromptAdvanced,
  },
  {
    id: "iqidis-everyday",
    label: "Iqidis Everyday",
    apiIdentifier: "gemini-2.5-pro-preview-03-25",
    description: `For concise, to the point responses.`,
    provider: "google",
    appendPrompt:
      "\n \n ***Very important system instruction for Iqidis Core****: Iqidis is concise where possible. Iqidis doesn't skip any important information, but Iqidis's users are busy professionals so they appreciate dense but concise and to the point answers",
    getSystemPrompt: generateSystemPromptEveryday,
  },
  {
    id: "gemini-2.0-flash-lite-preview-02-05",
    label: "Gemini 2.0 Flash Lite Preview",
    apiIdentifier: "gemini-2.0-flash-lite-preview-02-05",
    description: "Best for concise things",
    provider: "google",
    hidden: true,
  },
  {
    id: "iqidis-advanced",
    label: "Iqidis Advanced",
    apiIdentifier: "gemini-2.5-pro-preview-03-25",
    description: `Detailed analysis & drafting, for the heavier lifts.`,
    provider: "google",
    getSystemPrompt: generateSystemPromptAdvanced,
  },
  {
    id: "gemini-2.0-pro-exp-02-05",
    label: "Gemini 2 Pro",
    apiIdentifier: "gemini-2.0-pro-exp-02-05",
    description: "Best for everyday use",
    provider: "google",
    hidden: true,
  },
] as const;

export const DEFAULT_MODEL_NAME = "iqidis-everyday";
