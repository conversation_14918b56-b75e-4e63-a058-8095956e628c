import { openai } from "@ai-sdk/openai";
import { anthropic } from "@ai-sdk/anthropic";
import { experimental_wrapLanguageModel as wrapLanguageModel } from "ai";
import { models } from "./models";
import { customMiddleware } from "./custom-middleware";

export const customModel = (apiIdentifier: string) => {
  const model = models.find((m) => m.apiIdentifier === apiIdentifier);

  if (!model) {
    throw new Error(`Model ${apiIdentifier} not found`);
  }

  return wrapLanguageModel({
    model:
      model.provider === "anthropic"
        ? anthropic(apiIdentifier)
        : openai(apiIdentifier),
    middleware: customMiddleware,
  });
};
