// starter-prompts.ts

export interface IStarterPrompt {
    title: string;
    description: string;
    defaultText: string;
  }
  
  export interface IStarterCategory {
    category: string;
    prompts: IStarterPrompt[];
  }
  
  export const STARTER_PROMPTS: IStarterCategory[] = [
    {
      category: "Litigation",
      prompts: [
        {
          title: "Motion to Dismiss Outline",
          description: "Basic structure for a motion to dismiss in civil court.",
          defaultText: "Dear Court, ...",
        },
        {
          title: "Deposition Question Bank",
          description: "Comprehensive list of questions for a witness deposition.",
          defaultText: "1) Please state your name for the record...\n2) ...",
        },
        {
          title: "Evidence Admissibility Analysis",
          description: "Framework for evaluating evidence admissibility.",
          defaultText: "Step 1: Identify the evidence...\nStep 2: ...",
        },
      ],
    },
    {
      category: "Corporate & Contracts",
      prompts: [
        {
          title: "Contract Revision Analysis",
          description:
            "Checklist and guidelines for reviewing and revising commercial contracts.",
          defaultText: "1) Parties and definitions...\n2) Payment terms...\n3) ...",
        },
      ],
    },
    {
      category: "Legal Research",
      prompts: [
        {
          title: "Statutory Interpretation Template",
          description: "Basic template for analyzing statutes and regulations.",
          defaultText: "When interpreting a statute, consider legislative intent...",
        },
      ],
    },
  ];
  