import { db } from "@/lib/db";
import { user, subscription, plan } from "@/lib/db/schema";
import { eq, and, lt } from "drizzle-orm";
import { Logger } from '@/lib/utils/Logger';
import { EmailService } from "@/lib/email/service";

export async function handleExpiredTrials() {
  try {
    const now = new Date();
    
    // Find all expired extended trials
    const expiredTrials = await db
      .select({
        userId: subscription.userId,
        email: user.email,
        subscriptionId: subscription.id,
      })
      .from(subscription)
      .innerJoin(user, eq(subscription.userId, user.id))
      .where(
        and(
          eq(subscription.status, "active"),
          eq(subscription.extendedTrial, true),
          lt(subscription.trialEndsAt, now)
        )
      );
    
    // Get the free plan
    const [freePlan] = await db
      .select()
      .from(plan)
      .where(eq(plan.name, "free"))
      .limit(1);
    
    if (!freePlan) {
      throw new Error("Free plan not found");
    }
    
    // Downgrade expired trials to free plan
    for (const trial of expiredTrials) {
      try {
        // Update user's subscription tier
        await db
          .update(user)
          .set({
            subscriptionTier: "free",
            nextSubscriptionTier: null,
          })
          .where(eq(user.id, trial.userId));
        
        // Update subscription
        await db
          .update(subscription)
          .set({
            planId: freePlan.id,
            status: "active",
            autoRenew: true,
            updatedAt: new Date(),
            extendedTrial: false,
          })
          .where(eq(subscription.id, trial.subscriptionId));
        
        Logger.info(`Downgraded expired trial for user ${trial.userId} to free plan`);
        
        // Send email notification
        if (trial.email) {
          const emailResult = await EmailService.sendTrialEndedEmail(trial.email);
          if (emailResult.success) {
            Logger.info(`Sent trial ended email to ${trial.email}`);
          } else {
            Logger.error(`Failed to send trial ended email to ${trial.email}:`, emailResult.error);
          }
        }
      } catch (error) {
        Logger.error(`Failed to downgrade trial for user ${trial.userId}:`, error);
      }
    }
    
    return expiredTrials.length;
  } catch (error) {
    Logger.error("Failed to handle expired trials:", error);
    throw error;
  }
}
