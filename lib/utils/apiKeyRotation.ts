import { Logger } from "./Logger";

interface ApiKeyConfig {
  key: string;
  name: string;
  enabled: boolean;
  usageCount: number;
  lastUsed: any;
}

const googlePalmKeys: ApiKeyConfig[] = [
  // { key: process.env.GOOGLE_PALM_KEY || "", name: "Key 1 (sabih693)", enabled: !!process.env.GOOGLE_PALM_KEY, usageCount: 0, lastUsed: 0 },
  // { key: process.env.GOOGLE_PALM_KEY_2 || "", name: "Key 2 (sabihsid93)", enabled: !!process.env.GOOGLE_PALM_KEY_2, usageCount: 0, lastUsed: 0 },
  // { key: process.env.GOOGLE_PALM_KEY_3 || "", name: "Key 3 (Devansh)", enabled: !!process.env.GOOGLE_PALM_KEY_3, usageCount: 0, lastUsed: 0 },
  // { key: process.env.GOOGLE_PALM_KEY_4 || "", name: "Key 4 (zsheikh93)", enabled: !!process.env.GOOGLE_PALM_KEY_4, usageCount: 0, lastUsed: 0 },
  // { key: process.env.GOOGLE_PALM_KEY_5 || "", name: "Key 5 (zamari)", enabled: !!process.env.GOOGLE_PALM_KEY_5, usageCount: 0, lastUsed: 0 },
  // { key: process.env.GOOGLE_PALM_KEY_6 || "", name: "Key 6 (delighter)", enabled: !!process.env.GOOGLE_PALM_KEY_6, usageCount: 0, lastUsed: 0 },
  // { key: process.env.GOOGLE_PALM_KEY_7 || "", name: "Key 7 (just-advice-456210-c9)", enabled: !!process.env.GOOGLE_PALM_KEY_7, usageCount: 0, lastUsed: 0 },
  { key: process.env.GOOGLE_PALM_KEY_8 || "", name: "Key 8 (iqidis-developer)", enabled: !!process.env.GOOGLE_PALM_KEY_8, usageCount: 0, lastUsed: 0 },
];

export function getGooglePalmApiKey(): string {
  const enabledKeys = googlePalmKeys.filter(k => k.enabled && k.key);
  
  if (enabledKeys.length === 0) {
    Logger.error("No enabled Google Palm API keys available");
    throw new Error("No enabled Google Palm API keys available");
  }

  const randomIndex = Math.floor(Math.random() * enabledKeys.length);
  const keyToUse = enabledKeys[randomIndex];
  
  const keyIndex = googlePalmKeys.findIndex(k => k.key === keyToUse.key);
  googlePalmKeys[keyIndex].usageCount++;
  googlePalmKeys[keyIndex].lastUsed = Date.now();
  
  Logger.info(`Using Google Palm ${keyToUse.name}`);
  // logKeyUsageStats();
  return keyToUse.key;
}

function logKeyUsageStats(): void {
  const stats = googlePalmKeys.map(key => 
    `${key.name}- usage count: ${key.usageCount}, last used: ${key.lastUsed ? new Date(key.lastUsed).toISOString() : 'never'}`
  );
  
  Logger.info('Google Palm API key usage stats:', stats);
}



