// A utility class to track timing for requests without passing parameters
import { performance } from 'perf_hooks';
import { Logger } from "./Logger";

type TimingData = {
  startTime: number;
  markers: Record<string, number>;
};

export class RequestTimer {
  private static timers: Map<string, TimingData> = new Map();

  /**
   * Start timing a new request
   * @param requestId Unique identifier for the request (chatId + timestamp works well)
   * @returns The requestId for reference
   */
  static startRequest(requestId: string): string {
    this.timers.set(requestId, {
      startTime: performance.now(),
      markers: {}
    });
    
    // Set a cleanup timeout (5 minutes)
    // setTimeout(() => {
    //   this.timers.delete(requestId);
    // }, 5 * 60 * 1000);
    
    return requestId;
  }

  /**
   * Mark a point in time during request processing
   * @param requestId The request identifier
   * @param markerName Name of the timing marker
   */
  static markEvent( markerName: string,requestId?: string): void {
    if (!requestId) return;
    const timer = this.timers.get(requestId);
    if (timer) {
      timer.markers[markerName] = performance.now();
    }
  }

  /**
   * Get elapsed time since request start in seconds (with 2 decimal places)
   * @param requestId The request identifier
   * @returns Formatted elapsed time string or "0.00" if timer not found
   */
  static getElapsedTime(requestId?: string): string {
    try {
    if (!requestId) return "0.00";
    const timer = this.timers.get(requestId);
    if (!timer) return "0.00";
    
    const elapsedMs = performance.now() - timer.startTime;
    return (elapsedMs / 1000).toFixed(2);
    } catch (error) {
      Logger.warn("Error getting elapsed time:", error)
      return "0.00";
    }
  }

  /**
   * Get elapsed time between two markers or from start to a marker
   * @param requestId The request identifier
   * @param endMarker The ending marker name
   * @param startMarker Optional starting marker name (uses request start if omitted)
   * @returns Formatted elapsed time string or "0.00" if timer not found
   */
  static getElapsedTimeBetween(endMarker: string, startMarker?: string,requestId?: string, ): string {
    if (!requestId) return "0.00";
    const timer = this.timers.get(requestId); 
    if (!timer) return "0.00";
    
    const endTime = timer.markers[endMarker];
    if (!endTime) return "0.00";
    
    const startTime = startMarker ? timer.markers[startMarker] : timer.startTime;
    if (!startTime) return "0.00";
    
    const elapsedMs = endTime - startTime;
    return (elapsedMs / 1000).toFixed(2);
  }

  /**
   * Clean up the timer for a request
   * @param requestId The request identifier
   */
  static endRequest(requestId: string): void {
    this.timers.delete(requestId);
  }
}