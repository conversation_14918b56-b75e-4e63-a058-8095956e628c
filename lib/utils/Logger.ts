export class Logger {
  static log(level: "debug" | "info" | "warn" | "error", message: string, data?: any) {
    const formattedMessage = `${message}`;

    if (level === "error") {
      console.error(formattedMessage, data || "");
    } else if (level === "warn") {
      console.warn(formattedMessage, data || "");
    } else if (level === "debug") {
        if (process.env.DEBUG_MODE === "true") {
          console.debug(formattedMessage, data || "");
        }
    } else {
      console.log(formattedMessage, data || "");
    }
  }

  static debug(message: string, data?: any) {
    this.log("debug", message, data);
  }

  static info(message: string, data?: any) {
    this.log("info", message, data);
  }

  static warn(message: string, data?: any) {
    this.log("warn", message, data);
  }

  static error(message: string, data?: any) {
    this.log("error", message, data);
  }
}
