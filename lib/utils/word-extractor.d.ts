// In your new file: /types/word-extractor.d.ts

declare module 'word-extractor' {
  // This describes the main class exported by the module.
  class WordExtractor {
    /**
     * Extracts content from a .doc or .docx file.
     * @param file The path to the file (string) or the file's content as a Buffer.
     * @returns A Promise that resolves to a Document object.
     */
    extract(file: string | Buffer): Promise<Document>;
  }

  // This describes the Document object returned by the extract method.
  interface Document {
    /**
     * Retrieves the main body text from the document.
     */
    getBody(): string;

    /**
     * Retrieves the footnotes from the document.
     */
    getFootnotes(): string;

    /**
     * Retrieves the endnotes from the document.
     */
    getEndnotes(): string;

    /**
     * Retrieves the headers from the document.
     */
    getHeaders(): string;

    /**
     * Retrieves the annotations from the document.
     */
    getAnnotations(): string;
  }

  // This tells TypeScript that `import WordExtractor from 'word-extractor'` is correct.
  export default WordExtractor;
}