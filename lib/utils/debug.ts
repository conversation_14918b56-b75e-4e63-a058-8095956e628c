import debug from 'debug';

/**
 * Debug utility for application-wide logging
 * Usage: 
 * - Development: Logs are enabled by default
 * - Production: Enable via localStorage.debug = 'app:*'
 */

// Create namespace for different parts of the application
export const createDebugger = (namespace: string) => {
  // Enable in development by default
  if (process.env.NODE_ENV === 'development') {
    debug.enable(`app:${namespace}*`);
  }
  
  return debug(`app:${namespace}`);
};

// Create specific debuggers
export const dbDebug = createDebugger('db');      // Database operations
export const ragDebug = createDebugger('rag');    // RAG processing
export const uploadDebug = createDebugger('upload'); // File uploads
export const embeddingDebug = createDebugger('embedding'); // Embedding generation 

// Core debuggers
export const aiDebug = createDebugger('ai');
export const docDebug = createDebugger('doc');
export const toolDebug = createDebugger('tool');

// Specialized debuggers
export const aiPromptDebug = aiDebug.extend('prompt');
export const aiResponseDebug = aiDebug.extend('response');
export const docCreateDebug = docDebug.extend('create'); 