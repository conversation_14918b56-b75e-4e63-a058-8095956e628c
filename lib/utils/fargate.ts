import { Logger } from "./Logger";
import { getPostgresUrl } from "./env";

export async function processPdfViaFargate(documentId: string, url: string) {
  try {
    Logger.info("Processing PDF via Fargate service", { documentId, url });

    // The Fargate service endpoint URL
    const fargateEndpoint = 
      process.env.PDF_PROCESSOR_FARGATE_URL || 
      "pdf-processor-lb-1570518785.us-east-1.elb.amazonaws.com/process-pdf";

    // Format the request payload
    const payload = {
      pdf_url: url,
      include_data_uri: true,
      optimize_size: true,
      target_dpi: 150,
      source_document_id: documentId,
      database_url: getPostgresUrl(),
    };

    // Call the Fargate endpoint
    const response = await fetch(`http://${fargateEndpoint}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Fargate service error (${response.status}): ${errorText}`);
    }

    // Parse and return the response
    const result = await response.json();
    Logger.info("PDF processing via Fargate successful", { documentId });

    return result;
  } catch (error) {
    Logger.error("Error processing PDF via Fargate", {
      documentId,
      url,
      error: error,
    });
    throw error;
  }
}

/**
 * Polls the Fargate service for job status until completion or timeout
 * @param jobId - The ID of the job to check
 * @param documentId - The ID of the document being processed
 * @param options - Optional configuration for polling
 * @returns The final job status or null if timed out
 */
export async function waitForFargateJobCompletion(
  jobId: string,
  documentId: string,
  options = {
    maxRetries: 200,
    pollingInterval: 2000, // 2 seconds
    statusEndpoint: process.env.PDF_PROCESSOR_STATUS_URL || 
      "pdf-processor-lb-1570518785.us-east-1.elb.amazonaws.com/job-status"
  }
) {
  Logger.info("Waiting for PDF processing job to complete", { documentId, jobId });
  
  let jobCompleted = false;
  let retryCount = 0;
  let finalStatus = null;
  
  while (!jobCompleted && retryCount < options.maxRetries) {
    await new Promise(resolve => setTimeout(resolve, options.pollingInterval));
    
    try {
      const statusResponse = await fetch(
        `http://${options.statusEndpoint}/${jobId}`
      );
      
      if (!statusResponse.ok) {
        throw new Error(`Status check failed: ${statusResponse.statusText}`);
      }
      
      const jobStatus = await statusResponse.json();
      Logger.debug("PDF processing status", { 
        documentId, 
        jobId,
        progress: jobStatus.progress,
        status: jobStatus.status 
      });
      
      if (jobStatus.status === "COMPLETED" && jobStatus.progress === "100.0%") {
        jobCompleted = true;
        Logger.info("PDF processing completed successfully", {
          documentId,
          jobId,
          pageCount: jobStatus.total_pages
        });
        
        finalStatus = jobStatus;
      }
      
      retryCount++;
    } catch (statusError) {
      Logger.error("Error checking job status", {
        documentId,
        jobId,
        error: statusError,
        retryCount
      });
      retryCount++;
    }
  }
  
  if (!jobCompleted) {
    Logger.warn("PDF processing job did not complete in time", {
      documentId,
      jobId,
      maxRetries: options.maxRetries
    });
  }
  
  return finalStatus;
}
