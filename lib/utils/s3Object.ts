import {
  GetObjectCommand,
  GetObjectCommandInput,
  PutObjectCommand,
  PutObjectCommandInput,
  S3Client
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
})

export async function getSignedUrlToUpload(
  key: string,
  mimeType: string = 'application/pdf',
  bucket: string = 'iqidis-document',
) {

  const commandInput: PutObjectCommandInput = {
    Bucket: bucket,
    Key: key,
    ContentType: mimeType
  }

  const command = new PutObjectCommand(commandInput)
  const signedUrl = await getSignedUrl(s3Client, command, {
    expiresIn: 3600
  })

  return signedUrl
}

export async function getSignedUrlToDownload({
  key,
  bucket,
  mimeType = 'application/pdf',
  otherOptions = {},
}: {
  key: string
  bucket: string
  mimeType?: string
  otherOptions?: Record<string, string>
}, options?: { expiresIn?: number }) {
  const commandInput: GetObjectCommandInput = {
    Bucket: bucket,
    Key: key,
    ResponseContentType: mimeType,
    ResponseContentDisposition: 'inline',
    ...otherOptions,
  }

  const command = new GetObjectCommand(commandInput)
  const signedUrl = await getSignedUrl(s3Client, command, {
    expiresIn: options?.expiresIn || 3600
  })

  return signedUrl
}
