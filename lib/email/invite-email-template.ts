export function createInviteEmail({
  resetLink,
  userEmail,
}: {
  resetLink: string;
  userEmail: string;
}): string {
  const logoUrl =
    "https://sd1fwqxb1ktiukqm.public.blob.vercel-storage.com/Cropped%20-%20Center%20w%20Logo-wuzZpALqg5F7V7FktC6yQmZ7lNCj72.png";

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Iqidis – Set Your Password</title>
        <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            background-color: #f8f9fa;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #000;
            text-decoration: none;
        }
        .content {
            padding: 30px 20px;
            background: #ffffff;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #0070f3 !important;
            color: #ffffff !important;
            text-decoration: none !important;
            border-radius: 5px;
            margin: 20px 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            mso-line-height-rule: exactly;
            line-height: 100%;
        }
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #666;
        }
        .note {
            font-size: 14px;
            margin-top: 20px;
            background-color: #fff8e1;
            padding: 15px;
            border-radius: 5px;
        }
        </style>
    </head>
    <body>
        <div class="container">
        <div class="header">
           <div class="logo-container">
                <img src="${logoUrl}" alt="Iqidis Logo" width="60" height="60" style="display:block; margin: 0 auto;" />
            </div>
        </div>
        <div class="content">
            <h2>Welcome to Iqidis!</h2>
            <p>Hello,</p>
            <p>We're excited to welcome you to Iqidis! You've been invited to join our platform with the email address: <strong>${userEmail}</strong>.</p>
            <p>To activate your account and get started, please set your password by clicking the button below:</p>
            
            <div style="text-align: center;">
                <a href="${resetLink}" class="button">Set Your Password</a>
            </div>

            <div class="note">
                🔐 For security reasons, this link will expire in 24 hours.
            </div>
            
            <p>If you did not expect this invitation, you can safely ignore this email.</p>
            
            <p>We look forward to having you on board!<br>The Iqidis Team</p>
        </div>
        <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} Iqidis. All rights reserved.</p>
        </div>
        </div>
    </body>
    </html>
  `;
}
