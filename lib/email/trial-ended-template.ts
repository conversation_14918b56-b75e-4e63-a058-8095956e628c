interface TrialEndedEmailProps {
  userEmail: string;
}

export function createTrialEndedEmail({ userEmail }: TrialEndedEmailProps): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  
  // SVG logo converted to string format for email embedding
  const logoSvg = `
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      viewBox="0 0 206.57 225.4" 
      width="100" 
      height="100"
      style="mix-blend-mode: multiply;"
    >
      <defs>
        <style>
          .cls-1 {
            fill: #fff;
          }
          .cls-2 {
            fill: #662483;
            mix-blend-mode: multiply;
            opacity: .32;
          }
          .cls-3 {
            isolation: isolate;
          }
        </style>
      </defs>
      <g class="cls-3">
        <g id="Layer_1" data-name="Layer 1">
          <g>
            <rect class="cls-2" x="25.82" y="73.97" width="154.94" height="77.47" transform="translate(-9.42 215.99) rotate(-90)"/>
            <rect class="cls-2" x="25.82" y="73.97" width="154.94" height="77.47" transform="translate(57.33 258.5) rotate(-120)"/>
            <rect class="cls-2" x="25.82" y="73.97" width="154.94" height="77.47" transform="translate(136.38 261.95) rotate(-150)"/>
            <rect class="cls-2" x="25.82" y="73.97" width="154.94" height="77.47" transform="translate(206.57 225.4) rotate(-180)"/>
            <rect class="cls-2" x="25.82" y="73.97" width="154.94" height="77.47" transform="translate(249.09 158.66) rotate(150)"/>
            <rect class="cls-2" x="25.82" y="73.97" width="154.94" height="77.47" transform="translate(252.53 79.61) rotate(120)"/>
          </g>
          <g>
            <path class="cls-1" d="M72.58,103.68c-.29,0-.53-.09-.73-.28-.2-.19-.3-.43-.3-.71s.1-.52.3-.71c.2-.19.44-.28.73-.28s.51.09.71.28c.2.19.3.43.3.71s-.1.52-.29.71c-.19.19-.43.28-.72.28ZM71.74,106.58h1.65v15.32h-1.65v-15.32Z"/>
            <path class="cls-1" d="M84.42,122.32c-1.48,0-2.83-.36-4.05-1.09-1.22-.73-2.19-1.7-2.92-2.93-.72-1.22-1.09-2.58-1.09-4.07,0-1.11.21-2.15.63-3.12.42-.97,1-1.83,1.74-2.57s1.6-1.32,2.57-1.74c.97-.42,2.01-.63,3.12-.63,1.36,0,2.52.33,3.5.98.97.65,1.73,1.51,2.27,2.57v-3.16h1.65v22.97h-1.65v-10.78c-.54,1.06-1.3,1.92-2.27,2.57-.98.65-2.14.98-3.5.98ZM84.43,107.8c-1.18,0-2.26.29-3.22.87-.96.58-1.73,1.36-2.31,2.33-.57.98-.86,2.05-.86,3.22s.29,2.31.87,3.29c.58.98,1.36,1.75,2.33,2.32.97.57,2.03.86,3.19.86s2.18-.29,3.02-.88c.84-.59,1.49-1.37,1.94-2.35.45-.98.67-2.06.67-3.23s-.23-2.28-.68-3.25c-.45-.97-1.1-1.74-1.94-2.32-.84-.58-1.85-.87-3.01-.87Z"/>
            <path class="cls-1" d="M96.58,103.68c-.29,0-.53-.09-.73-.28-.2-.19-.3-.43-.3-.71s.1-.52.3-.71c.2-.19.44-.28.73-.28s.51.09.71.28c.2.19.3.43.3.71s-.1.52-.29.71c-.19.19-.43.28-.72.28ZM95.73,106.58h1.65v15.32h-1.65v-15.32Z"/>
            <path class="cls-1" d="M114.19,98.92h1.65v22.97h-1.65v-3.16c-.53,1.07-1.29,1.93-2.27,2.58-.98.65-2.15.97-3.51.97-1.11,0-2.15-.21-3.12-.63-.97-.42-1.83-1-2.57-1.74s-1.32-1.6-1.74-2.57-.63-2.01-.63-3.12.21-2.16.63-3.13c.42-.97,1-1.83,1.74-2.57s1.6-1.32,2.57-1.74c.97-.42,2.01-.63,3.12-.63,1.36,0,2.53.32,3.51.97.98.65,1.74,1.5,2.27,2.57v-10.78ZM108.43,120.67c1.17,0,2.18-.29,3.02-.87.84-.58,1.49-1.36,1.94-2.33.45-.98.67-2.06.67-3.23s-.23-2.29-.68-3.27c-.45-.97-1.1-1.75-1.94-2.33-.84-.58-1.85-.87-3.01-.87s-2.24.29-3.21.87-1.74,1.36-2.31,2.34c-.58.98-.87,2.06-.87,3.25s.29,2.29.88,3.26,1.37,1.74,2.34,2.31c.97.57,2.03.86,3.17.86Z"/>
            <path class="cls-1" d="M120.67,103.68c-.29,0-.53-.09-.73-.28-.2-.19-.3-.43-.3-.71s.1-.52.3-.71c.2-.19.44-.28.73-.28s.51.09.71.28c.2.19.3.43.3.71s-.1.52-.29.71c-.19.19-.43.28-.72.28ZM119.83,106.58h1.65v15.32h-1.65v-15.32Z"/>
            <path class="cls-1" d="M130.19,122.18c-.85-.01-1.68-.16-2.49-.45s-1.52-.68-2.12-1.17c-.6-.49-1.02-1.05-1.26-1.67l1.46-.63c.17.46.51.87,1,1.24.5.37,1.06.66,1.68.87.63.21,1.24.31,1.85.31.68,0,1.33-.12,1.93-.35.6-.23,1.09-.57,1.47-1s.57-.93.57-1.52c0-.61-.2-1.09-.6-1.45-.4-.35-.91-.64-1.52-.86s-1.25-.43-1.91-.62c-1.12-.33-2.1-.66-2.92-1-.82-.34-1.46-.78-1.91-1.31-.45-.53-.67-1.23-.67-2.1s.26-1.68.78-2.34c.52-.66,1.21-1.18,2.05-1.54.85-.36,1.76-.54,2.73-.54,1.26,0,2.4.28,3.42.83,1.03.55,1.75,1.28,2.17,2.17l-1.35.8c-.2-.47-.53-.87-.96-1.21-.44-.34-.94-.6-1.5-.78-.56-.18-1.13-.28-1.7-.29-.71,0-1.38.1-2,.33-.62.23-1.12.56-1.49,1s-.57.96-.57,1.57.17,1.08.52,1.41c.35.33.83.59,1.46.79.62.2,1.34.43,2.16.7.95.3,1.83.62,2.66.99s1.49.81,1.99,1.36c.5.54.75,1.24.74,2.1,0,.9-.27,1.68-.8,2.34-.54.66-1.24,1.17-2.1,1.53-.86.36-1.79.53-2.76.51Z"/>
          </g>
        </g>
      </g>
    </svg>
  `;
  
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Premium Trial Has Ended</title>
        <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f8fb;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        .logo-container {
            margin-bottom: 10px;
        }
        .content {
            padding: 30px 20px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #662483;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #6c757d;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
            border-radius: 0 0 8px 8px;
        }
        h2 {
            color: #270f57;
            font-weight: 600;
        }
        </style>
    </head>
    <body>
        <div class="container">
        <div class="header">
            <div class="logo-container">
                ${logoSvg}
            </div>
        </div>
        <div class="content">
            <h2 style="text-align: center;">Your Premium Trial Has Ended</h2>
            <p>Hello,</p>
            <p>Your 30-day premium trial has ended.</p>
            <p>Your account has been automatically downgraded to the free plan.</p>
            
            <div style="text-align: center;">
            <a href="${baseUrl}/subscription" class="button">Upgrade to Premium</a>
            </div>
            
            <p>To continue enjoying premium features, you can upgrade your account at any time.</p>
            
            <p>Best regards,<br>The Iqidis Team</p>
        </div>
        <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
           
        </div>
        </div>
    </body>
    </html>
  `;
}
