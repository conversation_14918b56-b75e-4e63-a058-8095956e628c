import Mixpanel from "mixpanel";
import { Logger } from "@/lib/utils/Logger";

const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;

if (!MIXPANEL_TOKEN) {
  Logger.error("Mixpanel token is missing! Check your environment variables.");
}

const mp = MIXPANEL_TOKEN ? Mixpanel.init(MIXPANEL_TOKEN) : null;

/**
 * Helper to inject distinct_id from userId if present.
 * Do NOT add distinct_id manually to event properties; just provide userId.
 */
function withDistinctId(properties?: Record<string, any>) {
  if (!properties) return undefined;
  if (properties.userId && !properties.distinct_id) {
    return { ...properties, distinct_id: properties.userId };
  }
  return properties;
}

/**
 * Track an event to Mixpanel (server-side)
 * Usage: trackServerEvent('User Login', { userId: '123', email: '<EMAIL>' })
 * Do NOT add distinct_id manually; just provide userId.
 */
export const trackServerEvent = (
  eventName: string,
  properties?: Record<string, any>
) => {
  if (!mp) {
    Logger.warn("Mixpanel not initialized - skipping event tracking");
    return;
  }

  try {
    const eventProperties = {
      timestamp: new Date().toISOString(),
      source: 'server',
      ...withDistinctId(properties),
    };

    mp.track(eventName, eventProperties);
    // Logger.debug(`Mixpanel event tracked: ${eventName}`, eventProperties);
  } catch (error) {
    Logger.error(`Mixpanel tracking error for event ${eventName}:`, error);
  }
};

export const identifyUser = (userId: string, properties: Record<string, any>) => {
  if (!mp) {
    Logger.warn("Mixpanel not initialized - skipping user identification");
    return;
  }

  try {
    const userProperties = {
      $created: new Date().toISOString(),
      userId,
      ...properties,
    };
    mp.people.set(userId, userProperties);
    // Logger.debug(`Mixpanel user identified: ${userId}`, userProperties);
  } catch (error) {
    Logger.error(`Mixpanel identification error for user ${userId}:`, error);
  }
};
