import mixpanel from "mixpanel-browser";
import { Logger } from "../utils/Logger";

const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;

export const initMixpanel = () => {
  if (!MIXPANEL_TOKEN) {
    console.warn("Mixpanel token is missing! Check your .env file.");
    return;
  }
  try {
    mixpanel.init(MIXPANEL_TOKEN, { autocapture: true });
  } catch (error) {
    Logger.error(`Mixpanel Error`, error);
  }
};

export const trackEvent = (
  eventName: string,
  properties?: Record<string, any>
) => {
  if (typeof window !== "undefined") {
    try {
      mixpanel.track(eventName, properties);
    } catch (error) {
      Logger.error(`Mixpanel Error`, error);
    }
  }
};

export const identifyUser = (userId: string, email: string) => {
  if (typeof window === "undefined") return;
  try {
    mixpanel.identify(userId);
    mixpanel.people.set({
      $email: email,
      userId: userId,
    });
  } catch (error) {
    Logger.error(`Mixpanel Error`, error);
  }
};
