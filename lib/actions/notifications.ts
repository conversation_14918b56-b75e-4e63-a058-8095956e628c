'use server'

import { db } from "@/lib/db";
import { notifications, notificationTargets } from "@/lib/db/schema";
import { auth } from "@/app/(auth)/auth";
import { eq, and, desc } from "drizzle-orm";

export async function getNotifications() {
  const session = await auth();
  if (!session?.user?.id) {
    // Return empty array instead of throwing error
    return [];
  }

  const userNotifications = await db
    .select({
      id: notifications.id,
      type: notifications.type,
      title: notifications.title,
      description: notifications.description,
      link: notifications.link,
      createdAt: notifications.createdAt,
      isRead: notificationTargets.isRead,
      readAt: notificationTargets.readAt,
    })
    .from(notifications)
    .innerJoin(
      notificationTargets,
      eq(notifications.id, notificationTargets.notificationId)
    )
    .where(eq(notificationTargets.userId, session.user.id))
    .orderBy(desc(notifications.createdAt));

  return userNotifications;
}

export async function markNotificationAsRead(notificationId: string) {
  const session = await auth();
  if (!session?.user?.id) {
    return; // Return silently instead of throwing
  }

  await db
    .update(notificationTargets)
    .set({
      isRead: true,
      readAt: new Date(),
    })
    .where(
      and(
        eq(notificationTargets.notificationId, notificationId),
        eq(notificationTargets.userId, session.user.id)
      )
    );
}

export async function markAllNotificationsAsRead() {
  const session = await auth();
  if (!session?.user?.id) {
    return; // Return silently instead of throwing
  }

  await db
    .update(notificationTargets)
    .set({
      isRead: true,
      readAt: new Date(),
    })
    .where(eq(notificationTargets.userId, session.user.id));
}

export async function createNotification({
  type,
  title,
  description,
  userIds,
  link
}: {
  type: string;
  title: string;
  description: string;
  userIds: string[];
  link?: string 
}) {
  const session = await auth();
  if (!session?.user?.id) {
    return null; // Return null instead of throwing
  }

  // Create the notification
  const [notification] = await db
    .insert(notifications)
    .values({
      type,
      title,
      description,
      link
    })
    .returning();

  // Create notification targets for each user
  await db.insert(notificationTargets).values(
    userIds.map((userId) => ({
      notificationId: notification.id,
      userId,
      isRead: false,
    }))
  );

  return notification;
} 
