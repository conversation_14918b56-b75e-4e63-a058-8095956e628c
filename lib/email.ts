// Add this function to your existing email.ts file
import { EmailService } from "./email/service";
import { Logger } from "./utils/Logger";

export async function sendTrialActivationEmail(email: string) {
  try {
    // Use the EmailService with the new template
    const result = await EmailService.sendTrialActivationEmail(email);
    
    if (result.success) {
      Logger.info(`Sent trial activation email to ${email}`);
      return true;
    } else {
      Logger.error("Failed to send trial activation email", result.error);
      return false;
    }
  } catch (error) {
    Logger.error("Failed to send trial activation email", error);
    return false;
  }
}



