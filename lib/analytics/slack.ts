import { Logger } from "@/lib/utils/Logger";
import { plan } from '@/lib/db/schema';
import { eq } from "drizzle-orm";
import {db} from "@/lib/db";

/**
 * Sends an error alert to Slack
 * @param title - The title of the alert
 * @param errorType - The type of error
 * @param details - Additional error details including chatId, userId, etc.
 */
export const sendSlackAlert = async (
  title: string,
  errorType: string,
  details: Record<string, any>
) => {
  const webhookUrl = process.env.SLACK_WEBHOOK_URL;

  // Only send slack alert if env is production (not preview or development)
  if (process.env.VERCEL_ENV !== "production") {
    Logger.debug("Skipping Slack alert in non-production environment.");
    return;
  }

  if (!webhookUrl) {
    Logger.error("Slack webhook URL not configured. Skipping Slack alert.");
    return;
  }

  try {
    // Extract important details
    const {
      chatId,
      userId,
      userEmail,
      errorMessage,
      error,
      modelIdentifier,
      statusCode,
      context,
    } = details;

    // Build common blocks for both payloads
    const commonBlocks = [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `🚨 ${title}`,
        },
      },
      {
        type: "section",
        fields: [
          {
            type: "mrkdwn",
            text: `*Error Type:*\n${errorType}${
              context ? `: (${context})` : ""
            }`,
          },
          {
            type: "mrkdwn",
            text: `*Model:*\n${modelIdentifier || "N/A"}`,
          },
        ],
      },
    ];

    // Add user information if available
    if (userId || userEmail || chatId) {
      commonBlocks.push({
        type: "section",
        fields: [
          {
            type: "mrkdwn",
            text: `*User:*\n${userId || "N/A"} ${
              userEmail ? `(${userEmail})` : ""
            }`,
          },
          {
            type: "mrkdwn",
            text: `*Chat ID:*\n${chatId || "N/A"}`,
          },
        ],
      });
    }

    // Add status code if available
    if (statusCode) {
      commonBlocks.push({
        type: "section",
        fields: [
          {
            type: "mrkdwn",
            text: `*Status Code:*\n${statusCode}`,
          },
        ],
      });
    }

    // Add error message if available
    if (errorMessage) {
      commonBlocks.push({
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*Error Message:*\n\`\`\`${errorMessage}\`\`\``,
        },
      });
    }

    // Create detailed blocks by cloning common blocks
    const detailedBlocks = [...commonBlocks];

    // Add full error details to detailed payload only
    if (error) {
      detailedBlocks.push({
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*Details:*\n\`\`\`${
            typeof error === "string" ? error : JSON.stringify(error, null, 2)
          }\`\`\``,
        },
      });
    }

    // Create both payloads
    const detailedPayload = {
      text: `${errorType} Error`, // Fallback text
      blocks: detailedBlocks,
    };

    const simplifiedPayload = {
      text: `${errorType} Error (Simplified)`,
      blocks: commonBlocks,
    };

    Logger.info("Sending detailed Slack alert");

    try {
      const response = await fetch(webhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(detailedPayload),
      });

      if (!response.ok) {
        Logger.error(
          `Failed to send detailed Slack alert: ${response.status} ${response.statusText}`
        );

        // Retry with simplified payload
        Logger.info("Retrying with simplified Slack alert");

        const retryResponse = await fetch(webhookUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(simplifiedPayload),
        });

        if (!retryResponse.ok) {
          Logger.error(
            `Failed to send simplified Slack alert: ${retryResponse.status} ${retryResponse.statusText}`
          );
        }
      }
    } catch (fetchError) {
      Logger.error("Error sending Slack alert:", fetchError);
    }
  } catch (error) {
    Logger.error("Error sending Slack alert:", error);
  }
};

/**
 * Sends a message to Slack when a user signs up
 * @param userId - The new user's ID
 * @param userEmail - The new user's email
 */
export const sendSlackMessage = async (userId: string, userEmail: string) => {
  const environment = process.env.VERCEL_ENV || process.env.NODE_ENV;
  const webhookUrl = process.env.SLACK_WEBHOOK_USER_SIGNUP_URL;

  // Only send slack alert if env is production (not preview or development)
  if (process.env.VERCEL_ENV !== "production") {
    Logger.debug("Skipping Slack message in non-production environment.");
    return;
  }

  if (!webhookUrl) {
    Logger.error("Slack webhook URL not configured. Skipping message.");
    return;
  }

  const payload = {
    text: `🎉 New Signup : ${userEmail}`,
    blocks: [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `🎉 New User Signup!`,
        },
      },
      {
        type: "section",
        fields: [
          {
            type: "mrkdwn",
            text: `*User ID:*\n${userId}`,
          },
          {
            type: "mrkdwn",
            text: `*Email:*\n${userEmail}`,
          },
          {
            type: "mrkdwn",
            text: `*From:*\n${environment} - environment`,
          },
        ],
      },
      {
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `Signed up at ${new Date().toLocaleString()}`,
          },
        ],
      },
    ],
  };

  try {
    const response = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      Logger.error(
        `Failed to send Slack message: ${response.status} ${response.statusText}`
      );
    } else {
      Logger.info("Successfully sent Slack message");
    }
  } catch (error) {
    Logger.error("Error sending Slack message:", error);
  }
};

/**
 * Sends a message to Slack when a user purchases a plan
 * @param userId - The ID of the user who made the purchase
 * @param userEmail - The email of the user who made the purchase
 * @param planName - The name of the plan purchased
 * @param amount - The amount paid
 */
export const sendSlackMessagePlanPurchase = async (
  userId: string,
  userEmail: string,
  planName: string,
  amount: number
) => {
  const webhookUrl = process.env.SLACK_WEBHOOK_USER_SIGNUP_URL;
  const environment = process.env.VERCEL_ENV || process.env.NODE_ENV;

  // Only send slack alert if env is production (not preview or development)
  if (process.env.VERCEL_ENV !== "production") {
    Logger.debug("Skipping Slack plan purchase message in non-production environment.");
    return;
  }

  if (!webhookUrl) {
    Logger.error("Slack webhook URL not configured. Skipping message.");
    return;
  }

  const payload = {
    text: `💳 Plan Purchased: ${planName} by ${userEmail}`,
    blocks: [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: "💳 New Plan Purchase!",
        },
      },
      {
        type: "section",
        fields: [
          {
            type: "mrkdwn",
            text: `*User ID:*\n${userId}`,
          },
          {
            type: "mrkdwn",
            text: `*Email:*\n${userEmail}`,
          },
          {
            type: "mrkdwn",
            text: `*Plan:*\n${planName}`,
          },
          {
            type: "mrkdwn",
            text: `*Amount:*\n$${amount.toFixed(2)}`,
          },
          {
            type: "mrkdwn",
            text: `*From:*\n${environment} - environment`,
          },
        ],
      },
      {
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `Purchased at ${new Date().toLocaleString()}`,
          },
        ],
      },
    ],
  };

  try {
    const response = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      Logger.error(
        `Failed to send Slack message: ${response.status} ${response.statusText}`
      );
    } else {
      Logger.info("Successfully sent slack plan purchase message");
    }
  } catch (error) {
    Logger.error("Error sending Slack plan purchase message:", error);
  }
};

/**
 * Sends a message to Slack when a user cancels their subscription
 * @param userId - The ID of the user who cancelled
 * @param userEmail - The email of the user who cancelled
 * @param planId - The ID of the cancelled plan
 */
export const sendSlackMessageSubscriptionCancelled = async (
  userId: string,
  userEmail: string,
  planId: string
) => {
  const webhookUrl = process.env.SLACK_WEBHOOK_USER_SIGNUP_URL;
  const environment = process.env.VERCEL_ENV || process.env.NODE_ENV;

  // Only send slack alert if env is production (not preview or development)
  if (process.env.VERCEL_ENV !== "production") {
    Logger.debug("Skipping Slack subscription cancellation message in non-production environment.");
    return;
  }

  if (!webhookUrl) {
    Logger.error("Slack webhook URL not configured. Skipping message.");
    return;
  }

  // Get plan name if possible
  let planName = planId;
  try {
    const [planDetails] = await db
      .select()
      .from(plan)
      .where(eq(plan.id, planId))
      .limit(1);
    
    if (planDetails) {
      planName = planDetails.name;
    }
  } catch (error) {
    Logger.error("Error fetching plan details:", error);
  }

  const payload = {
    text: `🚫 Subscription Cancelled: ${planName} by ${userEmail}`,
    blocks: [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: "🚫 Subscription Cancelled",
        },
      },
      {
        type: "section",
        fields: [
          {
            type: "mrkdwn",
            text: `*User ID:*\n${userId}`,
          },
          {
            type: "mrkdwn",
            text: `*Email:*\n${userEmail}`,
          },
          {
            type: "mrkdwn",
            text: `*Plan:*\n${planName}`,
          },
          {
            type: "mrkdwn",
            text: `*From:*\n${environment} - environment`,
          },
        ],
      },
      {
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `Cancelled at ${new Date().toLocaleString()}`,
          },
        ],
      },
    ],
  };

  try {
    const response = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      Logger.error(
        `Failed to send Slack message: ${response.status} ${response.statusText}`
      );
    } else {
      Logger.info("Successfully sent slack subscription cancellation message");
    }
  } catch (error) {
    Logger.error("Error sending Slack subscription cancellation message:", error);
  }
};

/**
 * Sends a feedback message to Slack
 * @param type - The type of feedback (problem or feature)
 * @param feedback - The feedback text
 * @param userEmail - The email of the user who submitted feedback
 * @param chatUrl - Optional URL to the chat where feedback was given
 */
export const sendSlackFeedback = async (
  type: string,
  feedback: string,
  userEmail: string,
  chatUrl?: string,
  attachments?: Array<{filename: string, content: Buffer}>
) => {
  try {
    const webhookUrl = process.env.SLACK_WEBHOOK_BUG_FEATURE_URL;

    // Only send slack alert if env is production (not preview or development)
    if (process.env.VERCEL_ENV !== "production") {
      Logger.debug("Skipping Slack feedback message in non-production environment.");
      return;
    }

    if (!webhookUrl) {
      Logger.error("Slack webhook URL not configured. Skipping feedback message.");
      return;
    }

    const feedbackType = type === 'problem' ? '🐛 Bug Report' : '💡 Feature Request';

    const payload = {
      text: `${feedbackType} from ${userEmail}`,
      blocks: [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: feedbackType,
          },
        },
        {
          type: "section",
          fields: [
            {
              type: "mrkdwn",
              text: `*From:*\n${userEmail}`,
            },
            {
              type: "mrkdwn",
              text: `*Submitted:*\n${new Date().toLocaleString()}`,
            },
          ],
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Feedback:*\n${feedback}`,
          },
        }
      ],
    };

    // Add chat URL if available
    if (chatUrl) {
      payload.blocks.push({
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*Chat URL:*\n<${chatUrl}|View Chat>`,
        },
      });
    }

    // Note about attachments
    if (attachments && attachments.length > 0) {
      payload.blocks.push({
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*Attachments:*\n${attachments.length} file(s) attached (${attachments.map(a => a.filename).join(', ')})`,
        },
      });
    }

    const response = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      Logger.error(
        `Failed to send Slack feedback message: ${response.status} ${response.statusText}`
      );
    } else {
      Logger.info("Successfully sent Slack feedback message");
    }
  } catch (error) {
    Logger.error("Error sending Slack feedback message:", error);
  }
};
