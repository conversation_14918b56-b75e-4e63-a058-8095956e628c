import { track } from '@vercel/analytics';
import { 
  ServerErrorEvent,
  ServerSuccessEvent,
  PerformanceEvent,
  EventType
} from './event-types';
import { trackEvent } from '../mixpanel/mixpanel';

/**
 * Logs a client-side event to Vercel Analytics
 * @param event - The event type
 * @param details - Additional metadata (optional)
 * @param sendAlert - Parameter kept for API compatibility (not used in client version)
 */
const logEvent = (
  event: EventType,
  details?: Record<string, any>,
  sendAlert: boolean = true
) => {
  track(event, details);
  try {
    trackEvent(event, details);
  } catch (error) {
    console.log(`Mixpanel Error`, error);
  }
};

export {
  logEvent
};