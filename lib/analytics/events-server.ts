import { track as trackServer } from '@vercel/analytics/server';
import { sendSlackAlert } from './slack';
import { logErrorToDatabase } from './errorTracking';
import { 
  ServerErrorEvent,
  ServerSuccessEvent,
  PerformanceEvent,
  SubscriptionEvent,
  EventType
} from './event-types';
import { trackServerEvent } from '../mixpanel/server/mixpanelServer';

/**
 * Logs a server-side event to Vercel Analytics with structured grouping
 * @param event - The event type
 * @param details - Additional metadata (optional)
 * @param sendAlert - Whether to send Slack alert for error events (default: true)
 */
const logServerEvent = (
  event: EventType,
  details?: Record<string, any>,
  sendAlert: boolean = true
) => {
  trackServer(event, details);
  
  // Add Mixpanel tracking for all server events
  trackServerEvent(event, details);
  
  // Check if it's a server error event and send Slack alert if sendAlert is true
  if (sendAlert && Object.values(ServerErrorEvent).includes(event as ServerErrorEvent)) {
    sendSlackAlert(
      `Server-Side Error Alert : ${event.toString().replace(/_/g, ' ')}`,
      event.toString(),
      details || {}
    );
  }
  
  // Also log to database for server errors
  if (Object.values(ServerErrorEvent).includes(event as ServerErrorEvent)) {
    logErrorToDatabase({
      eventType: event.toString(),
      userId: details?.userId,
      chatId: details?.chatId,
      errorMessage: details?.errorMessage || details?.error?.message,
      errorDetails: details?.error || details,
      source: 'server',
      url: details?.url,
      userAgent: details?.userAgent,
      severity: details?.severity || 'error'
    });
  }
};

export {
  logServerEvent
};