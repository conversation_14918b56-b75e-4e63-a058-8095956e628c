
export function fixMessageContent(text: string): string {
    const cleanedText = (
      text
        // Remove all backticks and markdown identifiers
        .replace(/```markdown\\n/g, "")
        .replace(/```markdown\n/g, "")
        .replace(/```markdown/g, "")
        .replace(/```/g, "")
        // .replace(/```[\s\S]*?```/g, "")
        // Convert 4-space indents before list bullets to 2-space per nesting level
        .replace(/^(( {4})+)([a-zA-Z0-9]+\.\s)/gm, (_, indent, __, bullet) => {
          const levels = indent.length / 4;
          return ' '.repeat(levels * 2) + bullet;
        })      
        // For lines with 4-space indent that are NOT bullets, reduce only to 2 spaces
        // But preserve if it's inside a list (we'll handle that manually)
        .replace(/^ {4}(?![-*+])/gm, '  ')
        // Handle horizontal divider lines (3 or more consecutive dashes/hyphens)
        .replace(/^(\s*)(-\s*){3,}[-X]*$/gm, '$1\n')
        // Remove stray <br> tags
        .replace(/<\/?\s*br\s*\/?>/gi, "\n")
        // Remove any remaining backticks
        .replace(/`/g, "")
        // Add spaces around em dashes
        // .replace(/(\S)—(\S)/g, '$1 — $2')
        // Handle faulty markdown heading common in gemini (e.g. **## Heading** -> ## Heading )
        .replace(/^\s*\*\*\s*(\s*#{1,6}\s*.*?)\s*\*\*\s*$/gm, '$1')
        // Handle faulty markdown heading common in gemini with double hash headings (e.g., ### ## Core Summary -> ### Core Summary)
        .replace(/^(#{1,6})\s*(#{2,6})\s*(.*)$/gm, '$1 $3')
    );
    return cleanedText
};
