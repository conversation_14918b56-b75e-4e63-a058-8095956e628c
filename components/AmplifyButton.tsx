import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { toast } from "sonner";
import { Logger } from "@/lib/utils/Logger";
import { ExtendedMessage } from "@/lib/types";
import { AmplifyDialog } from "./AmplifyDialog";
import { LightningBoltIcon } from "./icons";
import { logEvent } from "@/lib/analytics/events-client";
import { AmplifyEvent } from "@/lib/analytics/event-types";
import { useUser } from "@/contexts/UserContext";

// Custom sparkling bulb icon
const SparklingBulbIcon = ({ size = 14 }: { size?: number }) => (
	<svg
		height={size}
		width={size}
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		style={{ color: "currentcolor" }}
	>
		<path
			d="M9 21H15M12 3C8.68629 3 6 5.68629 6 9C6 11.2208 7.21497 13.1599 9 14.1973V17C9 17.5523 9.44772 18 10 18H14C14.5523 18 15 17.5523 15 17V14.1973C16.785 13.1599 18 11.2208 18 9C18 5.68629 15.3137 3 12 3Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M4.5 9.5L3 11M6 5L5 3.5M18 5L19 3.5M19.5 9.5L21 11M12 2V1"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
			className="animate-pulse"
		/>
	</svg>
);

interface AmplifyButtonProps {
	input: string;
	setInput: (value: string) => void;
	isLoading: boolean;
	messages: Array<ExtendedMessage>;
}

function PureAmplifyButton({
	input,
	setInput,
	isLoading,
	messages,
}: AmplifyButtonProps) {
	const [isAmplifying, setIsAmplifying] = useState(false);
	const [showSuggestion, setShowSuggestion] = useState(false);
	const [refinedPrompt, setRefinedPrompt] = useState("");
	const [currentAmplifyInput, setCurrentAmplifyInput] = useState("");
	const user = useUser();

	// Get the content to amplify when the Amplify button is clicked
	const getLatestUserMessageContent = (): string => {
		// First check if the current input has content - prioritize this
		if (input.trim()) {
			Logger.info("Amplify: Using current input for amplification", {
				inputPreview: input.substring(0, 50) + "...",
			});
			return input;
		}

		// If input is empty, find the last user message in the conversation
		const lastUserMessage = [...messages]
			.reverse()
			.find((msg) => msg.role === "user");

		// Log message count for debugging
		Logger.info(
			`Amplify: Found ${messages.filter((m) => m.role === "user").length} user messages in conversation`,
		);

		// If found and it has content, use that
		if (
			lastUserMessage &&
			typeof lastUserMessage.content === "string" &&
			lastUserMessage.content.trim()
		) {
			Logger.info(
				"Amplify: Input empty, using latest user message for amplification",
				{
					messageId: lastUserMessage.id,
					contentPreview:
						(lastUserMessage.content as string).substring(0, 50) + "...",
					messageTimestamp: lastUserMessage.createdAt,
				},
			);
			return lastUserMessage.content;
		}

		// If no user messages or the last one has no content, return empty input
		Logger.info("Amplify: No content available for amplification");
		return input;
	};

	const generateAmplifiedPrompt = async () => {
		// Get the latest user message to amplify
		const textToAmplify = getLatestUserMessageContent();

		if (!textToAmplify.trim()) {
			toast.error("Please enter some text to amplify");
			return;
		}

		// No need to update the input field since we're prioritizing it

		// Store the current text being amplified
		setCurrentAmplifyInput(textToAmplify);

		// Log the Amplify button click event with user email and admin status
		logEvent(AmplifyEvent.AMPLIFY_BUTTON_CLICK, {
			userId: user?.id,
			userEmail: user?.email,
			isAdmin: user?.isAdmin,
			inputLength: textToAmplify.length,
		});

		try {
			setIsAmplifying(true);
			setShowSuggestion(true);

			// Format conversation history from messages
			const conversationHistory = messages
				.slice(-10) // Only use the last 10 messages for context
				.map(
					(msg) =>
						`${msg.role}: ${typeof msg.content === "string" ? msg.content : JSON.stringify(msg.content)}`,
				)
				.join("\n");

			// Call the API to refine the prompt
			const response = await fetch("/api/prompts/refine", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					promptGoal: textToAmplify, // Use the current input or fall back to the latest user message
					textReferences: "", // No additional references
					conversationHistory,
				}),
			});

			if (!response.ok) {
				throw new Error("Failed to amplify prompt");
			}

			const data = await response.json();

			// Store the refined prompt
			setRefinedPrompt(data.refinedPrompt);
		} catch (error) {
			Logger.error("Error amplifying input:", error);
			toast.error("Failed to amplify input");
			setShowSuggestion(false);
		} finally {
			setIsAmplifying(false);
		}
	};

	const handleUse = () => {
		setInput(refinedPrompt);
		setShowSuggestion(false);
		toast.success("Amplified prompt applied");
		logEvent(AmplifyEvent.USE_BUTTON_CLICK);
	};

	const handleRegenerate = () => {
		generateAmplifiedPrompt();
		logEvent(AmplifyEvent.REGENERATE_BUTTON_CLICK);
	};

	const handleDismiss = () => {
		setShowSuggestion(false);
	};

	const handleSave = async (promptName: string, folderId: string) => {
		try {
			// Show loading toast
			const loadingToast = toast.loading("Saving prompt...");

			// Call the API to save the prompt
			const response = await fetch("/api/prompts", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					title: promptName,
					content: refinedPrompt,
					folderId: folderId,
				}),
			});

			// Dismiss loading toast
			toast.dismiss(loadingToast);

			if (!response.ok) {
				if (response.status === 409) {
					// Handle duplicate title error
					const errorMessage = await response.text();
					toast.error(errorMessage);
					return;
				}
				throw new Error("Failed to save prompt");
			}

			// Show success toast
			toast.success("Prompt saved to My Playbook!");
			logEvent(AmplifyEvent.SAVE_BUTTON_CLICK);

			// Close the suggestion dialog
			setShowSuggestion(false);

			// Trigger a custom event to notify the Playbook component to refresh its data
			if (typeof window !== "undefined") {
				const refreshEvent = new CustomEvent("refresh-playbook-data");
				window.dispatchEvent(refreshEvent);
			}
		} catch (error) {
			console.error("Error saving prompt:", error);
			toast.error("Failed to save prompt");
		}
	};

	return (
		<>
			<AmplifyDialog
				isOpen={showSuggestion}
				onOpenChange={(open) => {
					if (!open) {
						handleDismiss();
					}
				}}
				originalInput={currentAmplifyInput || input}
				refinedPrompt={refinedPrompt}
				onUse={handleUse}
				onRegenerate={handleRegenerate}
				onSave={handleSave}
				isLoading={isAmplifying}
			/>

			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						className={` md:pr-3
          rounded-full p-2 h-8 border-0 shadow-md transition-all duration-200
               bg-gradient-to-r from-purple-500 to-pink-500
               hover:from-purple-600 hover:to-pink-600 text-white flex items-center gap-1.5
              ${isLoading || isAmplifying || (!input.trim() && messages.filter((m) => m.role === "user").length === 0) ? " cursor-not-allowed" : ""}
            `}
						onClick={(event) => {
							event.preventDefault();
							if (!showSuggestion) {
								generateAmplifiedPrompt();
							} else {
								handleDismiss();
							}
						}}
						disabled={
							isLoading ||
							isAmplifying ||
							(!input.trim() &&
								messages.filter((m) => m.role === "user").length === 0)
						}
					>
						<LightningBoltIcon size={14} />
						<span className="hidden md:inline text-xs whitespace-nowrap">Amplify</span>
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					Automatically expand, refine, and elevate your prompt
				</TooltipContent>
			</Tooltip>
		</>
	);
}

export default React.memo(PureAmplifyButton);
