import { UPGRADE_PLAN_URL } from "@/lib/constants";
import { Button } from "./ui/button";
import { ExtendedMessage } from "@/lib/types";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface ErrorMessageProps {
  message: ExtendedMessage;
  onRetry?: (userMessageId?: string, useLegacyModel?: boolean) => void;
  chatId?: string; // Add chatId prop to use for transfer
}

export function ErrorMessage({ message, onRetry, chatId }: ErrorMessageProps) {
  const router = useRouter(); // Add router for navigation
  const [isRetryClicked, setIsRetryClicked] = useState(false);
  const [isLegacyRetryClicked, setIsLegacyRetryClicked] = useState(false);
  const isErrorMessage = Boolean(message.metadata?.error);
  if (!isErrorMessage) return null;
  
  const errorType = message.metadata?.error?.type;
  const showAlternateRetry = Boolean(message.metadata?.error?.showAlternateRetry);
  const uiMessage = message.metadata?.error?.uiMessage || "";
  const relatedUserMessageId = message.metadata?.error?.relatedUserMessageId;

  // Configure primary message based on error type
  let primaryMessage = "An error occurred while processing your request. Kindly retry your last message, or start a new chat. If the error persists, please contact <NAME_EMAIL>.";
  
  if (errorType === "USAGE_LIMIT_EXCEEDED") {
    primaryMessage = "You have reached a usage limit on your current plan. If you think this is a mistake, contact <NAME_EMAIL>.";
  } else if (errorType === "TOKEN_LIMIT_ERROR") {
    primaryMessage = "You have exhausted the token limit on this chat. Please start a new chat or use our 'Transfer Chat' feature.";
  }

  // Configure buttons based on error type
  const renderButtons = () => {
    // Upgrade Plan button
    const upgradeButton = (
      <Link href={UPGRADE_PLAN_URL}>
        <Button
          variant="outline"
          size="sm"
          className="self-start text-amber-600 dark:text-amber-400 border-amber-300 dark:border-amber-800 hover:bg-amber-100 dark:hover:bg-amber-900/30"
        >
          Upgrade Plan
        </Button>
      </Link>
    );

    // Retry button
    const retryButton = (
      <Button
        onClick={() => {
          if (onRetry) {
            onRetry(relatedUserMessageId);
            setIsRetryClicked(true);
          }
        }}
        variant="outline"
        size="sm"
        disabled={isRetryClicked}
        className="self-start text-amber-600 dark:text-amber-400 border-amber-300 dark:border-amber-800 hover:bg-amber-100 dark:hover:bg-amber-900/30"
      >
        Retry
      </Button>
    );

    // Retry with legacy model button
    const retryLegacyButton = (
      <Button
        onClick={() => {
          if (onRetry) {
            onRetry(relatedUserMessageId, true);
            setIsLegacyRetryClicked(true);
          }
        }}
        variant="outline"
        size="sm"
        disabled={isLegacyRetryClicked}
        className="self-start text-amber-600 dark:text-amber-400 border-amber-300 dark:border-amber-800 hover:bg-amber-100 dark:hover:bg-amber-900/30"
      >
        Retry with legacy model
      </Button>
    );

    // Transfer chat button - directly using router
    const transferChatButton = (
      <Button
        onClick={() => chatId && router.push(`/chat/transfer/${chatId}`)}
        variant="outline"
        size="sm"
        className="self-start text-amber-600 dark:text-amber-400 border-amber-300 dark:border-amber-800 hover:bg-amber-100 dark:hover:bg-amber-900/30"
      >
        Transfer Chat
      </Button>
    );

    // Return buttons based on error type
    switch (errorType) {
      case "USAGE_LIMIT_EXCEEDED":
        return <div className="flex flex-row gap-4">{upgradeButton}</div>;
      case "TOKEN_LIMIT_ERROR":
        return <div className="flex flex-row gap-4">{transferChatButton}</div>;
      default:
        return (
          <div className="flex flex-row gap-4">
            {retryButton}
            {showAlternateRetry && retryLegacyButton}
          </div>
        );
    }
  };

  return (
    <div className="w-full mt-2 p-4 rounded-md bg-amber-50 border border-amber-200 dark:bg-amber-900/20 dark:border-amber-800">
      <div className="flex flex-col gap-2">
        <p className="text-amber-600 dark:text-amber-400 text-sm">
          {primaryMessage}
        </p>
        {uiMessage && (
          <p className="text-amber-600 dark:text-amber-400 text-sm mt-2 border-t border-amber-200 dark:border-amber-800 pt-2">
            <strong>Details:</strong>{" "}
            {uiMessage}
          </p>
        )}
        
        {renderButtons()}
      </div>
    </div>
  );
}
