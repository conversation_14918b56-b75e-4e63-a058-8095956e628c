"use client";

import { memo } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { ChatRequestOptions, CreateMessage, Message } from "ai";
import { logEvent } from "@/lib/analytics/events-client";
import { GuideEvent } from "@/lib/analytics/event-types";
import {
  FileTextIcon,
  ShieldIcon,
  FileIcon,
  ClipboardIcon,
  BanIcon,
  Star,
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";

interface SuggestedActionsProps {
  chatId: string;
  setInput: (value: string) => void;
  submitForm: () => void;
}

function PureSuggestedActions({
  chatId,
  setInput,
  submitForm,
}: SuggestedActionsProps) {
  const suggestedActions = [
    {
      title: "Draft an NDA",
      description: "for hiring a contractor",
      action: "Draft a comprehensive NDA for hiring a contractor",
      icon: <ShieldIcon className="h-4 w-4 flex-shrink-0" />,
    },
    {
      title: "Create a policy",
      description: "for data privacy compliance",
      action:
        "Create a comprehensive data privacy policy that complies with GDPR and CCPA",
      icon: <FileTextIcon className="h-4 w-4 flex-shrink-0" />,
    },
    {
      title: "Write terms and conditions",
      description: "for a SaaS product",
      action: "Write comprehensive terms of service for a SaaS product",
      icon: <FileIcon className="h-4 w-4 flex-shrink-0" />,
    },
    {
      title: "Generate a contract",
      description: "for consulting services",
      action:
        "Generate a comprehensive consulting services contract with standard terms and conditions",
      icon: <ClipboardIcon className="h-4 w-4 flex-shrink-0" />,
    },
    {
      title: "Draft a cease and desist",
      description: "for intellectual property infringement",
      action:
        "Draft a formal cease and desist letter for intellectual property infringement",
      icon: <BanIcon className="h-4 w-4 flex-shrink-0" />,
    },
  ];

  return (
    <div className="flex flex-col gap-1 text-left">
      <div className="flex items-center gap-1">
        <div className="p-1 rounded-lg">
          <Star className="h-5 w-5 text-amber-400" />
        </div>
        <h3 className="text-lg font-medium text-[rgb(var(--base-navy))]">
          <span className="relative z-10">Quick Actions</span>
        </h3>
      </div>
      <div className="rounded-xl p-1 transition-shadow duration-200">
        <ul className="space-y-0.5 relative z-1 pr-6">
          {suggestedActions.map((suggestedAction, index) => (
            <li key={`suggested-action-${suggestedAction.title}-${index}`}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    onClick={async () => {
                      window.history.replaceState({}, "", `/chat/${chatId}`);
                      logEvent(GuideEvent.SUGGESTED_ACTION, {
                        action: suggestedAction.action,
                      });

                      // Set the input value to the suggested action
                      setInput(suggestedAction.action);

                      // Use requestAnimationFrame to ensure state is updated before submitting
                      requestAnimationFrame(() => {
                        // Trigger the form submission
                        submitForm();
                      });
                    }}
                    className="justify-start w-full py-1.5 px-2 h-auto flex items-center gap-2 rounded-md ml-[22px]"
                  >
                    <div className="text-[rgb(var(--base-navy))]">
                      {suggestedAction.icon}
                    </div>
                    <span className="text-sm truncate max-w-[80%]">
                      {suggestedAction.title}
                    </span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent
                  side="left"
                  className="max-w-[180px] whitespace-normal"
                >
                  {suggestedAction.description}
                </TooltipContent>
              </Tooltip>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);
