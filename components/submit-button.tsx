"use client";

import { useFormStatus } from "react-dom";

import { LoaderIcon } from "@/components/icons";

import { Button } from "./ui/button";

export function SubmitButton({
  children,
  isSuccessful,
  disabled: externallyDisabled = false,
  className = "",
}: {
  children: React.ReactNode;
  isSuccessful: boolean;
  disabled?: boolean;
  className?: string;
}) {
  const { pending } = useFormStatus();
  const isDisabled = pending || isSuccessful || externallyDisabled;

  return (
    <Button
      type={pending ? "button" : "submit"}
      aria-disabled={isDisabled}
      disabled={isDisabled}
      className={`relative transition-all duration-200 disabled:opacity-50 ${className || "navy-button bg-indigo-600 hover:bg-indigo-700 text-white text-base font-medium border border-indigo-500/50 shadow-[0_2px_10px_rgba(99,102,241,0.25)] h-12 rounded-lg mt-2"}`}
    >
      {children}

      {(pending || isSuccessful) && (
        <span className="animate-spin absolute right-4">
          <LoaderIcon />
        </span>
      )}

      <output aria-live="polite" className="sr-only">
        {pending || isSuccessful ? "Loading" : "Submit form"}
      </output>
    </Button>
  );
}
