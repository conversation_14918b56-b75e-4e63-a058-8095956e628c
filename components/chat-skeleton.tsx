import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";

export function ChatSkeleton() {
  return (
    <div className="flex flex-col min-h-screen mx-4 sm:mx-8 md:mx-16 lg:mx-32 xl:mx-80 mt-8 sm:mt-16 md:mt-24 lg:mt-32 xl:mt-40">
      {/* Messages Container */}
      <div className="flex-1 space-y-6 p-4 overflow-y-auto">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="space-y-6">
            {/* AI Message */}
            <div className="flex items-start gap-4">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="space-y-2 w-full max-w-2xl">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-24 w-full rounded-lg" />
                  <div className="flex gap-2">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </div>
            </div>

            {/* User Message */}
            <div className="flex items-start gap-4 justify-end">
              <div className="space-y-2 w-full max-w-2xl">
                <div className="flex items-center gap-2 justify-end">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-20" />
                </div>
                <Skeleton className="h-16 w-full rounded-lg" />
              </div>
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
          </div>
        ))}
      </div>

      {/* Input Area */}
      <div className="fixed inset-x-0 bottom-0">
        <div className="bg-gradient-to-t from-background from-50% to-transparent h-36 pointer-events-none" />
        <div className="bg-background border-t p-4">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 md:px-8">
            <div className="flex items-end gap-4">
              <div className="flex-1 space-y-4">
                <Skeleton className="h-12 w-full rounded-lg" />
                <div className="flex justify-between">
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-6" />
                    <Skeleton className="h-6 w-6" />
                  </div>
                  <Skeleton className="h-6 w-6" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ChatSkeleton;
