"use client";

import { useState, useEffect, useRef } from "react";
import { 
  PinIcon, 
  ChevronDownIcon, 
  ChevronRightIcon, 
  TagIcon, 
  ArrowDownIcon, 
  ArrowUpIcon, 
  ArrowUpDownIcon  // Import the new icon
} from "lucide-react";
import { ChatWithTags as Chat, Tag } from "@/lib/db/schema";
import { useDrag, useDrop } from "react-dnd";
import { ItemTypes } from "@/lib/dnd-types";
import { useParams } from "next/navigation";
import { ChatItem } from "@/components/sidebar-history"; // Import ChatItem
import { useTagManagement } from "@/hooks/use-tag-management";
import { TagManagementDialog } from "@/components/tag-management-dialog";
import { mutate } from 'swr';
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useIsMobile } from "@/hooks/use-mobile";

// Add new types for sorting
type SortOption = "alphabetical" | "lastModified";
type SortDirection = "asc" | "desc";

interface PinnedChatsProps {
  chats: Chat[];
  onUnpin: (chatId: string) => void;
  onReorderPins: (chatIds: string[]) => void;
  setOpenMobile: (open: boolean) => void;
  onDelete: (chatId: string) => void; // Add delete handler
  onBulkDelete: () => void; // Add bulk delete handler
  onPinChat: (chatId: string) => void; // Add this prop
  viewMode: "all" | "pinned" | "matters";
  setViewMode: (mode: "all" | "pinned" | "matters") => void;
}

type DragItem = {
  type: string;
  id: string;
  index?: number;
  source?: "history" | "pinned" | "folder";
  folderId?: string;
};

// We'll create a wrapper component that adds drag-and-drop functionality
const DraggableChatItem = ({ 
  chat, 
  index, 
  isActive, 
  onUnpin, 
  onPinChat,
  moveChat, 
  setOpenMobile,
  pinnedChats,
  onDelete,
  onBulkDelete,
  onManageTags
}: { 
  chat: Chat; 
  index: number;
  isActive: boolean; 
  onUnpin: (chatId: string) => void;
  onPinChat: (chatId: string) => void;
  moveChat: (dragIndex: number, hoverIndex: number) => void;
  setOpenMobile: (open: boolean) => void;
  pinnedChats: any[];
  onDelete: (chatId: string) => void;
  onBulkDelete: () => void;
  onManageTags?: () => void;
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [canPinDrag, setCanPinDrag] = useState(true);
  
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.PINNED_CHAT,
    item: { 
      type: ItemTypes.PINNED_CHAT, 
      id: chat.id, 
      index,
      source: "pinned" 
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  
  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.PINNED_CHAT, ItemTypes.CHAT, ItemTypes.FOLDER_CHAT],
    hover: (item: DragItem, monitor) => {
      if (!ref.current) {
        return;
      }
      
      // Only handle reordering for pinned chats
      if (item.type !== ItemTypes.PINNED_CHAT) {
        return;
      }
      
      const dragIndex = item.index;
      const hoverIndex = index;
      
      if (dragIndex === undefined || hoverIndex === dragIndex) {
        return;
      }
      
      moveChat(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });
  
  drag(drop(ref));
  
  return (
    <div
      ref={ref as any}
      className={`${isDragging ? "opacity-50" : "opacity-100"} ${
        isOver ? "bg-sidebar-accent/30" : ""
      }`}
      style={{ cursor: "move" }}
      draggable={canPinDrag}
    >
      <ChatItem
        chat={chat}
        isActive={isActive}
        onDelete={onDelete}
        onPinChat={onPinChat}
        onUnpinChat={onUnpin}
        setOpenMobile={setOpenMobile}
        onBulkDelete={onBulkDelete}
        pinnedChats={pinnedChats}
        onManageTags={onManageTags}
        setCanPinDrag={setCanPinDrag}
      />
    </div>
  );
};

export function SidebarPinnedChats({ 
  chats, 
  onUnpin, 
  onReorderPins,
  setOpenMobile,
  onDelete,
  onBulkDelete,
  onPinChat,
  viewMode,
  setViewMode
}: PinnedChatsProps) {
  const { id } = useParams();
  const [pinnedChats, setPinnedChats] = useState<Chat[]>(chats);
  const pinnedSectionRef = useRef<HTMLDivElement>(null);
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // Maximum number of pinned chats to show initially
  const MAX_VISIBLE_CHATS = 5;
  
  // Add sorting state
  const [sortOption, setSortOption] = useState<SortOption>("lastModified");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const isMobile = useIsMobile();
  
  // Add tag management hook
  const { 
    isDialogOpen, 
    currentChat, 
    openTagDialog, 
    closeTagDialog, 
    handleSaveTags,
    isLoading: isTagsLoading
  } = useTagManagement({
    onUpdateTags: async (chatId, tags) => {
      // Update local state
      setPinnedChats(prevChats => 
        prevChats.map(chat => 
          chat.id === chatId ? { ...chat, tags } : chat
        )
      );
      
      // Trigger global revalidation to update all components
      mutate("/api/chat-org/sidebar-data");
    }
  });
  
  // Function to switch to pinned view mode
  const handleSeeMoreClick = () => {
    setViewMode("pinned");
  };
  
  useEffect(() => {
    setPinnedChats(chats);
  }, [chats]);
  
  // Add sorting function
  const sortChats = (chatsToSort: Chat[]): Chat[] => {
    const sorted = [...chatsToSort];
    
    if (sortOption === "alphabetical") {
      sorted.sort((a, b) => {
        const titleA = a.title?.toLowerCase() || '';
        const titleB = b.title?.toLowerCase() || '';
        return sortDirection === "asc" 
          ? titleA.localeCompare(titleB)
          : titleB.localeCompare(titleA);
      });
    } else if (sortOption === "lastModified") {
      sorted.sort((a, b) => {
        const dateA = new Date(a.updatedAt || a.createdAt).getTime();
        const dateB = new Date(b.updatedAt || b.createdAt).getTime();
        return sortDirection === "asc" 
          ? dateA - dateB 
          : dateB - dateA;
      });
    }
    
    return sorted;
  };
  
  // Apply sorting when sort options change
  useEffect(() => {
    if (pinnedChats.length > 0) {
      const sortedChats = sortChats(pinnedChats);
      setPinnedChats(sortedChats);
      onReorderPins(sortedChats.map(chat => chat.id));
    }
  }, [sortOption, sortDirection]);
  
  const moveChat = (dragIndex: number, hoverIndex: number) => {
    const dragChat = pinnedChats[dragIndex];
    const newPinnedChats = [...pinnedChats];
    newPinnedChats.splice(dragIndex, 1);
    newPinnedChats.splice(hoverIndex, 0, dragChat);
    
    setPinnedChats(newPinnedChats);
    onReorderPins(newPinnedChats.map(chat => chat.id));
  };
  
  // Add drop functionality to the pinned section
  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.CHAT, ItemTypes.FOLDER_CHAT],
    drop: (item: DragItem) => {
      if (item.source !== "pinned") {
        onPinChat(item.id);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });
  
  // Create a structure that matches what ChatItem expects
  const pinnedChatsFormatted = pinnedChats.map(chat => ({
    chatId: chat.id,
    chat
  }));
  
  drop(pinnedSectionRef);
  
  return (
    <div className="mb-4" ref={pinnedSectionRef}>
      <div
        className={`px-1 py-1 text-xs font-semibold text-sidebar-foreground/70 flex items-center justify-between ${
          isOver ? "bg-sidebar-accent/30 rounded" : ""
        }`}
      >
        <div
          className="flex items-center gap-1 cursor-pointer flex-grow"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          {/* <PinIcon className="h-3 w-3" /> */}
          <span>PINNED</span>
        </div>
        <div className="flex items-center mr-[-2px]">
          {/* Sort dropdown button*/}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="h-5 w-5 mr-1 flex items-center justify-center text-sidebar-foreground/90 hover:text-sidebar-foreground">
                <ArrowUpDownIcon className="h-3.5 w-3.5" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <div className="px-2 py-1.5 text-xs font-semibold">Sort by</div>
              <DropdownMenuItem
                className="text-xs cursor-pointer flex justify-between"
                onClick={() => setSortOption("alphabetical")}
              >
                <span>Alphabetical (A-Z)</span>
                {sortOption === "alphabetical" && <span>✓</span>}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-xs cursor-pointer flex justify-between"
                onClick={() => setSortOption("lastModified")}
              >
                <span>Last Modified</span>
                {sortOption === "lastModified" && <span>✓</span>}
              </DropdownMenuItem>

              <div className="px-2 py-1.5 text-xs font-semibold mt-1">
                Direction
              </div>
              <DropdownMenuItem
                className="text-xs cursor-pointer flex justify-between"
                onClick={() => setSortDirection("asc")}
              >
                <span className="flex items-center gap-1">
                  <ArrowUpIcon className="h-3 w-3" />
                  Ascending
                </span>
                {sortDirection === "asc" && <span>✓</span>}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-xs cursor-pointer flex justify-between"
                onClick={() => setSortDirection("desc")}
              >
                <span className="flex items-center gap-1">
                  <ArrowDownIcon className="h-3 w-3" />
                  Descending
                </span>
                {sortDirection === "desc" && <span>✓</span>}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Collapse button */}
          <button
            className="h-5 w-5 flex items-center justify-center text-sidebar-foreground/90 hover:text-sidebar-foreground"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? (
              <ChevronRightIcon className="h-3.5 w-3.5" />
            ) : (
              <ChevronDownIcon className="h-3.5 w-3.5" />
            )}
          </button>
        </div>
      </div>

      {!isCollapsed &&
        (pinnedChats.length === 0 ? (
          <div
            className={`px-2 py-2 text-xs text-sidebar-foreground/50 italic ${
              isOver ? "bg-sidebar-accent/30 rounded" : ""
            }`}
          >
            Pinned chats will appear here
          </div>
        ) : (
          <div className="space-y-1">
            {/* Show only the first MAX_VISIBLE_CHATS chats when not in pinned view mode */}
            {(viewMode === "pinned"
              ? pinnedChats
              : pinnedChats.slice(0, MAX_VISIBLE_CHATS)
            ).map((chat, index) => (
              <DraggableChatItem
                key={chat.id}
                chat={chat}
                index={index}
                isActive={chat.id === id}
                onUnpin={onUnpin}
                onPinChat={onPinChat}
                moveChat={moveChat}
                setOpenMobile={setOpenMobile}
                pinnedChats={pinnedChatsFormatted}
                onDelete={onDelete}
                onBulkDelete={onBulkDelete}
                onManageTags={() => openTagDialog(chat)}
              />
            ))}

            {/* Add a visual separator when we have exactly MAX_VISIBLE_CHATS chats */}
            {viewMode !== "pinned" &&
              pinnedChats.length <= MAX_VISIBLE_CHATS && (
                <div className="h-[1px] bg-gray-200 dark:bg-gray-800 mx-2 my-1"></div>
              )}

            {/* Show "See more" button if there are more than MAX_VISIBLE_CHATS and not in pinned view */}
            {viewMode !== "pinned" &&
              pinnedChats.length > MAX_VISIBLE_CHATS && (
                <div
                  className="p-2 text-xs text-center text-muted-foreground bg-muted/50 border-t border-gray-100 dark:border-gray-800 flex items-center justify-center gap-1 rounded-b-md cursor-pointer hover:bg-muted"
                  onClick={handleSeeMoreClick}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M7 13l5 5 5-5M7 6l5 5 5-5" />
                  </svg>
                  See more
                </div>
              )}
          </div>
        ))}

      {/* Add tag management dialog */}
      {isDialogOpen && (
        <TagManagementDialog
          open={isDialogOpen}
          onOpenChange={(open) => {
            if (!open) closeTagDialog();
          }}
          chat={currentChat}
          allChats={pinnedChats}
          onSave={handleSaveTags}
          isLoading={isTagsLoading}
          lightweightDialog={!isMobile}
          useDialog={isMobile}
          anchorElement={document.activeElement as HTMLElement}
        />
      )}
    </div>
  );
}

