import type { ComponentProps } from "react";

import { type SidebarTrigger, useSidebar } from "@/components/ui/sidebar";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";

import { SidebarToggleIcon } from "./icons";
import { Button } from "./ui/button";

export function SidebarToggle({
  className,
}: ComponentProps<typeof SidebarTrigger>) {
  const { toggleSidebar, open } = useSidebar();

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            onClick={toggleSidebar}
            variant="outline"
            className={`md:px-2 px-[10px] md:h-fit ${
              open
                ? "bg-sidebar-accent hover:bg-sidebar-accent/80"
                : "hover:bg-sidebar-accent/50"
            } transition-all duration-500`}
            style={{
              transform: open ? "scale(1)" : "scale(0.95)",
              transition: "all 500ms cubic-bezier(0.18, 0.89, 0.32, 1.28)",
            }}
          >
            <div
              style={{
                transform: open ? "rotate(0deg)" : "rotate(180deg)",
                transition:
                  "transform 500ms cubic-bezier(0.18, 0.89, 0.32, 1.28)",
                color: open
                  ? "hsl(var(--sidebar-accent-foreground))"
                  : "currentColor",
              }}
            >
              <SidebarToggleIcon size={108} />
            </div>
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom" sideOffset={5} className="z-[9999]">
          Toggle Sidebar
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
