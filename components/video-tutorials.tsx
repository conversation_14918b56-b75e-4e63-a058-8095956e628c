"use client";

import React from "react";
import { memo, useState } from "react";
import { But<PERSON> } from "./ui/button";
import { PlayCircle, Sparkles, ChevronDown, ChevronUp } from "lucide-react";
import { VideoModal } from "./video-modal";
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { logEvent } from "@/lib/analytics/events-client";
import { GuideEvent } from "@/lib/analytics/event-types";
import { useRouter } from "next/navigation";
import { PageLoader } from "@/components/ui/page-loader";

interface VideoTutorialsProps {
  chatId: string;
}

function PureVideoTutorials({ chatId }: VideoTutorialsProps) {
  const router = useRouter();
  const [isVideoModalOpen, setVideoModalOpen] = useState(false);
  const [currentVideo, setCurrentVideo] = useState({ url: "", title: "" });
  const [isExpanded, setIsExpanded] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  const videoTutorials = [
    {
      title: "Handling Matter Chats",
      description: "Learn how to manage your conversations",
      videoUrl: "https://app.storylane.io/share/asdjgovqhdez",
    },
    {
      title: "Researching",
      description: "How to use research capabilities",
      videoUrl: "https://app.storylane.io/share/yqhfydvw8l20",
    },
    {
      title: "User Preferences",
      description: "Customize your experience",
      videoUrl: "https://app.storylane.io/share/z7owg42armgm",
    },
    {
      title: "Requesting Features & Reporting Bugs",
      description: "How to provide feedback",
      videoUrl: "https://app.storylane.io/share/ru6jieq58ami",
    },
    {
      title: "Uploading Documents",
      description: "How to upload and analyze files",
      videoUrl: "https://app.storylane.io/share/dx4opekp1rut",
    },
    {
      title: "Using Playbook & Prompt Architect",
      description: "How to create custom prompts",
      videoUrl: "https://app.storylane.io/share/lvwfcbrcjxfi",
    },
    {
      title: "Using Amplify for Better Prompts",
      description: "How to enhance your prompts",
      videoUrl: "https://app.storylane.io/share/nm4swgkkmcym",
    },
    {
      title: "Transferring Long Chats",
      description: "How to split long conversations",
      videoUrl: "https://app.storylane.io/share/bslr88bvdksk",
    },
    {
      title: "Switching AI Models",
      description: "How to change AI models",
      videoUrl: "https://app.storylane.io/share/zkrebaswjw7v",
    },
  ];

  // Show only first 4 tutorials when collapsed
  const displayedTutorials = isExpanded ? videoTutorials : videoTutorials.slice(0, 4);

  const openVideoTutorial = (videoUrl: string, title: string) => {
    logEvent(GuideEvent.VIDEO_TUTORIAL_CLICK, { title, videoUrl });
    setCurrentVideo({ url: videoUrl, title });
    setVideoModalOpen(true);
  };

  const navigateToGuidesPage = () => {
    setIsNavigating(true);
    logEvent(GuideEvent.GUIDES_PAGE_CLICK, { chatId });
    router.push("/guides");
  };

  return (
    <>
      {isNavigating && <PageLoader message="Loading guides..." />}
      
      <div className="flex flex-col gap-2 text-left z-[999099999]">
        <div className="flex items-center gap-1">
          <div className="p-1 rounded-lg">
            <Sparkles className="h-5 w-5 text-amber-400" />
          </div>
          <h3 className="text-lg font-medium text-[rgb(var(--base-navy))]">
            <span className="relative z-10">Getting Started</span>
          </h3>
        </div>
        <div className="rounded-xl p-1 transition-shadow duration-200">
          <ul className={`space-y-0.5 relative pr-6 ${isExpanded ? "max-h-[220px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600" : ""}`}>
            {displayedTutorials.map((tutorial, index) => (
              <li key={`video-tutorial-${tutorial.title}-${index}`}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      type="button"
                      onClick={() =>
                        openVideoTutorial(tutorial.videoUrl, tutorial.title)
                      }
                      className="justify-start w-full py-1.5 px-2 h-auto flex items-center gap-2 rounded-md ml-[22px]"
                    >
                      <PlayCircle className="h-4 w-4 flex-shrink-0 text-[rgb(var(--base-navy))]" />
                      <span className="text-sm truncate max-w-[80%]">
                        {tutorial.title}
                      </span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="right"
                    className="max-w-[180px] whitespace-normal"
                  >
                    {tutorial.description}
                  </TooltipContent>
                </Tooltip>
              </li>
            ))}
          </ul>
          
          {videoTutorials.length > 4 && (
            <Button
              variant="ghost"
              onClick={navigateToGuidesPage}
              className="text-xs text-muted-foreground w-full flex items-center justify-center mt-1 py-1"
            >
              <span>View all guides</span>
              <ChevronDown className="h-3 w-3 ml-1 transform rotate-[-90deg]" />
            </Button>
          )}
        </div>
      </div>

      <VideoModal
        isOpen={isVideoModalOpen}
        onOpenChange={setVideoModalOpen}
        videoUrl={currentVideo.url}
        title={currentVideo.title}
      />
    </>
  );
}

export const VideoTutorials = memo(PureVideoTutorials, () => true);
