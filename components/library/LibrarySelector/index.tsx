"use client"

import { use<PERSON><PERSON>, use<PERSON>et<PERSON><PERSON> } from "jotai"
import { Config<PERSON><PERSON>ider, Mo<PERSON>, theme } from 'antd'
import { useTheme } from 'next-themes'
import { LibraryFilterAtom, LibrarySelectorDialogAtom, PathTitleMap } from "./store"
import { ChevronLeft } from "lucide-react"
import Home from "./views/Home"
import UploadView  from "./views/Upload"
import LibraryView from "./views/Library"
import LibraryFolderView from "./views/LibraryFolder"
import RecentView from "./views/Recent"

import type { Dispatch, SetStateAction } from "react"
import type {
  Attachment,
} from "ai";

import './style.css'
import { useVirtualPath } from "./hooks";
import { useDeepCompareEffect } from "ahooks";
import { getFolders } from "../request/folders";
import { useRequest } from "ahooks";

function ModalTitle() {
  const { theme: themeMode } = useTheme()
  const { path: viewVirtualPath, setPath } = useVirtualPath()

  return (<span className={`flex items-center text-lg font-semibold ${themeMode === 'dark' ? 'text-white' : 'text-main'}`}>
    {viewVirtualPath !== '' && (
      <ChevronLeft className={`size-6 cursor-pointer ${themeMode === 'dark' ? 'text-white' : 'text-main'} hover:bg-main/10 rounded-sm mr-1`}
        onClick={() => {
          setPath(viewVirtualPath.split('/').slice(0, -1).join('/'))
        }}
      />
    )}
    {PathTitleMap[viewVirtualPath]}
  </span>)
}

interface LibrarySelectorModalProps {
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
}

function LibrarySelectorModal(props: LibrarySelectorModalProps) {
  const [dialog, setDialog] = useAtom(LibrarySelectorDialogAtom)
  const { theme: themeMode } = useTheme()

  const closeDialog = () => {
    setDialog({ open: false, viewVirtualPath: '' })
  }

  const { data: { folders = [], rootFolder } = {} } = useRequest(() => {
    const queryString = new URLSearchParams({
      status: 'AVAILABLE',
    })
    return getFolders(queryString.toString())
  }, {})

  const setLibraryFilter = useSetAtom(LibraryFilterAtom)

  useDeepCompareEffect(() => {
    setLibraryFilter(prev => {
      return {
        ...prev,
        folders: folders.map(folder => ({
          folderId: folder.folderId,
          folderName: folder.folderName,
        })),
        rootFolderId: rootFolder?.folderId,
        currentFolderId: rootFolder?.folderId,
      }
    })
  }, [folders, rootFolder])

  return (
    <ConfigProvider theme={{
      algorithm: themeMode === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
    }}>
    <Modal
      open={dialog.open}
      title={<ModalTitle />}
      className="library-selector-modal"
      width={{
        xs: '100%',
        sm: '100%',
        md: '100%',
        lg: 800,
        xl: 900,
        xxl: 1000,
      }}
      maskClosable={false}
      centered
      footer={null}
      onCancel={closeDialog}
    >
      {dialog.viewVirtualPath === '' && <Home />}
      {dialog.viewVirtualPath === '/upload' && <UploadView
        closeDialog={closeDialog}
        attachments={props.attachments}
        setAttachments={props.setAttachments}
      />}
      {dialog.viewVirtualPath === '/library' && <LibraryView />}
      {dialog.viewVirtualPath === '/library/folder' && <LibraryFolderView
        attachments={props.attachments}
        setAttachments={props.setAttachments}
      />} 
      {dialog.viewVirtualPath === '/recent' && <RecentView
        attachments={props.attachments}
        setAttachments={props.setAttachments}
      />}
    </Modal>
    </ConfigProvider>
  )
}

export const useLibrarySelector = (optipns: LibrarySelectorModalProps) => {
  const [dialog, setDialog] = useAtom(LibrarySelectorDialogAtom)
  return {
    open: dialog.open,
    openDialog: () => {
      setDialog({ ...dialog, open: true })
    },
    closeDialog: () => {
      setDialog({ ...dialog, open: false })
    },
    LibrarySelectorModal: <LibrarySelectorModal {...optipns}/>,
  }
}