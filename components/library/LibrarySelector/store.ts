import { atom } from "jotai";

export const PathTitleMap: Record<string, string> = {
  '': 'Attach Files',
  '/library': 'Select Source',
  '/recent': 'Recent & Favorite Files',
  '/upload': 'Upload Files from Device',
  '/library/folder': 'Select Files from Library'
}

export const LibrarySelectorDialogAtom = atom<{
  open: boolean
  viewVirtualPath: '' | '/library' | '/recent' | '/upload' | '/library/folder' | string
}>({
  open: false,
  viewVirtualPath: '',
})

export const LibraryFilterAtom = atom<{
  folders: Array<{
    folderId: string
    folderName: string
  }>,
  rootFolderId?: string
  currentFolderId?: string
}>({
  folders: [],
})