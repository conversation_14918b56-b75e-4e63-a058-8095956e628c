import { useRequest } from "ahooks"
import { getFolders } from "../../request/folders"
import { Database, Folder } from "lucide-react"
import { useVirtualPath } from "../hooks"
import { Empty, Spin } from "antd"
import { LibraryFilterAtom } from "../store"
import { useAtom } from "jotai"
import { useTheme } from "next-themes"

export default function LibraryView() {
  const { setPath, path } = useVirtualPath()
  const { theme: themeMode } = useTheme()
  const { data: { folders = [], rootFolder, rootFolderId } = {}, loading } = useRequest(() => {
    const queryString = new URLSearchParams({
      status: 'AVAILABLE',
    })
    return getFolders(queryString.toString())
  }, {
    ready: path === '/library/folder' || path === '/library',
    cacheKey: 'library-folders',
    cacheTime: 10000,
  })
  const [, setLibraryFilter] = useAtom(LibraryFilterAtom)

  return (
    <Spin spinning={loading}>
      <div className="flex flex-col gap-3 py-1 min-h-[234px] max-h-[calc(100vh-250px)] overflow-y-auto md:px-6 px-3">
        <div className={`flex cursor-pointer gap-3 ${themeMode === 'dark' ? 'hover:bg-gray-800 border-gray-700' : 'hover:bg-gray-100 border-gray-200'} py-2 px-3 rounded-md border items-center`} onClick={() => {
          setPath('/library/folder')
          setLibraryFilter(prev => {
            return {
              ...prev,
              currentFolderId: rootFolderId,
            }
          })
        }}>
          <Database className="size-4"/>
          <div className="flex flex-col gap-1 flex-1">
            <div className={`text-sm font-medium ${themeMode === 'dark' ? 'text-white' : 'text-gray-700'}`}>All Documents</div>
            <div className="text-xs text-gray-500 font-medium">Browse entire library</div>
          </div>
          <div className="text-sm text-gray-500 font-medium">{rootFolder?.documentCount ?? '-'} file{+rootFolder?.documentCount === 1 ? '' : 's'}</div>
        </div>
        <div className="text-xs text-gray-500 font-medium">FOLDERS</div>
        {folders?.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No folders found"
            className="mt-8"
          />
        ) : (
          folders?.map((folder) => (
            <div key={folder.folderId} className={`flex cursor-pointer gap-3 ${themeMode === 'dark' ? 'hover:bg-gray-800 border-gray-700' : 'hover:bg-gray-100 border-gray-200'} py-2 px-3 rounded-md border items-start`} onClick={() => {
              setPath('/library/folder')
              setLibraryFilter(prev => {
                return {
                  ...prev,
                  currentFolderId: folder.folderId,
                }
              })
            }}>
              <div className="mt-1">
              <Folder className="size-4 text-function-message"/></div>
              <div className="flex flex-col gap-1 flex-1">
                <div className={`text-sm font-medium ${themeMode === 'dark' ? 'text-white' : 'text-gray-700'}`}>{folder.folderName}</div>
                <div className="text-xs text-gray-500 font-medium">{folder.folderDescription || 'No description'}</div>
              </div>
              <div className="text-sm text-gray-500 font-medium">{folder.documentCount} file{+folder.documentCount === 1 ? '' : 's'}</div>
            </div>
          ))
        )}
      </div>
    </Spin>
  )
}