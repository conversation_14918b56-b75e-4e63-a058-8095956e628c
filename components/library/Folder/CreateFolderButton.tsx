import { But<PERSON> } from "antd";
import React from "react";
import { FolderPlus } from "lucide-react";
import { FolderDialogAtom } from "./store";
import { useSet<PERSON>tom } from "jotai";

export function CreateFolderButton(props: {
  mode?: 'create' | 'edit'
  folderId?: string
  folderName?: string
  folderDescription?: string
  children?: React.ReactElement
  text?: string
}) {

  const setModalStatus = useSetAtom(FolderDialogAtom)

  if (props.children) {
    return React.cloneElement(props.children, {
      onClick: () => {
        setModalStatus(prev => {
          return {
            ...prev,
            open: true,
            mode: props.mode === 'edit' ? 'edit' : 'create',
            id: props.folderId,
            folderName: props.folderName,
            folderDescription: props.folderDescription,
          }
        })
      }
    })
  }

  return <Button color="default" className="dark:bg-card dark:hover:bg-background newFolderCreateBtn" variant="outlined" onClick={() => {
    setModalStatus(prev => {
      return {
        ...prev,
        open: true
      }
    })
  }}><FolderPlus className="size-4 text-gray-medium dark:text-white "/><span className="hidden sm:block dark:text-white">{props.text || 'New Folder'}</span></Button>  
}