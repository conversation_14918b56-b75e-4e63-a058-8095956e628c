import { useRequest, useSize } from "ahooks";
import { useState } from "react";
import { downloadDocuments } from "../utils/document";
import { toast } from "sonner";
import { deleteDocuments, moveDocuments } from "../request/document";

export function useRefresh() {
  const [refreshFlag, setRefreshFlag] = useState(0)
  return {
    refreshFlag,
    refresh: () => {
      setRefreshFlag(prev => prev + 1)
    }
  }
}

export function useDownloadDocuments() {
  const { loading: downloadDocumentsLoading, runAsync: downloadDocumentsRequest } = useRequest(async (ids: string[]) => {
    return downloadDocuments(ids)
  }, {
    manual: true,
    onSuccess: () => {
      toast.success('Document(s) downloaded successfully')
    },
    onError: () => {
      toast.error('Failed to download document(s)')
    },
  })

  return {
    downloadDocumentsLoading,
    downloadDocumentsRequest,
  }
}

export function useDeleteDocuments() {
  const { loading: deleteDocumentsLoading, runAsync: deleteDocumentsRequest } = useRequest(async (ids: string[]) => {
    return deleteDocuments(ids)
  }, {
    manual: true,
    onSuccess: () => {
      toast.success('Document(s) deleted successfully')
    },
    onError: () => {
      toast.error('Failed to delete document(s)')
    },
  })

  return {
    deleteDocumentsLoading,
    deleteDocumentsRequest,
  }
}

export function useMoveDocuments() {
  const { loading: moveDocumentsLoading, runAsync: moveDocumentsRequest } = useRequest(async (documentIds: string[], folderId: string) => {
    return moveDocuments(documentIds, folderId)
  }, {
    manual: true,
    onSuccess: (data) => {
      if (data.error) {
        toast.error(data.error)
        throw new Error(data.error)
      } else {
        toast.success('Document(s) moved successfully')
      }
    },
    onError: () => {
      toast.error('Failed to move document(s)')
    },
  })
  
  return {
    moveDocumentsLoading,
    moveDocumentsRequest,
  }
}

export function useTableScrollY(options: {
  container: HTMLElement | null,
  offset: number
}) {
  const { container, offset } = options

  const size = useSize(container) 
  const containerHeight = (size?.height ?? container?.offsetHeight ?? 500) - offset

  return {
    y: containerHeight,
  }
}