

import type {
  Attachment,
} from "ai";
import { executeWithConcurrency } from "@/lib/utils/retry";
import { Logger } from "@/lib/utils/Logger";
import { genSignature } from "@/lib/utils/auth";
import { retryOperation } from "@/lib/utils/retry";

export const uploadFileSecure = async (
  file: File,
  chatId?: string
): Promise<Attachment | undefined> => {
  try {
    Logger.info("Client: Starting protected upload", {
      name: file.name,
      size: file.size,
    });

    // Step 1: Request presigned URL from your API
    const { timestamp, signature } = genSignature();
    Logger.debug("Generated signature", {
      timestamp,
      signaturePrefix: signature.substring(0, 10) + "...",
    });

    Logger.debug("Sending prepare request to API", {
      endpoint: "/api/files/prepare",
      fileType: file.type,
      fileSize: file.size,
      multipart: file.size > 10 * 1024 * 1024,
    });

    const prepareResponse = await fetch(`/api/files/prepare`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: signature,
        "x-requested-time": timestamp.toString(),
      },
      body: JSON.stringify({
        files: [
          {
            mime: file.type,
            metadata: {
              name: file.name,
              size: file.size,
            },
            multipart: file.size > 10 * 1024 * 1024, // Using multipart for files > 10MB
          },
        ],
        channel: "core",
        partitionId: "core",
        chatId,
      }),
    });

    if (!prepareResponse.ok) {
      const errorText = await prepareResponse
        .text()
        .catch(() => "Failed to get error text");
      Logger.error("Prepare request failed", {
        status: prepareResponse.status,
        statusText: prepareResponse.statusText,
        errorText,
      });
      throw new Error(
        `Failed to get presigned URL: ${prepareResponse.status} ${prepareResponse.statusText}`
      );
    }

    const responseData = await prepareResponse.json();
    Logger.debug("Received prepare response", { responseData });

    const { data } = responseData;
    if (!data || !data[0]) {
      Logger.error("Invalid prepare response format", { responseData });
      throw new Error("Invalid response format from prepare endpoint");
    }

    // Handle multipart upload response format
    if (data[0].multipart) {
      const { documentId, bucket, key, uploadId } = data[0];
      Logger.debug("Extracted multipart upload details", {
        documentId,
        bucket,
        key,
        uploadId: uploadId.substring(0, 10) + "...",
      });

      // Upload process for multipart uploads
      const CHUNK_SIZE = 9 * 1024 * 1024;
      const chunks = Math.ceil(file.size / CHUNK_SIZE);
      const parts = new Array(chunks);

      Logger.info("Starting multipart upload", {
        documentId,
        chunks,
        fileSize: file.size,
      });

      // Create an array of upload functions - one for each part
      const uploadFunctions = Array.from({ length: chunks }, (_, i) => {
        const partNumber = i + 1;

        // Return a function that will be executed with concurrency control
        return async () => {
          try {
            Logger.debug(`Preparing to upload part ${partNumber}/${chunks}`, { documentId });

            // Get presigned URL for this part
            const partResponse = await fetch("/api/files/upload-part", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ bucket, key, uploadId, partNumber }),
            });

            if (!partResponse.ok) {
              throw new Error(`Failed to get part URL: ${partResponse.status}`);
            }

            const { url: partUrl } = await partResponse.json();

            // Calculate chunk boundaries
            const start = (partNumber - 1) * CHUNK_SIZE;
            const end = Math.min(start + CHUNK_SIZE, file.size);
            const chunk = file.slice(start, end);

            Logger.debug(`Uploading part ${partNumber}/${chunks}`, {
              documentId,
              size: chunk.size,
              start,
              end
            });

            // Upload the chunk with retry logic
            const uploadResult = await retryOperation(
              async () => {
                const uploadResponse = await fetch(partUrl, {
                  method: "PUT",
                  body: chunk
                });

                if (!uploadResponse.ok) {
                  throw new Error(`Failed to upload part: ${uploadResponse.status}`);
                }

                const etag = uploadResponse.headers.get("ETag");
                if (!etag) {
                  throw new Error("No ETag received for uploaded part");
                }

                Logger.debug(`Successfully uploaded part ${partNumber}/${chunks}`, { documentId });
                return { ETag: etag, PartNumber: partNumber };
              },
              3 // Max retries
            );

            parts[partNumber-1] = uploadResult;
            return uploadResult;
          } catch (error) {
            Logger.error(`Failed to upload part ${partNumber}`, { error, documentId });
            throw error;
          }
        };
      });

      // Execute all upload functions with controlled concurrency
      // Adjust MAX_CONCURRENT_UPLOADS based on your needs and testing
      const MAX_CONCURRENT_UPLOADS = 5; // Increased from 3 for faster uploads

      Logger.info(`Starting parallel upload with concurrency ${MAX_CONCURRENT_UPLOADS}`, {
        documentId,
        totalParts: chunks
      });

      try {
        await executeWithConcurrency(uploadFunctions, MAX_CONCURRENT_UPLOADS);

        Logger.info("All parts uploaded successfully, completing multipart upload", {
          documentId,
          parts: parts.length,
        });

        // Complete the multipart upload
        const completeResponse = await fetch("/api/files/complete-multipart", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            bucket,
            key,
            uploadId,
            parts,
          }),
        });

        if (!completeResponse.ok) {
          throw new Error(`Failed to complete upload: ${completeResponse.status}`);
        }

        const { url: finalUrl } = await completeResponse.json();

        // Return the attachment information
        Logger.info("Multipart upload completed successfully", {
          url: finalUrl,
          name: file.name,
          documentId,
        });

        return {
          url: finalUrl,
          name: file.name,
          contentType: file.type,
          document_id: documentId,
          pendingConfirm: true,
        };
      } catch (error) {
        Logger.error("Multipart upload failed", {
          error,
          documentId,
          fileName: file.name
        });
        throw error;
      }
    } else {
      // Standard upload with presigned URL
      const { url: uploadUrl, documentId } = data[0];
      Logger.debug("Extracted upload details", {
        uploadUrl: uploadUrl.split("?")[0],
        documentId,
      });

      // Step 2: Upload directly to S3 using presigned URL
      Logger.debug("Starting S3 upload", { documentId });
      const uploadResponse = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse
          .text()
          .catch(() => "Failed to get error text");
        Logger.error("S3 upload failed", {
          status: uploadResponse.status,
          statusText: uploadResponse.statusText,
          errorText,
        });
        throw new Error(
          `Failed to upload to S3: ${uploadResponse.status} ${uploadResponse.statusText}`
        );
      }

      Logger.debug("S3 upload successful, will confirm on submit", {
        documentId,
      });

      // Return the attachment information with a pendingConfirm flag
      const baseUrl = uploadUrl.split("?")[0];
      Logger.info("Upload completed successfully, pending confirmation", {
        url: baseUrl,
        name: file.name,
        documentId,
      });

      // After successful upload to S3, return immediately with pendingConfirm flag
      return {
        url: baseUrl,
        name: file.name,
        contentType: file.type,
        document_id: documentId,
        pendingConfirm: true, // Flag to track unconfirmed uploads
      };
    }
  } catch (error) {
    Logger.error("Client: Protected upload failed", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    });
    throw Error("Please try a different file, or contact support.");
  }
};