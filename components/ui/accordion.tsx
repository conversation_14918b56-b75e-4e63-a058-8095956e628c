"use client";

import * as React from "react";
import { cn } from "@/lib/utils"; // Optional: your own utility for merging classNames

type AccordionProps = {
  type?: "single" | "multiple";
  collapsible?: boolean;
  className?: string;
  children: React.ReactNode;
};

type AccordionItemProps = {
  value: string;
  children: React.ReactNode;
};

type AccordionTriggerProps = {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
};

type AccordionContentProps = {
  children: React.ReactNode;
  className?: string;
};

const AccordionContext = React.createContext<any>(null);

export function Accordion({
  type = "single",
  collapsible = false,
  className,
  children,
}: AccordionProps) {
  const [openItems, setOpenItems] = React.useState<string[]>([]);

  const toggleItem = (value: string) => {
    if (type === "multiple") {
      setOpenItems((prev) =>
        prev.includes(value)
          ? prev.filter((v) => v !== value)
          : [...prev, value]
      );
    } else {
      setOpenItems((prev) =>
        prev.includes(value)
          ? collapsible
            ? []
            : prev
          : [value]
      );
    }
  };

  return (
    <AccordionContext.Provider value={{ openItems, toggleItem }}>
      <div className={cn("w-full", className)}>{children}</div>
    </AccordionContext.Provider>
  );
}

export function AccordionItem({ value, children }: AccordionItemProps) {
  const { openItems } = React.useContext(AccordionContext);
  const isOpen = openItems.includes(value);

  return (
    <div data-open={isOpen} className="border-b last:border-none">
      <AccordionContext.Provider value={{ ...React.useContext(AccordionContext), itemValue: value }}>
        {children}
      </AccordionContext.Provider>
    </div>
  );
}

export function AccordionTrigger({
  children,
  className,
  onClick,
}: AccordionTriggerProps) {
  const { toggleItem, itemValue, openItems } = React.useContext(AccordionContext);
  const isOpen = openItems.includes(itemValue);

  return (
    <button
      onClick={() => {
        toggleItem(itemValue);
        onClick?.();
      }}
      className={cn(
        "w-full flex justify-between items-center py-4 text-left font-medium focus:outline-none",
        className
      )}
    >
      {children}
      <span className="ml-2">{isOpen ? "−" : "+"}</span>
    </button>
  );
}

export function AccordionContent({
  children,
  className,
}: AccordionContentProps) {
  const { openItems, itemValue } = React.useContext(AccordionContext);
  const isOpen = openItems.includes(itemValue);

  return isOpen ? (
    <div className={cn("pb-4 px-1 text-sm text-gray-600 dark:text-[rgb(var(--base-navy))]", className)}>
      {children}
    </div>
  ) : null;
}
