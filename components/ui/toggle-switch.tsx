import { useState } from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface ToggleSwitchProps {
  isEnabled: boolean;
  onToggle: () => void;
  label?: string;
  tooltip?: string;
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  className?: string;
}

export function ToggleSwitch({
  isEnabled,
  onToggle,
  label,
  tooltip,
  size = "md",
  disabled = false,
  className,
}: ToggleSwitchProps) {
  const sizeClasses = {
    sm: "w-8 h-4",
    md: "w-10 h-5",
    lg: "w-12 h-6",
  };

  const thumbSizeClasses = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
  };

  const toggleElement = (
    <button
      type="button"
      role="switch"
      aria-checked={isEnabled}
      onClick={onToggle}
      disabled={disabled}
      className={cn(
        "relative inline-flex shrink-0 cursor-pointer rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-1 focus-visible:ring-3 focus-visible:ring-[rgb(var(--base-navy))] focus-visible:ring-opacity-0 p-0",
        isEnabled
          ? "bg-[rgb(var(--base-navy))] border-[rgb(var(--base-navy))]"
          : "bg-zinc-100 dark:bg-zinc-800 border-zinc-300 dark:border-zinc-600",
        disabled && "opacity-50 cursor-not-allowed",
        sizeClasses[size],
        className
      )}
    >
      <motion.span
        layout
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 30,
        }}
        className={cn(
          "pointer-events-none absolute rounded-md shadow-lg transform ring-0",
          thumbSizeClasses[size],
          isEnabled ? "right-0 bg-white dark:bg-black" : "left-0 bg-white dark:bg-zinc-100"
        )}
      />
    </button>
  );

  if (!tooltip) {
    return (
      <div className="flex items-center gap-2">
        {toggleElement}
        {label && <span className="text-sm">{label}</span>}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Tooltip>
        <TooltipTrigger asChild>{toggleElement}</TooltipTrigger>
        <TooltipContent side="top" className="text-sm">
          {tooltip}
        </TooltipContent>
      </Tooltip>
      {label && <span className="text-sm">{label}</span>}
    </div>
  );
}
