// Multi select component
import React from "react";
import { Select, Tag } from "antd";
import { cn } from "@/lib/utils"; // Assuming you have this utility

const { Option } = Select;

type OptionType = {
  value: string;
  label: string;
};

interface AntdMultiSelectProps {
  options: OptionType[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  className?: string;
}

export function AntdMultiSelect({
  options,
  value,
  onChange,
  placeholder = "Select options",
  className,
}: AntdMultiSelectProps) {
  const allOption = options.find((opt) => opt.value === "all");
  const selectableOptions = options.filter((opt) => opt.value !== "all");

  const handleAntdChange = (selectedValues: string[]) => {
    let newSelectedValues = [...selectedValues];

    const wasAllSelected = value.includes("all");
    const isAllBeingSelected = newSelectedValues.includes("all");
    const isAnyIndividualSelected = newSelectedValues.some((val) => val !== "all");

    // If "all" is selected along with others, or switching from "all" to another value
    if (isAllBeingSelected && isAnyIndividualSelected) {
      // User is trying to switch away from "all"
      newSelectedValues = newSelectedValues.filter((val) => val !== "all");
    } else if (isAllBeingSelected) {
      // Only "all" is selected
      newSelectedValues = ["all"];
    } else if (wasAllSelected && isAnyIndividualSelected) {
      // Switching from "all" to something else
      newSelectedValues = newSelectedValues.filter((val) => val !== "all");
    } else if (newSelectedValues.length === 0 && allOption) {
      newSelectedValues = ["all"];
    }

    onChange(newSelectedValues);
  };

  // The internal value for Ant Design's Select needs to represent the current state.
  // If 'all' is in our `value` prop, AntdSelect should only see 'all'.
  // Otherwise, it should see the individual selected values (excluding 'all' if it somehow got there).
  const internalValue = value.includes("all") && allOption ? [allOption.value] : value.filter(v => v !== "all");


  return (
    <Select
      mode="multiple"
      allowClear
      style={{ width: "100%" }}
      placeholder={placeholder}
      value={internalValue}
      onChange={handleAntdChange}
      className={cn("antd-multi-select-custom", className)}
      showSearch // Enable search functionality
      filterOption={(input, option) => {
        // Explicitly cast option?.label to string to ensure toLowerCase() exists
        const label = String(option?.label ?? "");
        return label.toLowerCase().includes(input.toLowerCase());
      }}
      tagRender={({ label, value: tagValue, closable, onClose }) => {
        if (tagValue === "all" && allOption) {
          return (
            <Tag
              closable={false} // "All" tag should not be closable individually
              onClose={onClose}
              style={{ marginRight: 3 }}
            >
              {label}
            </Tag>
          );
        }
        return (
          <Tag
            closable={closable}
            onClose={onClose}
            style={{ marginRight: 3 }}
          >
            {label}
          </Tag>
        );
      }}
    >
      {allOption && (
        <Option key={allOption.value} value={allOption.value}>
          {allOption.label}
        </Option>
      )}
      {selectableOptions.map((option) => (
        <Option key={option.value} value={option.value}>
          {option.label}
        </Option>
      ))}
    </Select>
  );
}
