import { useTheme } from "next-themes";
import { LogoIqidis } from "@/components/icons";
import { cn } from "@/lib/utils";

interface PageLoaderProps {
	message?: string;
}

export function PageLoader({ message = "Loading..." }: PageLoaderProps) {
	const { resolvedTheme } = useTheme();

	return (
		<div
			className="fixed inset-0 flex flex-col items-center justify-center z-[999] backdrop-blur-md backdrop-saturate-150"
			style={{
				backgroundColor:
					resolvedTheme === "dark"
						? "rgba(0, 0, 0, 0.6)"
						: "rgba(255, 255, 255, 0.8)",
				backgroundImage:
					resolvedTheme === "dark"
						? "linear-gradient(to bottom, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5))"
						: "linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7))",
			}}
		>
			<div className="animate-spin-slow">
				<div className="relative flex items-center justify-center">
					<LogoIqidis
						size={100}
						mixBlendMode={resolvedTheme === "dark" ? "lighten" : "multiply"}
						isDark={resolvedTheme === "dark"}
					/>
				</div>
			</div>
			<p className={cn("text-lg font-medium mt-4")}>{message}</p>
		</div>
	);
}
