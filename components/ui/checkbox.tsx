'use client';

import * as React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, checked, onChange, ...props }, ref) => {
    const [isChecked, setIsChecked] = React.useState(checked || false);

    React.useEffect(() => {
      setIsChecked(!!checked);
    }, [checked]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setIsChecked(e.target.checked);
      if (onChange) {
        onChange(e);
      }
    };

    return (
      <div className="flex items-center space-x-2">
        <div className="relative flex items-center">
          <input
            type="checkbox"
            ref={ref}
            checked={isChecked}
            onChange={handleChange}
            className={cn(
              "peer h-4 w-4 shrink-0 opacity-0 absolute",
              className
            )}
            {...props}
          />
          <div className={cn(
            "h-4 w-4 shrink-0 rounded-sm border border-input bg-background ring-offset-background",
            "peer-focus-visible:outline-none peer-focus-visible:ring-2 peer-focus-visible:ring-primary",
            "peer-disabled:cursor-not-allowed peer-disabled:opacity-50",
            isChecked ? "bg-primary border-primary" : ""
          )}>
            {isChecked && (
              <Check className="h-4 w-4 text-primary-foreground" />
            )}
          </div>
        </div>
        {label && <span className="text-sm">{label}</span>}
      </div>
    );
  }
);

Checkbox.displayName = "Checkbox";

export { Checkbox };
