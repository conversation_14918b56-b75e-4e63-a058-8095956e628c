import { ZoomIn, ZoomOut, RotateCw, X } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Dialog, DialogContent, DialogTitle } from "./ui/dialog";
import { useState, useEffect } from "react";
import { LoaderIcon } from "./icons";
import { cn } from "@/lib/utils";
import { Logger } from "@/lib/utils/Logger";

interface FilePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  fileUrl: string;
  fileName: string;
  triggerClassName?: string;
}

export function FilePreviewModal({ isOpen, onClose, fileUrl, fileName, triggerClassName }: FilePreviewModalProps) {
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);
  const [actualFileUrl, setActualFileUrl] = useState('');
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsLoaded(false);
      setZoom(1);
      setRotation(0);
      setIsLoadingUrl(false);
      setActualFileUrl('');

      // Check if fileUrl is a document ID (UUID format) or a URL
      const isDocumentId = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(fileUrl);
      const isUrl = fileUrl.startsWith('http://') || fileUrl.startsWith('https://') || fileUrl.startsWith('/');

      if (isDocumentId) {
        // Fetch fresh signed URL for document ID
        setIsLoadingUrl(true);
        fetch(`/api/files/view?id=${fileUrl}`)
          .then(response => {
            if (response.ok) {
              return response.json();
            }
            throw new Error('Failed to get signed URL');
          })
          .then(data => {
            // Check if this is a DOCX file that needs special handling
            const extension = fileName.split('.').pop()?.toLowerCase() || '';
            if (['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt'].includes(extension)) {
              // Use Microsoft Office Online viewer for Office documents
              setActualFileUrl(`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(data.url)}`);
            } else {
              setActualFileUrl(data.url);
            }
            setIsLoadingUrl(false);
          })
          .catch(error => {
            Logger.error('Error fetching signed URL:', { error });
            setActualFileUrl(''); // Clear URL to show error
            setIsLoadingUrl(false);
          });
      } else if (isUrl) {
        // Use the URL directly
        setActualFileUrl(fileUrl);
        setIsLoadingUrl(false);
      } else {
        // Invalid input
        setActualFileUrl('');
        setIsLoadingUrl(false);
      }
    }
  }, [isOpen, fileUrl]);

  // Zoom controls
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.25));

  // Rotation control
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="w-[95vw] h-[95vh] max-w-[95vw] sm:max-w-[90vw] md:max-w-[85vw] lg:max-w-[80vw] p-0 gap-0 bg-white dark:bg-zinc-900 rounded-lg overflow-hidden flex flex-col">
        <DialogTitle>
          <span className="sr-only">File Preview: {fileName}</span>
        </DialogTitle>
        
        {/* Header bars */}
        <div className="flex-none bg-zinc-100 dark:bg-zinc-800 border-b border-zinc-200 dark:border-zinc-700">
          {/* Title bar */}
          <div className="flex items-center justify-between px-2 sm:px-4 py-1.5 sm:py-2 border-b border-zinc-200 dark:border-zinc-700">
            <h2 className="text-xs sm:text-sm font-medium text-zinc-900 dark:text-zinc-100 truncate max-w-[60vw]">{fileName}</h2>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 sm:h-8 sm:w-8 rounded-full hover:bg-zinc-200 dark:hover:bg-zinc-700"
              onClick={onClose}
            >
              <X className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              <span className="sr-only">Close preview</span>
            </Button>
          </div>

          {/* Controls bar */}
          <div className="flex items-center px-1 sm:px-2 py-1">
            <div className="flex items-center gap-2 sm:gap-4">
              <div className="flex items-center gap-0.5 sm:gap-1 px-1 sm:px-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 sm:h-8 sm:w-8 text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100 hover:bg-zinc-200 dark:hover:bg-zinc-700"
                  onClick={handleZoomOut}
                >
                  <ZoomOut className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                  <span className="sr-only">Zoom out</span>
                </Button>
                <span className="text-xs sm:text-sm text-zinc-600 dark:text-zinc-400 px-1 sm:px-2 min-w-[3rem] text-center">
                  {(zoom * 100).toFixed(0)}%
                </span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 sm:h-8 sm:w-8 text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100 hover:bg-zinc-200 dark:hover:bg-zinc-700"
                  onClick={handleZoomIn}
                >
                  <ZoomIn className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                  <span className="sr-only">Zoom in</span>
                </Button>
              </div>
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 sm:h-8 sm:w-8 text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100 hover:bg-zinc-200 dark:hover:bg-zinc-700"
                  onClick={handleRotate}
                >
                  <RotateCw className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                  <span className="sr-only">Rotate document</span>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Content area */}
        <div className="flex-1 overflow-auto bg-white dark:bg-zinc-900 relative">
          {/* Loading overlay for URL generation */}
          {isLoadingUrl && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-white dark:bg-zinc-900">
              <LoaderIcon size={32} color="currentColor" />
              <p className="mt-4 text-sm text-zinc-600 dark:text-zinc-400">Preparing document...</p>
            </div>
          )}

          {/* Error state when URL can't be loaded */}
          {!actualFileUrl && !isLoadingUrl && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-white dark:bg-zinc-900">
              <p className="text-sm text-zinc-600 dark:text-zinc-400">Unable to load document preview</p>
              <p className="mt-2 text-xs text-zinc-500 dark:text-zinc-500">The document may have expired or is no longer available</p>
            </div>
          )}

          {/* Loading overlay for iframe */}
          {actualFileUrl && !isLoaded && !isLoadingUrl && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-white dark:bg-zinc-900">
              <LoaderIcon size={32} color="currentColor" />
              <p className="mt-4 text-sm text-zinc-600 dark:text-zinc-400">Loading document...</p>
            </div>
          )}

          {/* Document iframe */}
          {actualFileUrl && !isLoadingUrl && (
            <div
              className={cn(
                "min-h-full",
                triggerClassName,
                "cursor-pointer hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors"
              )}
              style={{
                transform: rotation !== 0 ? `rotate(${rotation}deg)` : 'none',
                transformOrigin: 'center',
                opacity: isLoaded ? 1 : 0,
                transition: 'opacity 0.2s ease-in-out',
              }}
            >
              <iframe
                src={actualFileUrl}
                className="w-full h-full border-0"
                style={{
                  transform: `scale(${zoom})`,
                  transformOrigin: 'top left',
                  width: `${100/zoom}%`,
                  height: '100%',
                  minHeight: '100vh',
                }}
                title={`Preview of ${fileName}`}
                onLoad={() => setIsLoaded(true)}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}