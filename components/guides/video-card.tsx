"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Play, Clock } from "lucide-react";
import { useTheme } from "next-themes";

interface VideoTutorial {
  title: string;
  description: string;
  videoUrl: string;
  categories: string[];
  duration: string;
}

export function VideoCard({ tutorial }: { tutorial: VideoTutorial }) {
  const { resolvedTheme } = useTheme();

  const openVideo = () => window.open(tutorial.videoUrl, "_blank");

  const bgColor = `hsl(260, 60%, ${resolvedTheme === "dark" ? 25 : 85}%)`;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-all font-sans">
      <div
        className="relative h-40 rounded-lg mb-4 overflow-hidden cursor-pointer group hover:scale-[1.01] transition-transform"
        onClick={openVideo}
        style={{
          backgroundColor: bgColor,
          backgroundImage: "linear-gradient(to bottom right, rgba(0,0,0,0.05), rgba(0,0,0,0.15))",
        }}
      >
        <div className="absolute top-2 left-2 bg-black/50 px-3 py-1 rounded-md text-white text-sm font-medium z-20 font-sans">
          {tutorial.title}
        </div>

        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className={`rounded-full p-3 shadow-lg group-hover:scale-110 transition-transform ${resolvedTheme === "dark" ? "bg-gray-800/80" : "bg-gray-700/80"}`}>
            <Play className="h-8 w-8 text-white" fill="currentColor" />
          </div>
        </div>

        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-md flex items-center z-20">
          <Clock className="h-3 w-3 mr-1" />
          {tutorial.duration}
        </div>
      </div>

      <h3 className="text-lg font-semibold mb-2 font-playfair">{tutorial.title}</h3>
      <p className="text-sm text-muted-foreground mb-4 font-sans">{tutorial.description}</p>

      <div className="flex flex-wrap gap-2 mb-4">
        {tutorial.categories.map((category, index) => (
          <span
            key={index}
            className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full font-sans shadow-sm"
          >
            {category}
          </span>
        ))}
      </div>

      <Button variant="outline" className="w-full mt-2 font-sans transition-colors duration-300 hover:bg-primary hover:text-primary-foreground" onClick={openVideo}>
        Watch Tutorial
      </Button>
    </div>
  );
}
