import ReferralStatsCard from "./referrals/ReferralStatsCard";
import ReferralLinkCard from "./referrals/ReferralLinkCard";
import ReferralTableCard from "./referrals/ReferralTableCard";
import StandardReferralCard from "./referrals/referral-cards/StandardReferralCard";
import PowerReferralCard from "./referrals/referral-cards/PowerReferralCard";
import BarAssociationReferralCard from "./referrals/referral-cards/BarAssociationReferralCard";
import { useEffect, useState } from "react";
import { User } from "@/lib/db/schema";

export default function ReferralsTab() {
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState<Partial<User>>();
  const [referralLink, setReferralLink] = useState("");
  const [totalReferrals, setTotalReferrals] = useState(0);
  const [convertedReferrals, setConvertedReferrals] = useState(0);
  const [pendingReferrals, setPendingReferrals] = useState(0);
  const [totalCreditEarned, setTotalCreditEarned] = useState(0);
  const [referrals, setReferrals] = useState<
    {
      name: string;
      email: string;
      date: string;
      status: "pending" | "converted" | "expired";
      reward: string;
    }[]
  >([]);

  // useEffect(() => {
  //   const fetuser = async () => {
  //     try {
  //       setIsLoading(true);
  //       const response = await (await fetch("/api/user")).json();
  //       response?.user && setUser(response?.user);
  //       response?.referralLink && setReferralLink(response?.referralLink);
  //     } catch (e) {
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };
  //   fetuser();
  // }, []);

  useEffect(() => {
    let isCancelled = false;
    setIsLoading(true);

    const loadUser = async () => {
      try {
        const res = await fetch("/api/user");
        if (!res.ok) throw new Error("Failed to fetch user");

        const data = await res.json();
        if (isCancelled) return;

        if (data?.user) setUser(data.user);
        if (data?.referralLink) setReferralLink(data.referralLink);
      } catch (err) {
        if (!isCancelled) {
          console.error(err);
          // setError("Failed to load user data.");
        }
      }
    };

    const loadReferrals = async () => {
      try {
        const res = await fetch("/api/referral");
        if (!res.ok) throw new Error("Failed to fetch referral history");

        const data = await res.json();
        if (isCancelled) return;

        if (data?.referrals) setReferrals(data.referrals);
        if (data?.totalReferrals) setTotalReferrals(data.totalReferrals);
        if (data?.convertedReferrals)
          setConvertedReferrals(data.convertedReferrals);
        if (data?.pendingReferrals) setPendingReferrals(data.pendingReferrals);
        if (data?.totalRewardEarned)
          setTotalCreditEarned(data.totalRewardEarned);
      } catch (err) {
        if (!isCancelled) {
          console.error(err);
          // setError("Failed to load referral history.");
        }
      }
    };

    Promise.allSettled([loadUser(), loadReferrals()]).then(() => {
      if (!isCancelled) setIsLoading(false);
    });

    return () => {
      isCancelled = true;
      setIsLoading(false);
    };
  }, []);

  return (
    <div className="space-y-8">
      {/* Referral Cards - 3 column grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <StandardReferralCard />
        </div>
        <div>
          <PowerReferralCard />
        </div>
        <div>
          <BarAssociationReferralCard />
        </div>
      </div>

      {/* Stats and Referral Link */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <ReferralStatsCard
            totalReferrals={totalReferrals}
            totalCreditEarned={totalCreditEarned}
            convertedReferrals={convertedReferrals}
            pendingReferrals={pendingReferrals}
            isLoading={isLoading}
          />
        </div>
        <div>
          <ReferralLinkCard
            user={user as User}
            referralLink={referralLink}
            isLoading={isLoading}
          />
        </div>
      </div>

      {/* Referral Table */}
      <div>
        <ReferralTableCard referrals={referrals} isLoading={isLoading} />
      </div>
    </div>
  );
}
