
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { Gift, Users, Building } from "lucide-react";

export default function ReferralProgramCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="size-5 text-primary" />
          Iqidis Referral Program
        </CardTitle>
        <CardDescription>
          We&apos;re building something big—and we want you to grow with us
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Standard Referrals */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-secondary rounded-md">
              <Gift className="size-4 text-primary dark:text-white" />
            </div>
            <h3 className="text-lg font-semibold">Standard Referrals</h3>
          </div>
          <p className="text-sm font-medium">
            Refer a paid user → earn $50 credit per referral
          </p>
          <ul className="text-sm text-muted-foreground space-y-1 pl-6 list-disc">
            <li>Credits are <span className="font-medium">stackable</span> (4 referrals = 1 free month at $199)</li>
            <li>No limit on number of referrals</li>
            <li>Credit applies once the referral becomes a paying user</li>
          </ul>
        </div>

        <Separator />

        {/* Power Referrer Tier */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-secondary rounded-md">
              <Gift className="size-4 text-primary dark:text-white" />
            </div>
            <h3 className="text-lg font-semibold">Power Referrer Tier</h3>
          </div>
          <p className="text-sm font-medium">
            Refer 5+ paid users in 60 days → Unlock $50/month per referral, for 3 months
          </p>
          <ul className="text-sm text-muted-foreground space-y-1 pl-6 list-disc">
            <li>That&apos;s up to <span className="font-medium">$150 per referred user</span> paid out in cash</li>
            <li>Cash is paid monthly as long as the referral stays active and paid</li>
            <li>Applies to every referral after your fourth</li>
          </ul>
        </div>

        <Separator />

        {/* Bar Associations, Legal Orgs, & Group Outreach */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-secondary rounded-md">
              <Building className="size-4 text-primary" />
            </div>
            <h3 className="text-lg font-semibold">Bar Associations, Legal Orgs, & Group Outreach</h3>
          </div>
          <p className="text-sm">
            We also reward users who introduce Iqidis to:
          </p>
          <ul className="text-sm text-muted-foreground space-y-1 pl-6 list-disc">
            <li><span className="font-medium">Bar associations</span></li>
            <li><span className="font-medium">CLE program directors</span></li>
            <li><span className="font-medium">Practice group networks or firm-wide rollouts</span></li>
          </ul>
          <p className="text-sm">
            If your introduction leads to a formal partnership, CLE event, or bulk license deal, 
            you&apos;ll receive a <span className="font-medium">custom reward</span>, which may include:
          </p>
          <ul className="text-sm text-muted-foreground space-y-1 pl-6 list-disc">
            <li>Cash bonuses</li>
            <li>Free months</li>
            <li>Lifetime feature access</li>
            <li>Public recognition as a contributor or advisor</li>
          </ul>
          <p className="text-sm font-medium">
            To refer an organization or intro a contact:
            <br />
            → Email <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a> with the subject line: <i>Referral – Bar/Org</i>
          </p>
        </div>

        <Separator />

        {/* How to Refer */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">How to Refer</h3>
          <ul className="text-sm text-muted-foreground space-y-1 pl-6 list-disc">
            <li>For now, send introductions directly to <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a> (Attn: Alex)</li>
            <li>Referral links are launching soon for automated tracking</li>
          </ul>
        </div>

        <Separator />

        {/* Summary Table */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Summary</h3>
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Referral Type</TableHead>
                  <TableHead>Reward</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>1 user</TableCell>
                  <TableCell>$50 credit</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>4 users</TableCell>
                  <TableCell>Free month</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>5+ in 60 days</TableCell>
                  <TableCell>$50/month per user (cash, for 3 months)</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Bar/Org intro → event or deal</TableCell>
                  <TableCell>Custom reward (cash, access, recognition)</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
