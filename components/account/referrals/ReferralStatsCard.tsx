import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Users, DollarSign, Clock, Loader2, AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { fetcher } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export default function ReferralStatsCard({
  totalReferrals,
  totalCreditEarned,
  convertedReferrals,
  pendingReferrals,
  isLoading,
}: {
  totalReferrals: number;
  totalCreditEarned: number;
  convertedReferrals: number;
  pendingReferrals: number;
  isLoading: boolean;
}) {
  const router = useRouter();
  const [stripeCredit, setStripeCredit] = useState<number | null>(null);
  const [hasStripeAccount, setHasStripeAccount] = useState<boolean | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Mock data - in a real app, this would come from an API
  // const stats = {
  //   totalReferrals: 3,
  //   convertedReferrals: 2,
  //   pendingReferrals: 1,
  //   creditsEarned: 100,
  //   cashEarned: 0,
  //   daysLeft: 42,
  //   powerProgress: 40, // percentage to power referrer status
  // };

  // Fetch Stripe balance
  useEffect(() => {
    const fetchStripeBalance = async () => {
      setIsLoadingBalance(true);
      setError(null);
      try {
        const data = await fetcher("/api/stripe/balance");
        if (data && typeof data.credit === 'number') {
          setStripeCredit(data.credit);
          setHasStripeAccount(data.hasStripeAccount === true);
        }
      } catch (error) {
        console.error("Error fetching Stripe balance:", error);
        setError("Couldn't load balance");
      } finally {
        setIsLoadingBalance(false);
      }
    };

    fetchStripeBalance();
  }, []);

  // Create Stripe customer
  const createStripeCustomer = async () => {
    setIsLoadingBalance(true);
    try {
      await fetch("/api/stripe/create-customer", {
        method: "GET",
      });
      // Refetch balance after creating customer
      const data = await fetcher("/api/stripe/balance");
      if (data && typeof data.credit === 'number') {
        setStripeCredit(data.credit);
        setHasStripeAccount(data.hasStripeAccount === true);
      }
    } catch (error) {
      console.error("Error creating Stripe customer:", error);
      setError("Couldn't create Stripe account");
    } finally {
      setIsLoadingBalance(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex gap-2">
            <Users className="h-5 w-5 mt-[5px] text-primary dark:text-white" />
            Your Referral Stats
          </span>
          <Badge variant="outline" className="font-normal" >
            Standard Referrer
          </Badge>
        </CardTitle>
        <CardDescription>Track your referrals and rewards</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-1 md:border-none border-b border-border pb-[20px]">
            <p className="text-sm text-muted-foreground">Total Referrals</p>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <p className="text-2xl font-semibold">{totalReferrals}</p>
            )}
            <div className="flex gap-2 text-sm">
              <span className="text-muted-foreground">
                {convertedReferrals} converted
              </span>
              <span className="text-muted-foreground">|</span>
              <span className="text-muted-foreground">
                {pendingReferrals} pending
              </span>
            </div>
          </div>

          <div className="space-y-1 md:border-none border-b border-border pb-[20px]">
            <p className="text-sm text-muted-foreground">Credits Earned So Far</p>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <p className="text-2xl font-semibold flex items-center">
                <DollarSign className="h-4 w-4 mr-1" />
                {totalCreditEarned}
              </p>
            )}
            <p className="text-sm text-muted-foreground">
              {Math.round((totalCreditEarned / 199) * 100)}% of a free month
            </p>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Current Stripe Balance</p>
            {isLoadingBalance ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : error ? (
              <div className="flex items-center text-destructive">
                <AlertCircle className="h-4 w-4 mr-1" />
                <span className="text-sm">{error}</span>
              </div>
            ) : hasStripeAccount === false ? (
              <div className="space-y-2">
                <p className="text-sm">No Stripe Account</p>
                {/* Removed the Create Stripe Account button */}
              </div>
            ) : (
              <p className="text-2xl font-semibold flex items-center">
                <DollarSign className="h-4 w-4 mr-1" />
                {stripeCredit !== null ? (stripeCredit / 100).toFixed(2) : "0.00"}
              </p>
            )}
            <p className="text-sm text-muted-foreground">
              {hasStripeAccount === false 
                ? "Credits will be available on your next invoice after your first payment" 
                : "Available for your next payment"}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
