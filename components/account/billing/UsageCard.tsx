
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { RefreshCcw } from "lucide-react";
import { toast } from "sonner";
import { UsageTracker } from "../UsageTracker";
import { useState } from "react";

interface UsageCardProps {
  usageData: {
    queries: number;
    queryLimit: number;
    usageStats?: {
      dailyMessageCount: number;
      totalChats: number;
      memoryUsageMB: number;
      dailyMessageLimit?: number;
      totalChatLimit?: number;
      memoryLimit?: number;
    };
  };
  isLoading: boolean;
  onRefresh?: () => Promise<void>;
}

export function UsageCard({ usageData, isLoading, onRefresh }: UsageCardProps) {
  const [refreshing, setRefreshing] = useState(false);

  const refreshUsage = async () => {
    if (!onRefresh) return;
    
    setRefreshing(true);
    try {
      await onRefresh();
      toast.success("Usage data refreshed");
    } catch (error) {
      toast.error("Failed to refresh usage data");
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Usage</CardTitle>
            <CardDescription>Track your usage limits</CardDescription>
          </div>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={refreshUsage}
            disabled={refreshing || isLoading || !onRefresh}
          >
            <RefreshCcw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <UsageTracker
          queries={usageData.queries}
          queryLimit={usageData.queryLimit}
          isLoading={isLoading}
          usageStats={usageData.usageStats}
        />
      </CardContent>
    </Card>
  );
}
