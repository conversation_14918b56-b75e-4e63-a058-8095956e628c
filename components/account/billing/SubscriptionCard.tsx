import { useSession } from "next-auth/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Diamond } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Logger } from "@/lib/utils/Logger";
import { SUBSCRIPTION_TIERS } from "@/lib/constants";
import { Skeleton } from "@/components/ui/skeleton";

export function SubscriptionCard() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [isAdminManaged, setIsAdminManaged] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);

  // Get subscription details from session with fallbacks
  const tier =
    session?.user?.subscriptionTier &&
    SUBSCRIPTION_TIERS.includes(session.user.subscriptionTier)
      ? "premium"
      : "free";
  const isTrialActive = session?.user?.subscriptionTier === "premium-trial";
  const trialEndsAt = session?.user?.subscriptionEndDate;
  const isExtendedTrial = session?.user?.isExtendedTrialUser === true;
  // Check if user is an admin
  const isAdmin = session?.user?.isAdmin === true;

  // Check if subscription is admin managed - only run once when session is available
  useEffect(() => {
    // Skip if auth is still loading
    if (authStatus === "loading") {
      return;
    }
    
    // Skip if we've already determined admin managed status
    if (!isDataLoading && isAdminManaged !== undefined) {
      return;
    }
    
    const fetchSubscriptionDetails = async () => {
      try {
        // Check if session already has isAdminManaged property
        if (session?.user?.isAdminManaged !== undefined) {
          setIsAdminManaged(session.user.isAdminManaged);
          setIsDataLoading(false);
          return;
        }
        
        // Otherwise fetch from API
        const res = await fetch("/api/query/get-current-active-subscription");
        if (res.ok) {
          const data = await res.json();
          setIsAdminManaged(data?.subscription?.isAdminManaged === true);
        }
      } catch (error) {
        // Silent fail
        Logger.error("Error fetching subscription details:", error);
      } finally {
        setIsDataLoading(false);
      }
    };
    
    fetchSubscriptionDetails();
  }, [authStatus, session, isDataLoading, isAdminManaged]);

  const getTierLabel = () => {
    if (tier === "premium") return "Iqidis Core";
    return "Free";
  };

  const getTierDescription = () => {
    if (tier === "premium")
      return "Full acesss with all features and upgrades";
    return "Basic access with limited queries";
  };

  const handleUpgrade = () => {
    router.push("/subscription");
  };

  const handleManageBilling = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/stripe/customer-portal", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to create portal session");
      }

      const { url } = await response.json();
      window.location.href = url;
    } catch (error) {
      Logger.error("Error opening customer portal:", error);
      toast.error("Could not open payment portal. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  // Show loading skeleton while data is loading
  if (authStatus === "loading" || isDataLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-6 w-24" />
          </div>
          <Skeleton className="h-4 w-48 mt-2" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-4 w-full" />
        </CardContent>
        <CardFooter>
          <Skeleton className="h-10 w-full" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between flex-wrap-reverse">
          <CardTitle>Subscription</CardTitle>
          <Badge className="hover:bg-primary" variant={tier === "premium" ? "default" : "outline"}>
            {getTierLabel()}
          </Badge>
        </div>
        <CardDescription>Manage your subscription plan</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">{getTierDescription()}</p>

        {isTrialActive && trialEndsAt && (
          <div className="mt-2 text-sm text-amber-600 dark:text-amber-400">
            Trial ends on {new Date(trialEndsAt).toLocaleDateString()}
          </div>
        )}
      </CardContent>
      <CardFooter>
        {isAdminManaged ? (
          <div className="w-full text-center p-2 bg-blue-50 border border-blue-200 text-blue-700 rounded dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300">
            <p className="text-sm">
              Your subscription is managed by the administrator. Please contact support for any changes.
            </p>
          </div>
        ) : isAdmin ? (
          <div className="w-full text-center p-2 bg-green-50 border border-green-200 text-green-700 rounded dark:bg-green-900/20 dark:border-green-800 dark:text-green-300">
            <p className="text-sm">
              As an administrator, you have full access to premium features.
            </p>
          </div>
        ) : isExtendedTrial ? (
          <div className="space-y-3 w-full">
            <div className="text-center p-2 bg-amber-50 border border-amber-200 text-amber-700 rounded dark:bg-amber-900/20 dark:border-amber-800 dark:text-amber-300">
              <p className="text-sm">
                You have a complimentary 30 day full access trial.
              </p>
            </div>
            <div className="flex justify-center">
              <Button className="max-w-md h-12 gap-2 align-self-center" onClick={handleUpgrade}>
                <span>Upgrade Now</span>
              </Button>
            </div>
          </div>
        ) :tier === "free" ? (
            <Button className="w-full h-12 gap-2 align-self-center" onClick={handleUpgrade}>
              <span>Upgrade Now</span>
            </Button>
        ) : (
          <Button
            // variant="outline"
            className="w-full"
            onClick={handleManageBilling}
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                <span>Loading...</span>
              </>
            ) : (
              <span>Manage Subscription</span>
            )}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
