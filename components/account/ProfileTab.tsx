"use client";

import { ProfilePictureCard } from "./profile/ProfilePictureCard";
import { AccountSecurityCard } from "./profile/AccountSecurityCard";
import { PreferencesForm } from "@/components/preferences-form";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSession, SessionProvider } from "next-auth/react";
import { useState, useEffect } from "react";
import { toast } from "sonner";

// Create a wrapper component that includes SessionProvider
export default function ProfileTab() {
  return (
    <SessionProvider>
      <ProfileTabContent />
    </SessionProvider>
  );
}

// Separate the content component that uses useSession
function ProfileTabContent() {
  const { data: session, update: updateSession } = useSession();
  const [userData, setUserData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    organization: "",
    bio: "",
    avatarUrl: "",
  });
  const [preferences, setPreferences] = useState(null);
  const [isLoadingPreferences, setIsLoadingPreferences] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0); // Add a refresh key for forcing re-renders

  // Fetch user preferences
  useEffect(() => {
    const fetchPreferences = async () => {
      if (!session?.user?.id) return;
      
      setIsLoadingPreferences(true);
      try {
        const response = await fetch('/api/preferences');
        if (response.ok) {
          const prefsData = await response.json();
          
          setPreferences(prefsData);
        }
      } catch (error) {
        console.error("Error fetching preferences:", error);
      } finally {
        setIsLoadingPreferences(false);
      }
    };
    
    fetchPreferences();
  }, [session?.user?.id, refreshKey]); // Add refreshKey to dependencies

  // Update user data when session changes
  useEffect(() => {
    if (session?.user) {
      setUserData({
        firstName: session.user.firstname || "",
        lastName: session.user.lastname || "",
        email: session.user.email || "",
        organization: session.user.company || "",
        bio: "",
        avatarUrl: session.user.avatarUrl || "",
      });
    }
  }, [session, refreshKey]); // Add refreshKey to dependencies

  const handleAvatarUpdate = async (newAvatarUrl: string) => {
    try {
      setUserData(prev => ({ ...prev, avatarUrl: newAvatarUrl }));
      
      // Force a session update to get the new avatar URL
      await updateSession();
      
      // Log the updated session to verify the avatar URL is included
      console.log("Updated session:", session);
      
      // Clear all toasts with a small delay before showing success message

      await new Promise(resolve => setTimeout(resolve, 100));
      toast.success('Profile picture updated successfully');
    } catch (error) {
      console.error("Failed to update session:", error);
  
      await new Promise(resolve => setTimeout(resolve, 100));
      toast.error("Failed to update profile picture in session");
    }
  };

  // Function to force refresh data
  const refreshData = () => {
    setRefreshKey(prev => prev + 1);
    updateSession();
  };

  return (
    <SidebarProvider>
      <div className="grid lg:gap-6 lg:grid-cols-3 grid-cols-1 w-full">
        <div className="w-full">
          <ProfilePictureCard 
            userData={userData} 
            onAvatarUpdate={handleAvatarUpdate}
          />
          <AccountSecurityCard />
        </div>

        <div className="md:col-span-2 w-full">
          <PreferencesForm 
            initialData={preferences} 
            isLoading={isLoadingPreferences}
            onSuccessfulSubmit={refreshData} // Add callback for successful submissions
          />
        </div>
      </div>
    </SidebarProvider>
  );
}
