
import { Progress } from "@/components/ui/progress";
import { AlertTriangle } from "lucide-react";
import { DAILY_MESSAGE_LIMIT, TOTAL_CHAT_LIMIT, TOTAL_MEMORY_LIMIT_MB } from "@/lib/constants";
import { Logger } from "@/lib/utils/Logger";

interface UsageTrackerProps {
  queries: number;
  queryLimit: number;
  isLoading?: boolean;
  usageStats?: {
    dailyMessageCount: number;
    totalChats: number;
    memoryUsageMB: number;
    dailyMessageLimit?: number;
    totalChatLimit?: number;
    memoryLimit?: number;
    subscriptionTier?: string;
  };
  showRefreshButton?: boolean;
}

export function UsageTracker({ 
  queries, 
  queryLimit, 
  isLoading = false,
  usageStats,
  showRefreshButton
}: UsageTrackerProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-4 bg-muted rounded animate-pulse"></div>
        <div className="h-10 bg-muted rounded animate-pulse"></div>
        <div className="h-4 bg-muted rounded animate-pulse w-3/4"></div>
      </div>
    );
  }

  // Use actual usage data if available, with fallbacks
  const dailyMessages = usageStats?.dailyMessageCount ?? 0;
  const savedChats = usageStats?.totalChats ?? 0;
  const storageUsed = usageStats?.memoryUsageMB ?? 0;

   // Check if user is on a paid plan (infinite limits indicate paid plan)
   const isPaidPlan = usageStats?.dailyMessageLimit === null || usageStats?.totalChatLimit === null || usageStats?.memoryLimit  === null;

  
  // Use provided limits or constants
  const dailyLimit = isPaidPlan ? Infinity : DAILY_MESSAGE_LIMIT;
  const chatLimit = isPaidPlan ? Infinity :  TOTAL_CHAT_LIMIT;
  const memoryLimit = isPaidPlan ? Infinity :  TOTAL_MEMORY_LIMIT_MB;



  // Calculate percentages with safeguards against division by zero
  const messagesPercentage = dailyLimit !== Infinity ? Math.min(100, Math.round((dailyMessages / dailyLimit) * 100)) : 0;
  const chatsPercentage = chatLimit !== Infinity ? Math.min(100, Math.round((savedChats / chatLimit) * 100)) : 0;
  const storagePercentage = memoryLimit !== Infinity? Math.min(100, Math.round((storageUsed / memoryLimit) * 100)) : 0;

  // Format values for display
  const formatValue = (value: number) => {
    return isFinite(value) ? value.toLocaleString() : "∞";
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        {/* Daily Messages */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Daily Messages</span>
            <span className="text-sm text-muted-foreground">
              {formatValue(dailyMessages)}{!isPaidPlan && ` / ${formatValue(dailyLimit)}`}
            </span>
          </div>
          <Progress 
            value={messagesPercentage} 
            className="h-2 bg-muted"
          />
        </div>
        
        {/* Saved Chats */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Saved Chats</span>
            <span className="text-sm text-muted-foreground">
              {formatValue(savedChats)}{!isPaidPlan && ` / ${formatValue(chatLimit)}`}
            </span>
          </div>
          <Progress 
            value={chatsPercentage} 
            className="h-2 bg-muted"
          />
        </div>
        
        {/* Storage */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Storage</span>
            <span className="text-sm text-muted-foreground">
              {formatValue(storageUsed)}MB{!isPaidPlan && ` / ${formatValue(memoryLimit)}MB`}
            </span>
          </div>
          <Progress 
            value={storagePercentage} 
            className="h-2 bg-muted"
          />
        </div>
      </div>
      
    </div>
  );
}
