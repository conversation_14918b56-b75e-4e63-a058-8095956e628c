"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Search,
  Star,
  StarOff,
  Plus,
  Folder,
  Edit,
  Trash2,
  Upload,
  FileText,
  X,
  FolderPlus,
  BookOpen,
  Bookmark,
  Wand2,
  Pencil,
  CheckCircle,
} from "lucide-react";
import { toast } from "sonner";
import { usePromptFileUpload } from "@/hooks/use-prompt-file-upload";
import { useDocumentPersistence } from "@/hooks/use-document-persistence";
import { generateUUID, extractDocumentIdFromUrl, expandMimeType, PROMPT_FILE_ACCEPT_STRING } from "@/lib/utils";
import { Logger } from "@/lib/utils/Logger";

// UI Components
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { logEvent } from "@/lib/analytics/events-client";
import { AmplifyEvent, PlayBookEvent, PlayBookFlow, PlayBookStepName, PlayBookStepNumber } from "@/lib/analytics/event-types";
import { useUser } from "@/contexts/UserContext";

// Types
import { Prompt, PromptFolder } from "@/lib/db/schema";

// For fallback during loading
import { STARTER_PROMPTS, IStarterPrompt } from "@/lib/ai/starter-prompts";
import { validatePromptFiles } from "@/lib/utils";

// Custom DialogFooter component since it's not exported from dialog.tsx
const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 md:px-6 px-3 gap-3 ${
      className || ""
    }`}
    {...props}
  />
);

// Extended interfaces for database-backed prompts and folders
interface PromptWithFolder extends Prompt {
  folderName?: string;
  isSystemFolder?: boolean;
}
interface PromptArchitectStep {
  id: number;
  title: string;
  isComplete: boolean;
}

// Type guard removed - not used

interface PlaybookDrawerProps {
  open: boolean;
  onClose: () => void;
}

/**
 * This drawer has 3 tabs:
 * 1) Starter Pack
 * 2) My Playbook
 * 3) Prompt Architect
 *
 * We default to "Starter Pack" when opened.
 */
// Create a context to store and share the loaded data
const PlaybookContext = React.createContext<{
  systemFolders: PromptFolder[];
  userFolders: PromptFolder[];
  promptsByFolder: Record<string, Prompt[]>;
  favoritePrompts: PromptWithFolder[];
  recentPrompts: PromptWithFolder[];
  isLoading: boolean;
  loadingStates: { starter: boolean; playbook: boolean };
  setSystemFolders: React.Dispatch<React.SetStateAction<PromptFolder[]>>;
  setUserFolders: React.Dispatch<React.SetStateAction<PromptFolder[]>>;
  setPromptsByFolder: React.Dispatch<
    React.SetStateAction<Record<string, Prompt[]>>
  >;
  setFavoritePrompts: React.Dispatch<React.SetStateAction<PromptWithFolder[]>>;
  setRecentPrompts: React.Dispatch<React.SetStateAction<PromptWithFolder[]>>;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  setLoadingStates: React.Dispatch<
    React.SetStateAction<{ starter: boolean; playbook: boolean }>
  >;
  dataLoaded: { starter: boolean; playbook: boolean };
  setDataLoaded: React.Dispatch<
    React.SetStateAction<{ starter: boolean; playbook: boolean }>
  >;
}>({
  // Initialize with default values to prevent 'undefined' errors
  systemFolders: [],
  userFolders: [],
  promptsByFolder: {},
  favoritePrompts: [],
  recentPrompts: [],
  isLoading: false,
  loadingStates: { starter: false, playbook: false },
  setSystemFolders: () => {},
  setUserFolders: () => {},
  setPromptsByFolder: () => {},
  setFavoritePrompts: () => {},
  setRecentPrompts: () => {},
  setIsLoading: () => {},
  setLoadingStates: () => {},
  dataLoaded: { starter: false, playbook: false },
  setDataLoaded: () => {},
});

// Create a provider component to manage the shared state
function PlaybookProvider({ children }: { children: React.ReactNode }) {
  // State for folders and prompts
  const [systemFolders, setSystemFolders] = useState<PromptFolder[]>([]);
  const [userFolders, setUserFolders] = useState<PromptFolder[]>([]);
  const [promptsByFolder, setPromptsByFolder] = useState<
    Record<string, Prompt[]>
  >({});
  const [favoritePrompts, setFavoritePrompts] = useState<PromptWithFolder[]>(
    []
  );
  const [recentPrompts, setRecentPrompts] = useState<PromptWithFolder[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Track if data has been loaded for each tab to prevent unnecessary refetching
  const [dataLoaded, setDataLoaded] = useState({
    starter: false,
    playbook: false,
  });

  // State for tracking which tab is loading
  const [loadingStates, setLoadingStates] = useState({
    starter: false,
    playbook: false,
  });

  return (
    <PlaybookContext.Provider
      value={{
        systemFolders,
        userFolders,
        promptsByFolder,
        favoritePrompts,
        recentPrompts,
        isLoading,
        loadingStates,
        setSystemFolders,
        setUserFolders,
        setPromptsByFolder,
        setFavoritePrompts,
        setRecentPrompts,
        setIsLoading,
        setLoadingStates,
        dataLoaded,
        setDataLoaded,
      }}
    >
      {children}
    </PlaybookContext.Provider>
  );
}

// Create a wrapper component that includes the provider
export function PlaybookDrawer({ open, onClose }: PlaybookDrawerProps) {
  // Don't render anything if the drawer is not open
  if (!open) return null;

  return (
    <PlaybookProvider>
      <PlaybookDrawerContent open={open} onClose={onClose} />
    </PlaybookProvider>
  );
}

// Separate the content component from the provider wrapper
function PlaybookDrawerContent({ open, onClose }: PlaybookDrawerProps) {
  // Access the router for navigation
  const router = useRouter();
  // Control which tab is active (default: 'starter')
  const [activeTab, setActiveTab] = useState<string>("starter");
  // Get the current user ID for event logging
  const user = useUser();

  // State for Prompt Architect
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [promptGoal, setPromptGoal] = useState<string>("");
  const [textReferences, setTextReferences] = useState<string>("");
  const [refinedPrompt, setRefinedPrompt] = useState<string>("");
  const [isGeneratingPrompt, setIsGeneratingPrompt] = useState<boolean>(false);
  const [isEditingRefinedPrompt, setIsEditingRefinedPrompt] =
    useState<boolean>(false);
  const [editedRefinedPrompt, setEditedRefinedPrompt] = useState<string>("");
  const [promptName, setPromptName] = useState<string>("");
  const [showSaveDrawer, setShowSaveDrawer] = useState<boolean>(false);

  // Use our custom hooks for file uploads and document persistence
  const chatId = generateUUID(); // Generate a temporary chat ID for file uploads
  const { uploadedFiles, uploadQueue, handleFileUpload, clearUploadedFiles } =
    usePromptFileUpload(chatId);
  const { addDocuments, associateDocumentsWithMessage } =
    useDocumentPersistence(chatId);

  // Load folders when the Save to My Playbook dialog is opened
  useEffect(() => {
    if (showSaveDrawer) {
      const loadFolders = async () => {
        try {
          const response = await fetch("/api/prompts/folders?type=user", {
            headers: { "Cache-Control": "no-cache" }, // Don't use cache to ensure we get the latest folders
          });

          if (response.ok) {
            const folders = await response.json();
            setUserFolders(folders);
            console.log(
              "Loaded folders for Save to My Playbook dialog (useEffect):",
              folders
            );
          }
        } catch (error) {
          console.error(
            "Error loading folders for Save to My Playbook dialog:",
            error
          );
        }
      };

      loadFolders();
    }
  }, [showSaveDrawer]);

  const steps: PromptArchitectStep[] = [
    { id: 1, title: "Goal", isComplete: !!promptGoal },
    {
      id: 2,
      title: "References",
      isComplete: !!textReferences || uploadedFiles.length > 0,
    },
    { id: 3, title: "Preview", isComplete: false },
  ];

  // Use state from context
  const [searchQuery, setSearchQuery] = useState("");

  // Create a ref to track if the component is mounted
  const isMounted = React.useRef(true);

  // Create a state to track if the drawer has been rendered at least once
  const [hasBeenRendered, setHasBeenRendered] = useState(false);

  // When the drawer opens for the first time, mark it as rendered
  useEffect(() => {
    if (open && !hasBeenRendered) {
      setHasBeenRendered(true);
    }
  }, [open, hasBeenRendered]);

  // Type assertion helper removed - not used

  // State for folder/prompt creation and editing
  const [newFolderName, setNewFolderName] = useState("");
  const [newPromptData, setNewPromptData] = useState({
    title: "",
    content: "",
    folderId: "",
  });

  // Create a separate chatId for new prompt file uploads
  const newPromptChatId = generateUUID();
  const {
    uploadedFiles: newPromptFiles,
    uploadQueue: newPromptUploadQueue,
    handleFileUpload: handleNewPromptFileUpload,
    clearUploadedFiles: clearNewPromptFiles,
    removeUploadedFile: removeNewPromptFile
  } = usePromptFileUpload(newPromptChatId);
  const [editPromptData, setEditPromptData] = useState<Prompt | null>(null);

  // Helper function to reset new prompt dialog state
  const resetNewPromptDialog = useCallback(() => {
    clearNewPromptFiles();
    setNewPromptData({
      title: "",
      content: "",
      folderId: "",
    });
  }, [clearNewPromptFiles]);

  // Create a separate chatId for edit prompt file uploads
  const editPromptChatId = generateUUID();
  const {
    uploadedFiles: editPromptFiles,
    uploadQueue: editPromptUploadQueue,
    handleFileUpload: handleEditPromptFileUpload,
    clearUploadedFiles: clearEditPromptFiles,
    removeUploadedFile: removeEditPromptFile
  } = usePromptFileUpload(editPromptChatId);

  const [customizePromptData, setCustomizePromptData] = useState<{
    title: string;
    content: string;
  } | null>(null);
  const [showNewFolderDialog, setShowNewFolderDialog] = useState(false);

  // State for tracking the Playbook drawer visibility
  const [drawerVisibilityState, setDrawerVisibilityState] = useState<{
    element: HTMLElement | null;
    wasVisible: boolean;
  }>({ element: null, wasVisible: true });

  // Custom state handler for New Prompt dialog to handle drawer visibility
  const [showNewPromptDialog, setShowNewPromptDialogState] = useState(false);
  const setShowNewPromptDialog = (show: boolean) => {
    // Get the drawer element
    const drawerElement = document.querySelector(
      '[data-playbook="drawer"]'
    ) as HTMLElement;

    if (show) {
      // Reset the dialog state when opening to ensure clean state
      resetNewPromptDialog();

      // Store the current state before hiding
      setDrawerVisibilityState({
        element: drawerElement,
        wasVisible: true,
      });

      // Hide the drawer when opening the dialog
      if (drawerElement) {
        drawerElement.style.display = "none";
      }
    } else {
      // Restore the drawer when closing the dialog
      if (drawerVisibilityState.element) {
        drawerVisibilityState.element.style.display = "";
      }
    }

    // Update the dialog state
    setShowNewPromptDialogState(show);
  };

  const [showEditPromptDialog, setShowEditPromptDialog] = useState(false);

  // Effect to fetch prompt files when the edit dialog is opened
  useEffect(() => {
    if (showEditPromptDialog && editPromptData) {
      const fetchPromptFiles = async () => {
        try {
          // Clear any existing files first
          clearEditPromptFiles();

          Logger.info(`Fetching files for prompt ${editPromptData.id}`);

          // Fetch the prompt details with files
          const response = await fetch(`/api/prompts?id=${editPromptData.id}`, {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          });

          if (response.ok) {
            const promptDetails = await response.json();

            // If the prompt has files, load them into the edit prompt files state
            if (promptDetails.files && Array.isArray(promptDetails.files) && promptDetails.files.length > 0) {
              Logger.info(`Found ${promptDetails.files.length} files associated with prompt ${editPromptData.id}:`, promptDetails.files);

              // Define the type for prompt files
              interface PromptFile {
                fileName: string;
                fileUrl: string;
                fileType: string;
                fileSize?: number;
                id?: string;
              }

              // Convert the files to the format expected by the file upload hook
              const formattedFiles = promptDetails.files.map((file: PromptFile) => ({
                name: file.fileName,
                url: file.fileUrl,
                contentType: file.fileType,
                size: file.fileSize || 0,
                document_id: file.id || generateUUID(),
                uploaded: true
              }));

              // Store the formatted files in localStorage
              localStorage.setItem("pendingChatFiles", JSON.stringify(formattedFiles));

              // We can't directly set the files in state since the hook doesn't expose a setter
              // Instead, we'll clear existing files and store the new ones in localStorage
              // The files will be used when the prompt is saved

              Logger.info(`Successfully loaded ${formattedFiles.length} files for prompt ${editPromptData.id}`);
            } else {
              Logger.info(`No files found for prompt ${editPromptData.id}`);
            }
          } else {
            throw new Error(`Failed to fetch prompt details: ${response.status}`);
          }
        } catch (error) {
          Logger.error("Error fetching prompt files:", error);
          toast.error("Failed to load attached documents");
        }
      };

      fetchPromptFiles();
    }
  }, [showEditPromptDialog, editPromptData]);
  const [showCustomizePromptDialog, setShowCustomizePromptDialog] =
    useState(false);

  // State for favorites sorting
  const [favoritesSortMethod, setFavoritesSortMethod] = useState<
    "lastFavorited" | "alphabetical"
  >("lastFavorited");

  // State for recent prompts sorting
  const [recentSortMethod, setRecentSortMethod] = useState<
    "lastUsed" | "alphabetical"
  >("lastUsed");

  // Use the context for state management
  const playbookContext = React.useContext(PlaybookContext);

  // Destructure context values with safety checks
  const {
    systemFolders = [],
    setSystemFolders = () => {},
    userFolders = [],
    setUserFolders = () => {},
    promptsByFolder = {},
    setPromptsByFolder = () => {},
    favoritePrompts = [],
    setFavoritePrompts = () => {},
    recentPrompts = [],
    setRecentPrompts = () => {},
    isLoading = false,
    setIsLoading = () => {},
    loadingStates = { starter: false, playbook: false },
    setLoadingStates = () => {},
    dataLoaded = { starter: false, playbook: false },
    setDataLoaded = () => {},
  } = playbookContext;

  // Load playbook data (user folders, favorites, recent prompts, and prompts) with caching
  const loadPlaybookData = async () => {
    // Always refresh favorites and recent prompts to ensure they're up to date
    try {
      console.log("Refreshing favorites and recent prompts data");

      // Fetch favorites and recent prompts in parallel
      const [favoritesRes, recentRes] = await Promise.all([
        fetch("/api/prompts?type=favorites", {
          headers: { "Cache-Control": "no-cache", Pragma: "no-cache" }, // Don't cache favorites
        }),
        fetch("/api/prompts?type=recent&limit=20", {
          headers: { "Cache-Control": "no-cache", Pragma: "no-cache" }, // Don't cache recent prompts
        }),
      ]);

      // Process favorites
      if (favoritesRes.ok) {
        const favoritesData = await favoritesRes.json();
        console.log("Fetched favorites data:", favoritesData);
        setFavoritePrompts(favoritesData);
      }

      // Process recent prompts
      if (recentRes.ok) {
        const recentData = await recentRes.json();
        console.log("Fetched recent prompts data:", recentData);
        setRecentPrompts(recentData);
      }
    } catch (error) {
      console.error("Error refreshing favorites or recent prompts:", error);
    }

    // If other data is already loaded, don't fetch it again
    if (dataLoaded?.playbook) {
      return;
    }

    try {
      // Mark as loading
      setLoadingStates((prev) => ({ ...prev, playbook: true }));
      setIsLoading(true);

      // Fetch user folders (playbook)
      const userFoldersRes = await fetch("/api/prompts/folders?type=user", {
        headers: { "Cache-Control": "max-age=300" }, // Cache for 5 minutes
      });
      const userFoldersData = await userFoldersRes.json();
      setUserFolders(userFoldersData);

      // Fetch favorite prompts with no caching to ensure we get the latest data
      const favoritesRes = await fetch("/api/prompts?type=favorites", {
        headers: { "Cache-Control": "no-cache" }, // Don't cache favorites
      });
      const favoritesData = await favoritesRes.json();
      setFavoritePrompts(favoritesData);

      // Fetch prompts for user folders in parallel
      const promptsMap: Record<string, Prompt[]> = {};
      const fetchPromises = userFoldersData.map(
        async (folder: PromptFolder) => {
          const promptsRes = await fetch(`/api/prompts?folderId=${folder.id}`, {
            headers: { "Cache-Control": "max-age=300" }, // Cache for 5 minutes
          });
          const promptsData = await promptsRes.json();
          return { folderId: folder.id, prompts: promptsData };
        }
      );

      // Wait for all promises to resolve
      const results = await Promise.all(fetchPromises);

      // Build the prompts map
      results.forEach((result) => {
        promptsMap[result.folderId] = result.prompts;
      });

      // Update promptsByFolder with user folder prompts
      setPromptsByFolder((prevState) => ({
        ...prevState,
        ...promptsMap,
      }));

      // Mark playbook as loaded
      setLoadingStates((prev) => ({ ...prev, playbook: false }));
      setDataLoaded((prev) => ({ ...prev, playbook: true }));
    } catch (error) {
      console.error("Error fetching playbook data:", error);
      toast.error("Failed to load playbook");
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  };

  // Fetch data based on active tab and only if the drawer is open
  useEffect(() => {
    // Only load data if the drawer is open and has been rendered at least once
    if (!open || !hasBeenRendered) return;

    // Set mounted flag
    isMounted.current = true;

    // Check for pending prompt to save from localStorage (from Amplify button)
    if (typeof window !== "undefined") {
      const pendingPromptJson = localStorage.getItem("pendingPromptToSave");
      if (pendingPromptJson) {
        try {
          const pendingPrompt = JSON.parse(pendingPromptJson);
          // Set the new prompt data and show the dialog
          setNewPromptData({
            title: pendingPrompt.title || "",
            content: pendingPrompt.content || "",
            folderId: "",
          });

          // Switch to My Playbook tab
          setActiveTab("myPlaybook");

          // Get the drawer element before showing the dialog
          const drawerElement = document.querySelector(
            '[data-playbook="drawer"]'
          ) as HTMLElement;

          // Store the current state before hiding
          setDrawerVisibilityState({
            element: drawerElement,
            wasVisible: true,
          });

          // Hide the drawer when opening the dialog
          if (drawerElement) {
            drawerElement.style.display = "none";
          }

          // Show the new prompt dialog (don't reset since we just set the data)
          setShowNewPromptDialogState(true);

          // Clear the pending prompt from localStorage
          localStorage.removeItem("pendingPromptToSave");
        } catch (error) {
          console.error("Error parsing pending prompt:", error);
          localStorage.removeItem("pendingPromptToSave");
        }
      }
    }

    // Always refresh recent prompts and favorites when the drawer is opened
    // This ensures we always have the latest data regardless of which tab is active
    const refreshRecentAndFavorites = async () => {
      try {
        console.log("Refreshing recent prompts and favorites on drawer open");

        // Fetch both in parallel for better performance
        const [recentRes, favoritesRes] = await Promise.all([
          fetch("/api/prompts?type=recent&limit=20", {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }),
          fetch("/api/prompts?type=favorites", {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }),
        ]);

        // Process recent prompts
        if (recentRes.ok) {
          const recentData = await recentRes.json();
          console.log("Fetched recent prompts data:", recentData);
          setRecentPrompts(recentData);
        } else {
          console.error("Failed to fetch recent prompts:", recentRes.status);
        }

        // Process favorites
        if (favoritesRes.ok) {
          const favoritesData = await favoritesRes.json();
          console.log("Fetched favorites data:", favoritesData);
          setFavoritePrompts(favoritesData);
        } else {
          console.error("Failed to fetch favorites:", favoritesRes.status);
        }
      } catch (error) {
        console.error("Error refreshing recent prompts and favorites:", error);
      }
    };

    // Always refresh recent prompts and favorites when the drawer is opened
    refreshRecentAndFavorites();

    // Load data based on active tab with safety checks
    if (activeTab === "starter") {
      // Load starter pack data if not already loaded
      if (!dataLoaded?.starter) {
        loadStarterPackData();
      }
    } else if (activeTab === "myPlaybook") {
      // Always refresh playbook data when the My Playbook tab is selected
      // This ensures we always have the latest data
      loadPlaybookData();
    }

    // Cleanup function
    return () => {
      isMounted.current = false;
    };
  }, [open, activeTab, hasBeenRendered]);

  // Add event listener for refreshing playbook data
  useEffect(() => {
    // Only set up the event listener if the drawer is open
    if (!open) return;

    // Function to handle the refresh event
    const handleRefreshPlaybookData = () => {
      console.log("Received refresh-playbook-data event");

      // If we're on the My Playbook tab, refresh the data immediately
      if (activeTab === "myPlaybook") {
        // Force a refresh by resetting the dataLoaded flag and calling loadPlaybookData
        setDataLoaded((prev) => ({ ...prev, playbook: false }));
        loadPlaybookData();
      } else {
        // If we're not on the My Playbook tab, just reset the flag so it will refresh when the tab is selected
        setDataLoaded((prev) => ({ ...prev, playbook: false }));

        // Still refresh recent prompts and favorites in the background
        Promise.all([
          fetch("/api/prompts?type=favorites", {
            headers: { "Cache-Control": "no-cache" },
          }).then((res) => (res.ok ? res.json() : null)),
          fetch("/api/prompts?type=recent&limit=20", {
            headers: { "Cache-Control": "no-cache" },
          }).then((res) => (res.ok ? res.json() : null)),
        ])
          .then(([favoritesData, recentData]) => {
            if (favoritesData) setFavoritePrompts(favoritesData);
            if (recentData) setRecentPrompts(recentData);
          })
          .catch((error) => {
            console.error("Error refreshing data in background:", error);
          });
      }
    };

    // Add event listener
    window.addEventListener("refresh-playbook-data", handleRefreshPlaybookData);

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener(
        "refresh-playbook-data",
        handleRefreshPlaybookData
      );
    };
  }, [open, activeTab]);

  // When user clicks "New Folder" button
  const handleNewFolderClick = () => {
    // Log the event when user clicks 'new folder' button
    logEvent(PlayBookEvent.PLAYBOOK_FEATURE, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      flow: PlayBookFlow.FOLDER_CREATION,
      step: PlayBookStepName.NEW_FOLDER,
      stepNumber: PlayBookStepNumber.NEW_FOLDER,
    });

    setShowNewFolderDialog(true);
  };

  // Create a new folder - optimized for better INP
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);

  const handleCreateFolder = () => {
    if (!newFolderName.trim()) {
      toast.error("Folder name cannot be empty");
      return;
    }

    // Log the event when user clicks 'create folder' button
    logEvent(PlayBookEvent.PLAYBOOK_FEATURE, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      flow: PlayBookFlow.FOLDER_CREATION,
      step: PlayBookStepName.CREATE_FOLDER,
      stepNumber: PlayBookStepNumber.CREATE_FOLDER,
      folderName: newFolderName,
    });

    // Immediately update UI state to show loading
    setIsCreatingFolder(true);

    // Close the dialog immediately for better UX
    setShowNewFolderDialog(false);

    // Show a loading toast that we'll update later
    const pendingToast = toast.loading("Creating folder...");

    // Perform the API call asynchronously
    createFolderAsync(newFolderName, pendingToast);
  };

  // Separate async function to handle the API call
  const createFolderAsync = async (
    folderName: string,
    pendingToast: string | number
  ) => {
    try {
      const response = await fetch("/api/prompts/folders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: folderName }),
      });

      if (!response.ok) {
        // Check for specific error message for duplicate folder name
        if (response.status === 409) {
          const errorMessage = await response.text();
          toast.dismiss(pendingToast);
          toast.error(errorMessage);
          setIsCreatingFolder(false);
          return;
        }
        throw new Error("Failed to create folder");
      }

      const newFolder = await response.json();

      // Update state after successful API call
      setUserFolders((prevFolders) => [...prevFolders, newFolder]);
      setPromptsByFolder((prevState) => ({ ...prevState, [newFolder.id]: [] }));
      setNewFolderName("");

      // Update the toast
      toast.dismiss(pendingToast);
      toast.success("Folder created successfully");
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.dismiss(pendingToast);
      toast.error("Failed to create folder");
    } finally {
      setIsCreatingFolder(false);
    }
  };

  // Create a new prompt
  const handleCreatePrompt = async () => {
    const { title, content } = newPromptData;

    // Don't allow creating a prompt while files are uploading
    if (newPromptUploadQueue.length > 0) {
      toast.error("Please wait for file uploads to complete");
      return;
    }

    if (!title.trim() || !content.trim()) {
      toast.error("Title and content are required");
      return;
    }

    // Log the event when user creates a new prompt
    logEvent(PlayBookEvent.PLAYBOOK_FEATURE, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      flow: PlayBookFlow.PROMPT_CREATION,
      step: PlayBookStepName.CREATE_PROMPT,
      stepNumber: PlayBookStepNumber.CREATE_PROMPT,
      hasAttachments: newPromptFiles.length > 0,
    });

    try {
      // Create a loading toast with more detailed information
      const loadingToast = toast.loading(
        newPromptFiles.length > 0
          ? `Creating prompt with ${newPromptFiles.length} attached document${newPromptFiles.length > 1 ? 's' : ''}...`
          : "Creating prompt..."
      );

      // Prepare the request data with files
      // Convert the files to the format expected by the API
      interface UploadedFile {
        name: string;
        url: string;
        contentType?: string;
        size?: number;
      }

      const files = newPromptFiles.length > 0
        ? newPromptFiles.map((file: UploadedFile) => ({
            fileName: file.name,
            fileUrl: file.url,
            fileType: file.contentType || 'application/octet-stream',
            fileSize: file.size || 0,
          }))
        : undefined;



      const requestData = {
        ...newPromptData,
        files
      };

      const response = await fetch("/api/prompts", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestData),
      });

      // Dismiss the loading toast
      toast.dismiss(loadingToast);

      if (!response.ok) {
        if (response.status === 409) {
          // Handle duplicate title error
          const errorMessage = await response.text();
          toast.dismiss(loadingToast);
          toast.error(errorMessage);
          return;
        }
        throw new Error("Failed to create prompt");
      }

      logEvent(PlayBookEvent.CREATE_PROMPT, requestData)

      const newPrompt = await response.json();

      // Reset form and close dialog immediately for better UX
      resetNewPromptDialog();

      // Close the dialog which will also restore the drawer visibility
      setShowNewPromptDialog(false);

      // Ensure the drawer is fully visible after dialog closes
      const drawerElement = document.querySelector(
        '[data-playbook="drawer"]'
      ) as HTMLElement;
      if (drawerElement) {
        drawerElement.style.visibility = "";
        drawerElement.style.opacity = "";
      }

      // Show success message with file count information
      toast.success(
        newPromptFiles.length > 0
          ? `Prompt created successfully with ${newPromptFiles.length} attached document${newPromptFiles.length > 1 ? 's' : ''}`
          : "Prompt created successfully"
      );

      // Reload the user's folder data to ensure the UI is up-to-date
      try {
        console.log("Refreshing My Playbook data after creating prompt");

        // Fetch user folders, favorites, and recent prompts in parallel
        const [userFoldersRes, favoritesRes, recentRes] = await Promise.all([
          fetch("/api/prompts/folders?type=user", {
            headers: { "Cache-Control": "no-cache" },
          }),
          fetch("/api/prompts?type=favorites", {
            headers: { "Cache-Control": "no-cache" },
          }),
          fetch("/api/prompts?type=recent", {
            headers: { "Cache-Control": "no-cache" },
          }),
        ]);

        // Process user folders
        const userFoldersData = await userFoldersRes.json();
        setUserFolders(userFoldersData);

        // Process favorites
        if (favoritesRes.ok) {
          const favoritesData = await favoritesRes.json();
          setFavoritePrompts(favoritesData);
        }

        // Process recent prompts
        if (recentRes.ok) {
          const recentData = await recentRes.json();
          setRecentPrompts(recentData);
        }

        // Fetch prompts for all user folders
        const promptsMap: Record<string, Prompt[]> = {};
        const fetchPromises = userFoldersData.map(
          async (folder: PromptFolder) => {
            const promptsRes = await fetch(
              `/api/prompts?folderId=${folder.id}`,
              {
                headers: { "Cache-Control": "no-cache" },
              }
            );
            const promptsData = await promptsRes.json();
            return { folderId: folder.id, prompts: promptsData };
          }
        );

        // Wait for all promises to resolve
        const results = await Promise.all(fetchPromises);

        // Build the prompts map
        results.forEach((result) => {
          promptsMap[result.folderId] = result.prompts;
        });

        // Update promptsByFolder with user folder prompts
        setPromptsByFolder((prevState) => {
          // Preserve system folder prompts
          const systemFolderPrompts = Object.entries(prevState)
            .filter(([folderId]) =>
              systemFolders.some((f) => f.id === folderId)
            )
            .reduce(
              (acc, [folderId, prompts]) => ({ ...acc, [folderId]: prompts }),
              {}
            );

          return {
            ...systemFolderPrompts,
            ...promptsMap,
          };
        });
      } catch (refreshError) {
        console.error(
          "Error refreshing My Playbook data after creating prompt:",
          refreshError
        );
        // Fall back to just updating the specific folder
        const actualFolderId = newPrompt.folderId;
        setPromptsByFolder({
          ...promptsByFolder,
          [actualFolderId]: [
            ...(promptsByFolder[actualFolderId] || []),
            newPrompt,
          ],
        });
      }
    } catch (error) {
      console.error("Error creating prompt:", error);
      toast.error("Failed to create prompt");
    }
  };

  // Update an existing prompt
  const handleUpdatePrompt = async (
    promptToUpdate: Prompt,
    options?: { skipCustomizeDialog?: boolean }
  ) => {
    // Check if this is a system prompt (we don't want to update those in the database)
    const isSystemPrompt = systemFolders.some(
      (folder) => folder.id === promptToUpdate.folderId
    );

    // Track if this was called from the edit dialog
    const isFromDialog =
      editPromptData && editPromptData.id === promptToUpdate.id;

    // If this is a system prompt and we haven't explicitly asked to skip the customize dialog
    // (and it's not coming from the inline editor in PromptCard), show the customize dialog
    if (isSystemPrompt && !options?.skipCustomizeDialog && !isFromDialog) {
      // For system prompts, we'll just customize and use them instead of updating
      setCustomizePromptData({
        title: promptToUpdate.title,
        content: promptToUpdate.content,
      });
      setShowCustomizePromptDialog(true);
      return;
    }

    try {
      console.log("Updating prompt:", promptToUpdate.id, promptToUpdate.title);

      // First, optimistically update the UI to prevent the "failed to load prompt" error
      // This ensures the UI is updated immediately, even before the server responds
      const optimisticPrompt = { ...promptToUpdate, updatedAt: new Date() };

      // Update the prompt in the promptsByFolder state optimistically
      // Check if the folder has changed
      const originalFolderId = promptToUpdate.folderId;
      const newFolderId = promptToUpdate.folderId;
      // Since we're comparing the same values, this will always be false
      // This is intentional as we're not changing folders at this point
      const hasFolderChanged = false;

      setPromptsByFolder((prevState) => {
        let updatedState = { ...prevState };

        if (hasFolderChanged) {
          // If folder has changed, remove from original folder and add to new folder
          console.log(
            `Folder changed from ${originalFolderId} to ${newFolderId}`
          );

          // Remove from original folder
          if (updatedState[originalFolderId]) {
            updatedState[originalFolderId] = updatedState[
              originalFolderId
            ].filter((p) => p.id !== promptToUpdate.id);
          }

          // Add to new folder
          updatedState[newFolderId] = [
            ...(updatedState[newFolderId] || []),
            optimisticPrompt,
          ];
        } else {
          // If folder hasn't changed, just update in place
          const folderPrompts = updatedState[newFolderId] || [];
          updatedState[newFolderId] = folderPrompts.map((p) =>
            p.id === promptToUpdate.id ? optimisticPrompt : p
          );
        }

        return updatedState;
      });

      // Update in favorites if present (optimistically)
      if (favoritePrompts.some((p) => p.id === promptToUpdate.id)) {
        setFavoritePrompts((prevFavorites) =>
          prevFavorites.map((p) =>
            p.id === promptToUpdate.id
              ? {
                  ...optimisticPrompt,
                  folderName: p.folderName,
                  isSystemFolder: p.isSystemFolder,
                }
              : p
          )
        );
      }

      // Update in recent if present (optimistically)
      if (recentPrompts.some((p) => p.id === promptToUpdate.id)) {
        setRecentPrompts((prevRecent) =>
          prevRecent.map((p) =>
            p.id === promptToUpdate.id
              ? {
                  ...optimisticPrompt,
                  folderName: p.folderName,
                  isSystemFolder: p.isSystemFolder,
                }
              : p
          )
        );
      }

      // If this was called from the dialog, close it immediately after optimistic update
      if (isFromDialog) {
        setEditPromptData(null);
        setShowEditPromptDialog(false);
        // Clear any uploaded files
        clearEditPromptFiles();
      }

      // Now make the actual API call
      // Include any uploaded files if this is from the edit dialog
      let promptWithFiles = promptToUpdate;

      if (isFromDialog && editPromptFiles.length > 0) {
        // Define the type for edit prompt files
        interface EditPromptFile {
          name: string;
          url: string;
          contentType?: string;
          size?: number;
        }

        // Convert the files to the format expected by the API
        const files = editPromptFiles.map((file: EditPromptFile) => ({
          fileName: file.name,
          fileUrl: file.url,
          fileType: file.contentType || 'application/octet-stream',
          fileSize: file.size || 0,
        }));



        // Define an interface that extends Prompt to include files
        interface PromptWithFiles extends Prompt {
          files?: Array<{
            fileName: string;
            fileUrl: string;
            fileType: string;
            fileSize: number;
            id?: string;
          }>;
        }

        promptWithFiles = {
          ...promptToUpdate,
          files
        } as PromptWithFiles;
      }

      const response = await fetch("/api/prompts", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
        body: JSON.stringify(promptWithFiles),
      });

      if (!response.ok) {
        if (response.status === 409) {
          // Handle duplicate title error
          const errorMessage = await response.text();
          toast.error(errorMessage);
          return;
        }
        const errorText = await response.text();
        console.error("Server response error:", response.status, errorText);
        throw new Error(
          `Failed to update prompt: ${response.status} ${errorText}`
        );
      }

      const updatedPrompt = await response.json();
      console.log(
        "Prompt updated successfully:",
        updatedPrompt.id,
        updatedPrompt.title
      );

      // Update the state with the server response (to ensure consistency)
      // Check if the folder has changed
      const serverOriginalFolderId = promptToUpdate.folderId;
      const serverNewFolderId = updatedPrompt.folderId;
      const serverFolderChanged = serverOriginalFolderId !== serverNewFolderId;

      setPromptsByFolder((prevState) => {
        let updatedState = { ...prevState };

        if (serverFolderChanged) {
          // If folder has changed, remove from original folder and add to new folder
          console.log(
            `Server confirmed folder change from ${serverOriginalFolderId} to ${serverNewFolderId}`
          );

          // Remove from original folder
          if (updatedState[serverOriginalFolderId]) {
            updatedState[serverOriginalFolderId] = updatedState[
              serverOriginalFolderId
            ].filter((p) => p.id !== updatedPrompt.id);
          }

          // Add to new folder
          updatedState[serverNewFolderId] = [
            ...(updatedState[serverNewFolderId] || []),
            updatedPrompt,
          ];
        } else {
          // If folder hasn't changed, just update in place
          const updatedFolderPrompts = updatedState[serverNewFolderId] || [];
          updatedState[serverNewFolderId] = updatedFolderPrompts.map((p) =>
            p.id === updatedPrompt.id ? updatedPrompt : p
          );
        }

        return updatedState;
      });

      // Update in favorites if present (with server data)
      if (favoritePrompts.some((p) => p.id === updatedPrompt.id)) {
        setFavoritePrompts((prevFavorites) =>
          prevFavorites.map((p) =>
            p.id === updatedPrompt.id
              ? {
                  ...updatedPrompt,
                  folderName: p.folderName,
                  isSystemFolder: p.isSystemFolder,
                }
              : p
          )
        );
      }

      // Update in recent if present (with server data)
      if (recentPrompts.some((p) => p.id === updatedPrompt.id)) {
        setRecentPrompts((prevRecent) =>
          prevRecent.map((p) =>
            p.id === updatedPrompt.id
              ? {
                  ...updatedPrompt,
                  folderName: p.folderName,
                  isSystemFolder: p.isSystemFolder,
                }
              : p
          )
        );
      }

      // Show success message
      toast.success("Prompt updated successfully");

      // If the folder has changed, refresh the folder data to ensure the UI is up-to-date
      if (serverFolderChanged && serverNewFolderId) {
        // Refresh the data for the new folder
        try {
          console.log(`Refreshing data for folder ${serverNewFolderId}`);
          const promptsRes = await fetch(
            `/api/prompts?folderId=${serverNewFolderId}`,
            {
              headers: { "Cache-Control": "no-cache" },
            }
          );

          if (promptsRes.ok) {
            const promptsData = await promptsRes.json();
            setPromptsByFolder((prevState) => ({
              ...prevState,
              [serverNewFolderId]: promptsData,
            }));
          }
        } catch (refreshError) {
          console.error(
            `Error refreshing folder ${serverNewFolderId}:`,
            refreshError
          );
        }
      }
    } catch (error) {
      console.error("Error updating prompt:", error);
      toast.error("Failed to update prompt");

      // If we closed the dialog but the update failed, we need to reopen it
      if (isFromDialog) {
        setEditPromptData(promptToUpdate);
        setShowEditPromptDialog(true);
      }
    }
  };

  // Delete a prompt
  const handleDeletePrompt = async (promptId: string, folderId: string) => {
    if (!confirm("Are you sure you want to delete this prompt?")) return;

    try {
      const response = await fetch(`/api/prompts?id=${promptId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete prompt");
      if (response.ok) {
        logEvent(PlayBookEvent.DELETE_PROMPT, {
          promptId
        })
      }
      // Remove from promptsByFolder
      setPromptsByFolder({
        ...promptsByFolder,
        [folderId]: promptsByFolder[folderId].filter((p) => p.id !== promptId),
      });

      // Remove from favorites if present
      setFavoritePrompts(favoritePrompts.filter((p) => p.id !== promptId));

      // Remove from recent if present
      setRecentPrompts(recentPrompts.filter((p) => p.id !== promptId));

      toast.success("Prompt deleted successfully");
    } catch (error) {
      console.error("Error deleting prompt:", error);
      toast.error("Failed to delete prompt");
    }
  };

  // Remove a prompt from the recently used list
  const handleRemoveFromRecent = async (promptId: string) => {
    try {
      // Optimistically update the UI first
      setRecentPrompts(recentPrompts.filter((p) => p.id !== promptId));

      // Call the API to remove the prompt from recently used
      const response = await fetch(`/api/prompts/recent?id=${promptId}`, {
        method: "DELETE",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to remove prompt from recently used");
      }

      toast.success("Removed from recently used");
    } catch (error) {
      console.error("Error removing prompt from recently used:", error);
      toast.error("Failed to remove from recently used");

      // Refresh the recent prompts to ensure UI is in sync with server
      try {
        const recentRes = await fetch("/api/prompts?type=recent&limit=20", {
          headers: {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
        });

        if (recentRes.ok) {
          const recentData = await recentRes.json();
          setRecentPrompts(recentData);
        }
      } catch (refreshError) {
        console.error("Error refreshing recent prompts:", refreshError);
      }
    }
  };

  // Toggle favorite status
  const handleToggleFavorite = async (promptId: string) => {
    // Special case for refreshing favorites after creating a new prompt from legacy
    if (promptId === "refresh-favorites") {
      console.log("Refreshing favorites after creating a new prompt");
      try {
        const favoritesRes = await fetch("/api/prompts?type=favorites", {
          headers: { "Cache-Control": "no-cache" }, // Don't cache favorites
        });
        if (favoritesRes.ok) {
          const favoritesData = await favoritesRes.json();
          setFavoritePrompts(favoritesData);
        }
      } catch (refreshError) {
        console.error("Error refreshing favorites:", refreshError);
      }
      return;
    }

    try {
      console.log("Toggling favorite for prompt ID:", promptId);

      // Make the API call to toggle the favorite status
      const response = await fetch("/api/prompts", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
        },
        body: JSON.stringify({ id: promptId, action: "favorite" }),
      });

      if (!response.ok) throw new Error("Failed to update favorite status");

      const { prompt: updatedPrompt } = await response.json();
      console.log("Updated prompt from server:", updatedPrompt);

      // Immediately fetch the latest favorites to ensure we have the correct state
      const favoritesRes = await fetch("/api/prompts?type=favorites", {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (favoritesRes.ok) {
        const favoritesData = await favoritesRes.json();
        console.log("Updated favorites data:", favoritesData);
        setFavoritePrompts(favoritesData);
      }

      // Update in recent prompts if present
      setRecentPrompts((prevRecent) =>
        prevRecent.map((p) =>
          p.id === updatedPrompt.id
            ? { ...p, isFavorite: updatedPrompt.isFavorite }
            : p
        )
      );

      // For user prompts, update the promptsByFolder state
      if (!updatedPrompt.isSystemFolder) {
        setPromptsByFolder((prevState) => {
          const folderId = updatedPrompt.folderId;
          if (prevState[folderId]) {
            return {
              ...prevState,
              [folderId]: prevState[folderId].map((p) =>
                p.id === updatedPrompt.id ? { ...p, isFavorite: updatedPrompt.isFavorite } : p
              ),
            };
          }
          return prevState;
        });
      }

      toast.success(
        updatedPrompt.isFavorite
          ? "Added to favorites"
          : "Removed from favorites"
      );
    } catch (error) {
      console.error("Error toggling favorite:", error);
      toast.error("Failed to update favorite status");
      
      // Refresh data to ensure UI is in sync with server
      try {
        // Fetch latest favorites
        const favoritesRes = await fetch("/api/prompts?type=favorites", {
          headers: { "Cache-Control": "no-cache" },
        });
        if (favoritesRes.ok) {
          const favoritesData = await favoritesRes.json();
          setFavoritePrompts(favoritesData);
        }
        
        // Force refresh of starter pack data
        if (activeTab === "starter") {
          // Force refresh of starter pack data to ensure favorites are correctly reflected
          setDataLoaded((prev) => ({ ...prev, starter: false }));
          loadStarterPackData();
        } else if (activeTab === "myPlaybook") {
          loadPlaybookData();
        }
      } catch (refreshError) {
        console.error("Error refreshing data after error:", refreshError);
      }
    }
  };

  // Use a prompt
  const handleUsePrompt = async (prompt: Prompt | IStarterPrompt) => {
    try {
      // Log the event when user clicks 'use' button
      logEvent(PlayBookEvent.PLAYBOOK_FEATURE, {
        userId: user?.id,
        userEmail: user?.email,
        isAdmin: user?.isAdmin,
        step: PlayBookStepName.USE_PROMPT,
        stepNumber: PlayBookStepNumber.USE_PROMPT,
        promptId: "id" in prompt ? prompt.id : "starter-prompt",
        promptTitle: prompt.title,
      });

      Logger.info("Using prompt", {
        promptId: "id" in prompt ? prompt.id : "starter-prompt",
        promptTitle: prompt.title,
        isDbPrompt: "id" in prompt
      });

      // Add a loading toast to indicate the action is in progress
      const loadingToast = toast.loading("Using prompt...");

      // For database prompts, mark as used and get associated files
      if ("id" in prompt) {
        console.log(`Using prompt with ID: ${prompt.id}`);

        // Fetch the prompt with its files
        let promptFiles = [];
        try {
          Logger.info(`Fetching files for prompt ${prompt.id} to use in chat`);

          const promptDetailsResponse = await fetch(
            `/api/prompts?id=${prompt.id}`,
            {
              headers: {
                "Cache-Control": "no-cache, no-store, must-revalidate",
                Pragma: "no-cache",
                Expires: "0",
              },
            }
          );

          if (promptDetailsResponse.ok) {
            const promptDetails = await promptDetailsResponse.json();
            if (promptDetails.files && Array.isArray(promptDetails.files) && promptDetails.files.length > 0) {
              // Define the type for prompt files
              interface PromptFile {
                fileName: string;
                fileUrl: string;
                fileType: string;
                fileSize?: number;
                id?: string;
              }

              // Convert the files to the format expected by the chat component
              promptFiles = promptDetails.files.map((file: PromptFile) => {
                // Extract the original document ID from the file URL
                const extractedDocumentId = extractDocumentIdFromUrl(file.fileUrl);

                // Expand normalized MIME type back to full form for chat processing
                const expandedContentType = expandMimeType(file.fileType);

                Logger.info("Processing prompt file for chat:", {
                  fileName: file.fileName,
                  storedFileType: file.fileType,
                  expandedContentType: expandedContentType,
                  fileUrl: file.fileUrl,
                  extractedDocumentId,
                  promptFileId: file.id
                });

                return {
                  name: file.fileName,
                  url: file.fileUrl,
                  contentType: expandedContentType, // Use expanded MIME type for proper processing
                  size: file.fileSize || 0,
                  // Use the extracted document ID from URL, fallback to generating new one
                  document_id: extractedDocumentId || generateUUID(),
                  uploaded: true,
                  _attachment_id: generateUUID()
                };
              });

              Logger.info(
                `Found ${promptFiles.length} files associated with prompt ${prompt.id}:`,
                promptFiles
              );

              // Dispatch the files to the chat component
              const fileEvent = new CustomEvent('prompt-files-available', {
                detail: {
                  files: promptFiles,
                  messageId: generateUUID(), // Generate a unique message ID
                  isFromPlaybook: true // Flag to indicate files are from playbook
                }
              });
              window.dispatchEvent(fileEvent);
              Logger.info('Dispatched prompt files to chat component:', promptFiles);
            } else {
              const removeallAttachmentsEvent = new CustomEvent('remove-all-attachments');
              window.dispatchEvent(removeallAttachmentsEvent);
              Logger.info(`No files found for prompt ${prompt.id}`);
            }
          } else {
            throw new Error(`Failed to fetch prompt details: ${promptDetailsResponse.status}`);
          }
        } catch (fileError) {
          Logger.error("Error fetching prompt files:", fileError);
          toast.error("Failed to load attached documents, but continuing with prompt");
        }

        // Then update the server to mark as used
        try {
          console.log(`Marking prompt ${prompt.id} as used`);
          const response = await fetch("/api/prompts", {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
            body: JSON.stringify({
              id: prompt.id,
              action: "use",
            }),
          });

          // Dismiss the loading toast
          toast.dismiss(loadingToast);

          if (!response.ok) {
            console.error(
              `Failed to mark prompt ${prompt.id} as used. Status: ${response.status}`
            );
            toast.error("Failed to update recently used prompts");
          } else {
            const data = await response.json();
            console.log("Server response after marking prompt as used:", data);

            if (data.recentPrompts && data.recentPrompts.length > 0) {
              console.log(
                "Server returned updated recent prompts:",
                data.recentPrompts
              );
              // Update the state with the server's version of the recent prompts
              setRecentPrompts(data.recentPrompts);
              toast.success("Prompt used successfully");
            } else {
              console.log("Server did not return updated recent prompts");
              // Manually fetch the latest recent prompts
              try {
                const recentRes = await fetch(
                  "/api/prompts?type=recent&limit=20",
                  {
                    headers: {
                      "Cache-Control": "no-cache, no-store, must-revalidate",
                      Pragma: "no-cache",
                      Expires: "0",
                      "X-Refresh-Cache": Math.random().toString(),
                    },
                  }
                );

                if (recentRes.ok) {
                  const recentData = await recentRes.json();
                  console.log("Manually fetched recent prompts:", recentData);
                  setRecentPrompts(recentData);
                }
              } catch (fetchError) {
                console.error("Error fetching recent prompts:", fetchError);
              }
            }

            // Trigger a custom event to notify the Playbook component to refresh its data
            if (typeof window !== "undefined") {
              const refreshEvent = new CustomEvent("refresh-playbook-data");
              window.dispatchEvent(refreshEvent);
            }
          }
        } catch (apiError) {
          console.error("Error marking prompt as used:", apiError);
          toast.dismiss(loadingToast);
          toast.error("Error updating recently used prompts");
        }

        // If we have files associated with the prompt, add them to the chat
        if (promptFiles.length > 0) {
          try {
            // Generate a message ID for document association
            const messageId = generateUUID();
            Logger.info(`Storing ${promptFiles.length} files in localStorage for chat with messageId: ${messageId}`);

            // Define the type for prompt files
            interface PromptFile {
              name: string;
              url: string;
              contentType: string;
              size?: number;
              document_id?: string;
              uploaded?: boolean;
            }

            // Add the messageId to each file for proper association
            const filesWithMessageId = promptFiles.map((file: PromptFile) => ({
              ...file,
              messageId
            }));

            // Store the files in localStorage to be picked up by the chat component
            localStorage.setItem("pendingChatFiles", JSON.stringify(filesWithMessageId));

            // Also store the messageId separately for the chat component to use
            localStorage.setItem("pendingMessageId", messageId);

            // Pre-associate documents with the message ID
            try {
              const success = await associateDocumentsWithMessage(messageId);
              if (success) {
                Logger.info("Pre-associated documents with message:", { messageId });
              } else {
                Logger.warn("Failed to pre-associate documents with message:", { messageId });
              }
            } catch (associationError) {
              Logger.error("Error pre-associating documents with message:", {
                messageId,
                error: associationError
              });
            }

            Logger.info("Successfully stored prompt files for chat to use", {
              count: promptFiles.length,
              messageId
            });
          } catch (error) {
            Logger.error("Error storing prompt files for chat:", error);
            toast.error("Error preparing documents for chat");
          }
        }

        // CRITICAL: Store the current prompt text so the fix can use it (if we have files)
        if (promptFiles.length > 0) {
          // Get the content from the prompt
          const content =
            "content" in prompt
              ? prompt.content
              : (prompt as IStarterPrompt).defaultText;

          localStorage.setItem("pendingPromptText", content);
          Logger.info("Stored prompt text for robust temporary fix:", {
            promptLength: content.length,
            hasFiles: promptFiles.length > 0
          });
        }
      } else {
        // For non-database prompts (e.g., starter pack prompts that haven't been saved)
        toast.dismiss(loadingToast);
        toast.success("Prompt used successfully");
      }

      // Add a class for sliding animation
      const drawerElement = document.querySelector(".playbook-drawer");
      if (drawerElement) {
        drawerElement.classList.add("slide-out-right");

        // Wait for animation to complete before closing
        setTimeout(() => {
          // Don't reset dataLoaded.starter flag to avoid unnecessary reloading
          onClose();
        }, 300); // Match this to CSS animation duration
      } else {
        // If we can't find the element, just close normally
        onClose();
      }

      // Get the content from the prompt
      const content =
        "content" in prompt
          ? prompt.content
          : (prompt as IStarterPrompt).defaultText;



      // First, check if we're on a chat page by looking for the React setInput function in the global window object
      if (
        typeof window !== "undefined" &&
        (window as any).__CHAT_INPUT_SETTER__
      ) {
        // We're on a chat page, use the React setter function to update the input state
        const setInputFunction = (window as any).__CHAT_INPUT_SETTER__;
        setInputFunction(content);

        // Also update the DOM element for good measure
        const chatInputElement = document.querySelector(
          'textarea[placeholder*="Ask, draft, or research"]'
        ) as HTMLTextAreaElement;
        if (chatInputElement) {
          chatInputElement.focus();
        }

        // If we have files, trigger the file upload handler in the chat component
        if ("id" in prompt && (window as any).__CHAT_HANDLE_PROMPT_FILES__) {
          Logger.info("Triggering file handler for chat component");
          setTimeout(() => {
            try {
              (window as any).__CHAT_HANDLE_PROMPT_FILES__();
              Logger.info("Successfully triggered file handler");
            } catch (handlerError) {
              Logger.error("Error in file handler:", handlerError);
              toast.error("Error loading documents into chat");
            }
          }, 100);
        }

        // Option to auto-submit the form
        // Uncomment the following lines to automatically submit the form after inserting the prompt
        // if ((window as any).__CHAT_SUBMIT_FORM__) {
        //   setTimeout(() => {
        //     (window as any).__CHAT_SUBMIT_FORM__();
        //   }, 100);
        // }
      } else {
        // We're not on a chat page, use localStorage method to pass to new chat
        localStorage.setItem("pendingChatInput", content);

        // Navigate to the root page which creates a new chat
        router.push("/");
      }
    } catch (error) {
      console.error("Error using prompt:", error);
      toast.error("Failed to use prompt");
    }
  };

  // Use a customized prompt
  const handleUseCustomizedPrompt = () => {
    if (!customizePromptData) return;

    // Close the dialog
    setShowCustomizePromptDialog(false);

    // Add a class for sliding animation
    const drawerElement = document.querySelector(".playbook-drawer");
    if (drawerElement) {
      drawerElement.classList.add("slide-out-right");

      // Wait for animation to complete before closing
      setTimeout(() => {
        onClose();
      }, 300); // Match this to CSS animation duration
    } else {
      // If we can't find the element, just close normally
      onClose();
    }

    // First, check if we're on a chat page by looking for the React setInput function in the global window object
    if (
      typeof window !== "undefined" &&
      (window as any).__CHAT_INPUT_SETTER__
    ) {
      // We're on a chat page, use the React setter function to update the input state
      const setInputFunction = (window as any).__CHAT_INPUT_SETTER__;
      setInputFunction(customizePromptData.content);

      // Also update the DOM element for good measure
      const chatInputElement = document.querySelector(
        'textarea[placeholder*="Ask, draft, or research"]'
      ) as HTMLTextAreaElement;
      if (chatInputElement) {
        chatInputElement.focus();
      }

      // Option to auto-submit the form
      // Uncomment the following lines to automatically submit the form after inserting the prompt
      // if ((window as any).__CHAT_SUBMIT_FORM__) {
      //   setTimeout(() => {
      //     (window as any).__CHAT_SUBMIT_FORM__();
      //   }, 100);
      // }
    } else {
      // We're not on a chat page, use localStorage method to pass to new chat
      localStorage.setItem("pendingChatInput", customizePromptData.content);

      // Navigate to the root page which creates a new chat
      router.push("/");
    }
  };

  // Filter prompts by search query
  const filterPromptsBySearch = (prompts: Prompt[]) => {
    if (!searchQuery) return prompts;
    const query = searchQuery.toLowerCase();
    return prompts.filter(
      (p) =>
        p.title.toLowerCase().includes(query) ||
        p.content.toLowerCase().includes(query)
    );
  };

  // Filter folders by search query
  // Only show folders that either:
  // 1. Have a name that matches the search query, OR
  // 2. Contain prompts that match the search query
  const filterFoldersBySearch = (
    folders: PromptFolder[],
    folderPromptsMap: Record<string, Prompt[]>
  ) => {
    if (!searchQuery) return folders;
    const query = searchQuery.toLowerCase();

    return folders.filter((folder) => {
      // Check if folder name matches the search query
      if (folder.name.toLowerCase().includes(query)) {
        return true;
      }

      // Check if any prompts in this folder match the search query
      const folderPrompts = folderPromptsMap[folder.id] || [];
      return folderPrompts.some(
        (prompt) =>
          prompt.title.toLowerCase().includes(query) ||
          prompt.content.toLowerCase().includes(query)
      );
    });
  };

  const handleBack = () => {
    if (currentStep > 1) {
      // If going back from the Preview step, reset the refined prompt and related states
      if (currentStep === 3) {
        setRefinedPrompt("");
        setEditedRefinedPrompt("");
        setIsEditingRefinedPrompt(false);
        setPromptName("");
        setShowSaveDrawer(false);
      }
      setCurrentStep(currentStep - 1);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      // For step 2, only proceed if either text references or files are provided
      if (currentStep === 2 && !textReferences && uploadedFiles.length === 0) {
        toast.error("Please add at least one reference or upload a file");
        return;
      }

      // Log the event when user clicks 'next' button in Prompt Architect
      logEvent(PlayBookEvent.PLAYBOOK_FEATURE, {
        userId: user?.id,
        userEmail: user?.email,
        isAdmin: user?.isAdmin,
        flow: PlayBookFlow.PROMPT_ARCHITECT,
        step: `${PlayBookStepName.CLICK_NEXT_BUTTON} ${steps[currentStep - 1].title}`,
        stepNumber: (currentStep + 1).toString(),
      });

      // First advance to the next step immediately for better UX
      setCurrentStep(currentStep + 1);

      // If moving to the Preview step, generate the refined prompt in the background
      if (currentStep === 2) {
        // Set loading state
        setIsGeneratingPrompt(true);

        // Generate the refined prompt asynchronously
        (async () => {
          try {
            // Call the API to generate the refined prompt
            const response = await fetch("/api/prompts/refine", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                promptGoal,
                textReferences,
                // We'll handle file content extraction on the server side
                fileNames: uploadedFiles.map((file) => file.name),
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to generate refined prompt");
            }

            const data = await response.json();
            setRefinedPrompt(data.refinedPrompt);
          } catch (error) {
            console.error("Error generating refined prompt:", error);
            toast.error(
              "Failed to generate refined prompt. Using basic format instead."
            );

            // Create a basic prompt format as fallback
            let basicPrompt = `Goal: ${promptGoal}\n\n`;

            if (textReferences) {
              basicPrompt += `References:\n${textReferences}\n\n`;
            }

            if (uploadedFiles.length > 0) {
              basicPrompt += "Attached files:\n";
              uploadedFiles.forEach((file) => {
                basicPrompt += `- ${file.name}\n`;
              });
            }

            setRefinedPrompt(basicPrompt);
          } finally {
            setIsGeneratingPrompt(false);
          }
        })();
      }
    } else if (currentStep === steps.length) {
      // This is the final step, handle the finish action
      handleFinish();
    }
  };

  // Handle saving the prompt to My Playbook
  const handleSavePrompt = async (promptData: {
    title: string;
    content: string;
    folderId: string;
  }) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Saving prompt to My Playbook...");

      try {
        // Use files that are already uploaded by our custom hook
        let files: Array<{
          fileName: string;
          fileUrl: string;
          fileType: string;
          fileSize: number;
          document_id: string;
        }> = [];

        if (uploadedFiles.length > 0) {
          console.log(
            `Using ${uploadedFiles.length} already uploaded files for prompt...`
          );

          // Define the type for uploaded files
          interface UploadedFile {
            name: string;
            url: string;
            contentType: string;
            document_id: string;
          }

          // Convert the files to the format expected by the API
          files = uploadedFiles.map((file: UploadedFile) => ({
            fileName: file.name,
            fileUrl: file.url,
            fileType: file.contentType,
            fileSize: 0, // Size not needed for API
            document_id: file.document_id,
          }));

          console.log(`Using ${files.length} files for prompt:`, files);
        }

        // Call the API to save the prompt with files
        console.log("Saving prompt to API with files:", files);
        const response = await fetch("/api/prompts", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            ...promptData,
            files: files,
          }),
        });

        if (!response.ok) {
          if (response.status === 409) {
            // Handle duplicate title error
            const errorMessage = await response.text();
            toast.dismiss(loadingToast);
            toast.error(errorMessage);
            return false;
          }
          const errorText = await response.text();
          throw new Error(
            `Failed to save prompt: ${response.status} ${errorText}`
          );
        }

        // Dismiss loading toast
        toast.dismiss(loadingToast);
        toast.success("Prompt saved to My Playbook");

        // Close the save drawer
        setShowSaveDrawer(false);

        // Reset form
        setPromptName("");

        // Trigger a refresh of the playbook data
        if (typeof window !== "undefined") {
          const refreshEvent = new CustomEvent("refresh-playbook-data");
          window.dispatchEvent(refreshEvent);
        }

        return true;
      } catch (error) {
        console.error("Error in file upload or prompt save:", error);
        toast.dismiss(loadingToast);
        toast.error("Failed to save prompt to My Playbook");
        return false;
      }
    } catch (error) {
      console.error("Error in handleSavePrompt:", error);
      toast.error("Failed to save prompt to My Playbook");
      return false;
    }
  };

  const handleFinish = async () => {
    // Log the Prompt Architect Finish button click event with user ID and details
    logEvent(AmplifyEvent.PROMPT_ARCHITECT_FINISH, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      hasTextReferences: !!textReferences.trim(),
      hasFiles: uploadedFiles.length > 0,
      fileCount: uploadedFiles.length,
    });

    // Show loading toast and set a timeout to ensure it disappears after 2 seconds
    const loadingToast = toast.loading(
      "Processing files and preparing prompt..."
    );

    // Set a timeout to ensure the toast disappears after 2 seconds regardless of code path
    setTimeout(() => {
      toast.dismiss(loadingToast);
    }, 2000);

    // Use the refined prompt if available, otherwise construct a basic one
    let finalPrompt = refinedPrompt;

    // If for some reason the refined prompt is empty, create a basic one
    if (!finalPrompt) {
      finalPrompt = `Goal: ${promptGoal}\n\n`;

      finalPrompt += "References:\n";
      if (textReferences) {
        finalPrompt += textReferences + "\n\n";
      }

      if (uploadedFiles.length > 0) {
        finalPrompt += "Attached files:\n";
        uploadedFiles.forEach((file) => {
          finalPrompt += `- ${file.name}\n`;
        });
      }
    }

    try {
      // Generate a message ID early - this will be used for document association
      const messageId = generateUUID();
      Logger.info("Generated message ID for document association:", messageId);

      // Define the type for processed files
      interface ProcessedFile {
        name: string;
        url: string;
        contentType: string;
        size?: number;
        document_id?: string;
        uploaded?: boolean;
        messageId?: string;
      }

      // If we have uploaded files, use our custom hook to handle them
      let processedFiles: ProcessedFile[] = [];
      if (uploadedFiles.length > 0) {
        Logger.info(
          `Using ${uploadedFiles.length} already uploaded files for prompt...`,
          { chatId }
        );

        // Define the type for uploaded files
        interface UploadedFile {
          name: string;
          url: string;
          contentType: string;
          size?: number;
          document_id?: string;
          uploaded?: boolean;
        }

        // Files are already uploaded by our custom hook, just use them directly
        // Add the messageId to each file for proper association
        processedFiles = uploadedFiles.map((file: UploadedFile) => ({
          ...file,
          messageId, // Add the message ID to each file
        }));

        if (processedFiles.length === 0) {
          // Try to dismiss the loading toast again as a backup
          // (The primary dismissal is handled by the timeout set at the beginning of this function)
          toast.dismiss(loadingToast);
          toast.error("No files were successfully uploaded");
          return;
        }

        Logger.info(`Using ${processedFiles.length} files for prompt:`, {
          files: processedFiles,
          messageId,
        });

        // Store the files in localStorage to be picked up by the chat component
        // Use the EXACT same approach as Playbook prompts for consistency
        localStorage.setItem(
          "pendingChatFiles",
          JSON.stringify(processedFiles)
        );

        // Also store the messageId separately for the chat component to use
        localStorage.setItem("pendingMessageId", messageId);

        // CRITICAL: Store the current prompt text so the fix can use it
        localStorage.setItem("pendingPromptText", finalPrompt);

        Logger.info("Stored processed files and prompt text in localStorage for chat to use:", {
          count: processedFiles.length,
          messageId,
          promptLength: finalPrompt.length,
        });

        // Pre-associate documents with the message ID
        // This ensures the documents are linked to the message in the database
        // even if the chat component fails to do so
        try {
          const success = await associateDocumentsWithMessage(messageId);
          if (success) {
            Logger.info("Pre-associated documents with message:", {
              messageId,
            });
          } else {
            Logger.warn("Failed to pre-associate documents with message:", {
              messageId,
            });
          }
        } catch (associationError) {
          Logger.error("Error pre-associating documents with message:", {
            messageId,
            error: associationError,
          });
        }
      }

      // Try to dismiss the loading toast again as a backup
      // (The primary dismissal is handled by the timeout set at the beginning of this function)
      toast.dismiss(loadingToast);

      // Simply close the drawer
      onClose();

      // Use the same mechanism as handleUsePrompt to insert the prompt into the chat
      if (
        typeof window !== "undefined" &&
        (window as any).__CHAT_INPUT_SETTER__
      ) {
        // We're on a chat page, use the React setter function to update the input state
        const setInputFunction = (window as any).__CHAT_INPUT_SETTER__;
        setInputFunction(finalPrompt);

        // Also update the DOM element for good measure
        const chatInputElement = document.querySelector(
          'textarea[placeholder*="Ask, draft, or research"]'
        ) as HTMLTextAreaElement;
        if (chatInputElement) {
          chatInputElement.focus();
        }

        // If we have files, trigger the file upload handler in the chat component
        if (
          processedFiles.length > 0 &&
          (window as any).__CHAT_HANDLE_PROMPT_FILES__
        ) {
          Logger.info("Triggering file handler for chat component", {
            messageId,
          });

          // No auto-submit needed - the improved temporary fix will handle document processing
          // Files are already stored in localStorage above, no need to store again

          setTimeout(() => {
            try {
              // This will trigger the event that adds files to the chat input
              (window as any).__CHAT_HANDLE_PROMPT_FILES__();

              // Associate documents with the message again
              // This is a backup in case the pre-association failed
              associateDocumentsWithMessage(messageId).then((success) => {
                if (success) {
                  Logger.info(
                    "Successfully associated documents with message:",
                    { messageId }
                  );
                } else {
                  Logger.error("Failed to associate documents with message:", {
                    messageId,
                  });

                  // Try one more time with a delay
                  setTimeout(() => {
                    associateDocumentsWithMessage(messageId).then(
                      (retrySuccess) => {
                        if (retrySuccess) {
                          Logger.info(
                            "Successfully associated documents with message on retry:",
                            { messageId }
                          );
                        } else {
                          Logger.error(
                            "Failed to associate documents with message on retry:",
                            { messageId }
                          );
                        }
                      }
                    );
                  }, 1000);
                }
              });

              // ROBUST TEMPORARY FIX: Use stored prompt text to reliably trigger document processing
              // This avoids race conditions with React state updates
              Logger.info("Applying robust temporary fix for Prompt Architect: using stored prompt text");

              setTimeout(() => {
                // Get the stored prompt text (guaranteed to be correct)
                const storedPromptText = localStorage.getItem("pendingPromptText");

                if (storedPromptText && storedPromptText.trim().length > 0) {
                  const updatedValue = storedPromptText + '.';

                  Logger.info("Prompt Architect - Using stored prompt text:", storedPromptText);
                  Logger.info("Prompt Architect - Updated value with period:", updatedValue);

                  // Find the chat input textarea
                  const chatInputElement = document.querySelector(
                    'textarea[placeholder*="Ask, draft, or research"]'
                  ) as HTMLTextAreaElement;

                  if (chatInputElement) {
                    // Update the textarea value directly
                    chatInputElement.value = updatedValue;

                    // Update the React state if the setter is available
                    if ((window as any).__CHAT_INPUT_SETTER__) {
                      (window as any).__CHAT_INPUT_SETTER__(updatedValue);
                    }

                    // Dispatch a real input event to trigger processing
                    const inputEvent = new Event('input', { bubbles: true });
                    chatInputElement.dispatchEvent(inputEvent);

                    // Clean up the stored prompt text
                    localStorage.removeItem("pendingPromptText");

                    Logger.info("Robust temporary fix applied for Prompt Architect: added period to trigger document processing");
                  } else {
                    Logger.warn("Could not find chat input element for robust temporary fix");
                  }
                } else {
                  Logger.warn("No stored prompt text found for robust temporary fix");
                }
              }, 100); // Short delay to ensure DOM is ready
            } catch (handlerError) {
              Logger.error("Error triggering file handler:", handlerError);
              toast.error(
                "Error loading files into chat. Please try uploading them directly."
              );
            }
          }, 200); // Increased delay for better reliability
        }
      } else {
        // We're not on a chat page, use localStorage method to pass to new chat
        localStorage.setItem("pendingChatInput", finalPrompt);

        // Navigate to the root page which creates a new chat
        router.push("/");
      }

      // Reset the form for next time
      setCurrentStep(1);
      setPromptGoal("");
      setTextReferences("");
      clearUploadedFiles(); // Use the function from our custom hook
      setRefinedPrompt("");
      setEditedRefinedPrompt("");
      setIsEditingRefinedPrompt(false);
      setPromptName("");
      setShowSaveDrawer(false);

      // Try to dismiss the loading toast again as a backup
      // (The primary dismissal is handled by the timeout set at the beginning of this function)
      toast.dismiss(loadingToast);
      toast.success("Prompt created and ready to use!");
    } catch (error) {
      // Try to dismiss the loading toast again as a backup
      // (The primary dismissal is handled by the timeout set at the beginning of this function)
      toast.dismiss(loadingToast);
      Logger.error("Error in handleFinish:", error);
      toast.error("An error occurred while preparing the prompt");
    }
  };

  // Load starter pack data (system folders and prompts) with caching
  const loadStarterPackData = async () => {
    // If data is already loaded, don't fetch again
    if (dataLoaded?.starter) return;

    try {
      // Mark as loading
      setLoadingStates((prev) => ({ ...prev, starter: true }));
      setIsLoading(true);

      // Fetch system folders (starter pack)
      const systemFoldersRes = await fetch("/api/prompts/folders?type=system", {
        // Add cache control headers
        headers: { "Cache-Control": "max-age=3600" }, // Cache for 1 hour
      });
      const systemFoldersData = await systemFoldersRes.json();
      setSystemFolders(systemFoldersData);

      // Fetch recent prompts (shared between tabs) with improved caching
      const recentRes = await fetch("/api/prompts?type=recent", {
        headers: {
          "Cache-Control": "max-age=60", // Cache for 1 minute
          Pragma: "no-cache",
          Expires: "0",
        },
      });
      const recentData = await recentRes.json();
      setRecentPrompts(recentData);

      // Once we have the data, immediately mark as not loading
      setIsLoading(false);

      // Continue loading other data in the background
      const fetchPromises = systemFoldersData.map(
        async (folder: PromptFolder) => {
          const promptsRes = await fetch(`/api/prompts?folderId=${folder.id}`, {
            headers: { "Cache-Control": "max-age=3600" }, // Cache for 1 hour
          });
          const promptsData = await promptsRes.json();
          return { folderId: folder.id, prompts: promptsData };
        }
      );

      // Wait for all promises to resolve
      const results = await Promise.all(fetchPromises);

      // Build the prompts map
      const promptsMap: Record<string, Prompt[]> = {};
      results.forEach((result) => {
        promptsMap[result.folderId] = result.prompts;
      });

      // Update the state
      setPromptsByFolder(promptsMap);
      setLoadingStates((prev) => ({ ...prev, starter: false }));
      setDataLoaded((prev) => ({ ...prev, starter: true }));
    } catch (error) {
      // ...rest of the error handling code
    }
  };

  // Handle click on "New Prompt" button
  const handleNewPromptClick = () => {
    // Log the event when user clicks 'new prompt' button
    logEvent(PlayBookEvent.PLAYBOOK_FEATURE, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      flow: PlayBookFlow.PROMPT_CREATION,
      step: PlayBookStepName.NEW_PROMPT_BUTTON_CLICK,
      stepNumber: PlayBookStepNumber.NEW_PROMPT_BUTTON_CLICK,
    });

    setShowNewPromptDialog(true);
  };

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex justify-between items-center p-4 border-b dark:border-zinc-700">
        <h2 className="text-lg font-semibold text-[rgb(var(--base-navy))]">
          Iqidis Playbook
        </h2>
        <Button
          variant="ghost"
          onClick={() => onClose()}
          className="h-8 w-8 p-0"
        >
          <X size={16} />
        </Button>
      </div>

      {/* Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex-1 flex flex-col"
      >
        {/* Tab List */}
        <TabsList className="tabs-list border-b px-2 pt-2 dark:border-zinc-700 flex w-full bg-transparent overflow-x-auto scrollbar-hide">
          <TabsTrigger
            value="starter"
            className="tab-trigger mr-2 flex items-center gap-1 px-3 data-[state=active]:bg-[rgb(var(--base-navy))/10] data-[state=active]:text-[rgb(var(--base-navy))] data-[state=active]:border-b-2 data-[state=active]:border-[rgb(var(--base-navy))]"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
            </svg>
            Starter Pack
          </TabsTrigger>
          <TabsTrigger
            value="myPlaybook"
            className="tab-trigger mr-2 flex items-center gap-1 px-3 data-[state=active]:bg-[rgb(var(--base-navy))/10] data-[state=active]:text-[rgb(var(--base-navy))] data-[state=active]:border-b-2 data-[state=active]:border-[rgb(var(--base-navy))]"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path>
            </svg>
            My Playbook
          </TabsTrigger>
          <TabsTrigger
            value="architect"
            className="tab-trigger flex items-center gap-1 px-3 data-[state=active]:bg-[rgb(var(--base-navy))/10] data-[state=active]:text-[rgb(var(--base-navy))] data-[state=active]:border-b-2 data-[state=active]:border-[rgb(var(--base-navy))]"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
            </svg>
            Prompt Architect
          </TabsTrigger>
        </TabsList>

        {/* Starter Pack Tab Content */}
        <TabsContent
          value="starter"
          className="p-4 space-y-4 flex-1 overflow-auto md:max-h-[calc(100vh-210px)] max-h-[calc(100vh-270px)]"
        >
          {/* Heading / Quick Description */}
          <p className="text-sm text-muted-foreground mb-3">
            Pre-built prompts organized by practice area. Click &quot;Use&quot;
            to add the prompt to your chat.
          </p>

          {/* Search Bar */}
          <div className="relative mb-4">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              <Search className="h-4 w-4" />
            </div>
            <input
              type="text"
              placeholder="Search prompts..."
              className="w-full pl-10 pr-4 py-2 border border-sidebar-border bg-[var(--text-input-color-light)] dark:bg-sidebar-accent rounded-md text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-sidebar-foreground/50 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[rgb(var(--base-navy))] disabled:cursor-not-allowed disabled:opacity-50"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Recently Used section */}
          <div className="mb-5">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold text-[rgb(var(--base-navy))]">
                Recently Used
              </h3>
              <div className="flex items-center gap-2">
                <select
                  value={recentSortMethod}
                  onChange={(e) =>
                    setRecentSortMethod(
                      e.target.value as "lastUsed" | "alphabetical"
                    )
                  }
                  className="text-sm border rounded-md px-2 py-1 bg-background dark:border-gray-700 dark:text-gray-200"
                >
                  <option value="lastUsed">Last Used</option>
                  <option value="alphabetical">Alphabetical</option>
                </select>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 text-xs"
                  onClick={async () => {
                    try {
                      console.log("Manually refreshing recent prompts");
                      // Show loading toast
                      const loadingToast = toast.loading(
                        "Refreshing recent prompts..."
                      );

                      // Make the request with strong cache-busting headers
                      const recentRes = await fetch(
                        "/api/prompts?type=recent&limit=20",
                        {
                          headers: {
                            "Cache-Control":
                              "no-cache, no-store, must-revalidate",
                            Pragma: "no-cache",
                            Expires: "0",
                            // Add a random query parameter to bypass any caching
                            "X-Refresh-Cache": Math.random().toString(),
                          },
                        }
                      );

                      // Dismiss loading toast
                      toast.dismiss(loadingToast);

                      if (recentRes.ok) {
                        const recentData = await recentRes.json();
                        console.log(
                          "Manually refreshed recent prompts data:",
                          recentData
                        );

                        // Filter the data to match what will actually be displayed
                        // Apply the same filtering logic as the UI: search filter + content filter
                        const searchFiltered = searchQuery
                          ? recentData.filter((prompt: any) => {
                              const query = searchQuery.toLowerCase();
                              return prompt.title.toLowerCase().includes(query) ||
                                     prompt.content.toLowerCase().includes(query);
                            })
                          : recentData;

                        const filteredData = searchFiltered.filter(
                          (prompt: any) => prompt.content && prompt.content.trim() !== ""
                        );

                        // Update the state with the new data
                        setRecentPrompts(recentData);

                        // Show success toast with the actual filtered count
                        toast.success(
                          `Recent prompts refreshed (${filteredData.length} items)`
                        );

                        // Trigger a custom event to notify the Playbook component to refresh its data
                        if (typeof window !== "undefined") {
                          const refreshEvent = new CustomEvent(
                            "refresh-playbook-data"
                          );
                          window.dispatchEvent(refreshEvent);
                        }
                      } else {
                        throw new Error(
                          `Failed to refresh recent prompts: ${recentRes.status} ${recentRes.statusText}`
                        );
                      }
                    } catch (error) {
                      console.error(
                        "Error manually refreshing recent prompts:",
                        error
                      );
                      toast.error("Failed to refresh recent prompts");
                    }
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1"
                  >
                    <path d="M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38" />
                  </svg>
                  Refresh
                </Button>
              </div>
            </div>
            {isLoading ? (
              <div className="flex items-center gap-2 py-2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <p className="text-xs text-gray-400">
                  Loading recent prompts...
                </p>
              </div>
            ) : recentPrompts.length > 0 ? (
              (() => {
                // Filter prompts once to avoid repeating the logic
                const filteredPrompts = filterPromptsBySearch(
                  recentPrompts
                ).filter(
                  (prompt) => prompt.content && prompt.content.trim() !== ""
                );

                // Sort prompts based on the selected sort method
                const sortedPrompts = [...filteredPrompts].sort((a, b) => {
                  if (recentSortMethod === "alphabetical") {
                    // Sort alphabetically by title
                    return a.title.localeCompare(b.title);
                  } else {
                    // Sort by last used (most recent first)
                    // If lastUsed is null, treat it as oldest
                    if (!a.lastUsed) return 1;
                    if (!b.lastUsed) return -1;
                    return (
                      new Date(b.lastUsed).getTime() -
                      new Date(a.lastUsed).getTime()
                    );
                  }
                });

                return sortedPrompts.length > 0 ? (
                  <div className="bg-[var(--chat-background-color)] rounded-md overflow-hidden shadow-sm">
                    <div className="max-h-[360px] min-h-[200px] overflow-y-scroll custom-scrollbar border border-gray-100 dark:border-gray-800 rounded-t-md">
                      {sortedPrompts.map((prompt) => {
                        // We know these are PromptWithFolder objects
                        const promptWithFolder = prompt as PromptWithFolder;
                        return (
                          <PromptCard
                            key={prompt.id}
                            prompt={prompt}
                            folderName={promptWithFolder.folderName}
                            isSystemFolder={promptWithFolder.isSystemFolder}
                            onUse={() => handleUsePrompt(prompt)}
                            onEdit={(updatedPrompt, options) =>
                              handleUpdatePrompt(
                                updatedPrompt as Prompt,
                                options
                              )
                            }
                            onDelete={() =>
                              handleDeletePrompt(prompt.id, prompt.folderId)
                            }
                            onToggleFavorite={() =>
                              handleToggleFavorite(prompt.id)
                            }
                            onRemoveFromRecent={() =>
                              handleRemoveFromRecent(prompt.id)
                            }
                            isInRecentSection={true}
                          />
                        );
                      })}
                    </div>
                    {sortedPrompts.length > 5 && (
                      <div className="p-2 text-xs text-center text-muted-foreground bg-muted/50 border-t border-gray-100 dark:border-gray-800 flex items-center justify-center gap-1 rounded-b-md">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="12"
                          height="12"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M7 13l5 5 5-5M7 6l5 5 5-5" />
                        </svg>
                        Scroll to see all {sortedPrompts.length} prompts (
                        {sortedPrompts.length - 5} more)
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-600 text-sm italic">
                    No matching prompts found for &quot;{searchQuery}&quot;
                  </p>
                );
              })()
            ) : (
              <p className="text-xs text-gray-400 italic">
                No recently used prompts
              </p>
            )}
          </div>

          {/* Categories as Expandable Lists */}
          <div className="space-y-4">
            {isLoading && loadingStates.starter ? (
              <div className="py-8 text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Loading starter pack...
                </p>
                <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              </div>
            ) : systemFolders.length > 0 ? (
              // Filter folders based on search query
              (() => {
                const filteredFolders = filterFoldersBySearch(
                  systemFolders,
                  promptsByFolder
                );
                return filteredFolders.length > 0 ? (
                  filteredFolders.map((folder: PromptFolder) => (
                    <CategoryExpandableList
                      key={folder.id}
                      folder={folder}
                      prompts={filterPromptsBySearch(
                        promptsByFolder[folder.id] || []
                      )}
                      onUsePrompt={handleUsePrompt}
                      onEditPrompt={(updatedPrompt, options) =>
                        handleUpdatePrompt(updatedPrompt as Prompt, options)
                      }
                      onDeletePrompt={handleDeletePrompt}
                      onToggleFavorite={handleToggleFavorite}
                      onAddPrompt={(folderId) => {
                        setNewPromptData({ ...newPromptData, folderId });
                        setShowNewPromptDialog(true);
                      }}
                      isSystemFolder={true}
                    />
                  ))
                ) : searchQuery ? (
                  <div className="text-center py-4">
                    <p className="text-gray-600 text-sm">
                      No matching prompts found for &quot;{searchQuery}&quot;
                    </p>
                  </div>
                ) : null;
              })()
            ) : (
              // Fallback to static data if no system folders are found
              // For legacy data, we can't easily filter folders, so we show all
              STARTER_PROMPTS.map((category) => (
                <CategoryExpandableList
                  key={category.category}
                  legacyCategory={category}
                  onUsePrompt={handleUsePrompt}
                  onEditPrompt={(updatedPrompt, options) =>
                    handleUpdatePrompt(updatedPrompt as Prompt, options)
                  }
                  onToggleFavorite={handleToggleFavorite}
                  isSystemFolder={true}
                />
              ))
            )}
          </div>
        </TabsContent>

        {/* My Playbook Tab Content */}
        <TabsContent
          value="myPlaybook"
          className="p-4 space-y-4 flex-1 overflow-auto md:max-h-[calc(100vh-210px)] max-h-[calc(100vh-270px)]"
        >
          {/* Title */}
          <h3 className="text-sm font-semibold text-[rgb(var(--base-navy))]">
            My Saved Prompts
          </h3>

          {/* Search + Buttons */}
          <div className="flex flex-wrap items-center gap-2 mb-4">
            <div className="relative w-full md:w-auto grow md:grow-0">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                <Search className="h-4 w-4" />
              </div>
              <input
                type="text"
                placeholder="Search my prompts..."
                className="w-full pl-10 pr-4 py-2 border border-sidebar-border bg-[var(--text-input-color-light)] dark:bg-sidebar-accent rounded-md text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-sidebar-foreground/50 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[rgb(var(--base-navy))] disabled:cursor-not-allowed disabled:opacity-50"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button
              variant="default"
              size="sm"
              onClick={handleNewFolderClick}
              className="navy-button"
            >
              <Folder className="size-4 mr-1" />
              New Folder
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNewPromptClick}
              className="text-[rgb(var(--base-navy))] border-[rgb(var(--base-navy))]"
            >
              <Plus className="h-4 w-4 mr-1" />
              New Prompt
            </Button>
          </div>

          {/* Sub-tabs: All, Favorites, Recently Used */}
          <Tabs
            defaultValue="all"
            className="mt-2"
            onValueChange={(value) => {
              // Refresh favorites when the favorites tab is selected
              if (value === "favorites") {
                console.log("Favorites tab selected, refreshing data");
                fetch("/api/prompts?type=favorites", {
                  headers: {
                    "Cache-Control": "no-cache, no-store, must-revalidate",
                    Pragma: "no-cache",
                    Expires: "0",
                  },
                })
                  .then((response) => {
                    if (!response.ok) {
                      throw new Error("Failed to fetch favorites");
                    }
                    return response.json();
                  })
                  .then((data) => {
                    console.log("Refreshed favorites data:", data);

                    // Ensure we're only setting favorites that are actually marked as favorites
                    const validFavorites = data.filter(
                      (p: PromptWithFolder) => p.isFavorite === true
                    );
                    if (validFavorites.length !== data.length) {
                      console.warn(
                        "Filtered out non-favorite prompts from tab selection",
                        data
                          .filter((p: PromptWithFolder) => !p.isFavorite)
                          .map((p: PromptWithFolder) => p.id)
                      );
                    }

                    setFavoritePrompts(validFavorites);
                  })
                  .catch((error) =>
                    console.error("Error refreshing favorites:", error)
                  );
              }

              // Refresh recent prompts when the recent tab is selected
              if (value === "recent") {
                console.log("Recent tab selected, refreshing data");
                fetch("/api/prompts?type=recent&limit=20", {
                  headers: {
                    "Cache-Control": "no-cache, no-store, must-revalidate",
                    Pragma: "no-cache",
                    Expires: "0",
                  },
                })
                  .then((response) => {
                    if (!response.ok) {
                      throw new Error("Failed to fetch recent prompts");
                    }
                    return response.json();
                  })
                  .then((data) => {
                    console.log("Refreshed recent prompts data:", data);
                    setRecentPrompts(data);
                  })
                  .catch((error) =>
                    console.error("Error refreshing recent prompts:", error)
                  );
              }
            }}
          >
            <TabsList className="w-full flex mb-4">
              <TabsTrigger
                value="all"
                className="flex-1 data-[state=active]:bg-[rgb(var(--base-navy))]/10 data-[state=active]:text-[rgb(var(--base-navy))]"
              >
                All
              </TabsTrigger>
              <TabsTrigger
                value="favorites"
                className="flex-1 data-[state=active]:bg-[rgb(var(--base-navy))]/10 data-[state=active]:text-[rgb(var(--base-navy))]"
              >
                Favorites
              </TabsTrigger>
              <TabsTrigger
                value="recent"
                className="flex-1 data-[state=active]:bg-[rgb(var(--base-navy))]/10 data-[state=active]:text-[rgb(var(--base-navy))]"
              >
                Recently Used
              </TabsTrigger>
            </TabsList>

            {/* All */}
            <TabsContent value="all" className="mt-4">
              {isLoading && loadingStates.playbook ? (
                <div className="py-8 text-center">
                  <p className="text-sm text-gray-600 mb-2">
                    Loading your playbook...
                  </p>
                  <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
                </div>
              ) : userFolders.length > 0 ? (
                <div className="space-y-4">
                  {/* Filter folders based on search query */}
                  {(() => {
                    const filteredFolders = filterFoldersBySearch(
                      userFolders,
                      promptsByFolder
                    );
                    return filteredFolders.length > 0 ? (
                      filteredFolders.map((folder: PromptFolder) => (
                        <CategoryExpandableList
                          key={folder.id}
                          folder={folder}
                          prompts={filterPromptsBySearch(
                            promptsByFolder[folder.id] || []
                          )}
                          onUsePrompt={handleUsePrompt}
                          onEditPrompt={(updatedPrompt, options) =>
                            handleUpdatePrompt(updatedPrompt as Prompt, options)
                          }
                          onDeletePrompt={handleDeletePrompt}
                          onToggleFavorite={handleToggleFavorite}
                          onAddPrompt={(folderId) => {
                            setNewPromptData({ ...newPromptData, folderId });
                            setShowNewPromptDialog(true);
                          }}
                          isSystemFolder={false}
                        />
                      ))
                    ) : searchQuery ? (
                      <div className="text-center py-4">
                        <p className="text-gray-600 text-sm">
                          No matching prompts found for &quot;{searchQuery}
                          &quot;
                        </p>
                      </div>
                    ) : null;
                  })()}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600 text-sm mb-4">
                    You don&apos;t have any folders yet. Create one to start
                    saving prompts.
                  </p>
                  <Button onClick={() => setShowNewFolderDialog(true)}>
                    <Folder className="h-4 w-4 mr-2" />
                    Create Your First Folder
                  </Button>
                </div>
              )}
            </TabsContent>

            {/* Favorites */}
            <TabsContent value="favorites" className="mt-3">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-[rgb(var(--base-navy))]">
                  Your Favorite Prompts
                </h4>
                <div className="flex items-center gap-2">
                  {/* Sort dropdown */}
                  <div className="relative">
                    <select
                      className="h-8 px-2 text-xs border border-gray-200 rounded-md bg-background dark:bg-zinc-800 dark:border-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={favoritesSortMethod}
                      onChange={(e) =>
                        setFavoritesSortMethod(
                          e.target.value as "lastFavorited" | "alphabetical"
                        )
                      }
                    >
                      <option value="lastFavorited">
                        Sort by: Last Favorited
                      </option>
                      <option value="alphabetical">
                        Sort by: Alphabetical
                      </option>
                    </select>
                  </div>

                  {/* Refresh button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-xs"
                    onClick={async () => {
                      try {
                        console.log("Manually refreshing favorites");
                        const favoritesRes = await fetch(
                          "/api/prompts?type=favorites",
                          {
                            headers: {
                              "Cache-Control":
                                "no-cache, no-store, must-revalidate",
                              Pragma: "no-cache",
                              Expires: "0",
                            },
                          }
                        );
                        if (favoritesRes.ok) {
                          const favoritesData = await favoritesRes.json();
                          console.log(
                            "Manually refreshed favorites data:",
                            favoritesData
                          );

                          // Ensure we're only setting favorites that are actually marked as favorites
                          const validFavorites = favoritesData.filter(
                            (p: PromptWithFolder) => p.isFavorite === true
                          );
                          if (validFavorites.length !== favoritesData.length) {
                            console.warn(
                              "Filtered out non-favorite prompts from manual refresh",
                              favoritesData
                                .filter((p: PromptWithFolder) => !p.isFavorite)
                                .map((p: PromptWithFolder) => p.id)
                            );
                          }

                          setFavoritePrompts(validFavorites);
                          toast.success(
                            `Favorites refreshed (${validFavorites.length} items)`
                          );
                        } else {
                          throw new Error("Failed to refresh favorites");
                        }
                      } catch (error) {
                        console.error(
                          "Error manually refreshing favorites:",
                          error
                        );
                        toast.error("Failed to refresh favorites");
                      }
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1"
                    >
                      <path d="M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38" />
                    </svg>
                    Refresh
                  </Button>
                </div>
              </div>
              {isLoading ? (
                <p className="text-sm text-gray-600">Loading favorites...</p>
              ) : favoritePrompts.length > 0 ? (
                <div className="border rounded-md overflow-hidden shadow-sm divide-y divide-gray-100 bg-white">
                  {(() => {
                    // Filter prompts first
                    const filteredPrompts = filterPromptsBySearch(
                      favoritePrompts
                    )
                      // Make sure we only display prompts that are actually marked as favorites
                      .filter(
                        (prompt) =>
                          prompt.content &&
                          prompt.content.trim() !== "" &&
                          prompt.isFavorite === true
                      );

                    // Sort prompts based on the selected sort method
                    const sortedPrompts = [...filteredPrompts].sort((a, b) => {
                      if (favoritesSortMethod === "alphabetical") {
                        // Sort alphabetically by title
                        return a.title.localeCompare(b.title);
                      } else {
                        // Sort by last used (most recent first)
                        // If lastUsed is null, treat it as oldest
                        if (!a.lastUsed) return 1;
                        if (!b.lastUsed) return -1;
                        return (
                          new Date(b.lastUsed).getTime() -
                          new Date(a.lastUsed).getTime()
                        );
                      }
                    });

                    return sortedPrompts.map((prompt) => {
                      // We know these are PromptWithFolder objects
                      const promptWithFolder = prompt as PromptWithFolder;
                      return (
                        <PromptCard
                          key={prompt.id}
                          prompt={prompt}
                          folderName={promptWithFolder.folderName}
                          isSystemFolder={promptWithFolder.isSystemFolder}
                          onUse={() => handleUsePrompt(prompt)}
                          onEdit={(updatedPrompt, options) =>
                            handleUpdatePrompt(updatedPrompt as Prompt, options)
                          }
                          onDelete={() =>
                            handleDeletePrompt(prompt.id, prompt.folderId)
                          }
                          onToggleFavorite={() =>
                            handleToggleFavorite(prompt.id)
                          }
                        />
                      );
                    });
                  })()}
                </div>
              ) : (
                <p className="text-gray-600 text-sm italic">
                  No favorites saved yet. Click the star icon on any prompt to
                  add it to favorites.
                </p>
              )}
            </TabsContent>

            {/* Recently Used */}
            <TabsContent value="recent" className="mt-3">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-[rgb(var(--base-navy))]">
                  Your Recently Used Prompts
                </h4>
                <div className="flex items-center gap-2">
                  <select
                    value={recentSortMethod}
                    onChange={(e) =>
                      setRecentSortMethod(
                        e.target.value as "lastUsed" | "alphabetical"
                      )
                    }
                    className="text-sm border rounded-md px-2 py-1 bg-background dark:border-gray-700 dark:text-gray-200"
                  >
                    <option value="lastUsed">Last Used</option>
                    <option value="alphabetical">Alphabetical</option>
                  </select>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-xs"
                    onClick={async () => {
                      try {
                        // console.log("Manually refreshing recent prompts");
                        // Show loading toast
                        const loadingToast = toast.loading(
                          "Refreshing recent prompts..."
                        );

                        // Make the request with strong cache-busting headers
                        const recentRes = await fetch(
                          "/api/prompts?type=recent&limit=20",
                          {
                            headers: {
                              "Cache-Control":
                                "no-cache, no-store, must-revalidate",
                              Pragma: "no-cache",
                              Expires: "0",
                              // Add a random query parameter to bypass any caching
                              "X-Refresh-Cache": Math.random().toString(),
                            },
                          }
                        );

                        // Dismiss loading toast
                        toast.dismiss(loadingToast);

                        if (recentRes.ok) {
                          const recentData = await recentRes.json();
                          console.log(
                            "Manually refreshed recent prompts data:",
                            recentData
                          );

                          // Filter the data to match what will actually be displayed
                          // Apply the same filtering logic as the UI: search filter + content filter
                          const searchFiltered = searchQuery
                            ? recentData.filter((prompt: any) => {
                                const query = searchQuery.toLowerCase();
                                return prompt.title.toLowerCase().includes(query) ||
                                       prompt.content.toLowerCase().includes(query);
                              })
                            : recentData;

                          const filteredData = searchFiltered.filter(
                            (prompt: any) => prompt.content && prompt.content.trim() !== ""
                          );

                          // Update the state with the new data
                          setRecentPrompts(recentData);

                          // Show success toast with the actual filtered count
                          toast.success(
                            `Recent prompts refreshed (${filteredData.length} items)`
                          );

                          // Trigger a custom event to notify the Playbook component to refresh its data
                          if (typeof window !== "undefined") {
                            const refreshEvent = new CustomEvent(
                              "refresh-playbook-data"
                            );
                            window.dispatchEvent(refreshEvent);
                          }
                        } else {
                          throw new Error(
                            `Failed to refresh recent prompts: ${recentRes.status} ${recentRes.statusText}`
                          );
                        }
                      } catch (error) {
                        console.error(
                          "Error manually refreshing recent prompts:",
                          error
                        );
                        toast.error("Failed to refresh recent prompts");
                      }
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1"
                    >
                      <path d="M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38" />
                    </svg>
                    Refresh
                  </Button>
                </div>
              </div>
              {isLoading ? (
                <p className="text-sm text-gray-600">
                  Loading recent prompts...
                </p>
              ) : recentPrompts.length > 0 ? (
                (() => {
                  // Filter prompts once to avoid repeating the logic
                  const filteredPrompts = filterPromptsBySearch(
                    recentPrompts
                  ).filter(
                    (prompt) => prompt.content && prompt.content.trim() !== ""
                  );

                  // Sort prompts based on the selected sort method
                  const sortedPrompts = [...filteredPrompts].sort((a, b) => {
                    if (recentSortMethod === "alphabetical") {
                      // Sort alphabetically by title
                      return a.title.localeCompare(b.title);
                    } else {
                      // Sort by last used (most recent first)
                      // If lastUsed is null, treat it as oldest
                      if (!a.lastUsed) return 1;
                      if (!b.lastUsed) return -1;
                      return (
                        new Date(b.lastUsed).getTime() -
                        new Date(a.lastUsed).getTime()
                      );
                    }
                  });

                  return sortedPrompts.length > 0 ? (
                    <div className="border rounded-md overflow-hidden shadow-sm divide-y divide-gray-100 bg-white">
                      <div className="max-h-[360px] min-h-[200px] overflow-y-scroll custom-scrollbar border-b border-gray-100">
                        {sortedPrompts.map((prompt) => {
                          // We know these are PromptWithFolder objects
                          const promptWithFolder = prompt as PromptWithFolder;
                          return (
                            <PromptCard
                              key={prompt.id}
                              prompt={prompt}
                              folderName={promptWithFolder.folderName}
                              isSystemFolder={promptWithFolder.isSystemFolder}
                              onUse={() => handleUsePrompt(prompt)}
                              onEdit={(updatedPrompt, options) =>
                                handleUpdatePrompt(
                                  updatedPrompt as Prompt,
                                  options
                                )
                              }
                              onDelete={() =>
                                handleDeletePrompt(prompt.id, prompt.folderId)
                              }
                              onToggleFavorite={() =>
                                handleToggleFavorite(prompt.id)
                              }
                              onRemoveFromRecent={() =>
                                handleRemoveFromRecent(prompt.id)
                              }
                              isInRecentSection={true}
                            />
                          );
                        })}
                      </div>
                      {sortedPrompts.length > 5 && (
                        <div className="p-2 text-xs text-center text-gray-500 bg-gray-50 border-t flex items-center justify-center gap-1">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M7 13l5 5 5-5M7 6l5 5 5-5" />
                          </svg>
                          Scroll to see all {sortedPrompts.length} prompts (
                          {sortedPrompts.length - 5} more)
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-600 text-sm italic">
                      No matching prompts found for &quot;{searchQuery}&quot;
                    </p>
                  );
                })()
              ) : (
                <p className="text-gray-600 text-sm italic">
                  No recently used prompts. Use a prompt to see it appear here.
                </p>
              )}
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* Prompt Architect Tab Content */}
        <TabsContent
          value="architect"
          className="p-4 space-y-4 flex-1 overflow-auto md:max-h-[calc(100vh-224px)] max-h-[calc(100vh-300px)]"
        >
          {/* Progress Steps */}
          <div className="flex justify-between items-center mb-6 max-w-3xl mx-auto px-2">
            {steps.map((step) => (
              <div key={step.id} className="flex items-center flex-1">
                <div className="flex items-center">
                  <div
                    className={`w-7 h-7 rounded-full flex items-center justify-center border-2 flex-shrink-0
                      ${
                        currentStep === step.id
                          ? "bg-[rgb(var(--base-navy))] text-white dark:bg-white dark:text-black border-[rgb(var(--base-navy))]"
                          : step.isComplete
                          ? "border-[rgb(var(--base-navy))] text-[rgb(var(--base-navy))]"
                          : "border-gray-300 text-gray-300"
                      }`}
                  >
                    {step.id}
                  </div>
                  <span
                    className={`ml-2 text-sm whitespace-nowrap ${
                      currentStep === step.id
                        ? "text-[rgb(var(--base-navy))] font-medium"
                        : step.isComplete
                        ? "text-[rgb(var(--base-navy))]"
                        : "text-gray-300"
                    }`}
                  >
                    {step.title}
                  </span>
                </div>
                {step.id < steps.length && (
                  <div
                    className={`h-0.5 mx-2 flex-1 ${
                      step.isComplete
                        ? "bg-[rgb(var(--base-navy))]"
                        : "bg-gray-200"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Step Content */}
          <div className="max-w-2xl mx-auto px-2">
            {currentStep === 1 && (
              <div className="space-y-3">
                <h2 className="text-lg font-semibold">
                  What do you want to achieve?
                </h2>
                <p className="text-gray-600 text-sm">
                  Describe the legal document, analysis, or advice you need.
                </p>
                <textarea
                  value={promptGoal}
                  onChange={(e) => setPromptGoal(e.target.value)}
                  placeholder="e.g., Draft a motion for summary judgment in an employment discrimination case..."
                  className="w-full h-32 p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-[rgb(var(--base-navy))] bg-[var(--text-input-color-light)] dark:bg-zinc-900 shadow-sm resize-y text-sm"
                />
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-4">
                <div className="space-y-3">
                  <h2 className="text-lg font-semibold">References</h2>
                  <p className="text-gray-600 text-sm">
                    Add relevant case law, statutes, or documents.
                  </p>
                </div>

                {/* Text References Input */}
                <div className="space-y-2">
                  <label
                    htmlFor="textReferences"
                    className="block text-sm font-medium"
                  >
                    Enter text references:
                  </label>
                  <textarea
                    id="textReferences"
                    value={textReferences}
                    onChange={(e) => setTextReferences(e.target.value)}
                    placeholder="e.g., California jurisdiction, Smith v. Jones (2015), Section 230 of the Communications Decency Act..."
                    className="w-full min-h-[120px] p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-[rgb(var(--base-navy))] bg-[var(--text-input-color-light)] dark:bg-zinc-900 shadow-sm text-sm resize-y"
                  />
                </div>

                {/* File Upload Area */}
                <div className="space-y-2">
                  <label
                    htmlFor="fileUpload"
                    className="block text-sm font-medium"
                  >
                    Attach any relevant documents as references:
                  </label>
                  <div
                    className="border-2 border-dashed rounded-lg p-6 transition-colors hover:border-primary/50 hover:bg-muted/50"
                    onDragOver={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onDrop={async (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const files = Array.from(e.dataTransfer.files);
                      await handleFileUpload(files);
                    }}
                  >
                    <input
                      type="file"
                      multiple
                      className="hidden"
                      id="fileUpload"
                      onChange={async (e) => {
                        const files = Array.from(e.target.files || []);
                        await handleFileUpload(files);
                      }}
                      accept={PROMPT_FILE_ACCEPT_STRING}
                    />
                    <label
                      htmlFor="fileUpload"
                      className="cursor-pointer flex flex-col items-center"
                    >
                      <Upload className="h-8 w-8 mb-2 text-muted-foreground" />
                      <span className="text-sm font-medium">
                        Click to upload or drag and drop
                      </span>
                      <span className="text-xs text-muted-foreground mt-1">
                        PDF, DOCX, DOC, TXT
                      </span>
                    </label>
                  </div>

                  {/* Display Uploaded Files */}
                  {uploadedFiles.length > 0 && (
                    <div className="mt-4">
                      <p className="text-sm font-medium mb-2">
                        Uploaded files:
                      </p>
                      <ul className="text-sm space-y-2">
                        {uploadedFiles.map((file, index) => (
                          <li
                            key={index}
                            className="flex items-center justify-between p-2 bg-muted rounded-md"
                          >
                            <div className="flex items-center gap-2 max-w-[calc(100%-2rem)] overflow-hidden">
                              <FileText className="h-4 w-4 flex-shrink-0" />
                              <span className="truncate" title={file.name}>
                                {file.name}
                              </span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                // Create a new array without the file at this index
                                const newFiles = uploadedFiles.filter(
                                  (_, i) => i !== index
                                );
                                // Clear all files and re-add the filtered list
                                clearUploadedFiles();
                                if (newFiles.length > 0) {
                                  localStorage.setItem(
                                    "pendingChatFiles",
                                    JSON.stringify(newFiles)
                                  );
                                  // Trigger the file handler to reload the files
                                  if (
                                    typeof window !== "undefined" &&
                                    (window as any).__CHAT_HANDLE_PROMPT_FILES__
                                  ) {
                                    setTimeout(() => {
                                      (
                                        window as any
                                      ).__CHAT_HANDLE_PROMPT_FILES__();
                                    }, 100);
                                  }
                                }
                              }}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="space-y-4">
                <div className="space-y-3">
                  <h2 className="text-lg font-semibold">Preview</h2>
                  <p className="text-gray-600 text-sm">
                    Review, edit, and save your refined prompt.
                  </p>
                </div>

                {/* Input Summary - Always visible */}
                <div className="border rounded-md p-4 bg-muted/30 space-y-4">
                  {/* Goal Section */}
                  <div>
                    <h3 className="text-sm font-semibold mb-1">Goal:</h3>
                    <p className="text-sm whitespace-pre-wrap">{promptGoal}</p>
                  </div>

                  {/* References Section */}
                  <div>
                    <h3 className="text-sm font-semibold mb-1">References:</h3>
                    {textReferences ? (
                      <div className="text-sm whitespace-pre-wrap">
                        {textReferences}
                      </div>
                    ) : null}

                    {uploadedFiles.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium">Attached files:</p>
                        <ul className="text-sm list-disc pl-5 mt-1">
                          {uploadedFiles.map((file, index) => (
                            <li
                              key={index}
                              className="break-all"
                              title={file.name}
                            >
                              <span className="inline-block max-w-full overflow-hidden text-ellipsis">
                                {file.name}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {!textReferences && uploadedFiles.length === 0 && (
                      <p className="text-sm text-gray-500 italic">
                        No references provided
                      </p>
                    )}
                  </div>
                </div>

                {/* Refined Prompt Section */}
                <div className="border-2 border-primary/20 rounded-md p-4 bg-primary/5 space-y-3">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-semibold">Refined Prompt:</h3>
                    {!isGeneratingPrompt &&
                      !isEditingRefinedPrompt &&
                      refinedPrompt && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setIsEditingRefinedPrompt(true);
                            setEditedRefinedPrompt(refinedPrompt);
                          }}
                          className="h-7 text-xs"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      )}
                  </div>

                  {isGeneratingPrompt ? (
                    <div className="flex flex-col items-center justify-center py-6">
                      <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mb-3"></div>
                      <p className="text-sm text-gray-600">
                        Generating your refined prompt...
                      </p>
                    </div>
                  ) : isEditingRefinedPrompt ? (
                    <div className="space-y-3">
                      <textarea
                        value={editedRefinedPrompt}
                        onChange={(e) => setEditedRefinedPrompt(e.target.value)}
                        className="w-full min-h-[200px] p-3 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-y"
                      />
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setIsEditingRefinedPrompt(false);
                            setEditedRefinedPrompt(refinedPrompt);
                          }}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => {
                            setRefinedPrompt(editedRefinedPrompt);
                            setIsEditingRefinedPrompt(false);
                          }}
                        >
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="text-sm whitespace-pre-wrap p-3 rounded border"
                      style={{
                        backgroundColor: "var(--chat-background-color)",
                      }}
                    >
                      {refinedPrompt || (
                        <p className="text-gray-400 italic">
                          Your refined prompt will appear here once generated...
                        </p>
                      )}
                    </div>
                  )}

                  {!isGeneratingPrompt &&
                    !isEditingRefinedPrompt &&
                    refinedPrompt && (
                      <p className="text-xs text-gray-500">
                        This refined prompt incorporates your goal, references,
                        and optimizes the structure for the best results.
                      </p>
                    )}
                </div>

                {/* Save Button - Only show if we have a refined prompt */}
                {!isGeneratingPrompt && refinedPrompt && (
                  <div className="flex justify-end">
                    <Button
                      variant="outline"
                      className="gap-2"
                      onClick={async () => {
                        // Set a default prompt name based on the goal
                        if (!promptName) {
                          const defaultName =
                            promptGoal.substring(0, 40) +
                            (promptGoal.length > 40 ? "..." : "");
                          setPromptName(defaultName);
                        }

                        // Load user folders before showing the dialog
                        try {
                          const response = await fetch(
                            "/api/prompts/folders?type=user",
                            {
                              headers: { "Cache-Control": "no-cache" }, // Don't use cache to ensure we get the latest folders
                            }
                          );

                          if (response.ok) {
                            const folders = await response.json();
                            setUserFolders(folders);
                            console.log(
                              "Loaded folders for Save to My Playbook dialog:",
                              folders
                            );
                          }
                        } catch (error) {
                          console.error(
                            "Error loading folders for Save to My Playbook dialog:",
                            error
                          );
                        }

                        setShowSaveDrawer(true);
                      }}
                    >
                      <Star className="h-4 w-4" />
                      Save to My Playbook
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 1 || isGeneratingPrompt}
                className="text-sm"
              >
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && !promptGoal.trim()) ||
                  isGeneratingPrompt
                }
                className="text-sm navy-button"
              >
                {isGeneratingPrompt ? (
                  <div className="flex items-center gap-2">
                    <span>Generating</span>
                    <div className="h-4 w-4 border-2 border-white dark:border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                ) : currentStep === steps.length ? (
                  "Finish"
                ) : (
                  "Next"
                )}
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* New Folder Dialog */}
      <Dialog open={showNewFolderDialog} onOpenChange={setShowNewFolderDialog}>
        <DialogContent className="max-w-[600px] !dark:dark-background">
          <DialogHeader>
            <DialogTitle className="pl-6">Create New Folder</DialogTitle>
          </DialogHeader>
          <div className="py-4 md:px-6 px-3">
            <Label htmlFor="folderName">Folder Name</Label>
            <Input
              id="folderName"
              value={newFolderName}
              onChange={(e) => {
                // Use the event's current value directly to avoid closure issues
                const value = e.target.value;
                // Update immediately for responsive UI
                setNewFolderName(value);
              }}
              placeholder="Enter folder name"
              className="mt-2"
              // Disable autocomplete to prevent browser suggestions from causing re-renders
              autoComplete="off"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowNewFolderDialog(false)}
              disabled={isCreatingFolder}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateFolder}
              className="navy-button md:mb-0 mb-2"
              disabled={isCreatingFolder || !newFolderName.trim()}
            >
              {isCreatingFolder ? (
                <div className="flex items-center">
                  <div className="h-4 w-4 md:mb-0 mb-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span>Creating...</span>
                </div>
              ) : (
                "Create Folder"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Prompt Dialog */}
      <Dialog
        open={showNewPromptDialog}
        onOpenChange={(open) => {
          setShowNewPromptDialog(open);
          // Clear uploaded files and reset form when dialog closes to prevent residual display
          if (!open) {
            resetNewPromptDialog();
          }
        }}
      >
        <DialogContent className="max-w-2xl z-[110] w-full max-h-[90vh] flex flex-col" overlayClassName="z-[109]">
          <DialogHeader className="flex-shrink-0 px-6 pt-6">
            <DialogTitle>Create New Prompt</DialogTitle>
            <button
              type="button"
              onClick={() => setShowNewPromptDialog(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 focus:outline-none"
              aria-label="Close"
            >
              <X className="w-5 h-5" />
            </button>
          </DialogHeader>
          <div className="py-4 space-y-4 w-full max-w-full overflow-y-auto flex-1 min-h-0 md:px-6 px-3">
            <div className="w-full max-w-full overflow-hidden">
              <Label htmlFor="promptTitle">Title</Label>
              <Input
                id="promptTitle"
                value={newPromptData.title}
                onChange={(e) =>
                  setNewPromptData({
                    ...newPromptData,
                    title: e.target.value,
                  })
                }
                placeholder="Enter prompt title"
                className="mt-2 w-full max-w-full"
              />
            </div>
            <div className="w-full max-w-full overflow-hidden">
              <Label htmlFor="promptFolder">Folder (Optional)</Label>
              <select
                id="promptFolder"
                value={newPromptData.folderId}
                onChange={(e) =>
                  setNewPromptData({
                    ...newPromptData,
                    folderId: e.target.value,
                  })
                }
                className="w-full max-w-full mt-2 p-2 border border-input rounded-md bg-background text-base focus-visible:outline-none focus-visible:border-[rgb(var(--base-navy))] focus:border-[rgb(var(--base-navy))] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transition-colors"
              >
                <option value="">No folder (will be saved to General)</option>
                {userFolders
                  .filter((folder) => folder.name.toLowerCase() !== "general") // Filter out any "General" folder
                  .map((folder) => (
                    <option key={folder.id} value={folder.id}>
                      {folder.name}
                    </option>
                  ))}
              </select>
            </div>
            <div className="w-full max-w-full overflow-hidden">
              <Label htmlFor="promptContent">Content</Label>
              <Textarea
                id="promptContent"
                value={newPromptData.content}
                onChange={(e) =>
                  setNewPromptData({
                    ...newPromptData,
                    content: e.target.value,
                  })
                }
                placeholder="Enter prompt content"
                className="mt-2 min-h-[200px] w-full max-w-full resize-none"
              />
            </div>

            {/* Document Attachment Section */}
            <div className="space-y-2 w-full max-w-full overflow-hidden">
              <Label htmlFor="promptDocuments">Attach Documents</Label>
              <div
                className="border-2 border-dashed rounded-lg p-4 transition-colors hover:border-primary/50 hover:bg-muted/50 w-full max-w-full"
                onDragOver={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDrop={async (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const files = Array.from(e.dataTransfer.files);
                  await handleNewPromptFileUpload(files);
                }}
              >
                <input
                  type="file"
                  multiple
                  className="hidden"
                  id="promptDocuments"
                  onChange={async (e) => {
                    const files = Array.from(e.target.files || []);
                    // Validate files before uploading
                    const { validFiles, errors } = validatePromptFiles(files);
                    if (errors.length > 0) {
                      errors.forEach(error => toast.error(error));
                    }
                    if (validFiles.length > 0) {
                      await handleNewPromptFileUpload(validFiles);
                    }
                  }}
                  accept={PROMPT_FILE_ACCEPT_STRING}
                />
                <label
                  htmlFor="promptDocuments"
                  className="cursor-pointer flex flex-col items-center"
                >
                  <Upload className="h-6 w-6 mb-2 text-muted-foreground" />
                  <span className="text-sm font-medium">
                    Click to upload or drag and drop
                  </span>
                  <span className="text-xs text-muted-foreground mt-1">
                    PDF, DOCX, DOC, TXT
                  </span>
                </label>
              </div>

              {/* Display Files (Uploaded + In Queue) */}
              {(newPromptFiles.length > 0 || newPromptUploadQueue.length > 0) && (
                <div className="mt-2 w-full overflow-hidden">
                  <p className="text-sm font-medium mb-2">
                    Attached documents:
                  </p>
                  <ul className="text-sm space-y-2 w-full">
                    {/* Display successfully uploaded files */}
                    {newPromptFiles.map((file, index) => (
                      <li
                        key={`uploaded-${index}`}
                        className="flex items-center justify-between p-2 bg-muted rounded-md w-full overflow-hidden"
                      >
                        <div className="flex items-center gap-2 min-w-0 flex-1 mr-2 overflow-hidden">
                          <FileText className="h-4 w-4 flex-shrink-0" />
                          <div className="min-w-0 flex-1 overflow-hidden">
                            <span
                              className="block text-sm w-full"
                              title={file.name}
                              style={{
                                overflowWrap: 'break-word',
                                hyphens: 'auto'
                              }}
                            >
                              {file.name}
                            </span>
                          </div>
                          <span className="text-xs text-green-600 flex items-center gap-1 flex-shrink-0 whitespace-nowrap">
                            <CheckCircle className="h-3 w-3" />
                            Uploaded
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // Remove the specific file using the new removeUploadedFile function
                            removeNewPromptFile(file);
                            // Clean up localStorage and reset file input
                            localStorage.removeItem("pendingChatFiles");
                            // Reset the file input to allow re-uploading the same file
                            const fileInput = document.getElementById("promptDocuments") as HTMLInputElement;
                            if (fileInput) {
                              fileInput.value = "";
                            }
                            // Show success toast
                            toast.success(`Removed "${file.name}"`);
                          }}
                          className="h-6 w-6 p-0 flex-shrink-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </li>
                    ))}

                    {/* Display files currently being uploaded */}
                    {newPromptUploadQueue.map((filename, index) => (
                      <li
                        key={`uploading-${index}`}
                        className="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-100 dark:border-blue-800 w-full overflow-hidden"
                      >
                        <div className="flex items-center gap-2 min-w-0 flex-1 overflow-hidden">
                          <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin flex-shrink-0"></div>
                          <div className="min-w-0 flex-1 overflow-hidden">
                            <span
                              className="block text-sm text-blue-700 dark:text-blue-300 w-full break-all leading-tight"
                              title={filename}
                              style={{
                                wordBreak: 'break-all',
                                overflowWrap: 'break-word',
                                hyphens: 'auto'
                              }}
                            >
                              {filename}
                            </span>
                          </div>
                          <span className="text-xs text-blue-600 dark:text-blue-400 flex-shrink-0 whitespace-nowrap">
                            Uploading...
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
          <DialogFooter className="flex-shrink-0">
            <Button
              variant="outline"
              onClick={() => {
                // Reset the dialog state first
                resetNewPromptDialog();

                // Close the dialog which will restore the drawer visibility
                setShowNewPromptDialog(false);

                // Ensure the drawer is fully visible after dialog closes
                const drawerElement = document.querySelector(
                  '[data-playbook="drawer"]'
                ) as HTMLElement;
                if (drawerElement) {
                  drawerElement.style.visibility = "";
                  drawerElement.style.opacity = "";
                }
              }}
              disabled={newPromptUploadQueue.length > 0}
            >
              Cancel
            </Button>
            <Button
              className="navy-button"
              onClick={handleCreatePrompt}
              disabled={newPromptUploadQueue.length > 0 || !newPromptData.title.trim() || !newPromptData.content.trim()}
            >
              {newPromptUploadQueue.length > 0 ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Uploading Files...</span>
                </div>
              ) : (
                "Create Prompt"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Customize Prompt Dialog */}
      <Dialog
        open={showCustomizePromptDialog}
        onOpenChange={setShowCustomizePromptDialog}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Customize Prompt Before Using</DialogTitle>
            <p className="text-sm text-gray-500 mt-1">
              Edit the prompt content below to customize it before using.
            </p>
          </DialogHeader>
          {customizePromptData && (
            <div className="py-4 space-y-4">
              <div>
                <Label htmlFor="customizePromptTitle">Title</Label>
                <Input
                  id="customizePromptTitle"
                  value={customizePromptData.title}
                  onChange={(e) =>
                    setCustomizePromptData({
                      ...customizePromptData,
                      title: e.target.value,
                    })
                  }
                  placeholder="Enter prompt title"
                  className="mt-2"
                  readOnly
                />
              </div>
              <div>
                <Label htmlFor="customizePromptContent">Content</Label>
                <Textarea
                  id="customizePromptContent"
                  value={customizePromptData.content}
                  onChange={(e) =>
                    setCustomizePromptData({
                      ...customizePromptData,
                      content: e.target.value,
                    })
                  }
                  placeholder="Enter prompt content"
                  className="mt-2 min-h-[200px]"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCustomizePromptDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleUseCustomizedPrompt}>
              Use Customized Prompt
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Prompt Dialog */}
      <Dialog
        open={showEditPromptDialog}
        onOpenChange={(open) => {
          setShowEditPromptDialog(open);
          // Clear uploaded files when dialog closes to prevent residual display
          if (!open) {
            clearEditPromptFiles();
          }
        }}
      >
        <DialogContent className="max-w-2xl w-full max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Edit Prompt</DialogTitle>
          </DialogHeader>
          {editPromptData && (
            <div className="py-4 space-y-4 w-full max-w-full overflow-y-auto flex-1 min-h-0 md:px-6 px-3">
              <div className="w-full max-w-full overflow-hidden">
                <Label htmlFor="editPromptTitle">Title</Label>
                <Input
                  id="editPromptTitle"
                  value={editPromptData.title}
                  onChange={(e) =>
                    setEditPromptData({
                      ...editPromptData,
                      title: e.target.value,
                    })
                  }
                  placeholder="Enter prompt title"
                  className="mt-2 w-full max-w-full"
                />
              </div>
              <div className="w-full max-w-full overflow-hidden">
                <Label htmlFor="editPromptFolder">Folder</Label>
                <select
                  id="editPromptFolder"
                  value={editPromptData.folderId}
                  onChange={(e) =>
                    setEditPromptData({
                      ...editPromptData,
                      folderId: e.target.value,
                    })
                  }
                  className="w-full max-w-full mt-2 p-2 border border-input rounded-md bg-background text-base focus-visible:outline-none focus-visible:border-[rgb(var(--base-navy))] focus:border-[rgb(var(--base-navy))] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transition-colors"
                >
                  <option value="">General (Default)</option>
                  {userFolders
                    .filter((folder) => folder.name.toLowerCase() !== "general") // Filter out any "General" folder
                    .map((folder) => (
                      <option key={folder.id} value={folder.id}>
                        {folder.name}
                      </option>
                    ))}
                </select>
              </div>
              <div className="w-full max-w-full overflow-hidden">
                <Label htmlFor="editPromptContent">Content</Label>
                <Textarea
                  id="editPromptContent"
                  value={editPromptData.content}
                  onChange={(e) =>
                    setEditPromptData({
                      ...editPromptData,
                      content: e.target.value,
                    })
                  }
                  placeholder="Enter prompt content"
                  className="mt-2 min-h-[200px] w-full max-w-full resize-none"
                />
              </div>

              {/* Document Attachment Section */}
              <div className="space-y-2 w-full max-w-full overflow-hidden">
                <Label htmlFor="editPromptDocuments">Attached Documents</Label>
                <div
                  className="border-2 border-dashed rounded-lg p-4 transition-colors hover:border-primary/50 hover:bg-muted/50 w-full max-w-full"
                  onDragOver={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onDrop={async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const files = Array.from(e.dataTransfer.files);
                    await handleEditPromptFileUpload(files);
                  }}
                >
                  <input
                    type="file"
                    multiple
                    className="hidden"
                    id="editPromptDocuments"
                    onChange={async (e) => {
                      const files = Array.from(e.target.files || []);
                      await handleEditPromptFileUpload(files);
                    }}
                    accept={PROMPT_FILE_ACCEPT_STRING}
                  />
                  <label
                    htmlFor="editPromptDocuments"
                    className="cursor-pointer flex flex-col items-center"
                  >
                    <Upload className="h-6 w-6 mb-2 text-muted-foreground" />
                    <span className="text-sm font-medium">
                      Click to upload or drag and drop
                    </span>
                    <span className="text-xs text-muted-foreground mt-1">
                      PDF, DOCX, DOC, TXT, Excel, PowerPoint, PNG, JPG
                    </span>
                  </label>
                </div>

                {/* Display Uploaded Files */}
                {editPromptFiles.length > 0 && (
                  <div className="mt-2 w-full overflow-hidden">
                    <p className="text-sm font-medium mb-2">
                      Attached documents:
                    </p>
                    <ul className="text-sm space-y-2 w-full">
                      {editPromptFiles.map((file, index) => (
                        <li
                          key={index}
                          className="flex items-center justify-between p-2 bg-muted rounded-md w-full overflow-hidden"
                        >
                          <div className="flex items-center gap-2 min-w-0 flex-1 mr-2 overflow-hidden">
                            <FileText className="h-4 w-4 flex-shrink-0" />
                            <div className="min-w-0 flex-1 overflow-hidden">
                              <span
                                className="block text-sm w-full leading-tight"
                                title={file.name}
                                style={{
                                  overflowWrap: 'break-word',
                                  hyphens: 'auto'
                                }}
                              >
                                {file.name}
                              </span>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // Remove the specific file using the new removeUploadedFile function
                              removeEditPromptFile(file);
                              // Clean up localStorage and reset file input
                              localStorage.removeItem("pendingChatFiles");
                              // Reset the file input to allow re-uploading the same file
                              const fileInput = document.getElementById("editPromptDocuments") as HTMLInputElement;
                              if (fileInput) {
                                fileInput.value = "";
                              }
                              // Show success toast
                              toast.success(`Removed "${file.name}"`);
                            }}
                            className="h-6 w-6 p-0 flex-shrink-0"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="editPromptFavorite"
                  checked={editPromptData.isFavorite}
                  onChange={(e) =>
                    setEditPromptData({
                      ...editPromptData,
                      isFavorite: e.target.checked,
                    })
                  }
                  className="mr-2"
                />
                <Label htmlFor="editPromptFavorite" className="cursor-pointer">
                  Add to favorites
                </Label>
              </div>
            </div>
          )}
          <DialogFooter className="flex-shrink-0">
            <Button
              variant="outline"
              onClick={() => setShowEditPromptDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() =>
                editPromptData &&
                handleUpdatePrompt(editPromptData, {
                  skipCustomizeDialog: true,
                })
              }
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Save Prompt Drawer */}
      <Dialog open={showSaveDrawer} onOpenChange={setShowSaveDrawer}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Save to My Playbook</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4 md:px-6 px-3">
            <div>
              <Label htmlFor="savePromptName">Prompt Name</Label>
              <Input
                id="savePromptName"
                value={promptName}
                onChange={(e) => setPromptName(e.target.value)}
                placeholder="Enter a name for this prompt"
                className="mt-2"
                autoFocus
              />
            </div>
            <div>
              <Label htmlFor="savePromptFolder">Folder</Label>
              <div className="flex gap-2 mt-2">
                <select
                  id="savePromptFolder"
                  value={newPromptData.folderId}
                  onChange={(e) =>
                    setNewPromptData({
                      ...newPromptData,
                      folderId: e.target.value,
                    })
                  }
                  className="flex-1 p-2 border border-gray-300 rounded-md"
                >
                  <option value="">General (Default)</option>
                  {userFolders
                    .filter((folder) => folder.name.toLowerCase() !== "general") // Filter out any "General" folder
                    .map((folder) => (
                      <option key={folder.id} value={folder.id}>
                        {folder.name}
                      </option>
                    ))}
                </select>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowNewFolderDialog(true)}
                  title="Create New Folder"
                >
                  <FolderPlus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSaveDrawer(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (!promptName.trim()) {
                  toast.error("Please enter a name for your prompt");
                  return;
                }

                // Set up the new prompt data
                const newPrompt = {
                  title: promptName,
                  content: refinedPrompt,
                  folderId: newPromptData.folderId || "",
                };

                // Save the prompt
                handleSavePrompt(newPrompt);

                // Close the drawer
                setShowSaveDrawer(false);
              }}
              disabled={!promptName.trim()}
            >
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

/**
 * A compact, horizontally-aligned card component for displaying a prompt
 * with title, description, and right-aligned action buttons in the same row
 */
interface PromptCardProps {
  prompt: Prompt | PromptWithFolder | IStarterPrompt;
  folderName?: string;
  isSystemFolder?: boolean;
  onUse: (prompt: any) => void;
  onEdit?: (prompt: any, options?: { skipCustomizeDialog?: boolean }) => void;
  onDelete?: (promptId: string, folderId: string) => void;
  onToggleFavorite?: (promptId: string) => void;
  onRemoveFromRecent?: (promptId: string) => void;
  isInRecentSection?: boolean;
}

function PromptCard({
  prompt,
  folderName,
  isSystemFolder,
  onUse,
  onEdit,
  onDelete,
  onToggleFavorite,
  onRemoveFromRecent,
  isInRecentSection,
}: PromptCardProps) {
  // Check if it's a database prompt or a legacy prompt
  const isDbPrompt = "id" in prompt;
  const isLegacyId =
    isDbPrompt &&
    typeof (prompt as Prompt).id === "string" &&
    (prompt as Prompt).id.startsWith("legacy-");
  const title = prompt.title;
  const content = isDbPrompt
    ? (prompt as Prompt).content
    : (prompt as IStarterPrompt).defaultText;
  const description =
    content && content.trim()
      ? content.substring(0, 100) + (content.length > 100 ? "..." : "")
      : "";
  
  // For system folder prompts, we need to check if they're in the favorites list
  const { favoritePrompts = [] } = React.useContext(PlaybookContext);
  
  // Determine if this prompt is favorited
  let isFavorite = false;
  
  if (isDbPrompt) {
    const dbPrompt = prompt as Prompt;
    
    if (isSystemFolder) {
      // For system prompts, check if it exists in the favorites list
      isFavorite = favoritePrompts.some(fav => fav.id === dbPrompt.id);
    } else {
      // For user prompts, use the isFavorite property directly
      isFavorite = dbPrompt.isFavorite || false;
    }
  }

  // Check if the prompt has attached files
  const hasFiles = isDbPrompt && (prompt as any).files && Array.isArray((prompt as any).files) && (prompt as any).files.length > 0;
  const fileCount = hasFiles ? (prompt as any).files.length : 0;

  // State for inline editing
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editedTitle, setEditedTitle] = useState(title);
  const [editedContent, setEditedContent] = useState(content);
  const [editedIsFavorite, setEditedIsFavorite] = useState(isFavorite);
  const [editedFolderId, setEditedFolderId] = useState<string>("");
  const [userFolders, setUserFolders] = useState<PromptFolder[]>([]);
  const [isLoadingFolders, setIsLoadingFolders] = useState(false);
  const [showNewFolderInput, setShowNewFolderInput] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);

  // Create a separate chatId for inline edit file uploads
  const inlineEditChatId = generateUUID();
  const {
    uploadedFiles: inlineEditFiles,
    uploadQueue: inlineEditUploadQueue,
    handleFileUpload: handleInlineEditFileUpload,
    clearUploadedFiles: clearInlineEditFiles,
    removeUploadedFile: removeInlineEditFile
  } = usePromptFileUpload(inlineEditChatId);

  // State for existing prompt files (separate from the upload mechanism)
  const [existingPromptFiles, setExistingPromptFiles] = useState<Array<any>>([]);

  // Reset edit state when prompt changes
  useEffect(() => {
    setEditedTitle(title);
    setEditedContent(content);
    setEditedIsFavorite(isFavorite);
    setEditedFolderId(isDbPrompt ? (prompt as Prompt).folderId : "");
  }, [prompt, title, content, isFavorite, isDbPrompt]);

  // Load prompt files when entering edit mode
  useEffect(() => {
    if (isEditing && isDbPrompt) {
      const fetchPromptFiles = async () => {
        try {
          // Clear any existing files first
          clearInlineEditFiles();
          setExistingPromptFiles([]);

          Logger.info(`Fetching files for prompt ${(prompt as Prompt).id} in inline edit mode`);

          // Fetch the prompt details with files
          const response = await fetch(`/api/prompts?id=${(prompt as Prompt).id}`, {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          });

          if (response.ok) {
            const promptDetails = await response.json();

            // If the prompt has files, load them into our separate state for existing files
            if (promptDetails.files && Array.isArray(promptDetails.files) && promptDetails.files.length > 0) {
              Logger.info(`Found ${promptDetails.files.length} files associated with prompt ${(prompt as Prompt).id}:`, promptDetails.files);

              // Define the type for prompt files
              interface PromptFile {
                fileName: string;
                fileUrl: string;
                fileType: string;
                fileSize?: number;
                id?: string;
              }

              // Convert the files to the format we need
              const formattedFiles = promptDetails.files.map((file: PromptFile) => ({
                name: file.fileName,
                url: file.fileUrl,
                contentType: file.fileType,
                size: file.fileSize || 0,
                document_id: file.id || generateUUID(),
                uploaded: true
              }));

              // Store the files in our state, NOT in localStorage
              setExistingPromptFiles(formattedFiles);
            }
          }
        } catch (error) {
          Logger.error(`Error fetching files for prompt ${(prompt as Prompt).id}:`, error);
          toast.error("Failed to load attached documents");
        }
      };

      fetchPromptFiles();
    }
  }, [isEditing, isDbPrompt, prompt]);

  // Load user folders when entering edit mode
  const loadUserFolders = async () => {
    try {
      setIsLoadingFolders(true);
      const response = await fetch("/api/prompts/folders?type=user", {
        headers: { "Cache-Control": "max-age=300" }, // Cache for 5 minutes
      });

      if (!response.ok) throw new Error("Failed to load folders");

      const folders = await response.json();
      setUserFolders(folders);
    } catch (error) {
      console.error("Error loading folders:", error);
      toast.error("Failed to load folders");
    } finally {
      setIsLoadingFolders(false);
    }
  };

  // Create a new folder
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error("Folder name cannot be empty");
      return;
    }

    setIsCreatingFolder(true);
    try {
      const response = await fetch("/api/prompts/folders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: newFolderName }),
      });

      if (!response.ok) {
        if (response.status === 409) {
          const errorMessage = await response.text();
          toast.error(errorMessage);
          return;
        }
        throw new Error("Failed to create folder");
      }

      const newFolder = await response.json();

      // Update folders list
      setUserFolders([...userFolders, newFolder]);

      // Select the new folder
      setEditedFolderId(newFolder.id);

      // Reset folder creation state
      setNewFolderName("");
      setShowNewFolderInput(false);

      toast.success("Folder created successfully");
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.error("Failed to create folder");
    } finally {
      setIsCreatingFolder(false);
    }
  };

  // Handle saving the edited prompt
  const handleSaveEdit = () => {
    if (onEdit) {
      try {
        setIsSaving(true);

        // Combine existing files and newly uploaded files
        const allFiles = [...existingPromptFiles, ...inlineEditFiles];

        // Define the type for the combined files
        interface CombinedFile {
          name: string;
          url: string;
          contentType?: string;
          size?: number;
          document_id?: string;
        }

        // Convert the files to the format expected by the API
        const files = allFiles.length > 0
          ? allFiles.map((file: CombinedFile) => ({
              fileName: file.name,
              fileUrl: file.url,
              fileType: file.contentType || 'application/octet-stream',
              fileSize: file.size || 0,
              id: file.document_id // Preserve the original document ID
            }))
          : undefined;

        // Log the files being sent to the API
        if (files && files.length > 0) {
          Logger.info(`Sending ${files.length} files to update prompt API:`, { files });
        }

        // Create an updated prompt object
        // All prompts should now be in database format
        const updatedPrompt = {
          ...prompt,
          title: editedTitle,
          content: editedContent,
          isFavorite: editedIsFavorite,
          folderId: editedFolderId || (prompt as Prompt).folderId, // Use the selected folder or keep the existing one
          files: files // Include the files in the update
        };

        console.log(
          "Saving edited prompt:",
          isDbPrompt ? (updatedPrompt as Prompt).id : "legacy-prompt",
          editedTitle,
          `with ${files ? files.length : 0} files`
        );

        // Immediately exit edit mode to prevent the "failed to load prompt" error
        // This ensures the UI is updated immediately, even before the server responds
        setIsEditing(false);

        // Call the parent's onEdit function with the updated prompt
        // This will handle the API call and state updates
        // Pass skipCustomizeDialog: true to prevent the customize dialog from showing
        onEdit(updatedPrompt, { skipCustomizeDialog: true });

        // Reset saving state after a short delay
        setTimeout(() => {
          setIsSaving(false);
        }, 500);
      } catch (error) {
        console.error("Error in handleSaveEdit:", error);
        toast.error("Failed to save prompt");
        setIsSaving(false);
      }
    }
  };

  // Handle canceling the edit
  const handleCancelEdit = () => {
    // Reset to original values
    setEditedTitle(title);
    setEditedContent(content);
    setEditedIsFavorite(isFavorite);

    // Clear any uploaded files
    clearInlineEditFiles();

    // Clear existing files state
    setExistingPromptFiles([]);

    // Clean up localStorage to prevent any side effects
    if (typeof window !== "undefined") {
      localStorage.removeItem("pendingChatFiles");
    }

    // Exit edit mode
    setIsEditing(false);
  };

  return (
    <div className="py-2.5 px-3 bg-[var(--chat-background-color)] text-muted-foreground  hover:text-[rgb(var(--base-navy))]  transition-all duration-200 rounded-md group">
      {isEditing ? (
        // Inline editor mode
        <div className="space-y-3">
          <div>
            <Label htmlFor="inlineEditTitle">Title</Label>
            <Input
              id="inlineEditTitle"
              value={editedTitle}
              onChange={(e) => setEditedTitle(e.target.value)}
              placeholder="Enter prompt title"
              className="mt-1 text-sm bg-[var(--text-input-color-light)] dark:bg-zinc-900"
            />
          </div>
          <div>
            <Label htmlFor="inlineEditContent">Content</Label>
            <Textarea
              id="inlineEditContent"
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              placeholder="Enter prompt content"
              className="mt-1 min-h-[100px] text-sm bg-[var(--text-input-color-light)] dark:bg-zinc-900"
            />
          </div>
          {isDbPrompt && (
            <div className="space-y-3">
              {/* Add to favorites checkbox */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="inlineEditFavorite"
                  checked={editedIsFavorite}
                  onChange={(e) => setEditedIsFavorite(e.target.checked)}
                  className="mr-2"
                />
                <Label
                  htmlFor="inlineEditFavorite"
                  className="cursor-pointer text-sm"
                >
                  Add to favorites
                </Label>
              </div>

              {/* Save to folder dropdown */}
              <div className="space-y-2">
                <Label
                  htmlFor="inlineEditFolder"
                  className="flex items-center justify-between text-sm"
                >
                  <span>Save to folder</span>
                  {!showNewFolderInput && (
                    <Button
                      variant="link"
                      className="px-0 h-6 text-xs font-medium"
                      onClick={(e) => {
                        e.preventDefault();
                        // Load folders if not already loaded
                        if (userFolders.length === 0 && !isLoadingFolders) {
                          loadUserFolders();
                        }
                        setShowNewFolderInput(true);
                      }}
                    >
                      + New Folder
                    </Button>
                  )}
                </Label>

                {showNewFolderInput ? (
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        value={newFolderName}
                        onChange={(e) => setNewFolderName(e.target.value)}
                        placeholder="Enter folder name"
                        className="text-sm h-8"
                        disabled={isCreatingFolder}
                      />
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          className="h-8 text-xs navy-button"
                          onClick={handleCreateFolder}
                          disabled={isCreatingFolder || !newFolderName.trim()}
                        >
                          {isCreatingFolder ? (
                            <div className="flex items-center">
                              <div className="h-3 w-3 border-2 border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                              <span>Creating</span>
                            </div>
                          ) : (
                            "Create"
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs"
                          onClick={() => setShowNewFolderInput(false)}
                          disabled={isCreatingFolder}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="relative">
                    <select
                      id="inlineEditFolder"
                      value={editedFolderId}
                      onChange={(e) => setEditedFolderId(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm bg-background dark:bg-zinc-800 dark:border-gray-700 dark:text-gray-200"
                      onClick={() => {
                        // Load folders if not already loaded
                        if (userFolders.length === 0 && !isLoadingFolders) {
                          loadUserFolders();
                        }
                      }}
                      disabled={isLoadingFolders}
                    >
                      <option value="">
                        No folder (will be saved to General)
                      </option>
                      {userFolders
                        .filter(
                          (folder) => folder.name.toLowerCase() !== "general"
                        ) // Filter out any "General" folder
                        .map((folder) => (
                          <option key={folder.id} value={folder.id}>
                            {folder.name}
                          </option>
                        ))}
                    </select>
                    {isLoadingFolders && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                        <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Document Attachment Section */}
              <div className="space-y-2">
                <Label htmlFor="inlineEditDocuments">Attached Documents</Label>
                <div
                  className="border-2 border-dashed rounded-lg p-3 transition-colors hover:border-primary/50 hover:bg-muted/50"
                  onDragOver={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onDrop={async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const files = Array.from(e.dataTransfer.files);
                    await handleInlineEditFileUpload(files);
                  }}
                >
                  <input
                    type="file"
                    multiple
                    className="hidden"
                    id="inlineEditDocuments"
                    onChange={async (e) => {
                      const files = Array.from(e.target.files || []);
                      // Validate files before uploading
                      const { validFiles, errors } = validatePromptFiles(files);
                      if (errors.length > 0) {
                        errors.forEach(error => toast.error(error));
                      }
                      if (validFiles.length > 0) {
                        await handleInlineEditFileUpload(validFiles);
                      }
                    }}
                    accept={PROMPT_FILE_ACCEPT_STRING}
                  />
                  <label
                    htmlFor="inlineEditDocuments"
                    className="cursor-pointer flex flex-col items-center"
                  >
                    <Upload className="h-5 w-5 mb-1 text-muted-foreground" />
                    <span className="text-xs font-medium">
                      Click to upload or drag and drop
                    </span>
                    <span className="text-xs text-muted-foreground mt-0.5">
                      PDF, DOCX, DOC, TXT
                    </span>
                  </label>
                </div>

                {/* Display All Files (Existing + Newly Uploaded) */}
                {(existingPromptFiles.length > 0 || inlineEditFiles.length > 0) && (
                  <div className="mt-2 w-full overflow-hidden">
                    <p className="text-xs font-medium mb-1">
                      Attached documents:
                    </p>
                    <ul className="text-xs space-y-1 w-full">
                      {/* Display existing files */}
                      {existingPromptFiles.map((file, index) => (
                        <li
                          key={`existing-${index}`}
                          className="flex items-center justify-between p-1.5 bg-muted rounded-md w-full overflow-hidden"
                        >
                          <div className="flex items-center gap-1.5 min-w-0 flex-1 mr-1 overflow-hidden">
                            <FileText className="h-3 w-3 flex-shrink-0" />
                            <div className="min-w-0 flex-1 overflow-hidden">
                              <span
                                className="block text-xs w-full break-all leading-tight"
                                title={file.name}
                                style={{
                                  wordBreak: 'break-all',
                                  overflowWrap: 'break-word',
                                  hyphens: 'auto'
                                }}
                              >
                                {file.name}
                              </span>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // Remove this file from existing files
                              const newExistingFiles = [...existingPromptFiles];
                              newExistingFiles.splice(index, 1);
                              setExistingPromptFiles(newExistingFiles);
                              // Clean up localStorage and reset file input
                              localStorage.removeItem("pendingChatFiles");
                              // Reset the file input to allow re-uploading the same file
                              const fileInput = document.getElementById("inlineEditDocuments") as HTMLInputElement;
                              if (fileInput) {
                                fileInput.value = "";
                              }
                              // Show success toast
                              toast.success(`Removed "${file.name}"`);
                            }}
                            className="h-5 w-5 p-0 flex-shrink-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </li>
                      ))}

                      {/* Display newly uploaded files */}
                      {inlineEditFiles.map((file, index) => (
                        <li
                          key={`new-${index}`}
                          className="flex items-center justify-between p-1.5 bg-muted rounded-md w-full overflow-hidden"
                        >
                          <div className="flex items-center gap-1.5 min-w-0 flex-1 mr-1 overflow-hidden">
                            <FileText className="h-3 w-3 flex-shrink-0" />
                            <div className="min-w-0 flex-1 overflow-hidden">
                              <span
                                className="block text-xs w-full break-all leading-tight"
                                title={file.name}
                                style={{
                                  wordBreak: 'break-all',
                                  overflowWrap: 'break-word',
                                  hyphens: 'auto'
                                }}
                              >
                                {file.name}
                              </span>
                            </div>
                            <span className="text-[10px] text-blue-500 flex-shrink-0 whitespace-nowrap">(New)</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // Remove the specific file using the hook's removeUploadedFile function
                              removeInlineEditFile(file);                    
                              // Show success toast
                              toast.success(`Removed "${file.name}"`);
                            }}
                            className="h-5 w-5 p-0 flex-shrink-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
          <div className="flex justify-end space-x-2 pt-1">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancelEdit}
              className="h-7 text-xs"
              disabled={isSaving}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSaveEdit}
              className="h-7 text-xs navy-button"
              disabled={isSaving}
            >
              {isSaving ? (
                <div className="flex items-center">
                  <span className="mr-1">Saving</span>
                  <div className="h-3 w-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : (
                "Save Changes"
              )}
            </Button>
          </div>
        </div>
      ) : (
        // Normal display mode
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <div className="min-w-0 flex-1 w-full sm:w-auto">
            <h3 className="font-bold text-gray-900 dark:text-gray-100 text-sm group-hover:text-[rgb(var(--base-navy))] dark:group-hover:text-white truncate" title={title}>
              {title}
            </h3>
            <p className="text-xs text-muted-foreground line-clamp-1 mt-0.5 group-hover:text-[rgb(var(--base-navy))]/80 dark:group-hover:text-gray-300">
              {description && (
                <span className="inline-flex items-center">{description}</span>
              )}
            </p>
            {/* Display file attachments if any */}
            {hasFiles && (
              <div className="flex items-center mt-1 text-xs text-muted-foreground">
                <FileText className="h-3 w-3 mr-1" />
                <span>{fileCount} {fileCount === 1 ? 'file' : 'files'} attached</span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-1 shrink-0 w-full sm:w-auto justify-end mt-2 sm:mt-0">
            {/* Remove from Recently Used button */}
            {isInRecentSection && onRemoveFromRecent && !isLegacyId && (
              <button
                onClick={() => {
                  console.log(
                    "Removing from recently used, prompt ID:",
                    (prompt as Prompt).id
                  );
                  onRemoveFromRecent((prompt as Prompt).id);
                }}
                className="p-1 text-muted-foreground hover:text-red-500 transition-colors"
                title="Remove from Recently Used list"
              >
                <Trash2 className="h-3.5 w-3.5" />
              </button>
            )}

            {/* Favorite button */}
            {onToggleFavorite && isDbPrompt && !isLegacyId && (
              <button
                onClick={() => {
                  console.log(
                    "Toggling favorite, prompt ID:",
                    (prompt as Prompt).id
                  );
                  onToggleFavorite((prompt as Prompt).id);
                }}
                className={`p-1 transition-colors ${
                  isFavorite
                    ? "text-yellow-500 hover:text-yellow-600"
                    : "text-muted-foreground hover:text-yellow-500"
                }`}
                title={
                  isFavorite ? "Remove from favorites" : "Add to favorites"
                }
              >
                <Star
                  className={`h-3.5 w-3.5 ${
                    isFavorite ? "fill-yellow-500" : "fill-none"
                  }`}
                />
              </button>
            )}

            {/* Save As... button for non-DB prompts */}
            {!isDbPrompt && onEdit && (
              <button
                onClick={() => onEdit(prompt, { skipCustomizeDialog: false })}
                className="p-1 text-muted-foreground hover:text-blue-500 transition-colors"
                title="Save as..."
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
              </button>
            )}

            {/* Edit button now toggles inline edit mode */}
            {onEdit && !isLegacyId && (
              <button
                onClick={() => {
                  setIsEditing(true);
                  // Set the initial folder ID
                  setEditedFolderId(
                    isDbPrompt ? (prompt as Prompt).folderId : ""
                  );
                  // Load user folders when entering edit mode
                  loadUserFolders();
                }}
                className="p-1 text-muted-foreground hover:text-blue-500 transition-colors"
                title="Edit prompt"
              >
                <Edit className="h-3.5 w-3.5" />
              </button>
            )}

            {!isLegacyId &&
              onDelete &&
              !isSystemFolder &&
              !isInRecentSection && (
                <button
                  onClick={() =>
                    onDelete((prompt as Prompt).id, (prompt as Prompt).folderId)
                  }
                  className="p-1 text-muted-foreground hover:text-red-500 transition-colors"
                  title="Delete"
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </button>
              )}

            <Button
              variant="default"
              size="sm"
              onClick={() => onUse(prompt)}
              className="ml-1 h-6 px-2 text-xs font-medium whitespace-nowrap navy-button"
              title="Insert prompt directly without modification"
            >
              Use
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * An expandable list component for categories
 */
interface CategoryExpandableListProps {
  folder?: PromptFolder;
  legacyCategory?: { category: string; prompts: IStarterPrompt[] };
  prompts?: Prompt[];
  onUsePrompt: (prompt: any) => void;
  onEditPrompt?: (
    prompt: Prompt,
    options?: { skipCustomizeDialog?: boolean }
  ) => void;
  onDeletePrompt?: (promptId: string, folderId: string) => void;
  onToggleFavorite?: (promptId: string) => void;
  onAddPrompt?: (folderId: string) => void;
  isSystemFolder?: boolean;
}

function CategoryExpandableList({
  folder,
  legacyCategory,
  prompts = [],
  onUsePrompt,
  onEditPrompt,
  onDeletePrompt,
  onToggleFavorite,
  onAddPrompt,
  isSystemFolder = false,
}: CategoryExpandableListProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Determine if we're using the legacy data or the new database structure
  const isLegacy = !!legacyCategory;
  const title = isLegacy ? legacyCategory!.category : folder!.name;
  const promptsList = isLegacy ? legacyCategory!.prompts : prompts;

  return (
    <div className="border border-gray-100 dark:border-gray-800 rounded-md overflow-hidden mb-3 shadow-sm">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-3 bg-muted dark:bg-muted/30 hover:bg-[rgb(var(--base-navy))]/10 transition-colors text-left"
      >
        <div className="flex items-center">
          {!isLegacy && (
            <span
              className={`inline-block w-2 h-2 rounded-full mr-2 ${
                isSystemFolder
                  ? "bg-[rgb(var(--base-navy))]/80"
                  : "bg-[rgb(var(--base-navy))]/90"
              }`}
              title={isSystemFolder ? "System folder" : "User folder"}
            ></span>
          )}
          <h3 className="font-semibold text-gray-800 dark:text-gray-200 text-sm hover:text-[rgb(var(--base-navy))]">
            {title}
          </h3>
          <span className="ml-2 text-xs text-muted-foreground bg-background/60 dark:bg-background/40 px-1.5 py-0.5 rounded-full">
            {promptsList.length}
          </span>
        </div>
        <div className="text-muted-foreground">
          {isExpanded ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M18 15l-6-6-6 6" />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M6 9l6 6 6-6" />
            </svg>
          )}
        </div>
      </button>

      {isExpanded && (
        <div className="divide-y divide-gray-100 dark:divide-gray-800 bg-[var(--chat-background-color)]">
          {promptsList.length === 0 ? (
            <div className="p-4 text-center">
              <p className="text-xs text-muted-foreground">
                No prompts in this {isLegacy ? "category" : "folder"} yet.
                {!isLegacy && !isSystemFolder && folder && (
                  <Button
                    variant="link"
                    size="sm"
                    className="p-0 h-auto text-xs text-[rgb(var(--base-navy))]"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Pass the folder ID to the parent component
                      if (onAddPrompt && folder) {
                        onAddPrompt(folder.id);
                      }
                    }}
                  >
                    Add one!
                  </Button>
                )}
              </p>
            </div>
          ) : (
            <div>
              {/* All prompts are treated as database prompts */}
              {promptsList.map((prompt, index) => {
                // Convert legacy prompts to database-like format
                const isLegacyPrompt = !("id" in prompt);
                let dbPrompt: Prompt;

                if (isLegacyPrompt) {
                  // Convert legacy prompt to database-like format
                  const legacyPrompt = prompt as IStarterPrompt;
                  dbPrompt = {
                    id: `legacy-${index}`,
                    title: legacyPrompt.title,
                    content: legacyPrompt.defaultText,
                    folderId: "starter-pack",
                    isFavorite: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    lastUsed: null,
                  };
                } else {
                  dbPrompt = prompt as Prompt;
                }

                return (
                  <PromptCard
                    key={
                      isLegacyPrompt ? `legacy-prompt-${index}` : dbPrompt.id
                    }
                    prompt={dbPrompt}
                    folderName={title}
                    isSystemFolder={isSystemFolder}
                    onUse={onUsePrompt}
                    onEdit={onEditPrompt}
                    onDelete={isLegacyPrompt ? undefined : onDeletePrompt}
                    onToggleFavorite={
                      isLegacyPrompt ? undefined : onToggleFavorite
                    }
                  />
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
