'use client';

import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { LightbulbIcon } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { FeedbackDialog } from './feedback-dialog';
import { useWindowSize } from 'usehooks-ts';

interface RequestFeatureProps {
  userEmail?: string;
}

export function RequestFeatureButton({ userEmail = 'Not logged in' }: RequestFeatureProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { width, height } = useWindowSize();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything during SSR or before mount
  if (!isMounted) return null;

  // Hide button on small screens or when height is too small
  if (width < 1170 || height < 600) return null;

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            onClick={() => setIsDialogOpen(true)}
            variant="outline"
            size="icon"
            className="h-9 w-9"
          >
            <LightbulbIcon className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Request a Feature</TooltipContent>
      </Tooltip>

      <FeedbackDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        type="feature"
        userEmail={userEmail}
      />
    </>
  );
}
