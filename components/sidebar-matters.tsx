"use client";

import { useState, useEffect, useRef } from "react";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarMenuItem, SidebarMenuButton } from "@/components/ui/sidebar";
import { 
  FolderIcon, 
  ChevronDownIcon, 
  ChevronRightIcon, 
  MoreHorizontalIcon,
  PlusIcon,
  EditIcon,
  TrashIcon,
  CheckIcon,
  PinIcon,
  TagIcon,
  XIcon
} from "lucide-react";
import { ChatWithTags as Chat, Tag } from "@/lib/db/schema";
import { useDrag, useDrop } from "react-dnd";
import { toast } from "sonner";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { SearchIcon } from "./icons";
import { Badge } from "@/components/ui/badge";
import { useTagManagement } from "@/hooks/use-tag-management";
import { TagManagementDialog } from "@/components/tag-management-dialog";
import { mutate } from 'swr';
import { ItemTypes } from "@/lib/dnd-types";

const formatTime = (dateString: Date | string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

interface Matter {
  id: string;
  name: string;
  chats: Chat[];
}

interface SidebarMattersProps {
  matters: Matter[];
  allChats: Chat[];
  onAddMatter: (name: string) => void;
  onRenameMatter: (id: string, name: string) => void;
  onDeleteMatter: (id: string) => void;
  onAddChatToMatter: (matterId: string, chatId: string) => void;
  onRemoveChatFromMatter: (matterId: string, chatId: string) => void;
  onReorderChatsInMatter: (matterId: string, chatIds: string[]) => void;
  onPinChat: (chatId: string) => void;
  setOpenMobile: (open: boolean) => void;
}

type DragItem = {
  type: string;
  id: string;
  index?: number;
  source?: "history" | "pinned" | "folder";
  folderId?: string;
};

const MatterChatItem = ({ 
  chat, 
  matterId,
  index, 
  isActive, 
  onRemove, 
  onPinChat,
  moveChat, 
  setOpenMobile,
  onManageTags
}: { 
  chat: Chat; 
  matterId: string;
  index: number;
  isActive: boolean; 
  onRemove: () => void;
  onPinChat: (chatId: string) => void;
  moveChat: (dragIndex: number, hoverIndex: number) => void;
  setOpenMobile: (open: boolean) => void;
  onManageTags: () => void;
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const tagMenuItemRef = useRef<HTMLDivElement>(null);
  const [isItemHovered, setIsItemHovered] = useState(false);
  
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.FOLDER_CHAT,
    item: { 
      type: ItemTypes.FOLDER_CHAT, 
      id: chat.id, 
      index, 
      folderId: matterId,
      source: "folder" 
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  
  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.FOLDER_CHAT],
    hover: (item: DragItem, monitor) => {
      if (!ref.current || item.folderId !== matterId) {
        return;
      }
      
      const dragIndex = item.index;
      const hoverIndex = index;
      
      if (dragIndex === undefined || hoverIndex === dragIndex) {
        return;
      }
      
      moveChat(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });
  
  drag(drop(ref));
  
  return (
    <SidebarMenuItem
      ref={ref as any}
      className={`relative ${isDragging ? 'opacity-50' : 'opacity-100'} ${isActive ? 'bg-sidebar-accent' : ''} ${isOver ? 'bg-sidebar-accent/30' : ''}`}
      style={{ cursor: 'move' }}
      onMouseEnter={() => setIsItemHovered(true)}
      onMouseLeave={() => setIsItemHovered(false)}
    >
      <SidebarMenuButton 
        className="w-full flex items-center gap-2 h-auto pt-1.5 pb-1 pl-6"
      >
        <div className="flex w-full pr-8">
          <Link
            href={`/chat/${chat.id}`}
            onClick={() => setOpenMobile(false)}
            className="min-w-0 flex-1 px-2 pb-1.5"
          >
            <div className="flex flex-col gap-0.5">
              <span className="truncate text-sm block font-semibold">
                {chat.title}
              </span>
              <span className="text-[11px] text-sidebar-foreground/40 block truncate leading-tight">
                {formatTime(chat.updatedAt || chat.createdAt)}
              </span>
              
              {/* Display tags with colors if present */}
              {chat.tags && chat.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {chat.tags.map((tag, i) => (
                    <Badge 
                      key={i} 
                      className="px-1.5 py-0 text-[10px] h-4 rounded-lg truncate max-w-[80px] border-0 font-normal"
                      style={{ 
                        backgroundColor: `${tag.color}20`, 
                        color: tag.color,
                        borderColor: tag.color
                      }}
                    >
                      <span className="truncate">{tag.name}</span>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </Link>
        </div>
      </SidebarMenuButton>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            className={`absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-sidebar-accent transition-opacity ${
              isItemHovered ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <MoreHorizontalIcon className="h-3 w-3" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="bottom" align="end">
          <DropdownMenuItem 
            onClick={() => {
              onManageTags();
              // Close the dropdown menu when opening the dialog
              document.body.click(); // This will close the dropdown
            }}
            // Use a div wrapper with ref since DropdownMenuItem doesn't accept refs directly
            asChild
          >
            <div ref={tagMenuItemRef}>
              <TagIcon className="h-4 w-4 mr-2" />
              Manage Tags
            </div>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onPinChat(chat.id)}>
            <PinIcon className="h-4 w-4 mr-2" />
            Pin
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onRemove} className="text-destructive">
            <TrashIcon className="h-4 w-4 mr-2" />
            Remove
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  );
};

const MatterItem = ({
  matter,
  index,
  allChats,
  onRename,
  onDelete,
  onAddChat,
  onRemoveChat,
  onReorderChats,
  onPinChat,
  setOpenMobile,
  autoEdit = false
}: {
  matter: Matter;
  index: number;
  allChats: Chat[];
  onRename: (name: string) => void;
  onDelete: () => void;
  onAddChat: (chatId: string) => void;
  onRemoveChat: (chatId: string) => void;
  onReorderChats: (chatIds: string[]) => void;
  onPinChat: (chatId: string) => void;
  setOpenMobile: (open: boolean) => void;
  autoEdit?: boolean;
}) => {
  const { id } = useParams();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isAddingChat, setIsAddingChat] = useState(false);
  const [selectedChatId, setSelectedChatId] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [matterChats, setMatterChats] = useState<Chat[]>(matter.chats);
  const [isHeaderHovered, setIsHeaderHovered] = useState(false);
  // Initialize editing state based on autoEdit prop
  const [isEditing, setIsEditing] = useState(autoEdit);
  const [editedName, setEditedName] = useState(matter.name);
  const folderHeaderRef = useRef<HTMLDivElement>(null);
  
  // Add tag management hook
  const { 
    isDialogOpen, 
    currentChat, 
    openTagDialog, 
    closeTagDialog, 
    handleSaveTags,
    isLoading: isTagsLoading
  } = useTagManagement({
    onUpdateTags: async (chatId, tags) => {
      // Update local state
      setMatterChats(prevChats => 
        prevChats.map(chat => 
          chat.id === chatId ? { ...chat, tags } : chat
        )
      );
      
      // Trigger global revalidation to update all components
      mutate("/api/chat-org/sidebar-data");
    }
  });

  // Add drop functionality to accept chats from other sections
  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.CHAT, ItemTypes.PINNED_CHAT],
    drop: (item: DragItem) => {
      // Add the chat to this folder
      onAddChat(item.id);
      return { addedToFolder: true };
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  // Apply the drop ref to the folder header
  drop(folderHeaderRef);

  // Auto-focus on the input when entering edit mode
  useEffect(() => {
    if (isEditing) {
      // Focus will be handled by the autoFocus attribute on the input
      // Expand the folder when editing to make it more visible
      setIsExpanded(true);
    }
  }, [isEditing]);

  // Handle submit for rename
  const handleSubmitRename = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (editedName && editedName !== matter.name) {
      onRename(editedName);
    } else {
      setEditedName(matter.name);
    }
    setIsEditing(false);
  };
  
  // Add ref for the tag menu item from MatterChatItem
  const tagMenuItemRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    setMatterChats(matter.chats);
  }, [matter.chats]);

  useEffect(() => {
    setEditedName(matter.name);
  }, [matter.name]);
  
  const moveChat = (dragIndex: number, hoverIndex: number) => {
    const dragChat = matterChats[dragIndex];
    const newMatterChats = [...matterChats];
    newMatterChats.splice(dragIndex, 1);
    newMatterChats.splice(hoverIndex, 0, dragChat);
    
    setMatterChats(newMatterChats);
    onReorderChats(newMatterChats.map(chat => chat.id));
  };
  
  const availableChats = allChats.filter(
    chat => !matter.chats.some(c => c.id === chat.id)
  );
  
  const handleAddChat = () => {
    if (selectedChatId) {
      onAddChat(selectedChatId);
      setIsAddingChat(false);
      setSelectedChatId("");
      setSearchQuery("");
    }
  };
  
  return (
    <div className="mb-1">
      <div 
        ref={folderHeaderRef}
        className={`flex items-center justify-between px-1 py-1 hover:bg-sidebar-accent/50 rounded-md ${isOver ? 'bg-sidebar-accent/70' : ''}`}
        onMouseEnter={() => setIsHeaderHovered(true)}
        onMouseLeave={() => setIsHeaderHovered(false)}
      >
        <div 
          className={`flex items-center gap-1 ${isEditing ? "" : "cursor-pointer"} flex-1`}
          onClick={() => !isEditing && setIsExpanded(!isExpanded)}
        >
          {isExpanded ? (
            <ChevronDownIcon className="h-4 w-4 text-sidebar-foreground/70" />
          ) : (
            <ChevronRightIcon className="h-4 w-4 text-sidebar-foreground/70" />
          )}
          <FolderIcon className="h-4 w-4 text-sidebar-foreground/70" />
          
          {isEditing ? (
            <form onSubmit={handleSubmitRename} className="flex-1">
              <input
                type="text"
                value={editedName}
                onChange={(e) => setEditedName(e.target.value)}
                onBlur={handleSubmitRename}
                autoFocus
                className="w-full bg-sidebar-accent/30 px-2 py-0.5 rounded border border-primary/50 outline-none text-sm font-medium focus:border-primary focus:ring-1 focus:ring-primary"
              />
            </form>
          ) : (
            <>
              <span className="text-sm font-medium">{matter.name}</span>
              <span className="text-xs text-sidebar-foreground/50 ml-1">
                ({matter.chats.length})
              </span>
            </>
          )}
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="icon" 
              className={`h-6 w-6 transition-opacity ${
                isHeaderHovered ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsAddingChat(true)}>
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Chat
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setIsEditing(true)}>
              <EditIcon className="h-4 w-4 mr-2" />
              Rename
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onDelete} className="text-destructive">
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {isExpanded && (
        <div className="mt-1 space-y-1">
          {matterChats.map((chat, chatIndex) => (
            <MatterChatItem
              key={chat.id}
              chat={chat}
              matterId={matter.id}
              index={chatIndex}
              isActive={chat.id === id}
              onRemove={() => onRemoveChat(chat.id)}
              onPinChat={onPinChat}
              moveChat={moveChat}
              setOpenMobile={setOpenMobile}
              onManageTags={() => {
                // Store the reference to the clicked element
                openTagDialog(chat);
              }}
            />
          ))}
          
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start pl-6 text-xs text-sidebar-foreground/70"
            onClick={() => setIsAddingChat(true)}
          >
            <PlusIcon className="h-3 w-3 mr-1" />
            Add Chat
          </Button>
        </div>
      )}
      
      {/* Replace Dialog with lightweight dialog */}
      {isDialogOpen && (
        <TagManagementDialog
          open={isDialogOpen}
          onOpenChange={(open) => {
            if (!open) closeTagDialog();
          }}
          chat={currentChat}
          allChats={allChats}
          onSave={handleSaveTags}
          isLoading={isTagsLoading}
          lightweightDialog={true}
          anchorElement={document.activeElement as HTMLElement}
        />
      )}
      
      <Dialog open={isAddingChat} onOpenChange={setIsAddingChat}>
        <DialogContent className="max-w-3xl w-full px-4">
          <DialogHeader>
            <DialogTitle>Add Chat to {matter.name}</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="relative">
              <div className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground">
                <SearchIcon />
              </div>
              <Input
                placeholder="Search chats..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div className="max-h-[240px] overflow-y-auto border rounded-md">
              {availableChats
                .filter(chat => 
                  chat.title.toLowerCase().includes(searchQuery.toLowerCase())
                )
                .map(chat => (
                  <div 
                    key={chat.id} 
                    className={`p-2 hover:bg-muted cursor-pointer flex items-center ${
                      selectedChatId === chat.id ? 'bg-muted' : ''
                    }`}
                    onClick={() => setSelectedChatId(chat.id)}
                  >
                    <div className="flex-1 truncate">
                      {chat.title.length > 75 ? `${chat.title.substring(0, 75)}...` : chat.title}
                    </div>
                    {selectedChatId === chat.id && (
                      <CheckIcon className="h-4 w-4 text-primary" />
                    )}
                  </div>
                ))}
              {availableChats.filter(chat => 
                chat.title.toLowerCase().includes(searchQuery.toLowerCase())
              ).length === 0 && (
                <div className="p-3 text-center text-muted-foreground text-sm">
                  No chats found
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingChat(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddChat} disabled={!selectedChatId}>
              Add
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export function SidebarMatters({
  matters,
  allChats,
  onAddMatter,
  onRenameMatter,
  onDeleteMatter,
  onAddChatToMatter,
  onRemoveChatFromMatter,
  onReorderChatsInMatter,
  onPinChat,
  setOpenMobile
}: SidebarMattersProps) {
  // Add state for collapsible functionality
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // Function to generate a unique folder name
  const generateUniqueFolderName = () => {
    const baseName = "New Folder";
    const existingNames = matters.map(m => m.name);
    
    if (!existingNames.includes(baseName)) {
      return baseName;
    }
    let counter = 2;
    let newName = `${baseName} (${counter})`;    
    while (existingNames.includes(newName)) {
      counter++;
      newName = `${baseName} (${counter})`;
    }
    
    return newName;
  };
  
  // Handle adding a new matter with a temporary ID and auto-editing mode
  const handleAddMatter = () => {
    const newName = generateUniqueFolderName();
    onAddMatter(newName);
    // The new matter will be added to the list and should be in edit mode automatically
  };
  
  if (matters.length === 0 && isCollapsed) {
    return (
      <div className="mb-2">
        <div 
          className="px-1 py-1 text-xs font-semibold text-sidebar-foreground/70 flex items-center justify-between cursor-pointer"
          onClick={() => setIsCollapsed(false)}
        >
          <div className="flex items-center gap-1">
            <FolderIcon className="h-3 w-3" />
            <span>MATTER FOLDERS</span>
          </div>
          <ChevronRightIcon className="h-3 w-3" />
        </div>
      </div>
    );
  }
  
  if (matters.length === 0) {
    return (
      <div className="mb-2">
        <div 
          className="px-1 py-1 text-xs font-semibold text-sidebar-foreground/70 flex items-center justify-between cursor-pointer"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          <div className="flex items-center gap-1">
            <FolderIcon className="h-3 w-3" />
            <span>MATTER FOLDERS</span>
          </div>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 mr-1"
              onClick={(e) => {
                e.stopPropagation();
                handleAddMatter();
              }}
            >
              <PlusIcon className="h-3 w-3" />
            </Button>
            {isCollapsed ? (
              <ChevronRightIcon className="h-3 w-3" />
            ) : (
              <ChevronDownIcon className="h-3 w-3" />
            )}
          </div>
        </div>
        {!isCollapsed && (
          <div className="px-2 py-2 text-xs text-sidebar-foreground/50 italic">
            No matters yet. Create one to organize your chats.
          </div>
        )}
      </div>
    );
  }
  
  return (
    <div className="mb-4">
      <div 
        className="px-1 py-1 text-xs font-semibold text-sidebar-foreground/70 flex items-center justify-between cursor-pointer"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className="flex items-center gap-1">
          <FolderIcon className="h-3 w-3" />
          <span>MATTER FOLDERS</span>
        </div>
        <div className="flex items-center">
          {!isCollapsed && (
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 mr-1"
              onClick={(e) => {
                e.stopPropagation();
                handleAddMatter();
              }}
            >
              <PlusIcon className="h-3 w-3" />
            </Button>
          )}
          {isCollapsed ? (
            <ChevronRightIcon className="h-3 w-3" />
          ) : (
            <ChevronDownIcon className="h-3 w-3" />
          )}
        </div>
      </div>
      
      {!isCollapsed && (
        <div className="space-y-1 mt-1">
          {matters.map((matter, index) => (
            <MatterItem
              key={matter.id}
              matter={matter}
              index={index}
              allChats={allChats}
              onRename={(name) => onRenameMatter(matter.id, name)}
              onDelete={() => onDeleteMatter(matter.id)}
              onAddChat={(chatId) => onAddChatToMatter(matter.id, chatId)}
              onRemoveChat={(chatId) => onRemoveChatFromMatter(matter.id, chatId)}
              onReorderChats={(chatIds) => onReorderChatsInMatter(matter.id, chatIds)}
              onPinChat={onPinChat}
              setOpenMobile={setOpenMobile}
              // Auto-edit if this is a newly created folder (has a temp ID)
              autoEdit={matter.id.startsWith('temp-')}
            />
          ))}
        </div>
      )}
    </div>
  );
}








