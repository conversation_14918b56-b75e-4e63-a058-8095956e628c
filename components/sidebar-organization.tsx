"use client";

import { useState, useEffect } from "react";
import { SidebarPinnedChats } from "@/components/sidebar-pinned-chats";
import { SidebarMatters } from "@/components/sidebar-matters";
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ChatWithTags as Chat } from "@/lib/db/schema";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { PinIcon, FolderIcon } from "lucide-react";

interface Matter {
  id: string;
  name: string;
  chats: Chat[];
}

interface SidebarOrganizationProps {
  allChats: Chat[];
  pinnedChats: Chat[];
  matters: Matter[];
  onPinChat: (chatId: string) => void;
  onUnpinChat: (chatId: string) => void;
  onReorderPins: (chatIds: string[]) => void;
  onAddMatter: (name: string) => void;
  onRenameMatter: (id: string, name: string) => void;
  onDeleteMatter: (id: string) => void;
  onAddChatToMatter: (matterId: string, chatId: string) => void;
  onRemoveChatFromMatter: (matterId: string, chatId: string) => void;
  onReorderChatsInMatter: (matterId: string, chatIds: string[]) => void;
  setOpenMobile: (open: boolean) => void;
  viewMode: "all" | "pinned" | "matters";
  setViewMode: (mode: "all" | "pinned" | "matters") => void;
  onDeleteChat: (chatId: string) => void;
  onBulkDelete: () => void;
}

export function SidebarOrganization({
  allChats,
  pinnedChats,
  matters,
  onPinChat,
  onUnpinChat,
  onReorderPins,
  onAddMatter,
  onRenameMatter,
  onDeleteMatter,
  onAddChatToMatter,
  onRemoveChatFromMatter,
  onReorderChatsInMatter,
  setOpenMobile,
  viewMode,
  setViewMode,
  onDeleteChat,
  onBulkDelete
}: SidebarOrganizationProps) {
  return (
    <DndProvider backend={HTML5Backend}>
      <div className="mb-2">
        <div className="flex items-center justify-between px-0.5 py-2">
          <div className="flex w-full space-x-1.5">
            <Button 
              variant="outline" 
              size="sm" 
              className={`flex-1 h-8 px-1.5 ${
                viewMode === "all" 
                  ? "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground" 
                  : "bg-white dark:bg-zinc-800 hover:bg-primary hover:text-primary-foreground"
              }`}
              onClick={() => setViewMode("all")}
            >
              <span className="flex items-center gap-1">
                All
              </span>
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className={`flex-1 h-8 px-1.5 ${
                viewMode === "pinned" 
                  ? "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground" 
                  : "bg-white dark:bg-zinc-800 hover:bg-primary hover:text-primary-foreground"
              }`}
              onClick={() => setViewMode("pinned")}
            >
              <span className="flex items-center gap-1">
                <PinIcon className={`h-3.5 w-3.5 dark:text-white ${viewMode === "pinned" ? "text-primary-foreground" : "text-gray-500"}`} />
                Pinned
              </span>
            </Button>
            {/* Comment out the Folders button */}
            {/*
            <Button 
              variant="outline" 
              size="sm" 
              className={`flex-1 h-8 px-1.5 ${
                viewMode === "matters" 
                  ? "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground" 
                  : "bg-white dark:bg-zinc-800 hover:bg-primary hover:text-primary-foreground"
              }`}
              onClick={() => setViewMode("matters")}
            >
              <span className="flex items-center gap-1">
                <FolderIcon className={`h-3.5 w-3.5 ${viewMode === "matters" ? "text-primary-foreground" : "text-gray-500"}`} />
                Folders
              </span>
            </Button>
            */}
          </div>
        </div>
      </div>

      {(viewMode === "all" || viewMode === "pinned") && (
        <SidebarPinnedChats
          chats={pinnedChats}
          onUnpin={onUnpinChat}
          onReorderPins={onReorderPins}
          setOpenMobile={setOpenMobile}
          onDelete={onDeleteChat}
          onBulkDelete={onBulkDelete}
          onPinChat={onPinChat}
          viewMode={viewMode}
          setViewMode={setViewMode}
        />
      )}
      
      {/* Comment out the SidebarMatters component */}
      {/*
      {(viewMode === "all" || viewMode === "matters") && (
        <SidebarMatters
          matters={matters}
          allChats={allChats}
          onAddMatter={onAddMatter}
          onRenameMatter={onRenameMatter}
          onDeleteMatter={onDeleteMatter}
          onAddChatToMatter={onAddChatToMatter}
          onRemoveChatFromMatter={onRemoveChatFromMatter}
          onReorderChatsInMatter={onReorderChatsInMatter}
          onPinChat={onPinChat}
          setOpenMobile={setOpenMobile}
        />
      )}
      */}
    </DndProvider>
  );
}

