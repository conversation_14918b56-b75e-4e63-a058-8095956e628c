import Link from "next/link";
import React, { memo } from "react";
import ReactMarkdown, { type Components } from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkSmartypants from "remark-smartypants";
import remarkBreaks from "remark-breaks";
import { CodeBlock } from "./code-block";
import rehypeRaw from "rehype-raw";
import rehypeSanitize, { defaultSchema } from "rehype-sanitize";

const customSchema = {
	...defaultSchema,
	tagNames: [...(defaultSchema.tagNames || []), "u"],
	attributes: {
		...(defaultSchema.attributes || {}),
		u: [],
	},
};
// Secondary renderer that processes markdown inside code blocks
const NestedMarkdown = ({
	children,
	role = "assistant",
}: {
	children: string;
	role?: "user" | "assistant";
}) => (
	<ReactMarkdown
		remarkPlugins={[remarkGfm]}
		rehypePlugins={[rehypeRaw, [rehypeSanitize, customSchema]]}
		components={{
			code: ({ children }) => <span className="ml-2 mt-2">{children}</span>,
			pre: ({ children }) => <div className="ml-2 mt-2">{children}</div>,
			p: ({ node, children, ...props }) => {
				return (
					<p className="mt-2 ml-4" {...props}>
						{children}
					</p>
				);
			},
			ol: ({ node, children, ...props }) => {
				return (
					<ol className="list-decimal list-outside ml-4 mt-2" {...props}>
						{children}
					</ol>
				);
			},
			li: ({ node, children, ...props }) => {
				return (
					<li className="py-1" {...props}>
						{children}
					</li>
				);
			},
			ul: ({ node, children, ...props }) => {
				return (
					<ul className="list-disc list-outsidemd:ml-4 ml-2 mt-2" {...props}>
						{children}
					</ul>
				);
			},
			strong: ({ node, children, ...props }) => {
				return (
					// <span className="font-bold" {...props}>
					//   {children}
					// </span>
					<strong className="font-bold" {...props}>
						{children}
					</strong>
				);
			},
			a: ({ node, children, ...props }) => {
				return (
					// @ts-expect-error
					<Link
						className="text-blue-500 hover:underline"
						target="_blank"
						rel="noreferrer"
						{...props}
					>
						{children}
					</Link>
				);
			},
			h1: ({ node, children, ...props }) => {
				return (
					<h1 className="text-base font-bold mt-6 mb-2" {...props}>
						{children}
					</h1>
				);
			},
			h2: ({ node, children, ...props }) => {
				return (
					<h2 className="text-base font-bold mt-2 mb-2" {...props}>
						{children}
					</h2>
				);
			},
			h3: ({ node, children, ...props }) => {
				return (
					<h3 className="text-base font-bold mt-6 mb-2" {...props}>
						{children}
					</h3>
				);
			},
			h4: ({ node, children, ...props }) => {
				return (
					<h4 className="text-base font-bold mt-6 mb-2" {...props}>
						{children}
					</h4>
				);
			},
			h5: ({ node, children, ...props }) => {
				return (
					<h5 className="text-base font-bold mt-6 mb-2" {...props}>
						{children}
					</h5>
				);
			},
			h6: ({ node, children, ...props }) => {
				return (
					<h6 className="text-base font-bold mt-6 mb-2" {...props}>
						{children}
					</h6>
				);
			},
			hr: ({ node, ...props }) => {
				return <div className="my-4" {...props} />;
			},
			u: ({ node, children, ...props }) => {
				return (
					<u className="font-bold" {...props}>
						{children}
					</u>
				);
			},
		}}
	>
		{children}
	</ReactMarkdown>
);

const components: Partial<Components> = {
	code: ({ node, className, children, ...props }) => {
		return <NestedMarkdown>{String(children)}</NestedMarkdown>;
	},
	// // @ts-expect-error
	// code: CodeBlock,
	pre: ({ children }) => <>{children}</>,
	p: ({ node, children, ...props }) => {
		return (
			<p className="mb-6 [li_&]:my-3 [li_&>strong]:inline-block" {...props}>
				{children}
			</p>
		);
	},
	ol: ({ node, children, ...props }) => {
		return (
			<ol className="list-decimal list-outside ml-4 mt-2" {...props}>
				{children}
			</ol>
		);
	},
	li: ({ node, children, ...props }) => {
		return (
			<li className="py-1 [&>p]:my-3" {...props}>
				{children}
			</li>
		);
	},
	ul: ({ node, children, ...props }) => {
		return (
			<ul className="list-disc list-outside ml-4 mt-2" {...props}>
				{children}
			</ul>
		);
	},
	table: ({ node, children, ...props }) => {
		return (
			<div className="reportTable">
				<table {...props}>
					{children}
				</table>
			</div>
		);
	},
	strong: ({ node, children, ...props }) => {
		return (
			// <span className="font-bold" {...props}>
			//   {children}
			// </span>
			<strong className="font-bold" {...props}>
				{children}
			</strong>
		);
	},
	a: ({ node, children, ...props }) => {
		return (
			// @ts-expect-error
			<Link
				className="text-blue-500 hover:underline"
				target="_blank"
				rel="noreferrer"
				{...props}
			>
				{children}
			</Link>
		);
	},
	h1: ({ node, children, ...props }) => {
		return (
			<h1 className="text-base font-bold mt-6 mb-2" {...props}>
				{children}
			</h1>
		);
	},
	h2: ({ node, children, ...props }) => {
		return (
			<h2 className="text-base font-bold mt-6 mb-2" {...props}>
				{children}
			</h2>
		);
	},
	h3: ({ node, children, ...props }) => {
		return (
			<h3 className="text-base font-bold mt-6 mb-2" {...props}>
				{children}
			</h3>
		);
	},
	h4: ({ node, children, ...props }) => {
		return (
			<h4 className="text-base font-bold mt-6 mb-2" {...props}>
				{children}
			</h4>
		);
	},
	h5: ({ node, children, ...props }) => {
		return (
			<h5 className="text-base font-bold mt-6 mb-2" {...props}>
				{children}
			</h5>
		);
	},
	h6: ({ node, children, ...props }) => {
		return (
			<h6 className="text-base font-bold mt-6 mb-2" {...props}>
				{children}
			</h6>
		);
	},
	hr: ({ node, ...props }) => {
		return <div className="my-4" {...props} />;
	},
	u: ({ node, children, ...props }) => {
		return (
			<u className="font-bold" {...props}>
				{children}
			</u>
		);
	},
};

const remarkPlugins = [remarkGfm, remarkSmartypants, remarkBreaks];

const NonMemoizedMarkdown = ({
	children,
	role = "assistant",
}: {
	children: string;
	role?: string;
}) => {
	return (
		<div className={`markdown-content ${role === "user" ? "[&>p]:mt-0" : ""}`}>
			<ReactMarkdown
				remarkPlugins={remarkPlugins}
				rehypePlugins={[rehypeRaw, [rehypeSanitize, customSchema]]}
				components={components}
			>
				{children}
			</ReactMarkdown>
		</div>
	);
};

export const Markdown = memo(
	NonMemoizedMarkdown,
	(prevProps, nextProps) =>
		prevProps.children === nextProps.children &&
		prevProps.role === nextProps.role,
);
