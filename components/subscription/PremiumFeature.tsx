'use client'
import React, { useState } from 'react';
import { Check } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface PremiumFeatureProps {
  name: string;
  premium?: boolean;
  tooltip?: string;
}

export const PremiumFeature = ({ 
  name, 
  premium = false,
  tooltip
}: PremiumFeatureProps) => {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <div 
      className={`flex items-start gap-2 py-1 group ${premium ? 'hover:-translate-y-0.5 transition-transform' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="rounded-full p-1 mt-0.5 bg-green-100">
        <Check className="h-3 w-3 text-green-600" />
      </div>
      
      {tooltip ? (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className={`text-sm ${premium ? 'font-medium' : ''} flex items-center`}>
                {name}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs max-w-[200px]">{tooltip}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        <span className={`text-sm ${premium ? 'font-medium' : ''} flex items-center`}>
          {name}
        </span>
      )}
    </div>
  );
};
