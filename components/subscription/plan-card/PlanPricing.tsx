import React from "react";
import { useSession } from "next-auth/react";

interface PlanPricingProps {
  price: string;
  billingCycle: "monthly" | "annual";
  monthlyEquivalent?: string;
  isEnterprise: boolean;
  id: "free" | "premium" | "premium-yearly" | "enterprise";
}

const PlanPricing = ({
  price,
  billingCycle,
  monthlyEquivalent,
  isEnterprise,
  id,
}: PlanPricingProps) => {
  // Always call hooks at the top level, regardless of conditions
  const { data: session } = useSession();
  const isCurrentPlan = React.useMemo(() => {
    if (!session?.user) return false;
    return session.user.subscriptionTier === id;
  }, [session, id]);

  // Format price to remove cents if it's a whole number
  const formattedPrice = React.useMemo(() => {
    // If price contains a decimal point followed by zeros, remove the decimal part
    if (price.includes('.00')) {
      return price.split('.')[0];
    }
    return price;
  }, [price]);

  // Don't display any pricing for Enterprise plans
  if (isEnterprise) {
    return null;
  }

  return (
    <div>
      <span className="text-4xl font-playfair font-bold">${formattedPrice}</span>
      <span className="text-muted-foreground ml-1">
        {id === "premium" && billingCycle === "monthly"
          ? "/month/license"
          : `/${billingCycle === "monthly" ? "month" : "year"}`}
      </span>
      {billingCycle === "annual" && monthlyEquivalent && (
        <div className="text-sm text-muted-foreground mt-1">
          {monthlyEquivalent} billed annually
        </div>
      )}
    </div>
  );
};

export default PlanPricing;
