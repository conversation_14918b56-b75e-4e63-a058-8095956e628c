
import { ReactNode } from "react";
import { Button } from "@/components/ui/button";

export interface FaqItem {
  question: string;
  answer: ReactNode;
}

export const getFaqData = (): FaqItem[] => {
  return [
    {
      question: "Can I switch between plans?",
      answer: "Yes, you can upgrade or downgrade your plan at any time. Changes to your subscription will be applied immediately."
    },
    {
      question: "Is there a free trial?",
      answer: "Yes, all paid plans include a 14-day free trial. You won't be charged until the trial period ends."
    },
    {
      question: "How does billing work?",
      answer: "We use Stripe for secure payment processing. You'll be billed either monthly or annually, depending on your chosen billing cycle."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards, including Visa, Mastercard, American Express, and Discover."
    },
    {
      question: "Are future upgrades included in my subscription?",
      answer: (
        <div className="space-y-2">
          <p>Yes, all future upgrades and new features are included in your subscription at the current price. We believe in providing continuous value to our subscribers without raising prices.</p>
        </div>
      )
    },
    {
      question: "Is Iqidis Essentials, the legal operating system, a separate product?",
      answer: (
        <div className="space-y-2">
          <p>No, we are building up and into Iqidis Essentials. The price you lock in now is the price you will maintain through the life of our product.</p>
        </div>
      )
    },
    {
      question: "Will the price never change?",
      answer: (
        <div className="space-y-2">
          <p>No. We have been on the receiving end of shifting goal posts and increasing costs. We are going for impact, not changing things or price gouging users.</p>
        </div>
      )
    },
    {
      question: "How secure is Iqidis?",
      answer: (
        <div className="space-y-2">
          <p>Security is fundamental to our platform. We implement end-to-end encryption to protect your sensitive legal documents and communications. All data transmitted through our platform is encrypted in transit and at rest.</p>
          <p>We maintain a strict no data retention policy, meaning your information is not stored longer than necessary, and we never use your data for AI training purposes.</p>
          <p>Our architecture ensures isolated user accounts, preventing any possibility of cross-contamination between different organizations data. Each user environment operates in its own secure container.</p>
        </div>
      )
    },
    {
      question: "Can I get a custom enterprise plan?",
      answer: (
        <div className="space-y-2">
          <p>Yes, we offer custom enterprise solutions tailored to your organizations needs.</p>
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={() => window.location.href = "mailto:<EMAIL>?subject=Custom Enterprise Plan Inquiry"}
          >
            Contact Sales
          </Button>
        </div>
      )
    }
  ];
};
