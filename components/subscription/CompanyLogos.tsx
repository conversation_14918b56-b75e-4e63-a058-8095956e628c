
'use client'
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import BillingCycleSelector from "./BillingCycleSelector";

interface CompanyLogosProps {
  billingCycle: "monthly" | "annual";
  onBillingCycleChange: (value: string) => void;
}

const CompanyLogos = ({
  billingCycle,
  onBillingCycleChange
}: CompanyLogosProps) => {
  const [select, setSelect] = useState<string>('')

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      setSelect(id)
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="pt-2 pb-8">
      <div className="flex justify-center gap-4 mb-6">
        <Button
          variant={select === 'enterprise-section' ? "default" : "outline"}
          onClick={() => scrollToSection('enterprise-section')}
          className="dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          Enterprise Sales
        </Button>
        <Button
          variant={select === 'faq-section' ? "default" : "outline"}
          onClick={() => scrollToSection('faq-section')}
          className="dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"

        >
          FAQ
        </Button>
      </div>

      <p className="text-center text-sm font-medium text-muted-foreground mb-6">
        Trusted by BigLaw, boutiques, solos, and in-house teams
      </p>

      <div className="flex justify-center">
        <BillingCycleSelector
          value={billingCycle}
          onChange={onBillingCycleChange}
        />
      </div>
    </div>
  );
};

export default CompanyLogos;
