import { useMediaQuery } from '@/hooks/use-media-query';
import React from 'react';

interface Testimonial {
  quote: string;
  author: string;
  role: string;
}

const TestimonialsCarousel = () => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  
  // Combine testimonials from both components
  const testimonials: Testimonial[] = [
    // Keep original testimonials
    {
      quote: "Iqidis Core has transformed how our legal team drafts documents. We're saving at least 15 hours per week.",
      author: "<PERSON>",
      role: "Legal Director",
    },
    {
      quote: "The unlimited queries feature alone is worth the subscription. Our productivity increased by 35% within the first month.",
      author: "<PERSON>",
      role: "Trial Attorney",
    },
    {
      quote: "Team collaboration made our workflow 3x more efficient. The ROI was immediate and substantial.",
      author: "<PERSON>",
      role: "Law Firm Partner",
    },
    // Additional testimonials from other components
    {
      quote: "<PERSON><PERSON><PERSON> drafts better than half the juniors I've trained.",
      author: "<PERSON>", 
      role: "Litigation Partner"
    },
    {
      quote: "It's like having a second brain for legal strategy.",
      author: "<PERSON>", 
      role: "Associate, Regulatory Law"
    },
    {
      quote: "<PERSON><PERSON><PERSON> helped me cut my research time by more than half.",
      author: "<PERSON>", 
      role: "Trial Lawyer"
    },
    {
      quote: "Clean, fast, and actually understands legal tone.",
      author: "Ayesha B.", 
      role: "Employment Counsel"
    },
    {
      quote: "Our complaints practice runs on Iqidis now.",
      author: "Tom H.", 
      role: "Class Action Partner"
    },
    {
      quote: "The AI actually reasons through structure—not just language.",
      author: "Cameron F.", 
      role: "Appellate Attorney"
    },
    {
      quote: "It's replaced CoCounsel and Spellbook in our workflow.",
      author: "Lisa G.", 
      role: "Mid-size Firm Partner"
    },
    {
      quote: "Nothing else compares for clause-level analysis.",
      author: "James W.", 
      role: "M&A Attorney"
    },
    {
      quote: "I used to dread drafting. Now Iqidis gets me 80% there.",
      author: "Rachel D.", 
      role: "Real Estate Counsel"
    },
    {
      quote: "It's the first legal AI tool that feels like it was built by lawyers.",
      author: "Eric C.", 
      role: "White Collar Litigator"
    }
  ];

  const TestimonialCard = ({ testimonial }: { testimonial: Testimonial }) => (
    <div className="p-5 rounded-lg bg-white/70 dark:bg-white/5 border border-gray-100 dark:border-gray-800 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1 relative overflow-hidden text-left h-full">
      <div className="absolute -top-3 -right-3 opacity-5 text-6xl font-playfair"></div>
      <p className="text-sm mb-4 whitespace-normal min-h-[80px]">&ldquo;{testimonial.quote}&rdquo;</p>
      <div className="text-left mt-auto">
        <p className="text-xs font-semibold">{testimonial.author}</p>
        <p className="text-xs text-muted-foreground">{testimonial.role}</p>
      </div>
    </div>
  );

  // Create a continuous scrolling row of testimonials
  const ContinuousScrollRow = () => {
    // Determine how many testimonials to show per group based on screen size
    const cardsPerRow = isMobile ? 1 : 3;
    
    return (
      <div className="w-full overflow-hidden">
        <div className={`animate-scroll-left whitespace-nowrap inline-block`}>
          {/* First set of testimonials */}
          <div className="inline-block whitespace-nowrap">
            {testimonials.map((testimonial, index) => (
              <div 
                key={`original-${index}`} 
                className={`inline-block ${isMobile ? 'w-[320px]' : 'w-[300px]'} px-3 align-top`}
              >
                <TestimonialCard testimonial={testimonial} />
              </div>
            ))}
          </div>
          
          {/* Duplicate set for continuous scrolling */}
          <div className="inline-block whitespace-nowrap">
            {testimonials.map((testimonial, index) => (
              <div 
                key={`duplicate-${index}`} 
                className={`inline-block ${isMobile ? 'w-[320px]' : 'w-[300px]'} px-3 align-top`}
              >
                <TestimonialCard testimonial={testimonial} />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-5xl mx-auto py-8">
      <ContinuousScrollRow />
    </div>
  );
};

export default TestimonialsCarousel;
