"use client";
import React, { useState, useEffect, useContext } from "react";
import { useRouter } from "next/navigation";
import { useSession, SessionProvider } from "next-auth/react";
import { Logger } from "@/lib/utils/Logger";
import {
  Plan as UIplan,
  UserSubscription,
  getSubscriptionPlans,
  getFaqData,
} from "./data";
import { Plan as DBPlan, Plan, Subscription } from "@/lib/db/schema";
import CompanyLogos from "./CompanyLogos";
import TestimonialsCarousel from "./TestimonialsCarousel";
import SubscriptionHeader from "./SubscriptionHeader";
import SubscriptionPlans from "./SubscriptionPlans";
import FaqSection from "./FaqSection";
import FloatingCTA from "./FloatingCTA";
import { toast } from "sonner";
import { Button } from "../ui/button";
import { ArrowRight } from "lucide-react";
import { ChatHeader } from "../chat-header";
import { PageLoader } from "@/components/ui/page-loader";

const SubscriptionPage = ({ page }: any) => {
  return (
    <SessionProvider>
      <SubscriptionPageContent page={page} />
    </SessionProvider>
  );
};

const SubscriptionPageContent = ({ page }: any) => {
  // Add a pageLoading state
  const [pageLoading, setPageLoading] = useState(true);
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">(
    "monthly"
  );
  const [processingTier, setProcessingTier] = useState<string | null>(null);
  const [tabAnimating, setTabAnimating] = useState(false);
  const [showScrollCTA, setShowScrollCTA] = useState(false);
  const [error, setError] = useState("");
  const [activePlans, setActivePlans] = useState<DBPlan[]>([]);
  const { data: session } = useSession();
  const [activeSubscription, setActiveSubscription] = useState<{
    subscription: Subscription;
    plan: Plan;
  } | null>();

  // User subscription state
  const [userSubscription, setUserSubscription] = useState<UserSubscription>({
    tier: "free",
    daysLeft: 14,
  });

  const router = useRouter();
  const plans = getSubscriptionPlans(billingCycle);
  const faqs = getFaqData();

  // Check if user is admin or managed by admin
  const isAdmin = session?.user?.isAdmin === true;

  const getActiveSubscription = async () => {
    try {
      const res = await fetch("/api/query/get-current-active-subscription", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to get active subscription");
      }

      setActiveSubscription(await res.json());
    } catch (error) {
      Logger.error("Failed to get the subscription from database", error);
    }
  };

  // Fetch active plans from the server
  const getActivePlans = async () => {
    try {
      const res = await fetch("/api/query/get-active-plans", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to get active plans");
      }

      const data = await res.json();

      if (!Array.isArray(data.plans)) {
        throw new Error("Expected data.plans to be an array");
      }

      setActivePlans(data.plans);
    } catch (error) {
      Logger.error("Failed to get the plans from database", error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load all required data
        await Promise.all([getActivePlans(), getActiveSubscription()]);
      } catch (error) {
        Logger.error("Failed to load subscription data", error);
      } finally {
        // Remove loading state from localStorage
        localStorage.removeItem("subscriptionPageLoading");
        setPageLoading(false);
      }
    };

    loadData();

    // Cleanup function
    return () => {
      localStorage.removeItem("subscriptionPageLoading");
    };
  }, []);

  // Return loading state if data is still loading
  if (pageLoading) {
    return <PageLoader message="Loading subscription details..." />;
  }

  const handleSubscribe = async (
    tier: "free" | "premium" | "premium-yearly" | "enterprise"
  ) => {
    if (!session?.user?.id) {
      setError("Please sign in to subscribe");
      return;
    }

    if (tier === "free") {
      router.push("/");
      setUserSubscription({ ...userSubscription, tier: "free" });
      return;
    }

    if (tier === "premium" || tier === "premium-yearly") {
      await handleCheckout(tier);
    }

    if (tier === "enterprise") {
      window.location.href =
        "mailto:<EMAIL>?subject=Enterprise Plan Inquiry";
      return;
    }

    setProcessingTier(tier);
    setError("");

  };

  // Add trial functionality
  const handleCheckout = async (tier: "premium" | "premium-yearly") => {
    if (!session?.user?.id) {
      setError("Please sign in to start a trial");
      return;
    }

    setProcessingTier("premium");
    setError("");

    try {
      // Get active plans
      if (activePlans.length === 0) {
        await getActivePlans();
      }

      // Find the premium plan - could be named "premium" or "premium-yearly" in the database
      const premiumPlan = activePlans.find((p) => p.name === tier);

      if (!premiumPlan) {
        throw new Error("Premium plan not found");
      }

      // Get user subscription info
      const subRes = await fetch(`/api/query/get-current-active-subscription`);
      if (!subRes.ok) {
        throw new Error("Failed to fetch subscription information");
      }

      const subData = await subRes.json();
      const isTrialActive = subData.subscription?.isTrialUsed==false;
      const endpoint = isTrialActive ? "/api/stripe/start-trial" : "/api/stripe/checkout";
      const res = await fetch(endpoint, {
        method: "POST",
        body: JSON.stringify({
          userId: session.user.id,
          planId: premiumPlan.id,
          priceId: premiumPlan.stripePriceId,
          isTrial: isTrialActive
        }),
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to start trial");
      }

      const { url } = await res.json();
      window.location.href = url;
    } catch (error) {
      Logger.error("Trial start error", error);
      setError(
        error instanceof Error ? error.message : "Failed to start trial"
      );
      setProcessingTier(null);
    }
  };

  const handleTabChange = (value: string) => {
    setTabAnimating(true);
    setBillingCycle(value as "monthly" | "annual");
    setTimeout(() => setTabAnimating(false), 300);
  };

  return (
    <div className="bg-[#f9f8fb] dark:bg-slate-900 w-full">
      <div className="container max-w-screen-xl mx-auto p-6 pt-14 relative">
        <SubscriptionHeader page={page} />

        {/* Company logos for social proof with billing cycle selector */}
        <CompanyLogos
          billingCycle={billingCycle}
          onBillingCycleChange={handleTabChange}
        />

        {/* Plans */}
        <SubscriptionPlans
          plans={plans[billingCycle]}
          userSubscription={userSubscription}
          billingCycle={billingCycle}
          processingTier={processingTier}
          tabAnimating={tabAnimating}
          onSubscribe={(
            tier: "free" | "premium" | "premium-yearly" | "enterprise"
          ) => handleSubscribe(tier)}
          // handleTrial={handleTrial}
          userSubscriptionTier={session?.user?.subscriptionTier ?? "free"}
          activePlans={activePlans}
          activeSubscription={activeSubscription?.subscription}
          hideButtons={
            isAdmin || activeSubscription?.subscription?.isAdminManaged === true
          }
        />

        {/* Testimonials Carousel Section */}
        <div className="max-w-5xl mx-auto mb-16 text-center">
          <h2 className="text-2xl font-playfair font-semibold mb-6 text-[#270f57] dark:text-[rgb(var(--base-navy))]">
            What Our Lawyers Say
          </h2>
          <TestimonialsCarousel />
        </div>

        {/* FAQ Section */}
        <FaqSection faqs={faqs} />

        {page === "welcome" && (
          <div className="flex justify-center mt-2">
            {/* Skip option */}
            <Button
              onClick={() => router.push("/")}
              variant="default"
              className="flex h-12 items-center gap-1"
            >
              {session?.user?.subscriptionTier === "free"
                ? "Continue with preview"
                : "Continue to Account"}
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        )}

        {/* Floating CTA */}
        {page !== "welcome" && <FloatingCTA show={showScrollCTA} />}
      </div>
    </div>
  );
};

export default SubscriptionPage;
