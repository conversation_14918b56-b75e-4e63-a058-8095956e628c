import React from "react";
import { useSidebar } from "./ui/sidebar";

const WelcomeText = () => {
	const { open } = useSidebar();
	return (
		<div
			className={`flex transition-all flex-col items-center justify-center sm:pb-5 pb-0 pt-6 lg:pt-0 ${open ? "lg:pl-0 md:pl-80 pl-0" : ""}`}
		>
			<h1 className="sm:text-4xl text-3xl font-bold font-playfair text-[rgb(var(--title-color))] mb-2  dark:text-[rgb(var(--base-navy))]">
				Welcome to <br className="block sm:hidden" /> Iqidis
			</h1>
			<p className="text-lg text-gray-600 dark:text-gray-400">
				Your AI-powered legal assistant
			</p>
		</div>
	);
};

export default WelcomeText;
