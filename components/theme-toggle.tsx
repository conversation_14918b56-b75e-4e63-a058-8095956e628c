import { <PERSON>, Sun } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { useTheme } from "next-themes";
import { PreferencesEvent } from "@/lib/analytics/event-types";
import { logEvent } from "@/lib/analytics/events-client";
import { useEffect, useState } from "react";

interface ThemeToggleProps {
  className?: string;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className }) => {
  const { setTheme, theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Ensure the theme is loaded on first render
  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);
    logEvent(PreferencesEvent.TOGGLE_THEME, { theme: newTheme });
  };

  if (!mounted) {
    return null; // Prevents SSR from rendering before the theme is loaded
  }

  return (
    <div className={`flex items-center gap-2 border-x border-border/30 sm:mx-3 mx-2 !px-1 darkLightToggleBox ${className ?? ""}`}>
      <Sun
        className={`md:h-6 md:w-6 h-[18px] w-[18px] md:block ${
          theme === "light" ? "sm:block" : "sm:hidden"
        }`}
        color="#f59e0b"
      />
      <Switch
        checked={theme === "dark"}
        onCheckedChange={toggleTheme}
        aria-label="Toggle dark mode"
        className="data-[state=checked]:bg-iqidis-vividPurple"
      />
      <Moon
        className={`md:h-6 md:w-6 h-[18px] w-[18px] md:block ${
          theme === "dark" ? "sm:block" : "sm:hidden"
        }`}
        color="#8b5cf6"
      />
    </div>
  );
};
