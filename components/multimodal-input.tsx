"use client";

import type {
	Attachment,
	ChatRequestOptions,
	CreateMessage,
	Message,
} from "ai";
import {
	cn as cx,
	validatePromptFileType,
	PROMPT_FILE_ACCEPT_STRING,
} from "@/lib/utils";
import type React from "react";
import {
	useRef,
	useEffect,
	useState,
	useCallback,
	type Dispatch,
	type SetStateAction,
	type ChangeEvent,
	memo,
} from "react";
import { toast } from "sonner";
import { useLocalStorage, useWindowSize } from "usehooks-ts";
import { track } from "@vercel/analytics";
import AudioInputButton from "./AudioInputButton";
import AmplifyButton from "./AmplifyButton";

import { sanitizeUIMessages } from "@/lib/utils";
import { uploadDebug } from "@/lib/utils/debug";
import { generateUUID } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { executeWithConcurrency } from "@/lib/utils/retry";

import {
	ArrowUpIcon,
	PaperclipIcon,
	StopIcon,
	CrossIcon,
	RedoIcon,
	FileIcon,
} from "./icons";
import { PreviewAttachment } from "./preview-attachment";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";
import equal from "fast-deep-equal";
import { Logger } from "@/lib/utils/Logger";
import {
	ServerErrorEvent,
	ServerSuccessEvent,
	ChatEvent,
} from "@/lib/analytics/event-types";
import { logEvent } from "@/lib/analytics/events-client";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { Switch } from "./ui/switch";
import { WelcomeSection } from "./welcome-section";
import { serializeError } from "@/lib/utils/errorUtils";
import { isFeatureAvailable, checkUsageLimits } from "@/lib/usage/features";
import { useUser } from "@/contexts/UserContext";
import { motion, AnimatePresence } from "framer-motion";
import { useSidebar } from "./ui/sidebar";
import { usePlaybook } from "./global-playbook-provider";
import {
	AlertTriangle,
	Brain,
	FileBox,
	Info,
	ArrowRight,
	Download,
	FileText,
} from "lucide-react";
import { ModelSelector } from "./model-selector";
import { DEFAULT_MODEL_NAME } from "@/lib/ai/models";
import { FileDrawer } from "./file-drawer";
import { genSignature } from "@/lib/utils/auth";
import { retryOperation } from "@/lib/utils/retry";
import { useLibrarySelector } from "./library/LibrarySelector";
import { marked } from "marked";
import { convertHtmlToDocxOrPDf } from "@/lib/utils/document-export";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { format as formatDate } from "date-fns";

type UploadResponse = {
	url: string;
	pathname: string;
	contentType: string;
	sourceDocumentId?: string;
	chunkCount?: number;
};

interface ToggleButtonProps {
	isActive: boolean;
	onClick: () => void;
	label: string;
	icon: React.ReactNode;
	shortLabel: string;
	disabled?: boolean;
}

const ToggleButton = ({
	isActive,
	onClick,
	icon,
	label,
	shortLabel,
	disabled = false,
}: ToggleButtonProps) => {
	return (
		<Tooltip>
			<TooltipTrigger asChild>
				<button
					type="button"
					onClick={onClick}
					disabled={disabled}
					className={cx(
						"flex items-center gap-2 px-3 h-10 rounded-md transition-all duration-200",
						"border shadow-sm hover:shadow hover:bg-zinc-100 dark:hover:bg-zinc-800/70",
						isActive
							? "bg-[rgb(var(--base-navy))] text-white dark:text-black font-semibold border-[rgb(var(--base-navy))] shadow"
							: "text-zinc-700 dark:text-zinc-300 border-zinc-200 dark:border-zinc-700/50",
						disabled &&
							"opacity-50 cursor-not-allowed hover:bg-transparent dark:hover:bg-transparent",
					)}
					aria-pressed={isActive}
				>
					{icon}
					<span className="text-sm whitespace-nowrap">{shortLabel}</span>
				</button>
			</TooltipTrigger>
			<TooltipContent
				side="top"
				className="text-sm font-normal max-w-[260px] text-center py-1.5 px-3"
			>
				{shortLabel}
			</TooltipContent>
		</Tooltip>
	);
};

interface InfoButtonProps {
	label: string;
}

const InfoButton = ({ label }: InfoButtonProps) => {
	const [isHovered, setIsHovered] = useState(false);

	return (
		<Tooltip>
			<TooltipTrigger asChild>
				<motion.button
					onHoverStart={() => setIsHovered(true)}
					onHoverEnd={() => setIsHovered(false)}
					className="ml-2 p-1.5 rounded-full bg-[#5b1d77] bg-opacity-10 hover:bg-opacity-20 text-[#5b1d77] focus:outline-none"
					whileHover={{ scale: 1.1 }}
					whileTap={{ scale: 0.95 }}
				>
					<Info size={20} strokeWidth={2.5} />
				</motion.button>
			</TooltipTrigger>
			<TooltipContent side="top" sideOffset={5}>
				<motion.div
					initial={{ opacity: 0, y: 5 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: 5 }}
					className="text-sm font-normal max-w-[260px] text-center py-1.5 px-3"
				>
					{label}
				</motion.div>
			</TooltipContent>
		</Tooltip>
	);
};

interface ToggleButtonWithInfoProps extends ToggleButtonProps {
	label: string;
}

const ToggleButtonWithInfo = ({
	isActive,
	onClick,
	icon,
	label,
	shortLabel,
	disabled = false,
}: ToggleButtonWithInfoProps) => {
	return (
		<Tooltip>
			<TooltipTrigger asChild>
				<button
					type="button"
					onClick={onClick}
					className="enhanced-button"
					aria-pressed={isActive}
					disabled={disabled}
				>
					{icon}
					<span className="text-sm whitespace-nowrap">{shortLabel}</span>
				</button>
			</TooltipTrigger>
			<TooltipContent
				side="top"
				className="text-sm font-normal max-w-[260px] text-center py-1.5 px-3"
			>
				{label}
			</TooltipContent>
		</Tooltip>
	);
};

function PureMultimodalInput({
	chatId,
	input,
	setInput,
	isLoading,
	stop,
	attachments,
	setAttachments,
	messages,
	setMessages,
	append,
	handleSubmit,
	className,
	setSubmitFormRef,
	selectedModelId,
	chatTitle,
}: {
	chatId: string;
	input: string;
	setInput: (value: string) => void;
	isLoading: boolean;
	stop: () => void;
	attachments: Array<Attachment>;
	setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
	messages: Array<Message>;
	setMessages: Dispatch<SetStateAction<Array<Message>>>;
	append: (
		message: Message | CreateMessage,
		chatRequestOptions?: ChatRequestOptions,
	) => Promise<string | null | undefined>;
	handleSubmit: (
		event?: {
			preventDefault?: () => void;
		},
		chatRequestOptions?: ChatRequestOptions,
	) => void;
	className?: string;
	setSubmitFormRef?: (submitFn: (useLegacyModel?: boolean) => void) => void;
	selectedModelId: string;
	chatTitle?: string;
}) {
	const textareaRef = useRef<HTMLTextAreaElement>(null);
	const { width } = useWindowSize();

	// Initialize state from localStorage but don't sync across tabs
	const [includeInternet, setIncludeInternet] = useState(() => {
		if (typeof window !== "undefined") {
			const saved = localStorage.getItem("includeInternet");
			return saved === "true";
		}
		return false;
	});

	const [ragEnabled, setRagEnabled] = useState(() => {
		if (typeof window !== "undefined") {
			const saved = localStorage.getItem("ragEnabled");
			return saved === "true";
		}
		return false;
	});

	// Update localStorage when states change, but don't trigger storage events
	useEffect(() => {
		localStorage.setItem("includeInternet", String(includeInternet));
	}, [includeInternet]);

	useEffect(() => {
		localStorage.setItem("ragEnabled", String(ragEnabled));
	}, [ragEnabled]);

	const [currentModel, setCurrentModel] = useState(
		selectedModelId || DEFAULT_MODEL_NAME,
	);
	const router = useRouter();
	const [isFileDrawerOpen, setIsFileDrawerOpen] = useState(false);
	const user = useUser();

	// Check if input should be disabled due to usage limits
	const isNewChat = messages.length === 0;
	const { isLimited, limitMessage } = checkUsageLimits(user, isNewChat);
	useEffect(() => {
		localStorage.setItem("isLimited", String(isLimited));
	}, [isLimited]);
	const { open: isSidebarOpen } = useSidebar();
	const { isPlaybookOpen } = usePlaybook();
	const [dismissedAttachmentWarning, setDismissedAttachmentWarning] =
		useState(false);
	// Add this localStorage hook for attachments
	const [storedAttachments, setStoredAttachments] = useLocalStorage<
		Array<Attachment>
	>("storedAttachments", []);

	// Sync attachments with localStorage on component mount
	useEffect(() => {
		if (storedAttachments.length > 0) {
			setAttachments(storedAttachments);
		}
	}, []);

	// Update localStorage whenever attachments change
	useEffect(() => {
		setStoredAttachments(attachments);
	}, [attachments, setStoredAttachments]);

	// Reset research mode and enhanced doc reader after submission
	const resetFeatureToggles = useCallback(() => {
		// if (includeInternet) setIncludeInternet(false);
		if (ragEnabled) setRagEnabled(false);
	}, [includeInternet, ragEnabled, setIncludeInternet, setRagEnabled]);

	// Adding this function to trigger confirmation in the background
	const confirmUploads = async (
		attachments: Attachment[],
		userMessage?: string,
	) => {
		const pendingAttachments = attachments.filter((a) => a.pendingConfirm);

		if (pendingAttachments.length === 0) return;

		try {
			// Fire and forget - don't await the response
			fetch("/api/files/confirm", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					documentId: pendingAttachments[0].document_id,
					url: pendingAttachments[0].url,
					contentType: pendingAttachments[0].contentType,
					filename: pendingAttachments[0].name,
					chatId,
					userMessage,
				}),
			});

			Logger.info("Triggered background document confirmation", {
				count: pendingAttachments.length,
				chatId,
			});
		} catch (error) {
			Logger.error("Failed to trigger document confirmation", error);
		}
	};

	const uploadFileSecure = async (
		file: File,
	): Promise<Attachment | undefined> => {
		try {
			Logger.info("Client: Starting protected upload", {
				name: file.name,
				size: file.size,
			});

			// Step 1: Request presigned URL from your API
			const { timestamp, signature } = genSignature();
			Logger.debug("Generated signature", {
				timestamp,
				signaturePrefix: signature.substring(0, 10) + "...",
			});

			Logger.debug("Sending prepare request to API", {
				endpoint: "/api/files/prepare",
				fileType: file.type,
				fileSize: file.size,
				multipart: file.size > 10 * 1024 * 1024,
			});

			const prepareResponse = await fetch(`/api/files/prepare`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: signature,
					"x-requested-time": timestamp.toString(),
				},
				body: JSON.stringify({
					files: [
						{
							mime: file.type,
							metadata: {
								name: file.name,
								size: file.size,
							},
							multipart: file.size > 10 * 1024 * 1024, // Using multipart for files > 10MB
						},
					],
					channel: "core",
					partitionId: "core",
					chatId,
				}),
			});

			if (!prepareResponse.ok) {
				const errorText = await prepareResponse
					.text()
					.catch(() => "Failed to get error text");
				Logger.error("Prepare request failed", {
					status: prepareResponse.status,
					statusText: prepareResponse.statusText,
					errorText,
				});
				throw new Error(
					`Failed to get presigned URL: ${prepareResponse.status} ${prepareResponse.statusText}`,
				);
			}

			const responseData = await prepareResponse.json();
			Logger.debug("Received prepare response", { responseData });

			const { data } = responseData;
			if (!data || !data[0]) {
				Logger.error("Invalid prepare response format", { responseData });
				throw new Error("Invalid response format from prepare endpoint");
			}

			// Handle multipart upload response format
			if (data[0].multipart) {
				const { documentId, bucket, key, uploadId } = data[0];
				Logger.debug("Extracted multipart upload details", {
					documentId,
					bucket,
					key,
					uploadId: uploadId.substring(0, 10) + "...",
				});

				// Upload process for multipart uploads
				const CHUNK_SIZE = 9 * 1024 * 1024;
				const chunks = Math.ceil(file.size / CHUNK_SIZE);
				const parts = new Array(chunks);

				Logger.info("Starting multipart upload", {
					documentId,
					chunks,
					fileSize: file.size,
				});

				// Create an array of upload functions - one for each part
				const uploadFunctions = Array.from({ length: chunks }, (_, i) => {
					const partNumber = i + 1;

					// Return a function that will be executed with concurrency control
					return async () => {
						try {
							Logger.debug(`Preparing to upload part ${partNumber}/${chunks}`, {
								documentId,
							});

							// Get presigned URL for this part
							const partResponse = await fetch("/api/files/upload-part", {
								method: "POST",
								headers: { "Content-Type": "application/json" },
								body: JSON.stringify({ bucket, key, uploadId, partNumber }),
							});

							if (!partResponse.ok) {
								throw new Error(
									`Failed to get part URL: ${partResponse.status}`,
								);
							}

							const { url: partUrl } = await partResponse.json();

							// Calculate chunk boundaries
							const start = (partNumber - 1) * CHUNK_SIZE;
							const end = Math.min(start + CHUNK_SIZE, file.size);
							const chunk = file.slice(start, end);

							Logger.debug(`Uploading part ${partNumber}/${chunks}`, {
								documentId,
								size: chunk.size,
								start,
								end,
							});

							// Upload the chunk with retry logic
							const uploadResult = await retryOperation(
								async () => {
									const uploadResponse = await fetch(partUrl, {
										method: "PUT",
										body: chunk,
									});

									if (!uploadResponse.ok) {
										throw new Error(
											`Failed to upload part: ${uploadResponse.status}`,
										);
									}

									const etag = uploadResponse.headers.get("ETag");
									if (!etag) {
										throw new Error("No ETag received for uploaded part");
									}

									Logger.debug(
										`Successfully uploaded part ${partNumber}/${chunks}`,
										{ documentId },
									);
									return { ETag: etag, PartNumber: partNumber };
								},
								3, // Max retries
							);

							parts[partNumber - 1] = uploadResult;
							return uploadResult;
						} catch (error) {
							Logger.error(`Failed to upload part ${partNumber}`, {
								error,
								documentId,
							});
							throw error;
						}
					};
				});

				// Execute all upload functions with controlled concurrency
				// Adjust MAX_CONCURRENT_UPLOADS based on your needs and testing
				const MAX_CONCURRENT_UPLOADS = 5; // Increased from 3 for faster uploads

				Logger.info(
					`Starting parallel upload with concurrency ${MAX_CONCURRENT_UPLOADS}`,
					{
						documentId,
						totalParts: chunks,
					},
				);

				try {
					await executeWithConcurrency(uploadFunctions, MAX_CONCURRENT_UPLOADS);

					Logger.info(
						"All parts uploaded successfully, completing multipart upload",
						{
							documentId,
							parts: parts.length,
						},
					);

					// Complete the multipart upload
					const completeResponse = await fetch(
						"/api/files/complete-multipart",
						{
							method: "POST",
							headers: { "Content-Type": "application/json" },
							body: JSON.stringify({
								bucket,
								key,
								uploadId,
								parts,
							}),
						},
					);

					if (!completeResponse.ok) {
						throw new Error(
							`Failed to complete upload: ${completeResponse.status}`,
						);
					}

					const { url: finalUrl } = await completeResponse.json();

					// Return the attachment information
					Logger.info("Multipart upload completed successfully", {
						url: finalUrl,
						name: file.name,
						documentId,
					});

					return {
						url: finalUrl,
						name: file.name,
						contentType: file.type,
						document_id: documentId,
						pendingConfirm: true,
					};
				} catch (error) {
					Logger.error("Multipart upload failed", {
						error,
						documentId,
						fileName: file.name,
					});
					throw error;
				}
			} else {
				// Standard upload with presigned URL
				const { url: uploadUrl, documentId } = data[0];
				Logger.debug("Extracted upload details", {
					uploadUrl: uploadUrl.split("?")[0],
					documentId,
				});

				// Step 2: Upload directly to S3 using presigned URL
				Logger.debug("Starting S3 upload", { documentId });
				const uploadResponse = await fetch(uploadUrl, {
					method: "PUT",
					body: file,
					headers: {
						"Content-Type": file.type,
					},
				});

				if (!uploadResponse.ok) {
					const errorText = await uploadResponse
						.text()
						.catch(() => "Failed to get error text");
					Logger.error("S3 upload failed", {
						status: uploadResponse.status,
						statusText: uploadResponse.statusText,
						errorText,
					});
					throw new Error(
						`Failed to upload to S3: ${uploadResponse.status} ${uploadResponse.statusText}`,
					);
				}

				Logger.debug("S3 upload successful, will confirm on submit", {
					documentId,
				});

				// Return the attachment information with a pendingConfirm flag
				const baseUrl = uploadUrl.split("?")[0];
				Logger.info("Upload completed successfully, pending confirmation", {
					url: baseUrl,
					name: file.name,
					documentId,
				});

				// After successful upload to S3, return immediately with pendingConfirm flag
				return {
					url: baseUrl,
					name: file.name,
					contentType: file.type,
					document_id: documentId,
					pendingConfirm: true, // Flag to track unconfirmed uploads
				};
			}
		} catch (error) {
			Logger.error("Client: Protected upload failed", {
				error: error instanceof Error ? error.message : String(error),
				stack: error instanceof Error ? error.stack : undefined,
				fileName: file.name,
				fileType: file.type,
				fileSize: file.size,
			});
			throw Error("Please try a different file, or contact support.");
		}
	};

	const adjustHeight = () => {
		if (textareaRef.current) {
			// Reset height to auto first to get accurate scrollHeight
			textareaRef.current.style.height = "auto";

			// Calculate new height based on content
			const newHeight = Math.max(
				// Increased minimum height to 36px
				36,
				// Increased max height to 200px since buttons have their own container
				Math.min(textareaRef.current.scrollHeight + 2, 200),
			);

			textareaRef.current.style.height = `${newHeight}px`;

			// Enable scrolling if content exceeds max height
			textareaRef.current.style.overflowY =
				textareaRef.current.scrollHeight > 200 ? "auto" : "hidden";
		}
	};

	// Call adjustHeight whenever input changes
	useEffect(() => {
		adjustHeight();
	}, [input]);

	// Handle window resize events to readjust height
	useEffect(() => {
		const handleResize = () => adjustHeight();
		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	// Listen for prompt files event
	useEffect(() => {
		const handlePromptFiles = (event: CustomEvent) => {
			const { files, messageId, isFromPlaybook } = event.detail;
			if (files && Array.isArray(files)) {
				Logger.info("Received files from prompt:", {
					files,
					messageId,
					chatId,
				});

				// Validate each file before adding to attachments
				const validFiles = files.filter((file) => {
					// Check if the file URL is valid (not a blob URL)
					const isValidUrl =
						file.url &&
						typeof file.url === "string" &&
						!file.url.startsWith("blob:");

					// Check if document_id is present
					const hasDocumentId = !!file.document_id;

					if (!isValidUrl) {
						Logger.warn("Skipping file with invalid URL:", file);
					}

					if (!hasDocumentId) {
						Logger.warn("File missing document_id:", file);
					}

					return isValidUrl && hasDocumentId;
				});

				if (validFiles.length > 0) {
					// Files should already be in the correct format from the hook
					// But let's ensure they have all required fields
					const formattedAttachments = validFiles.map((file) => ({
						url: file.url,
						name: file.name,
						contentType: file.contentType || "application/octet-stream",
						document_id: file.document_id,
						uploaded: true,
						_attachment_id: file._attachment_id || file.document_id,
						fromPlaybook: isFromPlaybook,
					}));

					Logger.info("Adding formatted files to attachments:", {
						attachments: formattedAttachments,
						messageId,
						chatId,
					});

					// Add the valid files to attachments
					if (isFromPlaybook) {
						setAttachments(formattedAttachments);
					} else {
						setAttachments((currentAttachments) => [
							...currentAttachments,
							...formattedAttachments,
						]);
					}

					// ROBUST TEMPORARY FIX: Use stored prompt text to reliably trigger document processing
					// This avoids race conditions with React state updates (same approach as Prompt Architect)
					Logger.info(
						"Applying robust temporary fix for Playbook prompts: using stored prompt text",
					);

					setTimeout(() => {
						// Get the stored prompt text (guaranteed to be correct)
						const storedPromptText = localStorage.getItem("pendingPromptText");

						if (storedPromptText && storedPromptText.trim().length > 0) {
							const updatedValue = storedPromptText + ".";

							Logger.info(
								"Playbook prompts - Using stored prompt text:",
								storedPromptText,
							);
							Logger.info(
								"Playbook prompts - Updated value with period:",
								updatedValue,
							);

							if (textareaRef.current) {
								// Update the textarea value directly
								textareaRef.current.value = updatedValue;

								// Update the React state
								setInput(updatedValue);

								// Dispatch a real input event to trigger processing
								const inputEvent = new Event("input", { bubbles: true });
								textareaRef.current.dispatchEvent(inputEvent);

								// Clean up the stored prompt text
								localStorage.removeItem("pendingPromptText");

								Logger.info(
									"Robust temporary fix applied for Playbook prompts: added period to trigger document processing",
								);
							} else {
								Logger.warn(
									"Textarea ref not available for robust temporary fix",
								);
							}
						} else {
							Logger.warn(
								"No stored prompt text found for robust temporary fix - falling back to current input",
							);

							// Fallback to the old approach if no stored text is available
							if (textareaRef.current) {
								const currentValue = textareaRef.current.value || input || "";
								if (currentValue.trim().length > 0) {
									const updatedValue = currentValue + ".";

									setInput(updatedValue);
									textareaRef.current.value = updatedValue;

									const inputEvent = new Event("input", { bubbles: true });
									textareaRef.current.dispatchEvent(inputEvent);

									Logger.info(
										"Fallback temporary fix applied: added period to current input",
									);
								}
							}
						}
					}, 100); // Short delay to ensure DOM is ready

					Logger.info("Documents from prompt loaded and ready for processing");

					// If we have a message ID, associate documents with it directly
					if (messageId) {
						Logger.info("Associating documents with message ID from event:", {
							messageId,
							chatId,
						});

						// Store the attachments in localStorage for immediate display
						try {
							// First, get any existing files for this chat
							const existingFilesStr = localStorage.getItem(
								`chat-files-${chatId}`,
							);
							let existingFiles = [];

							if (existingFilesStr) {
								try {
									existingFiles = JSON.parse(existingFilesStr);
									Logger.info("Found existing files in localStorage:", {
										count: existingFiles.length,
										chatId,
									});
								} catch (parseError) {
									Logger.error(
										"Error parsing existing files from localStorage:",
										parseError,
									);
									existingFiles = [];
								}
							}

							// Add the message ID to each attachment
							const attachmentsWithMessageId = formattedAttachments.map(
								(att) => ({
									...att,
									messageId, // Add the message ID to each attachment
								}),
							);

							// Store the combined files in localStorage
							const combinedFiles = [
								...existingFiles,
								...attachmentsWithMessageId,
							];
							localStorage.setItem(
								`chat-files-${chatId}`,
								JSON.stringify(combinedFiles),
							);

							Logger.info("Stored attachments in localStorage:", {
								count: combinedFiles.length,
								chatId,
								messageId,
							});

							// Associate documents with the message in the database
							// This is critical for the documents to be displayed in the chat

							// First, ensure the documents exist in the database
							// This is a common issue - the document might be uploaded but not yet saved in the database
							// We'll wait a short time to allow the document upload to complete
							setTimeout(() => {
								fetch("/api/message-documents", {
									method: "POST",
									headers: { "Content-Type": "application/json" },
									body: JSON.stringify({
										messageId,
										documentIds: formattedAttachments.map(
											(file) => file.document_id,
										),
									}),
								})
									.then((response) => {
										if (response.ok) {
											return response.json().then((data) => {
												Logger.info(
													"Successfully associated documents with message:",
													{
														messageId,
														chatId,
														response: data,
													},
												);

												// Clear the pending message ID from localStorage
												// Only do this after successful association
												if (
													localStorage.getItem("pendingMessageId") === messageId
												) {
													localStorage.removeItem("pendingMessageId");
													Logger.info(
														"Cleared pending message ID from localStorage:",
														messageId,
													);
												}
											});
										} else {
											return response
												.json()
												.then((errorData) => {
													Logger.error(
														"Failed to associate documents with message:",
														{
															messageId,
															chatId,
															status: response.status,
															error: errorData,
														},
													);

													// Try again with a longer delay
													// This gives more time for the documents to be saved in the database
													setTimeout(() => {
														fetch("/api/message-documents", {
															method: "POST",
															headers: { "Content-Type": "application/json" },
															body: JSON.stringify({
																messageId,
																documentIds: formattedAttachments.map(
																	(file) => file.document_id,
																),
															}),
														})
															.then((retryResponse) => {
																if (retryResponse.ok) {
																	return retryResponse.json().then((data) => {
																		Logger.info(
																			"Successfully associated documents with message on retry:",
																			{
																				messageId,
																				chatId,
																				response: data,
																			},
																		);

																		// Clear the pending message ID from localStorage
																		if (
																			localStorage.getItem(
																				"pendingMessageId",
																			) === messageId
																		) {
																			localStorage.removeItem(
																				"pendingMessageId",
																			);
																		}
																	});
																} else {
																	return retryResponse
																		.json()
																		.then((errorData) => {
																			Logger.error(
																				"Failed to associate documents with message on retry:",
																				{
																					messageId,
																					chatId,
																					status: retryResponse.status,
																					error: errorData,
																				},
																			);
																		});
																}
															})
															.catch((retryError) => {
																Logger.error(
																	"Error retrying document association:",
																	{
																		error: retryError,
																		messageId,
																		chatId,
																	},
																);
															});
													}, 2000); // Wait 2 seconds before retrying
												})
												.catch(() => {
													Logger.error("Failed to parse error response:", {
														messageId,
														chatId,
														status: response.status,
													});
												});
										}
									})
									.catch((error) => {
										Logger.error(
											"Failed to associate documents with message:",
											{
												error,
												messageId,
												chatId,
											},
										);
									});
							}, 500); // Wait 500ms before first attempt
						} catch (error) {
							Logger.error("Failed to store attachments in localStorage:", {
								error,
								messageId,
								chatId,
							});
						}
					} else {
						Logger.warn("No message ID provided for document association");

						// Generate a message ID for the documents
						const generatedMessageId = generateUUID();
						Logger.info("Generated message ID for document association:", {
							generatedMessageId,
							chatId,
						});

						// Store the attachments in localStorage for immediate display
						try {
							// First, get any existing files for this chat
							const existingFilesStr = localStorage.getItem(
								`chat-files-${chatId}`,
							);
							let existingFiles = [];

							if (existingFilesStr) {
								try {
									existingFiles = JSON.parse(existingFilesStr);
									Logger.info("Found existing files in localStorage:", {
										count: existingFiles.length,
										chatId,
									});
								} catch (parseError) {
									Logger.error(
										"Error parsing existing files from localStorage:",
										parseError,
									);
									existingFiles = [];
								}
							}

							// Add the generated message ID to each attachment
							const attachmentsWithMessageId = formattedAttachments.map(
								(att) => ({
									...att,
									messageId: generatedMessageId, // Add the generated message ID to each attachment
								}),
							);

							// Store the combined files in localStorage
							const combinedFiles = [
								...existingFiles,
								...attachmentsWithMessageId,
							];
							localStorage.setItem(
								`chat-files-${chatId}`,
								JSON.stringify(combinedFiles),
							);

							Logger.info(
								"Stored attachments in localStorage with generated message ID:",
								{
									count: combinedFiles.length,
									chatId,
									messageId: generatedMessageId,
								},
							);

							// Associate documents with the message in the database
							// This is critical for the documents to be displayed in the chat
							setTimeout(() => {
								fetch("/api/message-documents", {
									method: "POST",
									headers: { "Content-Type": "application/json" },
									body: JSON.stringify({
										messageId: generatedMessageId,
										documentIds: formattedAttachments.map(
											(file) => file.document_id,
										),
									}),
								})
									.then((response) => {
										if (response.ok) {
											return response.json().then((data) => {
												Logger.info(
													"Successfully associated documents with generated message ID:",
													{
														messageId: generatedMessageId,
														chatId,
														response: data,
													},
												);
											});
										} else {
											Logger.error(
												"Failed to associate documents with generated message ID:",
												{
													messageId: generatedMessageId,
													chatId,
												},
											);
										}
									})
									.catch((error) => {
										Logger.error(
											"Failed to associate documents with generated message ID:",
											{
												error,
												messageId: generatedMessageId,
												chatId,
											},
										);
									});
							}, 500); // Wait 500ms before first attempt
						} catch (error) {
							Logger.error(
								"Failed to store attachments in localStorage with generated message ID:",
								{
									error,
									chatId,
								},
							);
						}
					}

					// Clear the pending files from localStorage
					// Only do this after we've processed them
					if (localStorage.getItem("pendingChatFiles")) {
						localStorage.removeItem("pendingChatFiles");
						Logger.info("Cleared pending chat files from localStorage");
					}

					// Also make a direct API call to ensure the documents are registered in the database
					// This is a backup to ensure the documents are properly registered
					setTimeout(() => {
						// Make a GET request to the source-documents API to force a refresh
						fetch(`/api/source-documents?chatId=${chatId}`, {
							method: "GET",
							headers: {
								"Cache-Control": "no-cache, no-store, must-revalidate",
								Pragma: "no-cache",
								Expires: "0",
							},
						})
							.then((response) => {
								if (response.ok) {
									Logger.info("Successfully refreshed source documents:", {
										chatId,
									});
								} else {
									Logger.warn("Failed to refresh source documents:", {
										chatId,
										status: response.status,
									});
								}
							})
							.catch((error) => {
								Logger.error("Error refreshing source documents:", {
									error,
									chatId,
								});
							});
					}, 1000); // Wait 1 second before refreshing source documents
				} else {
					Logger.warn("No valid files found in prompt files");
					toast.error(
						"Unable to load documents from prompt. Please try uploading them directly.",
					);
				}
			}
		};

		const removeAllAttachments = () => {
			setAttachments([]);
		};

		window.addEventListener(
			"prompt-files-available",
			handlePromptFiles as EventListener,
		);
		window.addEventListener(
			"remove-all-attachments",
			removeAllAttachments as EventListener,
		);

		return () => {
			window.removeEventListener(
				"prompt-files-available",
				handlePromptFiles as EventListener,
			);

			window.removeEventListener(
				"remove-all-attachments",
				removeAllAttachments as EventListener,
			);
		};
	}, [setAttachments, chatId]);

	// Original useEffect for initial adjustment
	useEffect(() => {
		if (textareaRef.current) {
			adjustHeight();
		}
	}, []);

	const [localStorageInput, setLocalStorageInput] = useLocalStorage(
		"input",
		"",
	);

	useEffect(() => {
		if (textareaRef.current) {
			const domValue = textareaRef.current.value;
			// Prefer DOM value over localStorage to handle hydration
			const finalValue = domValue || localStorageInput || "";

			// Only set the input if it's currently empty to avoid overwriting prompt text
			// that was set programmatically by the Playbook
			if (!input || input.trim() === "") {
				setInput(finalValue);
			}
			adjustHeight();
		}
		// Only run once after hydration
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	useEffect(() => {
		setLocalStorageInput(input);
	}, [input, setLocalStorageInput]);

	const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
		setInput(event.target.value);
		// adjustHeight is now called via the useEffect that watches for input changes

		// Auto scroll to bottom when typing
		if (textareaRef.current) {
			textareaRef.current.scrollTop = textareaRef.current.scrollHeight;
		}
	};

	const fileInputRef = useRef<HTMLInputElement>(null);
	const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);
	const [deleteQueue, setDeleteQueue] = useState<Array<string>>([]);
	const [completedUploads, setCompletedUploads] = useState<string[]>([]);

	useEffect(() => {
		if (completedUploads.length > 0) {
			const timer = setTimeout(() => {
				setCompletedUploads([]);
			}, 2500);
			return () => clearTimeout(timer);
		}
	}, [completedUploads]);

	const submitForm = useCallback(
		(useLegacyModel?: boolean) => {
			if (!input.trim()) {
				return;
			}

			window.history.replaceState({}, "", `/chat/${chatId}`);

			// Ensure all attachments have the EXACT format expected by the chat API
			const processedAttachments = attachments.map((attachment) => ({
				...attachment,
				url: attachment.url,
				name: attachment.name,
				contentType: attachment.contentType || "application/octet-stream",
				document_id: attachment.document_id,
				uploaded: true,
				pendingConfirm: true,
				fromPlaybook: attachment.fromPlaybook, // Preserve the fromPlaybook flag
			}));

			// Log the attachments for debugging
			if (processedAttachments.length > 0) {
				Logger.info("Submitting message with attachments:", {
					count: processedAttachments.length,
					attachments: processedAttachments,
					chatId,
				});
			}

			// Get the message ID from localStorage or generate a new one
			// This is a critical step to ensure documents are properly associated with messages
			const pendingMessageId = localStorage.getItem("pendingMessageId");
			const messageId = pendingMessageId || generateUUID();

			// Create a data object with the message ID
			const requestData = {
				includeInternet,
				ragEnabled,
				messageId, // Pass the message ID as part of the data object
				useLegacyModel: useLegacyModel || false,
			};

			const requestOptions: ChatRequestOptions = {
				experimental_attachments: processedAttachments,
				data: requestData,
			};

			// log document chat info
			try {
				fetch("/api/documents/recently", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						documents: processedAttachments,
						additionalInfo: {
							requestData,
							chatId,
						},
					}),
				});
			} catch (error) {
				Logger.error("Failed to submit message:", {
					error,
					chatId,
				});
			}

			Logger.info("Using message ID for submission:", {
				messageId,
				isPending: !!pendingMessageId,
				chatId,
			});

			// Clear the pending message ID from localStorage
			if (pendingMessageId) {
				localStorage.removeItem("pendingMessageId");
				Logger.info(
					"Cleared pending message ID from localStorage:",
					pendingMessageId,
				);
			}

			// Start background confirmation of uploads
			const pendingAttachments = processedAttachments.filter(
				(a) => a.pendingConfirm,
			);
			if (pendingAttachments.length > 0) {
				confirmUploads(processedAttachments, input);
			}

			// Now call handleSubmit with the request options
			handleSubmit(undefined, requestOptions);

			// Reset feature toggles after submission
			resetFeatureToggles();

			// After submission, also try to associate documents again
			// This is a backup in case the first attempt failed
			if (processedAttachments.length > 0) {
				setTimeout(() => {
					fetch("/api/message-documents", {
						method: "POST",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify({
							messageId,
							documentIds: processedAttachments.map((att) => att.document_id),
						}),
					})
						.then((response) => {
							if (response.ok) {
								return response.json().then((data) => {
									Logger.info(
										"Successfully associated documents with message after submission:",
										{
											messageId,
											chatId,
											response: data,
										},
									);
								});
							} else {
								return response
									.json()
									.then((errorData) => {
										Logger.error(
											"Failed to associate documents with message after submission:",
											{
												messageId,
												chatId,
												status: response.status,
												error: errorData,
											},
										);

										// Try one more time with an even longer delay
										setTimeout(() => {
											fetch("/api/message-documents", {
												method: "POST",
												headers: { "Content-Type": "application/json" },
												body: JSON.stringify({
													messageId,
													documentIds: processedAttachments.map(
														(att) => att.document_id,
													),
												}),
											})
												.then((finalResponse) => {
													if (finalResponse.ok) {
														Logger.info(
															"Successfully associated documents with message on final retry:",
															{
																messageId,
																chatId,
															},
														);
													} else {
														Logger.error(
															"Failed to associate documents with message on final retry:",
															{
																messageId,
																chatId,
															},
														);
													}
												})
												.catch((finalError) => {
													Logger.error(
														"Error on final retry of document association:",
														{
															error: finalError,
															messageId,
															chatId,
														},
													);
												});
										}, 3000); // Wait 3 seconds before final retry
									})
									.catch(() => {
										Logger.error(
											"Failed to parse error response after submission:",
											{
												messageId,
												chatId,
												status: response.status,
											},
										);
									});
							}
						})
						.catch((error) => {
							Logger.error(
								"Failed to associate documents with message after submission:",
								{
									error,
									messageId,
									chatId,
								},
							);
						});
				}, 2000); // Wait 2 seconds after submission
			}

			setAttachments([]);
			setStoredAttachments([]); // Clear stored attachments
			setLocalStorageInput("");
			setInput(""); // Clear input

			// Reset textarea height - this will be handled by the useEffect that watches input
			// but we'll force the textarea value to be empty immediately
			if (textareaRef.current) {
				textareaRef.current.value = ""; // Ensure textarea is empty
			}

			if (width && width > 768) {
				textareaRef.current?.focus();
			}
		},
		[
			input,
			attachments,
			handleSubmit,
			setAttachments,
			setStoredAttachments,
			setLocalStorageInput,
			setInput,
			width,
			chatId,
			includeInternet,
			ragEnabled,
		],
	);

	console.log("attachments", attachments);

	// Register the submitForm function with the parent component
	useEffect(() => {
		if (setSubmitFormRef) {
			setSubmitFormRef(submitForm);
		}

		// Also expose submitForm to window for external components to use
		if (typeof window !== "undefined") {
			(window as any).__CHAT_SUBMIT_FORM__ = submitForm;

			// Cleanup when component unmounts
			return () => {
				delete (window as any).__CHAT_SUBMIT_FORM__;
			};
		}
	}, [submitForm, setSubmitFormRef]);

	const FILE_SIZE_LIMIT = 50 * 1024 * 1024;

	const uploadFile = async (file: File) => {
		try {
			Logger.info("Client: Starting upload", {
				name: file.name,
				size: file.size,
				chatId,
			});

			// Validate file type first
			const validation = validatePromptFileType(file);
			if (!validation.isValid) {
				toast.error(validation.error || "Unsupported file type");
				Logger.warn("Client: File type validation failed", {
					fileName: file.name,
					fileType: file.type,
					error: validation.error,
				});
				return undefined;
			}

			// Choose upload method based on file size
			if (file.size <= FILE_SIZE_LIMIT) {
				return await uploadFileSecure(file);
			} else {
				toast.error("File size exceeds current limit of 50 MB");
				logEvent(ServerErrorEvent.FILE_SIZE_EXCEEDED, {}, false);
				return undefined;
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unknown error";
			toast.error(`Upload failed: ${errorMessage}`);
			Logger.error("Client: Upload error", error);
			logEvent(ServerErrorEvent.FILE_UPLOAD_FAILED, {
				errorMessage,
				chatId,
				error: serializeError(error),
			});
			return undefined;
		}
	};

	const deleteFile = async (attachment: Attachment) => {
		try {
			Logger.info("Client: Starting file deletion", {
				url: attachment.url,
				name: attachment.name,
			});

			// For now, just return true to allow UI removal
			// In a production environment, you would want to implement
			// proper server-side deletion
			Logger.info("Client: File marked for deletion in UI only", {
				url: attachment.url,
				name: attachment.name,
				documentId: attachment.document_id,
			});

			// Optionally, you can still try to update any references in the database
			// by making a request to associate the document with null or remove it
			try {
				await fetch("/api/message-documents", {
					method: "DELETE",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						documentId: attachment.document_id,
						chatId,
					}),
				});
				Logger.info("Removed document association from database");
			} catch (dbError) {
				// Just log the error but don't fail the UI update
				Logger.warn("Failed to remove document association", dbError);
			}

			return true;
		} catch (error) {
			Logger.error("Client: Delete error", error);
			logEvent(ServerErrorEvent.FILE_DELETE_FAILED, {
				error: serializeError(error),
				chatId,
			});
			return false;
		}
	};

	const handleFileUpload = useCallback(
		async (event: ChangeEvent<HTMLInputElement>) => {
			Logger.info(" Client: Starting file upload process");
			const files = Array.from(event.target.files || []);
			Logger.info(
				"Client: Files to upload:",
				files.map((f) => ({ name: f.name, size: f.size })),
			);

			setUploadQueue(files.map((file) => file.name));

			try {
				Logger.info("Client: Initiating upload promises");
				const uploadPromises = files.map((file) => uploadFile(file));
				const uploadedAttachments = await Promise.all(uploadPromises);
				Logger.info("Client: All uploads completed", uploadedAttachments);

				const successfullyUploadedAttachments = uploadedAttachments.filter(
					(attachment) => attachment !== undefined,
				);

				// Show success toast if files were uploaded successfully
				if (successfullyUploadedAttachments.length > 0) {
					const fileCount = successfullyUploadedAttachments.length;
					toast.success(
						`${fileCount} ${
							fileCount === 1 ? "file" : "files"
						} uploaded successfully`,
					);

					// Add to completed uploads for visual indicator
					setCompletedUploads(
						successfullyUploadedAttachments
							.map((attachment) => attachment.name)
							.filter(Boolean) as string[],
					);
				}

				// Add the new attachments to the state
				setAttachments((currentAttachments) => {
					const newAttachments = [
						...currentAttachments,
						...successfullyUploadedAttachments,
					];

					Logger.info("Updated attachments state with new uploads:", {
						previousCount: currentAttachments.length,
						newCount: newAttachments.length,
						chatId,
					});

					return newAttachments;
				});
			} catch (error) {
				Logger.error("Client: Upload process failed", error);
				toast.error("Error uploading files");
			} finally {
				Logger.info(" Client: Upload process finished");
				setUploadQueue([]);
			}
		},
		[setAttachments, attachments.length, chatId],
	);

	const handleAttachmentDelete = useCallback(
		async (attachmentToDelete: Attachment) => {
			Logger.info("Client: Starting file deletion process");

			// Add to delete queue to show loading state
			setDeleteQueue((queue) =>
				[...queue, attachmentToDelete.name].filter(
					(name): name is string => name !== undefined,
				),
			);

			try {
				Logger.info("Client: Initiating delete operation");
				const deleteSuccessful = await deleteFile(attachmentToDelete);

				if (deleteSuccessful) {
					Logger.info("Client: Delete completed successfully");
					// Only update UI state after successful backend deletion
					setAttachments((currentAttachments) =>
						currentAttachments.filter((attachment) => {
							if (attachment.document_id) {
								return (
									attachment.document_id !== attachmentToDelete.document_id
								);
							}
							return attachment.url !== attachmentToDelete.url;
						}),
					);
					toast.success(`File deleted: ${attachmentToDelete.name}`);
				} else {
					toast.error(`Failed to delete file: ${attachmentToDelete.name}`);
				}
			} catch (error) {
				Logger.error("Client: Delete process failed", error);
				toast.error("Error deleting file");
			} finally {
				Logger.info("Client: Delete process finished");
				setDeleteQueue((queue) =>
					queue.filter((name) => name !== attachmentToDelete.name),
				);
			}
		},
		[setAttachments],
	);

	const [isDragging, setIsDragging] = useState(false);

	const handleDragEnter = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(true);
	}, []);

	const handleDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);
	}, []);

	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(true);
	}, []);

	const handleDrop = useCallback(
		(event: React.DragEvent<HTMLDivElement>) => {
			event.preventDefault();
			setIsDragging(false);

			if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
				const files = Array.from(event.dataTransfer.files);
				Logger.info(
					"Client: Files dropped:",
					files.map((f) => ({ name: f.name, size: f.size })),
				);

				setUploadQueue(files.map((file) => file.name));

				const uploadFiles = async () => {
					try {
						Logger.info("Client: Initiating upload promises for dropped files");
						const uploadPromises = files.map((file) => uploadFile(file));
						const uploadedAttachments = await Promise.all(uploadPromises);
						Logger.info(
							" Client: All dropped file uploads completed",
							uploadedAttachments,
						);

						const successfullyUploadedAttachments = uploadedAttachments.filter(
							(attachment) => attachment !== undefined,
						);

						// Show success toast if files were uploaded successfully
						if (successfullyUploadedAttachments.length > 0) {
							const fileCount = successfullyUploadedAttachments.length;
							toast.success(
								`${fileCount} ${
									fileCount === 1 ? "file" : "files"
								} uploaded successfully`,
							);

							// Add to completed uploads for visual indicator
							setCompletedUploads(
								successfullyUploadedAttachments
									.map((attachment) => attachment.name)
									.filter(Boolean) as string[],
							);
						}

						// Add the new attachments to the state
						setAttachments((currentAttachments) => {
							const newAttachments = [
								...currentAttachments,
								...successfullyUploadedAttachments,
							];

							Logger.info("Updated attachments state with new uploads:", {
								previousCount: currentAttachments.length,
								newCount: newAttachments.length,
								chatId,
							});

							return newAttachments;
						});
					} catch (error) {
						Logger.error("Client: Drop upload process failed", error);
						toast.error("Error uploading dropped files");
					} finally {
						Logger.info(" Client: Drop upload process finished");
						setUploadQueue([]);
					}
				};

				uploadFiles();
			}
		},
		[setAttachments, uploadFile, attachments.length, chatId],
	);

	useEffect(() => {
		if (typeof window !== "undefined") {
			Logger.info("Debug enabled?", localStorage.getItem("debug"));
			uploadDebug("MultimodalInput mounted");
		}
	}, []);

	const handleModelSelect = (modelId: string) => {
		setCurrentModel(modelId);
		router.refresh();
	};

	const { LibrarySelectorModal, openDialog, closeDialog } = useLibrarySelector({
		attachments,
		setAttachments,
	});

	const showAttachmentWarning =
		attachments.length >= 10 ||
		attachments.reduce(
			(total, attachment) => total + ((attachment as any).size || 0),
			0,
		) >
			7.5 * 1024 * 1024;

	const handleDownloadChatClick = async (format: string) => {
		// Convert each message to styled HTML
		const htmlMessages = await Promise.all(
			messages.map(async (msg) => {
				const rawHtml = await marked.parse(msg.content);
				const htmlContent = rawHtml
					.replace(/<ul>/g, "<ol>")
					.replace(/<\/ul>/g, "</ol>")
					.replace(/<pre>/g, "<p>")
					.replace(/<\/pre>/g, "</p>")
					.replace(/<code>/g, "<p>")
					.replace(/<\/code>/g, "</p>");

				const attachmentNames =
					msg.experimental_attachments
						?.map((att) => `<li>${att.name}</li>`)
						.join("") || "";

				const attachmentsSection = attachmentNames
					? `<div style="margin-top: 0.5em;">
              <strong>Attachments:</strong>
              <ul>${attachmentNames}</ul>
            </div>`
					: "";

				const roleLabel =
					msg.role === "assistant"
						? "Iqidis"
						: user?.firstname
							? user.firstname.charAt(0).toUpperCase() + user.firstname.slice(1)
							: "User";

				return `
          <div style="margin-bottom: 1.5em;">
            <p style="font-weight: bold; margin-bottom: 0.5em;"><u>${roleLabel}:</u></p>
            <span style="font-size: 11px; color: #888;">${
							msg.createdAt
								? formatDate(new Date(msg.createdAt), "MMM d, yyyy, hh:mm a")
								: ""
						}</span>
            ${attachmentsSection}
            <div>${htmlContent}</div>
          </div>
        `;
			}),
		);

		const chatName = chatTitle ? `<h2> ${chatTitle}</h2>` : "";

		// Get the absolute URL for the logo image
		const logoUrl =
			"https://sd1fwqxb1ktiukqm.public.blob.vercel-storage.com/Cropped%20-%20Center%20w%20Logo-wuzZpALqg5F7V7FktC6yQmZ7lNCj72.png";

		const iqidisLogoImg = `<img src="${logoUrl}" width="50" height="50" style="opacity:0.9;" alt="Iqidis Logo" />`;
		// Combine into full HTML doc
		const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8" />
          <style>
            body { font-family: Arial, sans-serif; }
            pre, code { font-family: Courier New, monospace; background: #f4f4f4; padding: 4px; border-radius: 4px; display: inline-block; }
            blockquote { border-left: 4px solid #ccc; margin: 1em 0; padding-left: 1em; color: #555; }
            ul, ol { margin-left: 1.2em; }
            a { color: #0000FF; text-decoration: underline; }
          </style>
        </head>
        <body>
          <div class="logo-container">${iqidisLogoImg}</div>
          ${chatName}
          ${htmlMessages.join("\n")}
        </body>
      </html>
    `;
		convertHtmlToDocxOrPDf(fullHtml, format);

		if (format == "docx") {
			logEvent(ChatEvent.EXPORT_CHAT_AS_DOCX);
		} else {
			logEvent(ChatEvent.EXPORT_CHAT_AS_PDF);
		}
	};

	return (
		<div
			className={cx(
				"transition-all duration-300 h-full",
				isSidebarOpen ? "xl:pl-[50px]" : "pl-0",
				isPlaybookOpen ? "xl:pr-[50px]" : "pr-0",
			)}
		>
			<div
				className={cx(
					"max-w-4xl mx-auto px-4 pb-0 chat-background-light h-full dark:chat-background-dark",
				)}
			>
				<div
					className={cx(
						"flex flex-col items-stretch w-full rounded-2xl pt-2 pb-2 h-full justify-between relative",
						isDragging &&
							"after:content-[''] after:absolute after:inset-0 after:border-2 after:border-dashed after:border-primary after:bg-gray-500/30 after:rounded-2xl after:z-10",
						className,
					)}
					onDragEnter={handleDragEnter}
					onDragLeave={handleDragLeave}
					onDragOver={handleDragOver}
					onDrop={handleDrop}
				>
					{/* Usage limit overlay */}
					{isLimited && (
						<div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-[45] flex items-center justify-center rounded-2xl px-6 py-8">
							<div className="flex flex-col items-center justify-center gap-5 text-center max-w-md">
								<div className="text-lg font-medium text-zinc-800 dark:text-zinc-100">
									{limitMessage}
								</div>
								<Button
									variant="default"
									className="text-white hover:brightness-110 transition-all duration-200 pointer-events-auto px-6 py-2 h-auto font-medium"
									onClick={() => (window.location.href = "/subscription")}
								>
									Upgrade to Premium
								</Button>
							</div>
						</div>
					)}
					<div className="flex-1 overflow-y-auto relative flex items-end">
						{messages.length === 0 ? (
							<WelcomeSection
								chatId={chatId}
								setInput={setInput}
								submitForm={submitForm}
							/>
						) : null}
					</div>
					<input
						type="file"
						className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
						ref={fileInputRef}
						multiple
						accept={PROMPT_FILE_ACCEPT_STRING}
						onChange={handleFileUpload}
						tabIndex={-1}
					/>
					{(attachments.length > 0 || uploadQueue.length > 0) && (
						<div className="sticky bottom-0 bg-white dark:bg-[var(--chat-background-color)] pt-2 z-40 px-2 sm:px-3 md:px-4 lg:px-0">
							{showAttachmentWarning && !dismissedAttachmentWarning && (
								<div className="p-2 sm:p-3 mb-2 rounded-md bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 flex items-center gap-1.5 sm:gap-2 w-fit relative">
									<AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 text-amber-600 dark:text-amber-500 flex-shrink-0" />
									<p className="text-amber-800 dark:text-amber-300 text-xs sm:text-sm md:text-base leading-tight sm:leading-normal">
										File size or quantity exceeds recommended limits. Consider
										reducing for optimal performance.
									</p>
									<button
										onClick={() => setDismissedAttachmentWarning(true)}
										className="ml-2 text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-200"
									>
										<CrossIcon size={14} />
									</button>
								</div>
							)}
							<div className="flex flex-row gap-2 overflow-x-auto items-end scrollbar-none multimodal-attachments">
								{attachments.map((attachment: any) => (
									<PreviewAttachment
										key={attachment._attachment_id || attachment.url}
										attachment={attachment}
										onDelete={() => handleAttachmentDelete(attachment)}
										isUploading={
											attachment.name
												? deleteQueue.includes(attachment.name)
												: false
										}
										showSuccessIndicator={
											attachment.name
												? completedUploads.includes(attachment.name)
												: false
										}
										disablePreview={attachment.fromPlaybook} // Disable preview for playbook attachments
									/>
								))}

								{uploadQueue.map((filename) => (
									<PreviewAttachment
										key={filename}
										attachment={{
											url: "",
											name: filename,
											contentType: "",
										}}
										isUploading={true}
									/>
								))}
							</div>
						</div>
					)}
					<div className="relative z-40 bg-white dark:bg-[var(--chat-background-color)] mt-auto">
						<div className="flex flex-row items-center justify-between mb-4 px-4 pt-1 overflow-auto lg:gap-0 gap-2 pb-2 lg:pb-0">
							<div className="flex flex-row items-center gap-2">
								<ToggleButtonWithInfo
									isActive={includeInternet}
									onClick={() => {
										// Log the Research Mode button click event
										logEvent(ChatEvent.RESEARCH_MODE_CLICK, {
											userId: user?.id,
											userEmail: user?.email,
											isAdmin: user?.isAdmin,
											featureTurnedOn: !includeInternet,
											chatId,
											hasConversationHistory: messages && messages.length > 0,
										});
										setIncludeInternet(!includeInternet);
									}}
									icon={<Brain size={22} strokeWidth={2} />}
									label="Tap into online legal resources and the Iqidis Database (federal and state courts, statutes, regulations, templates, and more)"
									shortLabel="Research"
								/>
								{/* <ToggleButtonWithInfo
                  isActive={ragEnabled}
                  onClick={() => {
                    // Log the Enhanced Doc Reader button click event
                    logEvent(ChatEvent.ENHANCED_DOC_READER_CLICK, {
                      userId: user?.id,
                      userEmail: user?.email,
                      isAdmin: user?.isAdmin,
                      featureTurnedOn: !ragEnabled,
                      chatId,
                      hasConversationHistory: messages && messages.length > 0,
                    });
                    setRagEnabled(!ragEnabled);
                  }}
                  icon={<FileBox size={22} strokeWidth={2} />}
                  label="Analyze handwritten notes, detailed documents, or mixed file types (PPT, Excel, images). This advanced mode may take longer but delivers deeper insights for complex materials"
                  shortLabel="Enhanced Doc Reader"
                /> */}
							</div>

							<div className="flex items-center gap-2">
								{messages.length > 0 && (
									<DropdownMenu>
										<Tooltip>
											<TooltipTrigger asChild>
												<DropdownMenuTrigger asChild>
													<Button
														className="h-10 rounded-md border border-zinc-200 dark:border-zinc-700/50 shadow-sm hover:shadow text-zinc-700 dark:text-zinc-300 bg-[var(--chat-background-color)] flex items-center gap-2"
														variant="ghost"
													>
														<Download className="flex-shrink-0" />
														<span className="hidden md:inline">
															Download Chat
														</span>
													</Button>
												</DropdownMenuTrigger>
											</TooltipTrigger>
											<TooltipContent>
												Export this entire matter chat
											</TooltipContent>
										</Tooltip>
										<DropdownMenuContent align="end" side="top">
											<DropdownMenuItem
												onClick={() => {
													handleDownloadChatClick("docx");
												}}
											>
												<FileText className="mr-2 h-4 w-4" />
												<span>Export as Word</span>
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() => {
													handleDownloadChatClick("pdf");
												}}
											>
												<FileText className="mr-2 h-4 w-4" />
												<span>Export as PDF</span>
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								)}
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="ghost"
											className="h-10 rounded-md border border-zinc-200 dark:border-zinc-700/50 shadow-sm hover:shadow text-zinc-700 dark:text-zinc-300 bg-[var(--chat-background-color)] flex items-center gap-2"
											onClick={() => setIsFileDrawerOpen(true)}
										>
											<FileIcon size={22} className="flex-shrink-0" />
											<span className="hidden md:inline">View Files</span>
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										View the files you&apos;ve uploaded in this matter chat
									</TooltipContent>
								</Tooltip>

								<ModelSelector
									selectedModelId={currentModel}
									chatId={chatId}
									className="border border-zinc-200 dark:border-zinc-700/50 shadow-sm hover:shadow rounded-md px-3 text-sm h-10 flex items-center justify-center bg-[var(--chat-background-color)]"
									onModelSelect={handleModelSelect}
								/>
							</div>
						</div>
						<div className="relative group/textarea">
							<div className="relative transition-transform duration-300 ease-out focus-within:scale-[1.0125] origin-bottom">
								<Textarea
									ref={textareaRef}
									placeholder="Drop files here or click to upload. Ask, draft, or research anything!"
									value={input}
									onChange={handleInput}
									disabled={isLimited}
									className={cx(
										"min-h-[36px] md:max-h-auto max-h-[200px] overflow-y-auto resize-none rounded-2xl !text-base pb-12 pt-4 px-4 dark:border-zinc-700 relative z-10",
										"transition-all duration-300 ease-out",
										"shadow-sm hover:shadow group/textarea",
										"focus:shadow-[0_4px_24px_-2px_rgba(0,0,0,0.08)] dark:focus:shadow-[0_4px_24px_-2px_rgba(0,0,0,0.3)]",
										"focus:outline-none focus:ring-1 focus:ring-[rgb(var(--base-navy))] focus:ring-opacity-50",
										"bg-white dark:bg-[var(--assistant-message-bg)] border border-zinc-200 dark:border-zinc-700",
										isLimited && "cursor-not-allowed opacity-50",
									)}
									rows={2}
									onKeyDown={(event) => {
										if (event.key === "Enter" && !event.shiftKey) {
											event.preventDefault();
											if (isLoading) {
												toast.error(
													"Please wait for Iqidis to finish its response!",
												);
											} else if (uploadQueue.length > 0) {
												toast.error(
													"Please wait for document processing to complete!",
												);
											} else {
												submitForm();
											}
										}
									}}
								/>
								<div className="absolute bottom-0 left-0 right-0 h-9 rounded-b-2xl group-focus-within/textarea:ring-1 group-focus-within/textarea:ring-[rgb(var(--base-navy))] group-focus-within/textarea:ring-opacity-50" />
								<div className="absolute p-2 flex flex-row justify-between z-20 bg-white dark:bg-[var(--assistant-message-bg)] rounded-b-2x md:w-[99%] w-[98%] md:right-auto right-[5px] border-0 left-[5px] bottom-[5px] pb-[0px] rounded-[20px]">
									<div className="glassmorphic-buttons flex items-center">
										<AttachmentsButton
											isLoading={isLoading}
											onClick={openDialog}
										/>
										<AudioInputButton
											setInput={setInput}
											isLoading={isLoading}
											textareaRef={textareaRef}
										/>
										<RedoButton
											messages={messages}
											submitForm={submitForm}
											isLoading={isLoading}
											setInput={setInput}
											textareaRef={textareaRef}
										/>
										{chatId && (
											<TransferChatButton
												chatId={chatId}
												messages={messages}
												isLoading={isLoading}
											/>
										)}
									</div>
									<div className="glassmorphic-buttons flex items-center">
										{isLoading ? (
											<StopButton stop={stop} setMessages={setMessages} />
										) : (
											<div className="flex items-center gap-2">
												<AmplifyButton
													input={input}
													setInput={setInput}
													isLoading={isLoading}
													messages={messages}
												/>
												<SendButton
													input={input}
													submitForm={submitForm}
													uploadQueue={uploadQueue}
												/>
											</div>
										)}
									</div>
								</div>
							</div>
						</div>
						<FileDrawer
							chatId={chatId}
							isOpen={isFileDrawerOpen}
							onClose={() => setIsFileDrawerOpen(false)}
						/>

						{/* Info message with hoverable tooltip for disclaimer */}
						<div className="flex justify-center items-center mt-2 text-sm text-gray-500 dark:text-gray-400 px-2">
							<Tooltip>
								<TooltipTrigger asChild>
									<div className="flex items-center">
										<Info size={16} className="mr-2" />
										<span>
											Iqidis can make mistakes, please review outputs. Hover to
											learn more.
										</span>
									</div>
								</TooltipTrigger>
								<TooltipContent
									side="top"
									className="max-w-md p-4 bg-white dark:bg-zinc-800 text-gray-700 dark:text-gray-300 shadow-lg rounded-md border border-gray-200 dark:border-zinc-700/50"
								>
									<h2 className="text-lg font-medium mb-2">Disclaimer:</h2>
									<p className="text-sm">
										Iqidis uses advanced AI systems trained on public data,
										proprietary systems, and internal legal workflows to assist
										with drafting, analysis, and research. However, Iqidis may
										generate outputs that contain errors or inaccuracies.
									</p>
									<p className="mt-4 text-sm">
										We are actively improving our citation verification and
										retrieval accuracy. You are responsible for reviewing all
										outputs before relying on them in legal practice. Iqidis is
										not a substitute for independent legal judgment, nor does it
										constitute legal advice.
									</p>
								</TooltipContent>
							</Tooltip>
						</div>
					</div>
				</div>
			</div>
			{LibrarySelectorModal}
		</div>
	);
}

export const MultimodalInput = memo(
	PureMultimodalInput,
	(prevProps, nextProps) => {
		if (prevProps.input !== nextProps.input) return false;
		if (prevProps.isLoading !== nextProps.isLoading) return false;
		if (!equal(prevProps.attachments, nextProps.attachments)) return false;

		return true;
	},
);

function PureAttachmentsButton({
	isLoading,
	onClick,
}: {
	isLoading: boolean;
	onClick?: () => void;
}) {
	return (
		<Tooltip>
			<TooltipTrigger asChild>
				<Button
					className="rounded-md p-2 h-8 dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200 flex items-center gap-1.5"
					onClick={(event) => {
						event.preventDefault();
						onClick?.();
					}}
					disabled={isLoading}
					variant="ghost"
					data-role="attachments-button"
				>
					<PaperclipIcon size={14} />
					<span className="hidden md:inline text-xs whitespace-nowrap">
						Attach
					</span>
				</Button>
			</TooltipTrigger>
			<TooltipContent>Attach files</TooltipContent>
		</Tooltip>
	);
}

const AttachmentsButton = memo(PureAttachmentsButton);

function PureStopButton({
	stop,
	setMessages,
}: {
	stop: () => void;
	setMessages: Dispatch<SetStateAction<Array<Message>>>;
}) {
	return (
		<Button
			className="rounded-md p-2 h-8 border dark:border-zinc-600 flex items-center gap-1.5"
			onClick={(event) => {
				event.preventDefault();
				stop();
				setMessages((messages) => sanitizeUIMessages(messages));
			}}
		>
			<StopIcon size={14} />
			<span className="hidden md:inline text-xs whitespace-nowrap">Stop</span>
		</Button>
	);
}

const StopButton = memo(PureStopButton);

function PureSendButton({
	submitForm,
	input,
	uploadQueue,
}: {
	submitForm: () => void;
	input: string;
	uploadQueue: Array<string>;
}) {
	return (
		<Button
			className="rounded-md p-2 md:pr-3 h-8 border dark:border-zinc-600 navy-button flex items-center gap-1.5"
			onClick={(event) => {
				event.preventDefault();
				submitForm();
			}}
			disabled={input.length === 0 || uploadQueue.length > 0}
		>
			<ArrowUpIcon size={14} />
			<span className="hidden md:inline text-xs whitespace-nowrap">Send</span>
		</Button>
	);
}

const SendButton = memo(PureSendButton, (prevProps, nextProps) => {
	if (prevProps.uploadQueue.length !== nextProps.uploadQueue.length)
		return false;
	if (prevProps.input !== nextProps.input) return false;
	return true;
});

function PureRedoButton({
	messages,
	submitForm,
	isLoading,
	setInput,
	textareaRef,
}: {
	messages: Array<Message>;
	submitForm: () => void;
	isLoading: boolean;
	setInput: (value: string) => void;
	textareaRef: React.RefObject<HTMLTextAreaElement>;
}) {
	const lastUserMessage = messages.findLast((m) => m.role === "user");
	const isEnabled = !isLoading && lastUserMessage;

	const handleRedoClick = () => {
		if (lastUserMessage && typeof lastUserMessage.content === "string") {
			setInput(lastUserMessage.content);
			if (textareaRef.current) {
				textareaRef.current.value = lastUserMessage.content;
				textareaRef.current.focus();
			}
			submitForm();
		}
	};

	return (
		<Tooltip>
			<TooltipTrigger asChild>
				<Button
					className="rounded-md p-2 h-8 dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200 flex items-center gap-1.5"
					onClick={handleRedoClick}
					disabled={!isEnabled}
					variant="ghost"
					data-role="redo-button"
				>
					<RedoIcon size={14} />
					<span className="hidden md:inline text-xs whitespace-nowrap">
						Redo
					</span>
				</Button>
			</TooltipTrigger>
			<TooltipContent>Redo your last prompt</TooltipContent>
		</Tooltip>
	);
}

const RedoButton = memo(PureRedoButton);

function PureTransferChatButton({
	chatId,
	messages,
	isLoading,
}: {
	chatId: string;
	messages: Array<Message>;
	isLoading: boolean;
}) {
	const router = useRouter();
	const isEnabled = !isLoading && messages.length > 0;

	if (!chatId) return null;

	return (
		<Tooltip>
			<TooltipTrigger asChild>
				<Button
					className="rounded-md p-2 h-8 dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200 flex items-center gap-1.5"
					onClick={() => router.push(`/chat/transfer/${chatId}`)}
					variant="ghost"
					data-role="transfer-button"
					disabled={!isEnabled}
				>
					<ArrowRight size={14} />
					<span className="hidden md:inline text-xs whitespace-nowrap">
						Transfer
					</span>
				</Button>
			</TooltipTrigger>
			<TooltipContent>
				Transfer this chat history to a new one if it’s getting too long
			</TooltipContent>
		</Tooltip>
	);
}

const TransferChatButton = memo(PureTransferChatButton);
