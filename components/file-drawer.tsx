"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FileIcon } from "./icons";
import { useSourceDocuments } from "@/hooks/use-source-documents";
import { Button } from "./ui/button";
import { X as CloseIcon, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";
import { Logger } from "@/lib/utils/Logger";
import { useFileViewer } from "@/hooks/use-file-viewer";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";

interface FileDrawerProps {
  chatId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function truncate(str: string, length: number): string {
  if (str.length <= length) return str;
  return str.slice(0, length) + "...";
}

export function FileDrawer({
  chatId,
  isOpen: propIsOpen,
  onClose,
}: FileDrawerProps) {
  // Add file viewer hook
  const { viewFile, isLoading: isViewLoading } = useFileViewer();

  // Use internal state to track open state, initialized from props
  const [isOpen, setIsOpen] = useState(propIsOpen);
  const { documents, isLoading, refresh } = useSourceDocuments(
    isOpen ? chatId : null
  );
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Update internal state when prop changes
  useEffect(() => {
    setIsOpen(propIsOpen);
  }, [propIsOpen]);

  // Listen for the open-file-drawer event
  useEffect(() => {
    const handleOpenFileDrawer = () => {
      Logger.info("Opening file drawer from event", { chatId });
      setIsOpen(true);
      refresh();
    };

    window.addEventListener("open-file-drawer", handleOpenFileDrawer);
    return () =>
      window.removeEventListener("open-file-drawer", handleOpenFileDrawer);
  }, [chatId, refresh]);

  // Listen for the refresh-file-drawer event
  useEffect(() => {
    const handleRefreshFileDrawer = () => {
      Logger.info("Refreshing file drawer from event", { chatId });
      refresh();
    };

    window.addEventListener("refresh-file-drawer", handleRefreshFileDrawer);
    return () =>
      window.removeEventListener(
        "refresh-file-drawer",
        handleRefreshFileDrawer
      );
  }, [chatId, refresh]);

  // Refresh documents when drawer is opened
  useEffect(() => {
    if (isOpen) {
      refresh();
    }
  }, [isOpen, refresh]);

  // Add this effect to handle playbook button visibility
  useEffect(() => {
    const playbookButton = document.querySelector(
      ".top-\\[15px\\].right-\\[30px\\]"
    );
    if (playbookButton) {
      if (isOpen) {
        // Either hide the button
        playbookButton.classList.add("opacity-0", "pointer-events-none");
        // Or move it to the left
        // playbookButton.classList.add('right-[640px]'); // Increased from 590px to 640px
      } else {
        // Restore visibility
        playbookButton.classList.remove("opacity-0", "pointer-events-none");
        // Or restore position
        // playbookButton.classList.remove('right-[640px]');
      }
    }
  }, [isOpen]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    refresh().then(() => {
      setTimeout(() => setIsRefreshing(false), 500);
    });
  };

  const formatDate = (date: string | Date) => {
    try {
      return (
        new Date(date).toLocaleDateString() +
        " " +
        new Date(date).toLocaleTimeString([], {
          hour: "numeric",
          minute: "2-digit",
          hour12: true,
        })
      );
    } catch (error) {
      Logger.warn("Error formatting date:", { date, error });
      return "Unknown date";
    }
  };

  // Group documents by message ID
  const groupedDocuments = (documents || []).reduce(
    (groups: Record<string, any[]>, doc: any) => {
      const messageId = doc.messageId || "ungrouped";
      if (!groups[messageId]) {
        groups[messageId] = [];
      }
      groups[messageId].push(doc);
      return groups;
    },
    {}
  );

  // Sort message groups by the oldest document in each group
  const sortedGroups = Object.entries(groupedDocuments).sort((a, b) => {
    const aDate = new Date(a[1][0].createdAt).getTime();
    const bDate = new Date(b[1][0].createdAt).getTime();
    return aDate - bDate;
  });

  // Handle closing the drawer
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      onClose();
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={handleOpenChange}>
      <SheetContent
        side="right"
        className="w-auto sm:w-[600px] p-6 flex flex-col [&>button]:hidden z-[1001]"
      >
        <SheetHeader className="space-y-0 flex-none">
          <div className="flex justify-between items-center">
            <SheetTitle className="text-lg font-semibold mt-2">
              Files {isLoading ? "..." : `(${documents?.length || 0})`}
            </SheetTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="h-8 w-8"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
                />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-8 w-8"
              >
                <CloseIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <SheetDescription className="sr-only">
            List of files associated with this chat
          </SheetDescription>
        </SheetHeader>

        <ScrollArea className="flex-1 mt-6">
          <div className="space-y-6 pr-4">
            {sortedGroups.map(([messageId, docs]) => (
              <div key={messageId} className="space-y-2">
                {messageId !== "ungrouped" && (
                  <div className="text-xs font-medium text-muted-foreground border-b pb-1">
                    Message {messageId.substring(0, 8)}...
                  </div>
                )}
                <div className="space-y-2">
                  {docs.map((doc: any) => (
                    <div
                      key={doc.id}
                      className="grid grid-cols-[auto_1fr_auto] items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors"
                    >
                      <FileIcon className="flex-shrink-0" />
                      <div className="min-w-0 overflow-hidden">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <p className="text-sm font-medium truncate">
                              {doc.filename || doc.title || "Untitled Document"}
                            </p>
                          </TooltipTrigger>
                          <TooltipContent>
                            {doc.filename || doc.title || "Untitled Document"}
                          </TooltipContent>
                        </Tooltip>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(doc.createdAt)}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs h-8 px-3"
                        onClick={() => viewFile(doc.id, doc.url)}
                        disabled={isViewLoading}
                      >
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4 p-3">
                    <div className="w-5 h-5 rounded bg-muted animate-pulse" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-3 bg-muted rounded w-1/2 animate-pulse" />
                    </div>
                  </div>
                ))}
              </div>
            )}

            {!isLoading && (!documents || documents.length === 0) && (
              <div className="text-center py-8">
                <p className="text-sm text-muted-foreground mb-2">
                  No files found
                </p>
                <p className="text-xs text-muted-foreground">
                  Files uploaded to this chat will appear here
                </p>
              </div>
            )}
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}