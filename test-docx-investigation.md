# Investigation Plan for .docx Files in Playbook Prompts

## Current Status
I've added comprehensive logging throughout the file attachment pipeline to track .docx files specifically. The development server is running and ready for testing.

## Test Plan

### Step 1: Test File Upload
1. Open the application at http://localhost:3000
2. Navigate to the Playbook tab
3. Create a new prompt with both a PDF and a .docx file
4. Monitor the browser console and server logs for differences

### Step 2: Test Prompt Usage
1. Use the created prompt in a chat
2. Check if both PDF and .docx files are properly loaded
3. Monitor the document ID extraction process

### Step 3: Check Database Storage
1. Verify that .docx files are stored correctly in the database
2. Compare the stored data between PDF and .docx files

## Key Areas to Monitor

### Upload Process
- File type detection (`file.type`)
- MIME type handling
- S3 URL generation
- Document ID generation

### Database Storage
- Prompt file creation
- File metadata storage
- URL storage format

### Retrieval Process
- File loading from database
- Document ID extraction from URL
- File conversion for chat use

## Expected Logging Output

### Upload Logs
```
Prompt Architect: Starting upload {
  name: "test.docx",
  type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  isDocx: true,
  isPdf: false
}
```

### Storage Logs
```
Creating prompt file {
  fileName: "test.docx",
  fileType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  isDocx: true,
  isPdf: false
}
```

### Retrieval Logs
```
Retrieved prompt with files {
  fileCount: 2,
  files: [
    { fileName: "test.pdf", fileType: "application/pdf", isDocx: false, isPdf: true },
    { fileName: "test.docx", fileType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document", isDocx: true, isPdf: false }
  ]
}
```

### Document ID Extraction Logs
```
extractDocumentIdFromUrl: {
  filename: "documentId.docx",
  extension: "docx",
  isDocx: true,
  documentId: "generated-uuid-here"
}
```

## Next Steps
1. Perform the test with actual files
2. Analyze the logs to identify any differences between PDF and .docx handling
3. Fix any identified issues
4. Remove the debug logging once the issue is resolved
