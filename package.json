{"name": "ai-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "prod": "VERCEL_ENV=production next dev --turbo", "preview": "VERCEL_ENV=preview next dev --turbo", "prebuild": "node scripts/lint-changed-files.js", "build": "next build", "start": "next start", "lint": "biome lint --write", "lint:unsafe": "biome lint --write --unsafe", "format": "biome format --write", "format:unsafe": "biome format --write --unsafe", "check:fix": "biome check --write", "check:fix:unsafe": "biome check --write --unsafe", "db:generate": "drizzle-kit generate", "db:generate:development": "VERCEL_ENV=development drizzle-kit generate", "db:generate:preview": "VERCEL_ENV=preview drizzle-kit generate", "db:generate:production": "VERCEL_ENV=production drizzle-kit generate", "db:migrate": "npx tsx lib/db/migrate.ts", "db:migrate:development": "VERCEL_ENV=development npx tsx lib/db/migrate.ts", "db:migrate:preview": "VERCEL_ENV=preview npx tsx lib/db/migrate.ts", "db:migrate:production": "VERCEL_ENV=production npx tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "VERCEL_ENV=development drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "db:seed-prompts": "npx tsx scripts/seed-prompts.ts", "db:import-prompts": "npx tsx scripts/import-json-prompts.ts", "db:generate-prompts-ts": "npx tsx scripts/import-json-prompts.ts generate", "db:direct-import": "npx tsx scripts/direct-import-prompts.ts", "db:remove-folders": "npx tsx scripts/remove-system-folders.ts", "db:import-preview": "npx tsx scripts/import-prompts-to-preview.ts", "db:reset-and-seed": "npx tsx scripts/reset-and-seed-prompts.ts", "db:reset-and-seed:dev": "npx tsx scripts/reset-and-seed-prompts.ts development", "db:reset-and-seed:preview": "npx tsx scripts/reset-and-seed-prompts.ts preview", "db:reset-and-seed:prod": "npx tsx scripts/reset-and-seed-prompts.ts production", "db:check-and-seed": "npx tsx scripts/check-and-seed-db.ts", "debug": "node --inspect-brk ./node_modules/next/dist/bin/next dev", "db:setup": "pnpm db:generate && pnpm db:migrate && pnpm db:check && pnpm db:check-and-seed", "db:setup:dev": "VERCEL_ENV=development pnpm db:setup", "db:setup:preview": "VERCEL_ENV=preview pnpm db:setup", "db:setup:production": "VERCEL_ENV=production pnpm db:setup", "db:migrate:pending": "tsx scripts/run-migration.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.0.6", "@ai-sdk/google": "^1.1.19", "@ai-sdk/openai": "1.3.18", "@ai-sdk/react": "^1.2.12", "@ant-design/cssinjs": "^1.23.0", "@ant-design/nextjs-registry": "^1.0.2", "@anthropic-ai/sdk": "^0.33.1", "@aws-sdk/client-lambda": "^3.772.0", "@aws-sdk/client-s3": "^3.802.0", "@aws-sdk/s3-request-presigner": "^3.802.0", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/state": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.35.3", "@eslint/compat": "^1.3.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@google/genai": "^1.4.0", "@hookform/resolvers": "^3.3.4", "@langchain/textsplitters": "^0.1.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.0", "@react-pdf/renderer": "^4.3.0", "@sentry/nextjs": "^9.35.0", "@stripe/stripe-js": "^7.2.0", "@types/axios": "^0.14.4", "@types/crypto-js": "^4.2.2", "@types/debug": "^4.1.12", "@types/jsonwebtoken": "^9.0.8", "@types/mixpanel-browser": "^2.60.0", "@types/unzipper": "^0.10.10", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.24.1", "@vercel/kv": "^3.0.0", "@vercel/postgres": "^0.10.0", "@vercel/speed-insights": "^1.1.0", "ahooks": "^3.8.5", "ai": "4.3.16", "antd": "^5.25.1", "axios": "^1.10.0", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "cross-spawn": "^7.0.6", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "debug": "^4.4.0", "diff-match-patch": "^1.0.5", "docx": "^9.1.1", "dotenv": "^16.4.5", "drizzle-orm": "0.34.0", "drizzle-zod": "0.5.1", "fast-deep-equal": "^3.1.3", "file-saver": "^2.0.5", "formidable": "^3.5.4", "framer-motion": "^11.11.10", "geist": "^1.3.1", "html-to-react": "^1.7.0", "jotai": "^2.12.4", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "lru-cache": "^11.1.0", "lucide-react": "^0.446.0", "marked": "^15.0.7", "mixpanel": "^0.18.1", "mixpanel-browser": "^2.65.0", "nanoid": "^5.1.5", "next": "15.3.4", "next-auth": "5.0.0-beta.25", "next-themes": "^0.3.0", "openai": "^4.97.0", "orderedmap": "^2.1.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^4.10.38", "postgres": "^3.4.3", "posthog-js": "^1.211.0", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.3", "react": "19.0.0-rc-45804af1-20241021", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.0.0-rc-45804af1-20241021", "react-hook-form": "^7.51.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-smartypants": "^3.0.2", "resend": "^4.1.2", "server-only": "^0.0.1", "sonner": "^1.5.0", "stripe": "^18.0.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "unzipper": "^0.12.3", "usehooks-ts": "^3.1.0", "uuid": "^11.1.0", "voyageai": "^0.0.4", "word-extractor": "^1.0.4", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@tailwindcss/typography": "^0.5.15", "@types/d3-scale": "^4.0.8", "@types/file-saver": "^2.0.7", "@types/formidable": "^3.4.5", "@types/lodash-es": "^4.17.12", "@types/marked": "^6.0.0", "@types/node": "^22.8.6", "@types/node-fetch": "^2.6.12", "@types/pdf-parse": "^1.1.4", "@types/pg": "8.10.9", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "chalk": "^4.1.2", "cross-env": "^7.0.3", "drizzle-kit": "0.25.0", "eslint": "^9.30.1", "eslint-config-next": "^15.3.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-tailwindcss": "^3.17.5", "node-fetch": "^3.3.2", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "^5.6.3"}, "overrides": {"@smithy/types": "^4.2.0", "brace-expansion": "^2.0.2", "undici": ">=5.29.0", "esbuild": ">=0.25.0", "nanoid": ">=5.0.9"}}