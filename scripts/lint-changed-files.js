#!/usr/bin/env node

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");
const chalk = require("chalk"); // For colorful console output

/**
 * Get a list of files changed since the last commit
 * @returns {string[]} Array of changed file paths
 */
function getChangedFiles() {
  try {
    // Get both staged and unstaged changes
    const stagedFiles = execSync("git diff --cached --name-only", {
      encoding: "utf-8",
    })
      .trim()
      .split("\n")
      .filter(Boolean);
    const unstagedFiles = execSync("git diff --name-only", {
      encoding: "utf-8",
    })
      .trim()
      .split("\n")
      .filter(Boolean);

    // Combine and remove duplicates
    return [...new Set([...stagedFiles, ...unstagedFiles])];
  } catch (error) {
    console.error(chalk.red("Error getting changed files:"), error.message);
    return [];
  }
}

/**
 * Filter files to only include those that ESLint should check
 * @param {string[]} files List of file paths
 * @returns {string[]} Filtered list of file paths
 */
function filterLintableFiles(files) {
  // Only check JavaScript, TypeScript, JSX, and TSX files
  const extensions = [".js", ".jsx", ".ts", ".tsx"];
  return files.filter((file) => {
    // Check if the file exists
    if (!fs.existsSync(file)) {
      console.log(chalk.yellow(`Warning: File ${file} does not exist, skipping.`));
      return false;
    }

    const ext = path.extname(file);
    return extensions.includes(ext);
  });
}

/**
 * Run ESLint on the specified files
 * @param {string[]} files List of file paths to lint
 * @returns {Object} Object containing lint results
 */
function runEslint(files) {
  if (files.length === 0) {
    return { success: true, output: "", errorFiles: [] };
  }

  try {
    // Run ESLint with the --format json flag to get structured output
    const filesString = files.join(" ");
    const output = execSync(`npx eslint ${filesString} --format json`, {
      encoding: "utf-8",
    });
    const results = JSON.parse(output);

    // Check if there are any errors
    const hasErrors = results.some(
      (result) => result.errorCount > 0 || result.fatalErrorCount > 0
    );

    // Get list of files with errors
    const errorFiles = results
      .filter((result) => result.errorCount > 0 || result.fatalErrorCount > 0)
      .map((result) => result.filePath);

    return {
      success: !hasErrors,
      output: results,
      errorFiles,
    };
  } catch (error) {
    // If ESLint throws an error, it means there are linting errors
    try {
      // Try to parse the output as JSON
      const output = error.stdout.toString();
      const results = JSON.parse(output);

      const errorFiles = results
        .filter((result) => result.errorCount > 0 || result.fatalErrorCount > 0)
        .map((result) => result.filePath);

      return {
        success: false,
        output: results,
        errorFiles,
      };
    } catch (parseError) {
      // If we can't parse the output, just return the error
      return {
        success: false,
        output: error.message,
        errorFiles: [],
      };
    }
  }
}

/**
 * Format and display ESLint errors
 * @param {Object} lintResults ESLint results object
 */
function displayLintErrors(lintResults) {
  console.log(chalk.red.bold("\n🚨 ESLint errors found in changed files:"));

  // Check if output is an array before using forEach
  if (Array.isArray(lintResults.output)) {
    lintResults.output.forEach((result) => {
      if (result.errorCount > 0 || result.fatalErrorCount > 0) {
        console.log(chalk.yellow(`\n📁 ${result.filePath}`));

        result.messages.forEach((msg) => {
          if (msg.severity === 2) {
            // Error severity
            console.log(
              chalk.red(
                `  Line ${msg.line}, Col ${msg.column}: ${msg.message} (${msg.ruleId})`
              )
            );
          }
        });
      }
    });
  } else {
    // If output is not an array, just display it as a string
    console.log(chalk.red(lintResults.output));
  }

  console.log(chalk.red.bold("\nPlease fix these errors before building."));
}

// Main execution
console.log(chalk.blue("🔍 Checking for ESLint errors in changed files..."));

// Get changed files
const changedFiles = getChangedFiles();
console.log(chalk.blue(`Found ${changedFiles.length} changed files.`));

// Filter to only include lintable files
const filesToLint = filterLintableFiles(changedFiles);
console.log(
  chalk.blue(`Linting ${filesToLint.length} JavaScript/TypeScript files.`)
);

if (filesToLint.length === 0) {
  console.log(
    chalk.green(
      "✅ No JavaScript/TypeScript files have been changed. Proceeding with build."
    )
  );
  process.exit(0);
}

// Run ESLint on changed files
const lintResults = runEslint(filesToLint);

if (!lintResults.success) {
  displayLintErrors(lintResults);
  process.exit(1); // Exit with error code to fail the build
} else {
  console.log(
    chalk.green(
      "✅ No ESLint errors found in changed files. Proceeding with build."
    )
  );
  process.exit(0);
}
