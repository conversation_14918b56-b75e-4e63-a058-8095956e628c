#!/usr/bin/env tsx
import * as dotenv from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "../lib/db/schema";
import { plan, subscription, user } from "../lib/db/schema";
import { desc, eq } from "drizzle-orm";
import chalk from "chalk";

dotenv.config();

const env = "development";
const envKey = `${env}_POSTGRES_URL`;
const postgresUrl = process.env[envKey];

if (!postgresUrl) {
  throw new Error(`No ${envKey} provided in .env`);
}

const client = postgres(postgresUrl, { max: 1 });
const db = drizzle(client, { schema });

async function listPlans() {
  try {
    console.log(chalk.blue(`🔍 Fetching plans from database (${env} environment)...\n`));
    
    // Query all plans
    const plans = await db.select().from(plan).orderBy(desc(plan.createdAt));
    
    if (plans.length === 0) {
      console.log(chalk.yellow("No plans found in the database."));
      return;
    }
    
    console.log(chalk.green(`Found ${plans.length} plans:`));
    console.log(chalk.green("=".repeat(80)));
    
    // Print each plan with formatting
    plans.forEach((p, index) => {
      console.log(chalk.white(`[${index + 1}] ${p.name}`));
      console.log(chalk.cyan(`    ID: ${p.id}`));
      console.log(chalk.cyan(`    Type: ${p.type || 'N/A'}`));
      console.log(chalk.cyan(`    Description: ${p.description || 'N/A'}`));
      console.log(chalk.cyan(`    Price: ${p.price || 'N/A'} (${p.interval || 'N/A'})`));
      console.log(chalk.cyan(`    Stripe Price ID: ${p.stripePriceId || 'N/A'}`));
      console.log(chalk.cyan(`    Active: ${p.isActive ? 'Yes' : 'No'}`));
      console.log(chalk.cyan(`    Created: ${p.createdAt?.toLocaleString()}`));
      console.log(chalk.cyan(`    Updated: ${p.updatedAt?.toLocaleString()}`));
      
      if (p.features) {
        console.log(chalk.cyan(`    Features: ${JSON.stringify(p.features, null, 2)}`));
      }
      
      console.log(chalk.green("-".repeat(80)));
    });
    
  } catch (error) {
    console.error(chalk.red("Failed to fetch plans:"), error);
    throw error;
  }
}

async function listSubscriptions() {
  try {
    console.log(chalk.blue(`\n🔍 Fetching subscriptions from database (${env} environment)...\n`));
    
    // Query all subscriptions with user emails
    const subscriptions = await db
      .select({
        subscription: subscription,
        userEmail: user.email,
      })
      .from(subscription)
      .leftJoin(user, eq(subscription.userId, user.id))
      .orderBy(desc(subscription.createdAt));
    
    if (subscriptions.length === 0) {
      console.log(chalk.yellow("No subscriptions found in the database."));
      return;
    }
    
    console.log(chalk.magenta(`Found ${subscriptions.length} subscriptions:`));
    console.log(chalk.magenta("=".repeat(80)));
    
    // Print each subscription with formatting
    subscriptions.forEach((sub, index) => {
      console.log(chalk.white(`[${index + 1}] Subscription`));
      console.log(chalk.cyan(`    ID: ${sub.subscription.id}`));
      console.log(chalk.cyan(`    User ID: ${sub.subscription.userId}`));
      console.log(chalk.cyan(`    User Email: ${sub.userEmail || 'Unknown'}`));
      console.log(chalk.cyan(`    Plan ID: ${sub.subscription.planId}`));
      console.log(chalk.cyan(`    Status: ${sub.subscription.status}`));
      console.log(chalk.cyan(`    Start Date: ${sub.subscription.startDate?.toLocaleString()}`));
      console.log(chalk.cyan(`    End Date: ${sub.subscription.endDate?.toLocaleString() || 'N/A'}`));
      console.log(chalk.cyan(`    Billing Cycle: ${sub.subscription.billingCycle || 'N/A'}`));
      console.log(chalk.cyan(`    Payment Method: ${sub.subscription.paymentMethod || 'N/A'}`));
      console.log(chalk.cyan(`    Payment ID: ${sub.subscription.paymentId || 'N/A'}`));
      console.log(chalk.cyan(`    Auto Renew: ${sub.subscription.autoRenew ? 'Yes' : 'No'}`));
      console.log(chalk.cyan(`    Created: ${sub.subscription.createdAt?.toLocaleString()}`));
      console.log(chalk.cyan(`    Updated: ${sub.subscription.updatedAt?.toLocaleString()}`));
      
      console.log(chalk.magenta("-".repeat(80)));
    });
    
  } catch (error) {
    console.error(chalk.red("Failed to fetch subscriptions:"), error);
    throw error;
  }
}

// Execute the functions
async function main() {
  try {
    await listPlans();
    await listSubscriptions();
  } catch (error) {
    console.error(chalk.red("Unhandled error:"), error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(chalk.red("Unhandled error:"), error);
    process.exit(1);
  });