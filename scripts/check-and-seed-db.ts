import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";
import * as dotenv from "dotenv";
import { sql } from "drizzle-orm";
import { Logger } from "../lib/utils/Logger";
import { getPostgresUrl } from "../lib/utils/env";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

dotenv.config();

const postgresUrl = getPostgresUrl();

if (!postgresUrl) {
  throw new Error("No POSTGRES_URL provided in .env");
}

const migrationClient = postgres(postgresUrl, { max: 1 });

async function checkAndSeedDatabase() {
  try {
    const db = drizzle(migrationClient);
    const env = process.env.VERCEL_ENV || "development";
    Logger.info(`Running in ${env} environment`);

    // Step 1: Check if the database has the necessary tables
    Logger.info("Checking if database tables exist...");

    try {
      // Try to query the prompt_folders table to see if it exists
      await db.execute(sql`SELECT COUNT(*) FROM prompt_folders`);
      Logger.info("prompt_folders table exists");
    } catch (error) {
      // If the table doesn't exist, run migrations
      Logger.info("prompt_folders table does not exist, running migrations...");

      // First enable pgvector extension and setup similarity function
      await db.execute(sql`
        CREATE EXTENSION IF NOT EXISTS vector;

        CREATE OR REPLACE FUNCTION cosine_similarity(a vector, b vector)
        RETURNS float
        LANGUAGE plpgsql
        AS $$
        BEGIN
            -- Ensure vectors are normalized
            RETURN 1 - (a <=> b);
        END;
        $$;
      `);

      // Then run the regular migrations
      await migrate(db, {
        migrationsFolder: `./lib/db/migrations/${env}`,
      });

      // Create the vector search index if needed
      try {
        await db.execute(sql`
          CREATE INDEX IF NOT EXISTS embeddings_embedding_idx
          ON "embeddings"
          USING ivfflat (embedding vector_cosine_ops)
          WITH (lists = 100);
        `);
      } catch (indexError) {
        // If the embeddings table doesn't exist yet, that's okay
        Logger.warn("Could not create vector index, embeddings table may not exist yet:", indexError);
      }

      Logger.info("Migrations completed successfully");
    }

    // Step 2: Check if system folders exist and seed if needed
    Logger.info("Checking if system folders exist...");

    try {
      const result = await db.execute(sql`
        SELECT COUNT(*) FROM prompt_folders WHERE is_system = true
      `);

      // Log the result structure to understand its format
      Logger.info("Query result structure:", JSON.stringify(result));

      // Handle different possible result formats
      let count = 0;

      // Check if result is an array (some drivers return array directly)
      if (Array.isArray(result) && result.length > 0) {
        const firstRow = result[0];
        if (firstRow && typeof firstRow === 'object') {
          if ('count' in firstRow) {
            count = parseInt(String(firstRow.count), 10);
          } else if ('COUNT' in firstRow) {
            count = parseInt(String(firstRow.COUNT), 10);
          } else if ('count(*)' in firstRow) {
            count = parseInt(String(firstRow['count(*)']), 10);
          } else {
            // If we can't find a named property, try the first value
            const firstValue = Object.values(firstRow)[0];
            if (firstValue !== undefined) {
              count = parseInt(String(firstValue), 10);
            }
          }
        }
      }
      // Check if result has a rows property (some drivers use this structure)
      else if (result && typeof result === 'object' && 'rows' in result && Array.isArray(result.rows) && result.rows.length > 0) {
        const firstRow = result.rows[0];
        if (firstRow && typeof firstRow === 'object') {
          if ('count' in firstRow) {
            count = parseInt(String(firstRow.count), 10);
          } else if ('COUNT' in firstRow) {
            count = parseInt(String(firstRow.COUNT), 10);
          } else if ('count(*)' in firstRow) {
            count = parseInt(String(firstRow['count(*)']), 10);
          } else {
            // If we can't find a named property, try the first value
            const firstValue = Object.values(firstRow)[0];
            if (firstValue !== undefined) {
              count = parseInt(String(firstValue), 10);
            }
          }
        }
      }

      Logger.info(`System folder count: ${count}`);

      if (count === 0) {
        Logger.info("No system folders found, running seed script...");
        await execAsync("npx tsx scripts/seed-prompts.ts");
        Logger.info("Seed script completed successfully");
      } else {
        Logger.info(`Found ${count} system folders, checking if they need updating...`);
        await execAsync("npx tsx scripts/seed-prompts.ts");
        Logger.info("Seed script check completed");
      }
    } catch (error) {
      Logger.error("Error checking system folders:", error);
      throw error;
    }

    Logger.info("Database check and seed completed successfully");
    await migrationClient.end();
    return true;
  } catch (error) {
    Logger.error("Database check and seed failed:", error);
    await migrationClient.end().catch(console.error);
    throw error;
  }
}

// Run the function if this script is executed directly
if (require.main === module) {
  checkAndSeedDatabase()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error("Error:", error);
      process.exit(1);
    });
}

export { checkAndSeedDatabase };
