import { db } from '../lib/db/db';
import { promptFolders, prompts } from '../lib/db/schema';
import { STARTER_PROMPTS } from '../lib/ai/starter-prompts';
import { eq } from 'drizzle-orm';
import { Logger } from '../lib/utils/Logger';

/**
 * Seed system folders and starter prompts
 */
async function seedSystemFolders() {
  try {
    Logger.info('Starting to seed system folders and prompts...');
    
    // Check if system folders already exist
    const existingFolders = await db
      .select()
      .from(promptFolders)
      .where(eq(promptFolders.isSystem, true));
    
    if (existingFolders.length > 0) {
      Logger.info(`Found ${existingFolders.length} existing system folders, checking if they need updating...`);
      
      // Check if all categories from STARTER_PROMPTS exist as system folders
      const existingFolderNames = existingFolders.map(folder => folder.name);
      const missingCategories = STARTER_PROMPTS.filter(
        category => !existingFolderNames.includes(category.category)
      );
      
      if (missingCategories.length === 0) {
        Logger.info('All system folders already exist, checking prompts...');
        
        // Create a map of folder names to IDs for easier lookup
        const folderMap = new Map(existingFolders.map(folder => [folder.name, folder.id]));
        
        // Check if all prompts exist in each folder
        for (const category of STARTER_PROMPTS) {
          const folderId = folderMap.get(category.category);
          if (!folderId) continue; // Should not happen based on previous check
          
          // Get existing prompts for this folder
          const existingPrompts = await db
            .select()
            .from(prompts)
            .where(eq(prompts.folderId, folderId));
          
          const existingPromptTitles = existingPrompts.map(prompt => prompt.title);
          const missingPrompts = category.prompts.filter(
            prompt => !existingPromptTitles.includes(prompt.title)
          );
          
          if (missingPrompts.length > 0) {
            Logger.info(`Adding ${missingPrompts.length} missing prompts to folder "${category.category}"`);
            
            // Add missing prompts
            for (const prompt of missingPrompts) {
              await db
                .insert(prompts)
                .values({
                  folderId,
                  title: prompt.title,
                  content: prompt.defaultText,
                  isFavorite: false,
                });
            }
          }
        }
        
        Logger.info('System folders and prompts are up to date');
        return;
      }
      
      // If we have some folders but not all, add the missing ones
      Logger.info(`Adding ${missingCategories.length} missing system folders...`);
      for (const category of missingCategories) {
        await createFolderWithPrompts(category);
      }
      
      Logger.info('Successfully added missing system folders and prompts');
      return;
    }
    
    // No system folders exist, create all of them
    Logger.info('No system folders found, creating all system folders and prompts...');
    
    for (const category of STARTER_PROMPTS) {
      await createFolderWithPrompts(category);
    }
    
    Logger.info('Successfully seeded all system folders and prompts');
  } catch (error) {
    Logger.error('Error seeding system folders:', error);
    throw error;
  }
}

/**
 * Helper function to create a folder with its prompts
 */
async function createFolderWithPrompts(category: typeof STARTER_PROMPTS[0]) {
  // Create the system folder
  const [folder] = await db
    .insert(promptFolders)
    .values({
      name: category.category,
      isSystem: true,
      userId: null, // System folders don't belong to a specific user
    })
    .returning();
  
  Logger.info(`Created system folder "${category.category}" with ID ${folder.id}`);
  
  // Add prompts from this category to the folder
  for (const prompt of category.prompts) {
    await db
      .insert(prompts)
      .values({
        folderId: folder.id,
        title: prompt.title,
        content: prompt.defaultText,
        isFavorite: false,
      });
  }
  
  Logger.info(`Added ${category.prompts.length} prompts to folder "${category.category}"`);
}

// Run the seed function
async function main() {
  try {
    await seedSystemFolders();
    process.exit(0);
  } catch (error) {
    Logger.error('Seed script failed:', error);
    process.exit(1);
  }
}

main();
