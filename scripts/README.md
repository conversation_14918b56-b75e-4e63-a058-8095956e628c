# Pre-build Lint Checker

This directory contains scripts that run as part of the build process.

## lint-changed-files.js

This script runs before the build process and checks for ESLint errors in files that have been changed since the last commit. It follows these steps:

1. Gets a list of all files changed since the last commit (both staged and unstaged changes)
2. Filters the list to only include JavaScript and TypeScript files
3. Runs ESLint on those files
4. If there are any errors, it displays them and fails the build
5. If there are no errors, it allows the build to proceed

### How it works

The script is configured to run automatically before the build process via the `prebuild` script in `package.json`. When you run `npm run build` or `pnpm build`, this script will run first.

### Benefits

This approach allows for incremental improvement of your codebase:

- New changes must meet ESLint standards
- Existing ESLint errors in unchanged files won't block the build
- You can gradually fix ESLint errors in your codebase without having to fix them all at once

### Requirements

- Git (to get the list of changed files)
- ESLint (to check for errors)
- chalk (for colorful console output)

### Customization

You can customize the script by:

- Modifying the file extensions it checks
- Changing how it determines which files have changed
- Adjusting the ESLint configuration it uses
- Modifying the error display format

### Troubleshooting

If you're having issues with the script:

1. Make sure you have Git installed and that you're in a Git repository
2. Ensure ESLint is properly configured in your project
3. Check that the chalk dependency is installed
4. Try running ESLint manually on specific files to see if there are any issues
