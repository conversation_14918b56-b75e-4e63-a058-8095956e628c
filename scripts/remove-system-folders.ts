import { Logger } from '../lib/utils/Logger';
import { promptFolders, prompts } from '../lib/db/schema';
import { inArray, sql } from 'drizzle-orm';

/**
 * Removes specific system folders and all their prompts
 */

// ### 1. Remove Default Folders

// To remove the default folders ("Legal Research", "Litigation", and "Corporate & Contracts"):

// ```bash
// pnpm db:remove-folders
// ```

// ### 2. Remove Specific Folders

// To remove specific folders, provide their names as arguments:

// ```bash
// pnpm db:remove-folders "Folder Name 1" "Folder Name 2" "Folder Name 3"
// ```

async function removeSystemFolders(folderNames: string[]) {
  try {
    // Dynamically import the database to avoid connection errors
    const { db } = await import('../lib/db/db');

    Logger.info(`Attempting to remove system folders: ${folderNames.join(', ')}`);

    // Find the folders to remove
    const foldersToRemove = await db
      .select()
      .from(promptFolders)
      .where(
        inArray(promptFolders.name, folderNames)
      );

    // Filter to only include system folders
    const systemFoldersToRemove = foldersToRemove.filter(
      (folder: { isSystem: boolean }) => folder.isSystem === true
    );

    if (systemFoldersToRemove.length === 0) {
      Logger.info('No matching system folders found');
      return;
    }

    Logger.info(`Found ${systemFoldersToRemove.length} matching system folders`);

    // Get the folder IDs
    const folderIds = systemFoldersToRemove.map((folder: { id: string }) => folder.id);

    // Count prompts in these folders
    const promptCount = await db
      .select({ count: sql`count(*)` })
      .from(prompts)
      .where(inArray(prompts.folderId, folderIds));

    Logger.info(`These folders contain ${promptCount[0].count} prompts that will be removed`);

    // Delete prompts in these folders
    await db
      .delete(prompts)
      .where(inArray(prompts.folderId, folderIds));

    Logger.info(`Deleted prompts from folders`);

    // Delete the folders
    await db
      .delete(promptFolders)
      .where(inArray(promptFolders.id, folderIds));

    Logger.info(`Successfully removed ${systemFoldersToRemove.length} system folders and their prompts`);

    // Log the names of removed folders
    for (const folder of systemFoldersToRemove) {
      Logger.info(`Removed folder: ${folder.name}`);
    }

    return systemFoldersToRemove;
  } catch (error) {
    Logger.error('Error removing system folders:', error);
    throw error;
  }
}

// If this script is run directly (not imported)
if (require.main === module) {
  // Default folders to remove
  const defaultFoldersToRemove = [
    'Legal Research',
    'Litigation',
    'Corporate & Contracts'
  ];

  // Get folders from command line arguments or use defaults
  const foldersToRemove = process.argv.slice(2).length > 0
    ? process.argv.slice(2)
    : defaultFoldersToRemove;

  removeSystemFolders(foldersToRemove)
    .then(() => {
      Logger.info('Removal completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      Logger.error('Removal failed:', error);
      process.exit(1);
    });
}

export { removeSystemFolders };
