#!/usr/bin/env tsx
import * as dotenv from 'dotenv'
import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import * as schema from '../lib/db/schema'
import { plan, subscription, user } from '../lib/db/schema'
import { desc, eq, isNull } from 'drizzle-orm'
import chalk from 'chalk'

dotenv.config()

const env = 'development'
const envKey = `${env}_POSTGRES_URL`
const postgresUrl = process.env[envKey]

if (!postgresUrl) {
    throw new Error(`No ${envKey} provided in .env`)
}

const client = postgres(postgresUrl, { max: 1 })
const db = drizzle(client, { schema })

async function insertPlans() {
    try {
        console.log(
            chalk.blue(
                `🔍 Inserting plans from database (${env} environment)...\n`,
            ),
        )

        console.log(chalk.yellow('No plans found in the database.'))
        const insertPlans = await db
            .insert(plan)
            .values([
                {
                    name: 'free',
                    type: 'free',
                    description: 'Free plan',
                    price: '0',
                    isActive: true,
                    interval: 'month',
                    stripePriceId: 'free',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                {
                    name: 'premium',
                    type: 'premium',
                    description: 'Premium plan',
                    price: '24.99',
                    interval: 'month',
                    isActive: true,
                    stripePriceId: 'price_1RH4hlCGYxT6u6V5UWNvJ8HW',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            ])
            .returning()
        console.log(chalk.green('Plans inserted successfully'))
    } catch (error) {
        console.error(chalk.red('Failed to fetch users:'), error)
        throw error
    }
}
// Execute the functions
async function main() {
    try {
        await insertPlans()
    } catch (error) {
        console.error(chalk.red('Unhandled error:'), error)
        process.exit(1)
    } finally {
        await client.end()
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(chalk.red('Unhandled error:'), error)
        process.exit(1)
    })
