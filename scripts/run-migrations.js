// Script to run database migrations
const { execSync } = require('child_process');
const path = require('path');

// Get the environment from command line arguments or default to development
const env = process.argv[2] || 'development';
const validEnvs = ['development', 'preview', 'production'];

if (!validEnvs.includes(env)) {
  console.error(`Invalid environment: ${env}. Must be one of: ${validEnvs.join(', ')}`);
  process.exit(1);
}

console.log(`Running migrations for ${env} environment...`);

try {
  // Run the migration
  execSync(`pnpm db:migrate:${env}`, { stdio: 'inherit' });
  console.log(`Migrations for ${env} environment completed successfully!`);
} catch (error) {
  console.error(`Error running migrations for ${env} environment:`, error.message);
  process.exit(1);
}
