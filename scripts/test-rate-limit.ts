#!/usr/bin/env tsx
import * as dotenv from "dotenv";
import chalk from "chalk";
import { Logger } from "../lib/utils/Logger";

// Load environment variables
dotenv.config();


import { GoogleAIService } from "../lib/services/GoogleAIService";
// console log all env variables in process.env for debugging
console.log("Loaded environment variables:", Object.keys(process.env)
  .sort()
  .forEach((key) => {
    console.log(`${key} = ${process.env[key]}`);
  }));

// Override console methods to add timestamps
const originalConsole = { ...console };
type ConsoleMethodName = 'log' | 'info' | 'warn' | 'error' | 'debug';
const methods: ConsoleMethodName[] = ["log", "info", "warn", "error", "debug"];

methods.forEach(method => {
  console[method] = (...args: any[]) => {
    const timestamp = new Date().toISOString();
    originalConsole[method](chalk.gray(`[${timestamp}]`), ...args);
  };
});

export function countTokens(text: string): number {
  return Math.ceil(text.length / 4);
}
// Constants for testing
const MODEL = "gemini-2.5-pro-preview-03-25";
const TOKEN_LIMIT_PER_MINUTE = 2000000; // 2 Million tokens/minute rate limit
const TOKEN_LIMIT_PER_REQUEST = 1000000; // 1 Million tokens per request limit

/**
 * Generates a large legal prompt by repeating a base prompt to reach desired token count
 */
function generateLargePrompt(approxTokenCount: number): string {
  // Base legal question prompt
  const baseLegalPrompt = `
I need legal advice regarding a complex merger and acquisition situation. Our company is considering acquiring a competitor in the same industry. The target company operates in multiple jurisdictions including California, New York, Texas, and internationally in Canada and the UK. 

The target company has approximately 500 employees, $50 million in annual revenue, and holds several patents related to our industry. We're concerned about potential antitrust issues, employee retention agreements, intellectual property transfer complications, and tax implications of the acquisition.

Specifically, I'd like to understand:
1. What antitrust review thresholds might we trigger at both federal and state levels?
2. How should we structure the deal to minimize tax liability?
3. What are the key considerations for handling the international aspects of this acquisition?
4. What due diligence should we prioritize given the IP portfolio?
5. What are the major regulatory filings we'll need to prepare?

Please provide a comprehensive analysis of the legal considerations and potential strategies for this acquisition.
`;

  // Calculate tokens in base prompt
  const baseTokenCount = countTokens(baseLegalPrompt);
  
  // Calculate how many repetitions we need
  const repetitionsNeeded = Math.ceil(approxTokenCount / baseTokenCount);
  
  // Create the large prompt by repeating the base prompt with variations
  let result = "";
  
  // List of company sizes, revenues, and jurisdictions to vary
  const companySizes = [500, 250, 1000, 750, 1500, 300, 2000, 100, 5000];
  const revenues = ["$50 million", "$25 million", "$100 million", "$75 million", "$200 million", "$10 million", "$500 million"];
  const jurisdictions = [
    "California, New York, Texas, and internationally in Canada and the UK",
    "Florida, Illinois, Washington, and internationally in Germany and France",
    "Massachusetts, Pennsylvania, Ohio, and internationally in Japan and Australia",
    "Texas, Georgia, Michigan, and internationally in Brazil and Mexico",
    "New York, California, Colorado, and internationally in India and Singapore",
    "Washington, Oregon, Nevada, and internationally in South Korea and Spain",
    "Illinois, Arizona, New Jersey, and internationally in Italy and China"
  ];
  const industries = [
    "technology", "healthcare", "manufacturing", "financial services", 
    "retail", "energy", "telecommunications", "pharmaceuticals", 
    "automotive", "aerospace", "food and beverage", "media and entertainment"
  ];
  
  for (let i = 0; i < repetitionsNeeded; i++) {
    // Select random variations for this repetition
    const companySize = companySizes[i % companySizes.length];
    const revenue = revenues[i % revenues.length];
    const jurisdiction = jurisdictions[i % jurisdictions.length];
    const industry = industries[i % industries.length];
    
    // Create a slightly varied version of the prompt
    const variedPrompt = `
I need legal advice regarding a complex merger and acquisition situation in the ${industry} sector. Our company is considering acquiring a competitor in the same industry. The target company operates in multiple jurisdictions including ${jurisdiction}. 

The target company has approximately ${companySize} employees, ${revenue} in annual revenue, and holds several patents related to our industry. We're concerned about potential antitrust issues, employee retention agreements, intellectual property transfer complications, and tax implications of the acquisition.

Specifically, I'd like to understand:
1. What antitrust review thresholds might we trigger at both federal and state levels?
2. How should we structure the deal to minimize tax liability?
3. What are the key considerations for handling the international aspects of this acquisition?
4. What due diligence should we prioritize given the IP portfolio?
5. What are the major regulatory filings we'll need to prepare?

Please provide a comprehensive analysis of the legal considerations and potential strategies for this acquisition. This is request number ${i+1} of my analysis.
`;
    
    result += `\n\nLEGAL QUESTION ${i+1}:\n${variedPrompt}`;
  }
  
  return result;
}

/**
 * Simple test to trigger rate limit by making multiple requests
 */
async function testRateLimit() {
  try {
    console.log(chalk.blue("🚀 Starting Rate Limit Test"));
    console.log(chalk.yellow(`Testing with model: ${MODEL}`));
    console.log(chalk.yellow(`Rate limit: ${TOKEN_LIMIT_PER_MINUTE} tokens/minute`));
    
    // Generate a prompt with approximately 800K tokens
    const prompt = generateLargePrompt(800000);
    const tokenCount = countTokens(prompt);
    console.log(chalk.yellow(`Generated prompt with approximately ${tokenCount} tokens`));
    
    // Make 3 requests in parallel to exceed the 2M tokens/minute limit
    console.log(chalk.blue("\n🔍 Making 3 parallel requests to trigger rate limit"));
    
    const requests = Array(16).fill(0).map(async (_, i) => {
      console.log(chalk.yellow(`Starting request ${i+1}...`));
      
      try {
        // Initialize stream
        const streamPromise = GoogleAIService.generateContentStream(
          MODEL,
          prompt,
          {
            temperature: 0.7,
            maxOutputTokens: 1024,
          },
          true
        );
        
        console.log(chalk.green(`✅ Request ${i+1} initialization successful`));
        const stream = await streamPromise;
        console.log(chalk.blue(`🔄 Request ${i+1} stream promise resolved`));
        
        // Consume stream
        let text = "";
        try {
          for await (const chunk of stream) {
            if (chunk.text) {
              if (!text){
                console.log(chalk.blue(`🔄 Request ${i+1} first chunk received`));
              }
              const chunkText = chunk.text;
              text += chunkText;
            }
          }
          console.log(chalk.green(`✅ Request ${i+1} consumption successful`));
          return { id: i+1, success: true, phase: "complete" };
        } catch (error) {
          console.error(chalk.red(`❌ Request ${i+1} consumption failed:`), error);
          return { id: i+1, success: false, phase: "consumption", error };
        }
      } catch (error) {
        console.error(chalk.red(`❌ Request ${i+1} initialization failed:`), error);
        return { id: i+1, success: false, phase: "initialization", error };
      }
    });
    
    // Wait for all requests to complete
    const results = await Promise.all(requests);
    
    // Print summary
    console.log(chalk.blue("\n📊 Results Summary:"));
    results.forEach(result => {
      if (result.success) {
        console.log(chalk.green(`✅ Request ${result.id}: Successful`));
      } else {
        console.log(chalk.red(`❌ Request ${result.id}: Failed during ${result.phase}`));
        console.log(chalk.red(`   Error: ${result.error}`));
      }
    });
    
    // Count errors by phase
    const initErrors = results.filter(r => !r.success && r.phase === "initialization").length;
    const consumptionErrors = results.filter(r => !r.success && r.phase === "consumption").length;
    
    console.log(chalk.blue("\n🔍 Error Analysis:"));
    console.log(chalk.yellow(`- Errors during initialization: ${initErrors}`));
    console.log(chalk.yellow(`- Errors during consumption: ${consumptionErrors}`));
    
    if (initErrors > 0) {
      console.log(chalk.cyan("\nConclusion: Rate limit errors can occur during stream initialization"));
    }
    
    if (consumptionErrors > 0) {
      console.log(chalk.cyan("\nConclusion: Rate limit errors can occur during stream consumption"));
    }
    
    if (initErrors === 0 && consumptionErrors === 0) {
      console.log(chalk.yellow("\nNo rate limit errors detected. Try increasing the token count or number of requests."));
    }
    
  } catch (error) {
    console.error(chalk.red("\n❌ Test failed with unexpected error:"), error);
  }
}

// Execute the function
testRateLimit()
  .then(() => {
    console.log(chalk.green("\n✅ Rate limit test completed"));
    process.exit(0);
  })
  .catch((error) => {
    console.error(chalk.red("\n❌ Rate limit test failed:"), error);
    process.exit(1);
  });




