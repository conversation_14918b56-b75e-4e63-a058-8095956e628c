{"extends": ["next/core-web-vitals", "plugin:import/recommended", "plugin:import/typescript", "prettier", "plugin:tailwindcss/recommended"], "plugins": ["tailwindcss", "@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "rules": {"tailwindcss/no-custom-classname": "off", "tailwindcss/classnames-order": "off", "@typescript-eslint/no-unused-vars": "warn", "no-unused-vars": "off"}, "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true}}}, "ignorePatterns": ["**/components/ui/**", "**/componentsV2/ui/**"]}