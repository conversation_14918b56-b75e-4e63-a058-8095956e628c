"use client";
import { createContext, useContext, ReactNode } from 'react';
import { User } from 'next-auth'

// Create the context with a default value of null
export const UserContext = createContext<User | undefined>(undefined);

// Create a provider component
export function UserProvider({ children, user }: { children: ReactNode; user: User | undefined }) {
  return <UserContext.Provider value={user}>{children}</UserContext.Provider>;
}

// Create a custom hook for consuming the context
export function useUser() {
  const context = useContext(UserContext);
  return context;
}