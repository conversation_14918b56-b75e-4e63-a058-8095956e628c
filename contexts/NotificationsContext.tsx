"use client";
import React, { createContext, useContext, useState, useEffect } from "react";
import {
  Notification,
  NotificationType,
} from "@/components/notifications/types";
import {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
} from "@/lib/actions/notifications";
import { toast } from "sonner";
import { usePathname } from "next/navigation";

interface NotificationsContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  filterByType: (type: NotificationType | "all") => Notification[];
  activeFilter: NotificationType | "all";
  setActiveFilter: (filter: NotificationType | "all") => void;
  isLoading: boolean;
  refreshNotifications: () => Promise<void>;
  isRefreshing: boolean;
}

const NotificationsContext = createContext<
  NotificationsContextType | undefined
>(undefined);

export const NotificationsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeFilter, setActiveFilter] = useState<NotificationType | "all">(
    "all"
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const pathname = usePathname();

  const fetchNotifications = async () => {
    try {
      setIsRefreshing(true)
      const data = await getNotifications();
      setNotifications(
        data.map((notification) => ({
          id: notification.id,
          title: notification.title,
          description: notification.description,
          type: notification.type as NotificationType,
          timestamp: notification.createdAt.toISOString(),
          isRead: notification.isRead ?? false,
          link: notification.link ?? "",
        }))
      );
    } catch (error) {
      setIsRefreshing(false);
      console.error("Error fetching notifications:", error);
      // toast.error("Failed to load notifications");
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchNotifications();

    // const interval = setInterval(() => {
    //   console.log("Polling notifications...");
    //   fetchNotifications();
    // }, 60000);
    // return () => clearInterval(interval);

  }, [pathname]);

  const unreadCount = notifications.filter(
    (notification) => !notification.isRead
  ).length;

  const handleMarkAsRead = async (id: string) => {
    try {
      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === id
            ? { ...notification, isRead: true }
            : notification
        )
      );
      await markNotificationAsRead(id);
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error("Failed to mark notification as read");
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      setNotifications((prev) =>
        prev.map((notification) => ({ ...notification, isRead: true }))
      );
      await markAllNotificationsAsRead();
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast.error("Failed to mark all notifications as read");
    }
  };

  const filterByType = (type: NotificationType | "all") => {
    if (type === "all") return notifications;
    return notifications.filter((notification) => notification.type === type);
  };

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead: handleMarkAsRead,
        markAllAsRead: handleMarkAllAsRead,
        filterByType,
        activeFilter,
        setActiveFilter,
        isLoading,
        refreshNotifications: fetchNotifications,
        isRefreshing
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationsContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationsProvider"
    );
  }
  return context;
};
