
import { getObject, copyObject } from './s3-client.js'
import { hash } from 'hasha'
import { get } from 'lodash-es'

export const handler = async (event) => {
  console.log(`Event: ${JSON.stringify(event, null, 2)}`)

  const record = event.Records[0]
  if (!record || record.eventName !== 'ObjectCreated:Put' || !record.s3.object.key) {
    return {
      statusCode: 400,
      body: JSON.stringify('No record found in event'),
    }
  }

  const { key, size } = record.s3.object
  const { name: bucketName } = record.s3.bucket
  const [type, env, date, id] = key.split('/')

  if (type !== 'temporary') {
    return {
      statusCode: 400,
    }
  }

  const s3Object = await getObject(bucketName, key)  
  
  const sha256 = await hash(s3Object.Body, {
    algorithm: 'sha256',
    encoding: 'hex'
  })

  console.log(`env: ${env}`)
  console.log(`date: ${date}`)
  console.log(`id: ${id}`)
  console.log(`bucketName: ${bucketName}`)
  console.log(`key: ${key}`)
  console.log(`size: ${size}`)
  console.log(`sha256: ${sha256}`)

  console.log('mimeType', s3Object.ContentType)
  // save to target bucket
  const storageKey = `default/${env}/${sha256.slice(0, 2)}/${sha256.slice(2,4)}/${sha256.slice(4)}`
  const targetBucket = process.env.TARGET_BUCKET ?? 'iqidis-artifact'
  await copyObject(bucketName, key, targetBucket, storageKey)

  // notify the server
  const PROTOCOL = process.env.PROTOCOL ?? 'https'
  const HOST = get(process.env, `${env.toUpperCase()}_HOST`, process.env.HOST)
  const PATH_UPLOADED = process.env.PATH_UPLOADED

  const payload = {
    id,
    storageKey,
    mime: s3Object.ContentType,
    sizeByte: size,
    contentHash: sha256,
    metadata: {
      bucketName,
      region: record?.awsRegion ?? process.env.AWS_REGION ?? 'us-east-1',
      storageProvider: 'S3'
    }
  }

  console.log(`${PROTOCOL}://${HOST}/${PATH_UPLOADED}`)
  console.log('payload', payload)
  const res = await fetch(`${PROTOCOL}://${HOST}/${PATH_UPLOADED}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-vercel-protection-bypass': process.env.VERCEL_AUTOMATION_BYPASS_SECRET ?? 'not-set'
    },
    body: JSON.stringify(payload)
  })

  console.log('update success', await res.json())

  const response = {
    statusCode: 200,
    body: JSON.stringify('success'),
  };
  return response;

}