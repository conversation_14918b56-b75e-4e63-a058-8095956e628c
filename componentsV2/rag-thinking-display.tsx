"use client";

import { useState, useEffect } from "react";
import { Logger } from "@/lib/utils/Logger";
import { cn } from "@/lib/utils";
import { CheckCircle, Eye, EyeOff } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LogoIqidis, SparklingSparkleIcon } from "./icons";
import { motion, AnimatePresence } from "framer-motion";

interface ThinkingStep {
  id: number;
  text: string;
  completed: boolean;
}

interface ThoughtItem {
  text: string;
}

interface RagThinkingDisplayProps {
  chatId: string;
  isLoading: boolean;
  isProcessing: boolean;
  query: string;
  rewrittenQuery?: string;
  hideThinking: boolean;
}

// Define steps once to reuse
const THINKING_STEPS: Omit<ThinkingStep, "completed">[] = [
  { id: 1, text: "Analyzing your question" },
  { id: 2, text: "Gathering relevant information" },
  { id: 3, text: "Organizing key concepts" },
  { id: 4, text: "Formulating final response" },
];

function AnimatedStepText({ text }: { text: string }) {
  return (
    <div className="relative h-5 overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.span
          key={text}
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -5 }}
          transition={{ duration: 0.25, ease: "easeInOut" }}
          className="absolute w-full text-shimmer font-medium"
        >
          {text}
        </motion.span>
      </AnimatePresence>
    </div>
  );
}


export function RagThinkingDisplay({
  chatId,
  isLoading,
  isProcessing,
  hideThinking,
}: RagThinkingDisplayProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [steps, setSteps] = useState<ThinkingStep[]>(
    THINKING_STEPS.map((step) => ({ ...step, completed: false }))
  );

  // Custom state update to ensure currentStep only increases
  const updateCurrentStep = (newStep: number) => {
    setCurrentStep((prev) => Math.max(prev, newStep));
  };

  // Reset steps function for reuse
  const resetSteps = () => {
    setCurrentStep(1);
    setSteps(THINKING_STEPS.map((step) => ({ ...step, completed: false })));
  };

  useEffect(() => {
    // Logger.info(`RagThinkingDisplay: Processing state changed to ${isProcessing} for chatId ${chatId}`);

    // If starting a new processing session
    if (isProcessing) {
      // Only reset steps if this is a new processing session
      setSteps((prevSteps) =>
        prevSteps.map((step) => ({ ...step, completed: false }))
      );
      setCurrentStep(1);

      const processRagThinking = async () => {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        setSteps((prev) =>
          prev.map((step: ThinkingStep) =>
            step.id === 1 ? { ...step, completed: true } : step
          )
        );
        updateCurrentStep(2);

        await new Promise((resolve) => setTimeout(resolve, 2000));
        setSteps((prev) =>
          prev.map((step: ThinkingStep) =>
            step.id === 2 ? { ...step, completed: true } : step
          )
        );
        updateCurrentStep(3);

        await new Promise((resolve) => setTimeout(resolve, 2000));
        setSteps((prev) =>
          prev.map((step: ThinkingStep) =>
            step.id === 3 ? { ...step, completed: true } : step
          )
        );
        updateCurrentStep(4);
      };

      processRagThinking();
    }

    // Reset steps when component unmounts
    return () => {
      // Logger.info(`RagThinkingDisplay: Cleaning up and resetting steps for chatId ${chatId}`);
      resetSteps();
    };
  }, [chatId, isProcessing]);

  useEffect(() => {
    if (currentStep === 4 && !isLoading && isProcessing) {
      // Logger.info(`RagThinkingDisplay: Final step reached with isLoading=${isLoading}, isProcessing=${isProcessing}`);

      setSteps((prev) =>
        prev.map((step: ThinkingStep) =>
          step.id === 4 ? { ...step, completed: true } : step
        )
      );
    }
  }, [currentStep, isLoading, isProcessing]);

  // Early return after all hooks if not processing or loading
  if (!isProcessing && !isLoading) {
    return null;
  }

  // Get the current active step
  const activeStep = steps.find((step) => step.id === currentStep) || steps[0];
  const progressPercentage = (currentStep / steps.length) * 100;

  return (
    <>
      {
        !hideThinking &&
        (
          <>
            <div className="w-full my-2 mx-auto flex md:max-w-4xl px-4">
              <div className="rounded-2xl p-3 w-full">
                <div className="flex items-center gap-2 mb-2">
                  {/* <div className="size-6 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border"> */}
                  {/* <SparklingSparkleIcon size={12} /> */}
                  <LogoIqidis size={24} />
                  {/* </div> */}
                  <div className="text-foreground text-sm font-medium flex items-center gap-1">
                    {/* <span className="text-shimmer font-medium">
                    {activeStep.text}
                  </span> */}

                    <AnimatedStepText text={activeStep.text} />

                    <span className="animate-dots ml-0.5">
                      <span className="h-1 w-1 rounded-full bg-foreground inline-block"></span>
                      <span className="h-1 w-1 rounded-full bg-foreground inline-block mx-0.5"></span>
                      <span className="h-1 w-1 rounded-full bg-foreground inline-block"></span>
                    </span>
                  </div>
                </div>

                {/* <div className="w-full bg-zinc-200 dark:bg-zinc-800/30 h-2 rounded-full mb-2 overflow-hidden">
                <div
                  className="h-2 rounded-full transition-all duration-700 ease-in-out"
                  style={{
                    width: `${progressPercentage}%`,
                    background: "rgb(var(--base-navy))",
                  }}
                ></div>
              </div> */}
              </div>
            </div>
            {/* <div className="relative flex items-center justify-center">
        <Loader2
          color="#662483"
          className="left-0 h-4 w-4 animate-spin mr-4"
        />
        <p className="text-center">{activeStep.text}...</p>
      </div> */}
          </>
        )}{" "}
    </>
  );
}
