import { useState, useEffect } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { ChatWithTags as Chat } from "@/lib/db/schema";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface BulkDeleteDialogProps {
	isOpen: boolean;
	onClose: () => void;
	chats: Chat[];
	onDelete: (chatIds: string[]) => Promise<void>;
}

export function BulkDeleteDialog({
	isOpen,
	onClose,
	chats,
	onDelete,
}: BulkDeleteDialogProps) {
	// === STATE ===
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedChats, setSelectedChats] = useState<Set<string>>(new Set());
	const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(
		null,
	);
	const [showConfirmDialog, setShowConfirmDialog] = useState(false);

	// === RESET ON OPEN/CLOSE ===
	useEffect(() => {
		if (!isOpen) {
			setSearchQuery("");
			setSelectedChats(new Set());
			setLastSelectedIndex(null);
			setShowConfirmDialog(false);
		}
	}, [isOpen]);

	// === FILTER CHATS ===
	const filteredChats = chats.filter((chat) =>
		chat.title.toLowerCase().includes(searchQuery.toLowerCase()),
	);

	// === HANDLERS FOR SELECTING ===
	const handleSelectChat = (
		chatId: string,
		index: number,
		isShiftKey: boolean,
	) => {
		const newSet = new Set(selectedChats);

		if (isShiftKey && lastSelectedIndex !== null) {
			// select a contiguous range
			const start = Math.min(lastSelectedIndex, index);
			const end = Math.max(lastSelectedIndex, index);
			for (let i = start; i <= end; i++) {
				if (i < filteredChats.length) {
					newSet.add(filteredChats[i].id);
				}
			}
		} else {
			// toggle single selection
			if (newSet.has(chatId)) {
				newSet.delete(chatId);
			} else {
				newSet.add(chatId);
			}
			setLastSelectedIndex(index);
		}

		setSelectedChats(newSet);
	};

	const handleSelectAll = () => {
		if (selectedChats.size === filteredChats.length) {
			setSelectedChats(new Set());
		} else {
			setSelectedChats(new Set(filteredChats.map((c) => c.id)));
		}
	};

	// === “Delete” BUTTON → SHOW CONFIRM DIALOG ===
	const confirmDelete = () => {
		if (selectedChats.size === 0) return;
		setShowConfirmDialog(true);
	};

	// === DELETE HANDLER ===
	const handleDelete = async () => {
		if (selectedChats.size === 0) return;

		// 1) Hide confirm dialog immediately so it can’t reappear
		setShowConfirmDialog(false);

		// 2) Tell parent to close the entire BulkDeleteDialog
		onClose();

		try {
			await onDelete(Array.from(selectedChats));
			toast.success(
				`${selectedChats.size} chat${
					selectedChats.size > 1 ? "s" : ""
				} deleted successfully`,
			);
		} catch (e) {
			toast.error("Failed to delete chats");
		}
	};

	// Prevent text selection when shift-clicking
	const preventTextSelection = (e: React.MouseEvent) => {
		if (e.shiftKey) {
			e.preventDefault();
		}
	};

	return (
		<>
			{/* ===== MAIN “SELECT CHATS” DIALOG ===== */}
			<Dialog
				open={isOpen && !showConfirmDialog}
				onOpenChange={(open) => {
					// If user clicks outside the main dialog and we're not in confirm mode, close everything
					if (!open && !showConfirmDialog) {
						onClose();
					}
				}}
			>
				<DialogContent className="max-w-3xl w-full px-4">
					<DialogHeader className="px-0">
						<DialogTitle>Delete Multiple Chats</DialogTitle>
					</DialogHeader>
					<div className="space-y-4 py-4">
						<Input
							placeholder="Search chats..."
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							className="mb-4"
						/>

						<div className="flex items-center space-x-2 mb-2">
							<Checkbox
								id="select-all"
								checked={
									selectedChats.size > 0 &&
									selectedChats.size === filteredChats.length
								}
								onChange={handleSelectAll}
							/>
							<label
								htmlFor="select-all"
								className="text-sm font-medium cursor-pointer"
								onClick={handleSelectAll}
							>
								Select All ({filteredChats.length})
							</label>
						</div>

						<div
							className="h-[calc(100vh-400px)] overflow-auto border rounded-md"
							onMouseDown={preventTextSelection}
						>
							{filteredChats.length > 0 ? (
								filteredChats.map((chat, index) => (
									<div
										key={chat.id}
										className="flex items-center space-x-2 p-2 hover:bg-muted/50 cursor-pointer select-none"
										onClick={(e) =>
											handleSelectChat(chat.id, index, e.shiftKey)
										}
									>
										<div className="flex items-center space-x-2 w-full">
											<Checkbox
												id={`chat-${chat.id}`}
												checked={selectedChats.has(chat.id)}
												onChange={(e) => {
													e.stopPropagation();
													const nativeEvent = e.nativeEvent as MouseEvent;
													handleSelectChat(
														chat.id,
														index,
														nativeEvent.shiftKey,
													);
												}}
											/>
											<span className="text-sm flex-1 cursor-pointer line-clamp-1">
												{chat.title.length > 75
													? `${chat.title.substring(0, 75)}...`
													: chat.title || "Untitled Chat"}
											</span>
										</div>
									</div>
								))
							) : (
								<div className="p-4 text-center text-muted-foreground">
									No chats found
								</div>
							)}
						</div>
					</div>

					<DialogFooter className="flex space-x-2 justify-between sm:justify-between px-0">
						<Button variant="outline" onClick={onClose}>
							Cancel
						</Button>
						<Button
							className="!m-0"
							variant="destructive"
							onClick={confirmDelete}
							disabled={selectedChats.size === 0}
						>
							Delete {selectedChats.size} chat
							{selectedChats.size !== 1 ? "s" : ""}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* ===== “ARE YOU SURE?” CONFIRMATION DIALOG ===== */}
			<AlertDialog
				open={isOpen && showConfirmDialog}
				onOpenChange={(open) => {
					// If user hits “Esc” or clicks outside confirm, hide confirm and return to main dialog
					if (!open) {
						setShowConfirmDialog(false);
					}
				}}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. This will permanently delete{" "}
							{selectedChats.size} chat
							{selectedChats.size !== 1 ? "s" : ""} and remove them from our
							database.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel
							onClick={() => {
								// On cancel, just hide confirm -> main dialog reappears seamlessly
								setShowConfirmDialog(false);
							}}
						>
							Cancel
						</AlertDialogCancel>
						<AlertDialogAction
							onClick={handleDelete}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
