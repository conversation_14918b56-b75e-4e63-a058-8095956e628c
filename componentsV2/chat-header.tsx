"use client";

import {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON>nI<PERSON>,
	TagIcon,
	Edit3,
	MoreHorizontal,
} from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { memo, useEffect, useState, useRef } from "react";
import { toast } from "sonner";
import useS<PERSON> from "swr";
import { useWindowSize } from "usehooks-ts";
import { Button } from "@/components/ui/button";
import { PageLoader } from "@/components/ui/page-loader";
import {
	Popover,
	PopoverTrigger,
	PopoverContent,
} from "@/components/ui/popover";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { NotificationsMenu } from "@/componentsV2/notifications/NotificationsMenu";
import { TagManagementDialog } from "@/componentsV2/tag-management-dialog";
import { useAddToMatter } from "@/hooks/use-add-to-matter";
import { useChatVisibility } from "@/hooks/use-chat-visibility";
import { useTagManagement } from "@/hooks/use-tag-management";
import { ChatEvent } from "@/lib/analytics/event-types";
import { logEvent } from "@/lib/analytics/events-client";
import type { ChatWithTags as Chat } from "@/lib/db/schema";
import { fetcher } from "@/lib/utils";
import { VisibilitySelector, type VisibilityType } from "./visibility-selector";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useUser } from "@/contexts/UserContext";

interface PinnedChat {
	chatId: string;
	position: number;
	chat: Chat;
}
interface Matter {
	id: string;
	name: string;
	chats: Chat[];
}
interface SidebarData {
	chats: Chat[];
	folders: Matter[];
	pinnedChats: PinnedChat[];
}

export function ChatHeader({
	chatId,
	selectedModelId,
	selectedVisibilityType,
	isReadonly,
	title: initialTitle, // rename to initialTitle
	isFreeActiveSubscription,
	showWarning = false,
}: {
	chatId: string;
	selectedModelId: string;
	selectedVisibilityType: VisibilityType;
	isReadonly: boolean;
	title?: string;
	isFreeActiveSubscription?: boolean;
	showWarning?: boolean;
}) {
	const router = useRouter();
	const user = useUser();
	const pathname = usePathname();
	const { width: windowWidth } = useWindowSize();
	const [isHovered, setIsHovered] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [isEditingTitle, setIsEditingTitle] = useState(false);
	const [editedTitle, setEditedTitle] = useState(initialTitle || "");
	const [isSavingTitle, setIsSavingTitle] = useState(false);
	const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);
	const titleRef = useRef<HTMLParagraphElement>(null);
	const [isTitleTruncated, setIsTitleTruncated] = useState(false);
	const [warningToolTipOpen, setWarningToolTipOpen] = useState(false);

	// Add visibility hook to track current visibility state
	const { visibilityType } = useChatVisibility({
		chatId,
		initialVisibility: selectedVisibilityType,
	});

	// Replace history API call with sidebar-data API call
	const { data: sidebarData, mutate } = useSWR(
		"/api/chat-org/sidebar-data",
		fetcher,
		{
			fallbackData: { chats: [], folders: [], pinnedChats: [] },
			revalidateOnFocus: true,
		},
	);

	const currentChatFromSidebar = sidebarData?.chats?.find(
		(chat: Chat) => chat.id === chatId,
	);
	const currentTitle = currentChatFromSidebar?.title ?? initialTitle;

	// Get current visibility from either the sidebar data or the hook
	const currentVisibility =
		currentChatFromSidebar?.visibility ?? visibilityType;

	// Check if title is truncated
	useEffect(() => {
		const checkTruncation = () => {
			if (titleRef.current) {
				const element = titleRef.current;
				setIsTitleTruncated(element.scrollWidth > element.clientWidth);
			}
		};

		checkTruncation();
		window.addEventListener("resize", checkTruncation);
		return () => window.removeEventListener("resize", checkTruncation);
	}, [currentTitle]);

	// Preload recent prompts when chat header mounts
	useEffect(() => {
		// Preload recent prompts in the background
		const preloadRecentPrompts = async () => {
			try {
				await fetch("/api/prompts?type=recent&limit=20", {
					headers: {
						"Cache-Control": "max-age=60", // Cache for 1 minute
						Pragma: "no-cache",
						Expires: "0",
					},
				});
			} catch (error) {
				console.error("Error preloading recent prompts:", error);
			}
		};

		preloadRecentPrompts();
	}, []);

	useEffect(() => {
		const doUpdate = async () => {
			const code = localStorage.getItem("referralCode");
			if (code) {
				const response = await fetch("/api/referral/update-user-referrer-id", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						email: user?.email,
						referralCode: code,
					}),
				});
				const isValid = await response.json();
				if (isValid) {
					localStorage.removeItem("referralCode");
				}
			}
		};

		doUpdate();
	}, [user?.email]);

	const navigateToGuides = () => {
		setIsLoading(true);
		router.push("/guides");
	};

	useEffect(() => {
		setIsLoading(false);
	}, [pathname]);

	useEffect(() => {
		setEditedTitle(currentTitle || "");
	}, [currentTitle]);

	// Add tag management hook
	const {
		isDialogOpen,
		currentChat,
		openTagDialog,
		closeTagDialog,
		handleSaveTags,
		isLoading: isTagsLoading,
	} = useTagManagement({
		onUpdateTags: async () => {
			// Revalidate sidebar data to update tags in the sidebar
			mutate();
		},
	});

	// Find the current chat in sidebar data
	const currentChatData = sidebarData?.chats?.find(
		(chat: Chat) => chat.id === chatId,
	);

	// Add to Matter hook
	const {
		isDialogOpen: isAddToMatterDialogOpen,
		currentChat: currentMatterChat,
		openDialog: openAddToMatterDialog,
		closeDialog: closeAddToMatterDialog,
		handleAddToMatter,
		isLoading: isAddToMatterLoading,
	} = useAddToMatter({
		onAddToMatter: async () => {
			// Revalidate sidebar data to update matters in the sidebar
			mutate();
		},
	});

	const handleTogglePin = async () => {
		if (!currentChatData) return;

		// Check if the current chat is pinned
		const isPinned = sidebarData?.pinnedChats?.some(
			(pinnedChat: PinnedChat) => pinnedChat.chatId === currentChatData.id,
		);

		try {
			if (isPinned) {
				// Optimistically update UI
				mutate(
					(currentData: SidebarData | undefined) => {
						if (!currentData) return currentData;

						return {
							...currentData,
							pinnedChats: currentData.pinnedChats.filter(
								(pinnedChat: PinnedChat) =>
									pinnedChat.chatId !== currentChatData.id,
							),
						};
					},
					false, // Don't revalidate immediately
				);

				// Call API to unpin
				const response = await fetch(
					`/api/chat-org/pinned-chats?chatId=${currentChatData.id}`,
					{
						method: "DELETE",
					},
				);

				if (!response.ok) throw new Error("Failed to unpin chat");
				if (response.ok) {
					logEvent(ChatEvent.UNPIN_CHAT, {
						chatId: currentChatData.id,
					});
				}

				// Revalidate to get actual data
				mutate();
			} else {
				// Optimistically update UI
				mutate(
					(currentData: SidebarData | undefined) => {
						if (!currentData) return currentData;

						// Create a simple pinnedChat entry for the UI
						const newPinnedChat = {
							chatId: currentChatData.id,
							position: (currentData.pinnedChats?.length || 0) + 1,
							chat: currentChatData,
						};

						return {
							...currentData,
							pinnedChats: [...(currentData.pinnedChats || []), newPinnedChat],
						};
					},
					false, // Don't revalidate immediately
				);

				// Call API to pin
				const response = await fetch("/api/chat-org/pinned-chats", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({ chatId: currentChatData.id }),
				});

				if (!response.ok) throw new Error("Failed to pin chat");
				if (response.ok) {
					logEvent(ChatEvent.PIN_CHAT, {
						chatId: currentChatData.id,
					});
				}

				// Revalidate to get actual data
				mutate();
			}
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			console.error("Error toggling pin status:", error);
		}
	};

	const handleTitleClick = () => {
		if (!isReadonly && currentTitle) {
			setIsEditingTitle(true);
			setTimeout(() => {
				const input = document.getElementById("chat-title-input");
				if (input) {
					(input as HTMLInputElement).focus();
					(input as HTMLInputElement).select();
				}
			}, 0);
		}
	};

	const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setEditedTitle(e.target.value);
	};

	const handleTitleSave = async () => {
		if (!editedTitle.trim()) {
			toast.error("Title cannot be empty");
			return;
		}
		if (editedTitle === currentTitle) {
			setIsEditingTitle(false);
			return;
		}
		setIsSavingTitle(true);
		try {
			const res = await fetch("/api/chat", {
				method: "PATCH",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ id: chatId, title: editedTitle }),
			});
			if (!res.ok) {
				throw new Error("Failed to rename chat");
			}
			mutate("/api/chat-org/sidebar-data"); // Refresh sidebar data
			setIsEditingTitle(false);
			toast.success("Chat title updated");
		} catch (err) {
			toast.error("Unable to update title");
		} finally {
			setIsSavingTitle(false);
		}
	};

	const handleTitleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			handleTitleSave();
		} else if (e.key === "Escape") {
			setIsEditingTitle(false);
			setEditedTitle(currentTitle || "");
		}
	};

	const handleTitleBlur = () => {
		if (isEditingTitle) {
			handleTitleSave();
		}
	};

	return (
		<>
			<header className="chat-background-light dark:chat-background-dark flex gap-1 inset-x-0 items-center h-[72px] md:z-[60] overflow-hidden p-4 sm:gap-2 top-0 z-[60]">
				<div className="flex justify-between items-center w-full min-w-0">
					{/* Mobile sidebar trigger */}
					<div className="flex items-center gap-1 sm:gap-2 md:gap-4">
						<SidebarTrigger className="flex md:hidden !h-10 !w-10 sm:!h-10 sm:!w-10 p-2 sm:p-2.5 [&>svg]:!size-5" />

						{/* Logo and title section */}
						<div className="flex items-center gap-1 sm:gap-2 min-w-0 flex-1">
							<p className="text-base sm:text-lg md:text-xl font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
								AI Assistant
							</p>
							{currentTitle && (
								<span className="text-gray-400 dark:text-gray-600 hidden sm:inline">
									/
								</span>
							)}
							{isEditingTitle ? (
								<input
									id="chat-title-input"
									type="text"
									value={editedTitle}
									onChange={handleTitleChange}
									onKeyDown={handleTitleKeyDown}
									onBlur={handleTitleBlur}
									className="text-sm sm:text-base text-gray-600 dark:text-gray-400 bg-transparent border-b border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none min-w-0 hidden sm:block w-32 sm:w-48 md:w-72 lg:w-96"
									disabled={isSavingTitle}
								/>
							) : currentTitle ? (
								<div className="flex items-center gap-1 sm:gap-2 group min-w-0 hidden sm:flex">
									<Tooltip>
										<TooltipTrigger asChild>
											<p
												ref={titleRef}
												className="text-sm sm:text-base text-gray-600 dark:text-gray-400 truncate cursor-pointer hover:text-gray-800 dark:hover:text-gray-200 transition-colors w-32 sm:w-48 lg:w-96"
												onClick={handleTitleClick}
												onMouseEnter={() => setIsHovered(true)}
												onMouseLeave={() => setIsHovered(false)}
											>
												{currentTitle}
											</p>
										</TooltipTrigger>
										{isTitleTruncated && (
											<TooltipContent
												side="bottom"
												className="max-w-[300px] sm:max-w-[400px] bg-gray-900 dark:bg-gray-800 text-white dark:text-gray-100 p-2"
											>
												<p className="text-sm break-words">{currentTitle}</p>
												{!isReadonly && (
													<p className="text-xs text-gray-400 mt-1">
														Click to rename chat
													</p>
												)}
											</TooltipContent>
										)}
									</Tooltip>
									{!isReadonly && (
										<Tooltip>
											<TooltipTrigger asChild>
												<button
													onClick={handleTitleClick}
													className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded hidden sm:block"
													aria-label="Edit chat title"
												>
													<Edit3 className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200" />
												</button>
											</TooltipTrigger>
											<TooltipContent>Click to rename chat</TooltipContent>
										</Tooltip>
									)}
								</div>
							) : null}
						</div>

						{/* Warning section */}
						<div className="flex items-center min-w-0">
							{showWarning && (
								<Tooltip
									open={warningToolTipOpen}
									onOpenChange={setWarningToolTipOpen}
								>
									<TooltipTrigger
										asChild
										onClick={() => setWarningToolTipOpen(true)}
									>
										<div className="flex items-center justify-center size-10 sm:size-8">
											<AlertTriangle className="size-6 sm:size-5 text-amber-500 dark:text-amber-400" />
										</div>
									</TooltipTrigger>
									<TooltipContent
										side="bottom"
										className="bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-700 w-72 sm:w-80 !block"
									>
										<div className="space-y-2">
											<p className="text-amber-800 dark:text-amber-200 text-sm">
												This chat is getting long. Consider starting a new chat
												for better performance.
											</p>
											{chatId && (
												<Button
													variant="outline"
													size="sm"
													className="w-full text-amber-700 dark:text-amber-300 border-amber-300 dark:border-amber-700 hover:bg-amber-100 dark:hover:bg-amber-900/30"
													onClick={() =>
														router.push(`/chat/transfer/${chatId}`)
													}
												>
													Transfer Chat
												</Button>
											)}
										</div>
									</TooltipContent>
								</Tooltip>
							)}
						</div>
					</div>

					{/* Action buttons */}
					<div className="flex items-center justify-end gap-2 lg:gap-3 shrink-0 overflow-hidden">
						{/* Pin/Unpin button - hidden on mobile, shown in more menu */}
						{currentTitle && (
							<Button
								variant="ghost"
								className={`hidden md:flex px-2 sm:px-3 h-10 sm:h-[36px] transition-all duration-200 ${
									sidebarData?.pinnedChats?.some(
										(pinnedChat: PinnedChat) => pinnedChat.chatId === chatId,
									)
										? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 hover:text-blue-800 dark:hover:text-blue-200 border border-blue-200 dark:border-blue-800"
										: "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-200 dark:border-gray-700"
								}`}
								disabled={!currentChatData}
								onClick={handleTogglePin}
							>
								<PinIcon
									className={`size-5 sm:size-4 ${
										sidebarData?.pinnedChats?.some(
											(pinnedChat: PinnedChat) => pinnedChat.chatId === chatId,
										)
											? "fill-current"
											: ""
									}`}
								/>
								<span className="hidden xl:inline text-sm">
									{sidebarData?.pinnedChats?.some(
										(pinnedChat: PinnedChat) => pinnedChat.chatId === chatId,
									)
										? "Unpin"
										: "Pin"}
								</span>
							</Button>
						)}

						{/* Tags button - hidden on mobile, shown in more menu */}
						{currentTitle && (
							<Popover
								open={isDialogOpen}
								onOpenChange={(open) => {
									if (open && currentChatData) {
										openTagDialog(currentChatData);
									} else if (!open) {
										closeTagDialog();
									}
								}}
							>
								<PopoverTrigger asChild>
									<Button
										variant="ghost"
										className="hidden md:flex px-2 sm:px-3 h-10 sm:h-[36px] transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"
										disabled={!currentChatData}
									>
										<TagIcon className="h-5 w-5 sm:h-4 sm:w-4" />
										<span className="hidden xl:inline text-sm">Tags</span>
									</Button>
								</PopoverTrigger>
								<TagManagementDialog
									open={isDialogOpen}
									onOpenChange={(open) => {
										if (!open) closeTagDialog();
									}}
									chat={currentChat}
									allChats={sidebarData?.chats || []}
									onSave={handleSaveTags}
									isLoading={isTagsLoading}
								/>
							</Popover>
						)}

						{/* Visibility selector - hidden on mobile, shown in more menu */}
						{!isReadonly && (
							<div className="hidden md:flex">
								<VisibilitySelector
									chatId={chatId}
									selectedVisibilityType={currentVisibility}
									className="shrink-0 h-10 sm:h-[36px]"
								/>
							</div>
						)}

						{/* More menu for mobile - shows pin, tags, and visibility */}
						{currentTitle && (
							<Popover open={isMoreMenuOpen} onOpenChange={setIsMoreMenuOpen}>
								<PopoverTrigger asChild>
									<Button
										variant="ghost"
										className="flex md:hidden !h-10 !w-10 sm:h-10 sm:w-10 p-2 sm:p-2.5 transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200 text-gray-700 dark:text-gray-300 [&>svg]:!size-5 sm:[&>svg]:!size-5"
									>
										<MoreHorizontal className="h-5 w-5 sm:h-5 sm:w-5" />
									</Button>
								</PopoverTrigger>
								<PopoverContent
									align="end"
									className="w-48 p-2 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-lg"
								>
									<div className="space-y-1">
										{/* Pin/Unpin in mobile menu */}
										<Button
											variant="ghost"
											className={`w-full justify-start px-2 py-1.5 h-auto text-sm transition-all duration-200 ${
												sidebarData?.pinnedChats?.some(
													(pinnedChat: PinnedChat) =>
														pinnedChat.chatId === chatId,
												)
													? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30"
													: "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
											}`}
											disabled={!currentChatData}
											onClick={() => {
												handleTogglePin();
												setIsMoreMenuOpen(false);
											}}
										>
											<PinIcon
												className={`size-5 mr-2 ${
													sidebarData?.pinnedChats?.some(
														(pinnedChat: PinnedChat) =>
															pinnedChat.chatId === chatId,
													)
														? "fill-current"
														: ""
												}`}
											/>
											{sidebarData?.pinnedChats?.some(
												(pinnedChat: PinnedChat) =>
													pinnedChat.chatId === chatId,
											)
												? "Unpin"
												: "Pin"}
										</Button>

										{/* Tags in mobile menu */}
										<Button
											variant="ghost"
											className="w-full justify-start px-2 py-1.5 h-auto text-sm transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300"
											disabled={!currentChatData}
											onClick={() => {
												if (currentChatData) {
													openTagDialog(currentChatData);
												}
												setIsMoreMenuOpen(false);
											}}
										>
											<TagIcon className="h-5 w-5 mr-2" />
											Tags
										</Button>

										{/* Visibility in mobile menu */}
										{!isReadonly && (
											<VisibilitySelector
												chatId={chatId}
												selectedVisibilityType={currentVisibility}
												showInMobileMenu={true}
											/>
										)}
									</div>
								</PopoverContent>
							</Popover>
						)}

						{/* Guides button */}
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									className="hidden md:flex px-2 sm:px-3 h-10 sm:h-[36px] transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"
									onClick={navigateToGuides}
								>
									<BookOpen className="h-5 w-5 sm:h-4 sm:w-4" />
									<span className="hidden xl:inline text-sm">Guides</span>
								</Button>
							</TooltipTrigger>
							<TooltipContent>View tutorials and guides</TooltipContent>
						</Tooltip>

						{/* Notifications - made bigger on mobile */}
						<NotificationsMenu />

						{/* Upgrade button */}
						{isFreeActiveSubscription && (
							<Button
								onClick={() => router.push("/subscription")}
								className="h-10 sm:h-[36px] px-2 sm:px-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-md hover:shadow-lg transition-all duration-200 text-xs sm:text-sm"
							>
								<span className="font-medium hidden sm:inline">Upgrade to</span>
								<span className="font-bold sm:ml-1">Pro</span>
							</Button>
						)}
					</div>
				</div>
			</header>
			{/* Full-page loader (under header) */}
			{isLoading && <PageLoader message="Loading..." />}
		</>
	);
}

// Update memo comparison to include all relevant props
export const ChatHeaderMemo = memo(ChatHeader, (prevProps, nextProps) => {
	return (
		prevProps.selectedModelId === nextProps.selectedModelId &&
		prevProps.chatId === nextProps.chatId &&
		prevProps.selectedVisibilityType === nextProps.selectedVisibilityType &&
		prevProps.isReadonly === nextProps.isReadonly
	);
});
