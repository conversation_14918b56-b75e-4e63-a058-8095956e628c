import { Plan, Subscription } from "@/lib/db/schema";
import { But<PERSON> } from "@/components/ui/button";
import { PLANS, SUBSCRIPTION_STATUS } from "@/lib/constants";
import { formatDate } from "date-fns";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "next/navigation";

type Props = {
  extraInfo?: string;
  onclick?: () => void;
  handleTrial?: () => void;
  activeSubscription?: Subscription;
  plan: Plan;
  loading: boolean;
  hideButtons?: boolean;
};
export function PriceCard({
  extraInfo,
  onclick,
  handleTrial,
  activeSubscription,
  plan,
  loading,
  hideButtons = false,
}: Props) {
  const isTrialActive =
    activeSubscription?.trialEndsAt &&
    new Date(activeSubscription.trialEndsAt) > new Date();

  const isFreePlan = plan.name === PLANS.FREE_PLAN;
  const router = useRouter();

  // Preview plan features
  const previewFeatures = [
    { title: "10 queries per day" },
    { title: "10 saved chats" },
    { title: "150 MB total memory available" },
    { title: "Limited support" },
  ];

  // Core plan features
  const coreFeatures = [
    { title: "Unlimited AI queries •" },
    { title: "Enhanced document analysis (PDF, Word, and more) •" },
    { title: "Research Mode – context-aware legal research with citations •" },
    { title: "Iqidis Playbook – build, chain, and reuse prompts with custom logic •" },
    { title: "Iqidis Amplify – enhance any prompt with one click •" },
    { title: "Citation-ready exports (PDF & Word) •" },
    { title: "All future upgrades included at your current price •" },
  ];

  // Coming next features
  const comingNextFeatures = [
    { title: "Centralized document library •" },
    { title: "Chat organization + saved matters •" },
    { title: "AI Drafting Assistant, with redlining and version control •" },
    { title: "Team collaboration tools •" },
    { title: "Usage & performance analytics •" },
    { title: "Billing & time tracking •" },
    { title: "Advanced Research - shepardizing and more •" },
  ];

  // Support features
  const supportFeatures = [
    { title: "Priority email support •" },
    { title: "Tailored onboarding available •" },
  ];

  return (
    <div className={`border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow w-full max-w-md p-6 flex flex-col ${isFreePlan ? "bg-white" : "bg-white"}`}>
      <div className="space-y-1">
        <div className="mb-4">
          {isFreePlan ? (
            <>
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-playfair font-semibold text-[#333] mb-1">Preview</h3>
                  <p className="text-sm text-[#666]">Try Iqidis with limited access</p>
                </div>
                <span className="text-xs bg-[#e6f7ef] text-[#00a67e] px-2 py-1 rounded-md">Current Plan</span>
              </div>
              <div className="mt-4">
                <p className="text-3xl font-playfair font-bold text-[#333]">$0 <span className="text-base font-normal text-[#666]">/year</span></p>
              </div>
            </>
          ) : (
            <>
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-playfair font-semibold text-[#4a4a6a] mb-1">Iqidis Core</h3>
                  <p className="text-sm text-[#666]">Lock in this price now for all current and future features. Pricing will increase soon.</p>
                </div>
                <span className="text-xs bg-[#e6f7ef] text-[#00a67e] px-2 py-1 rounded-md whitespace-nowrap">Current Plan</span>
              </div>
              <div className="mt-4 bg-gray-50 p-4 rounded-md">
                <p className="text-3xl font-playfair font-bold text-[#333]">$2,388 <span className="text-base font-normal text-[#666]">/year</span></p>
                <p className="text-sm text-[#666] mt-1">$199/mo billed annually</p>
              </div>
            </>
          )}
        </div>

        {isFreePlan ? (
          <>
            <div className="mt-6 mb-4">
              <div className="flex items-center gap-2 mb-4">
                <svg className="size-4 text-[#4a4a6a]" viewBox="0 0 24 24" fill="none">
                  <path d="M3.34 17L12 21.5 20.66 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M3.34 12L12 16.5 20.66 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M3.34 7L12 11.5 20.66 7 12 2.5 3.34 7Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <p className="text-sm font-medium text-[#4a4a6a]">All current Iqidis Core features, limited to:</p>
              </div>
              <ul className="space-y-3">
                {previewFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <svg className="size-4 flex-none text-green-500 mt-0.5" fill="none" viewBox="0 0 24 24">
                      <path d="M5 13L9 17L19 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <span className="text-sm text-[#333]">{feature.title}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="mt-auto pt-4">
              <Button
                onClick={() => router.push("/")} variant="ghost"
                className="w-full h-12 bg-white border border-gray-200 text-[#333] hover:bg-gray-50 py-2 flex items-center justify-center gap-2 font-medium"
              >
                <span>Continue with preview</span>
                <svg className="size-4" viewBox="0 0 24 24" fill="none">
                  <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="mt-6 pt-4">
              <div className="flex items-center gap-2 mb-4">
                <svg className="size-4 text-[#4a4a6a]" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <p className="text-sm font-medium text-[#4a4a6a]">Available Now</p>
              </div>
              <ul className="space-y-3">
                {coreFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <svg className="size-4 flex-none text-green-500 mt-0.5" fill="none" viewBox="0 0 24 24">
                      <path d="M5 13L9 17L19 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <span className="text-sm text-[#333]">{feature.title}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="mt-6">
              <div className="flex items-center gap-2 mb-4">
                <svg className="size-4 text-[#4a4a6a]" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" />
                  <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
                <p className="text-sm font-medium text-[#4a4a6a]">What&apos;s Coming Next</p>
              </div>
              <ul className="space-y-3">
                {comingNextFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <svg className="size-4 flex-none text-green-500 mt-0.5" fill="none" viewBox="0 0 24 24">
                      <path d="M5 13L9 17L19 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <span className="text-sm text-[#333]">{feature.title}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="mt-6">
              <div className="flex items-center gap-2 mb-4">
                <svg className="size-4 text-[#4a4a6a]" viewBox="0 0 24 24" fill="none">
                  <path d="M13 10V3L4 14H13V21L22 10H13Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <p className="text-sm font-medium text-[#4a4a6a]">Support</p>
              </div>
              <ul className="space-y-3">
                {supportFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <svg className="size-4 flex-none text-green-500 mt-0.5" fill="none" viewBox="0 0 24 24">
                      <path d="M5 13L9 17L19 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <span className="text-sm text-[#333]">{feature.title}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="mt-6 pt-4">
              <Button
                onClick={onclick || handleTrial}
                disabled={loading}
                className="w-full bg-indigo-600 text-white hover:bg-indigo-700 py-2 flex items-center justify-center gap-2 font-medium"
              >
                <span>Subscribe Now</span>
              </Button>
            </div>
          </>
        )}
      </div>
      {extraInfo && <p className="text-xs text-muted-foreground text-center mt-2">{extraInfo}</p>}
    </div>
  );
}
