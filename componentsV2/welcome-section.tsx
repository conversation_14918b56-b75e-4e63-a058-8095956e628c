"use client";

import React, { memo } from "react";
import { SecurityFeatures } from "./security-features";
import { SuggestedActions } from "./suggested-actions";
import { VideoTutorials } from "./video-tutorials";
import WelcomeText from "./welcome-text";

interface WelcomeSectionProps {
	chatId: string;
	setInput?: (value: string) => void;
	submitForm?: () => void;
}

const PureWelcomeSection = ({
	chatId,
	setInput,
	submitForm,
}: WelcomeSectionProps) => {
	return (
		<div className="w-full max-w-full sm:max-w-4xl mx-auto px-2 sm:px-4 py-4 sm:py-6 pt-0 flex flex-col overflow-y-auto max-h-[calc(100vh-250px)] sm:max-h-[calc(100vh-275px)] md:max-h-[calc(100vh-275px)] lg:max-h-[calc(100vh-270px)] hideScrollBar h-full xl:justify-center justify-between">
			<div className="text-center sticky top-[0px] pt-[0px] sm:pt-[40px] z-10">
				<WelcomeText />
			</div>

			<div className="flex flex-col lg:flex-row justify-center gap-3 sm:gap-4 mx-auto flex-wrap relative z-0">
				<div className="bg-white dark:bg-gray-800 rounded-xl p-3 sm:p-4 border border-gray-100 dark:border-gray-700 shadow-sm paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 max-w-[200px] sm:max-w-[250px] md:max-w-[280px] mx-auto md:mx-0">
					<VideoTutorials chatId={chatId} />
				</div>

				<div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-3 sm:p-4 border border-gray-100 dark:border-gray-700 paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 max-w-[200px] sm:max-w-[250px] md:max-w-[280px] mx-auto md:mx-0">
					{setInput && submitForm && (
						<SuggestedActions
							chatId={chatId}
							setInput={setInput}
							submitForm={submitForm}
						/>
					)}
				</div>

				<div className="bg-white dark:bg-gray-800 rounded-xl p-3 sm:p-4 border border-gray-100 dark:border-gray-700 shadow-sm paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 max-w-[200px] sm:max-w-[250px] md:max-w-[280px] mx-auto md:mx-0">
					<SecurityFeatures chatId={chatId} />
				</div>
			</div>
		</div>
	);
};

export const WelcomeSection = memo(PureWelcomeSection);
