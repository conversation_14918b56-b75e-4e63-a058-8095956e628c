import { SearchResult } from "@/lib/types";
import { formatDistanceToNow } from "date-fns";
import { MessageCircle, Search } from "lucide-react";

interface SearchResultItemProps {
  result: SearchResult;
  onSelect: () => void;
}

export function SearchResultItem({ result, onSelect }: SearchResultItemProps) {
  const formattedDate = formatDistanceToNow(new Date(result.timestamp), { addSuffix: true });
  
  return (
    <div
      onClick={onSelect}
      className="group relative flex items-center rounded-lg px-4 py-3 hover:bg-muted cursor-pointer transition-colors"
    >
      {/* Icon based on match type, for now show only Message icon */}
      <MessageCircle className="h-5 w-5 text-muted-foreground flex-shrink-0" />
      
      {/* Content */}
      <div className="relative grow overflow-hidden pl-4">
        <div className="flex items-center">
          <div className="relative grow overflow-hidden">
            {/* Title with padding to prevent overlap */}
            <div className="text-sm font-medium text-foreground pr-4">
              {result.chatTitle}
            </div>
            
            {/* Preview text */}
            {result.messagePreview && (
              <div className="pt-1 text-xs text-muted-foreground line-clamp-2">
                {result.messagePreview}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Date tooltip - visible on hover */}
      <div className="absolute right-4 top-3 text-xs text-muted-foreground bg-background px-1 opacity-0 group-hover:opacity-100 transition-opacity">
        {formattedDate}
      </div>
    </div>
  );
}
