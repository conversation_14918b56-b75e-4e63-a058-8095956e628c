"use client";

import * as pdfjs from "pdfjs-dist";
import type {
  TextItem,
  TextMarkedContent,
  PDFPageProxy,
  PDFDocumentProxy,
} from "pdfjs-dist/types/src/display/api";
import { useEffect, useRef } from "react";

import { ProcessedChunk } from "@/lib/types";
import { Logger } from "@/lib/utils/Logger";

// Constants
const PDF_PROCESSING_CONFIG = {
  SCALE_FACTOR: 0.5,
  JPEG_QUALITY: 0.6,
  INITIAL_SCALE: 1.0,
  WORKER_PATH: "/pdf.worker.mjs",
} as const;

// Types
interface PdfProcessorProps {
  file: File;
  onProcessed: (chunks: ProcessedChunk[]) => void;
}

interface PageRenderConfig {
  width: number;
  height: number;
  scale: number;
}

// Utility functions
const createCanvas = (config: PageRenderConfig): HTMLCanvasElement => {
  const canvas = document.createElement("canvas");
  canvas.height = config.height * config.scale;
  canvas.width = config.width * config.scale;
  return canvas;
};

const getCanvasContext = (
  canvas: HTMLCanvasElement
): CanvasRenderingContext2D => {
  const context = canvas.getContext("2d");
  if (!context) {
    throw new Error("Failed to get canvas context");
  }
  return context;
};

const extractTextFromContent = (
  items: (TextItem | TextMarkedContent)[]
): string => {
  return items
    .map((item) => ("str" in item ? item.str : ""))
    .join(" ")
    .trim();
};

// PDF Processing Class
class PdfPageProcessor {
  private readonly page: PDFPageProxy;
  private readonly filename: string;
  private readonly pageNumber: number;

  constructor(page: PDFPageProxy, filename: string, pageNumber: number) {
    this.page = page;
    this.filename = filename;
    this.pageNumber = pageNumber;
  }

  private async renderPageImage(): Promise<string> {
    const viewport = this.page.getViewport({
      scale: PDF_PROCESSING_CONFIG.INITIAL_SCALE,
    });
    const renderConfig: PageRenderConfig = {
      width: viewport.width,
      height: viewport.height,
      scale: PDF_PROCESSING_CONFIG.SCALE_FACTOR,
    };

    const canvas = createCanvas(renderConfig);
    const context = getCanvasContext(canvas);
    const scaledViewport = this.page.getViewport({
      scale: PDF_PROCESSING_CONFIG.SCALE_FACTOR,
    });

    await this.page.render({
      canvasContext: context,
      viewport: scaledViewport,
    }).promise;

    const base64Data = canvas.toDataURL(
      "image/jpeg",
      PDF_PROCESSING_CONFIG.JPEG_QUALITY
    );

    // // Log base64 data for validation
    // Logger.debug(`Page ${this.pageNumber} base64 data:`, {
    //   full: base64Data,
    //   preview: `${base64Data.substring(0, 100)}...`,
    //   length: base64Data.length,
    //   startsWithDataUrl: base64Data.startsWith("data:image/jpeg;base64,"),
    // });

    return base64Data;
  }

  async processPage(): Promise<ProcessedChunk> {
    try {
      Logger.debug(`Processing page ${this.pageNumber} for image extraction`);

      const imageBase64 = await this.renderPageImage();

      Logger.debug(`Page ${this.pageNumber} image extracted successfully`, {
        pageNumber: this.pageNumber,
        filename: this.filename,
        base64Length: imageBase64.length,
      });

      return {
        text: "", // Empty string since we don't need text
        pageNumber: this.pageNumber,
        image_base64: imageBase64,
        overlap: false,
        filename: this.filename,
      };
    } catch (error) {
      Logger.error(`Error processing page ${this.pageNumber} image:`, error);
      throw error;
    }
  }
}

// Main Component
export function PdfProcessor({ file, onProcessed }: PdfProcessorProps): null {
  // Add a ref to track if processing has already been done
  const hasProcessed = useRef(false);
  
  useEffect(() => {
    const processPdf = async (): Promise<void> => {
      // Skip if already processed
      if (hasProcessed.current) {
        Logger.debug(`Skipping duplicate processing for ${file.name}`);
        return;
      }
      
      try {
        Logger.info(`Starting PDF processing for file: ${file.name}`);
        
        pdfjs.GlobalWorkerOptions.workerSrc = PDF_PROCESSING_CONFIG.WORKER_PATH;
        const arrayBuffer = await file.arrayBuffer();
        const pdf = await pdfjs.getDocument(arrayBuffer).promise;
        
        Logger.info(`PDF loaded successfully. Total pages: ${pdf.numPages}`);

        const processPromises = Array.from(
          { length: pdf.numPages },
          async (_, index) => {
            const pageNumber = index + 1;
            const page = await pdf.getPage(pageNumber);
            const processor = new PdfPageProcessor(page, file.name, pageNumber);
            return processor.processPage();
          }
        );

        const chunks = await Promise.all(processPromises);

        // Mark as processed before calling onProcessed to prevent race conditions
        hasProcessed.current = true;
        
        Logger.info(`PDF processing completed successfully for ${file.name}`);
        onProcessed(chunks);
      } catch (error) {
        Logger.error(`PDF processing failed for ${file.name}:`, error);
        // Still call onProcessed with empty array to clean up state
        onProcessed([]);
        // Mark as processed to prevent retries
        hasProcessed.current = true;
      }
    };

    processPdf();
    
    // Cleanup function
    return () => {
      // If component unmounts before processing completes, we should still mark as processed
      hasProcessed.current = true;
    };
  }, [file, onProcessed]);

  return null;
}
