"use client";
import { ChevronUp } from "lucide-react";
import Image from "next/image";
import type { User } from "next-auth";
import { signOut } from "next-auth/react";
import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";
import { useTransition } from "react";

import { LoaderIcon } from "@/componentsV2/icons";
import {
  QuestionMarkIcon,
  FileTextIcon,
  PersonIcon,
  GearIcon,
} from "@radix-ui/react-icons";
import {
  VALID_DOCUMENTS,
} from "@/lib/types";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

const getDocumentPath = (doc: (typeof VALID_DOCUMENTS)[number]) => {
  return `/help/${doc.id}`;
};

export function SidebarUserNav({ user }: { user: User }) {
  const { setTheme, theme } = useTheme();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  return (
    // <SidebarMenu>
    //   <SidebarMenuItem>
    //     <DropdownMenu>
    //       <DropdownMenuTrigger asChild>
    //         <SidebarMenuButton className="data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10">
    //           <Image
    //             src={`https://avatar.vercel.sh/${user.email}`}
    //             alt={user.email ?? "User Avatar"}
    //             width={24}
    //             height={24}
    //             className="rounded-full"
    //           />
    //           <span className="truncate">{user?.email}</span>
    //           <ChevronUp className="ml-auto" />
    //         </SidebarMenuButton>
    //       </DropdownMenuTrigger>
    //       <DropdownMenuContent align="end">
    //         <DropdownMenuItem
    //           className="cursor-pointer"
    //           onSelect={() => router.push("/account")}
    //         >
    //           <GearIcon className="mr-2 h-4 w-4" />
    //           My Account
    //         </DropdownMenuItem>
    //         <DropdownMenuItem
    //           className="cursor-pointer relative"
    //           onSelect={() => {
    //             startTransition(() => {
    //               router.push("/preferences");
    //             });
    //           }}
    //         >
    //           <PersonIcon className="mr-2 h-4 w-4" />
    //           Preferences
    //           {isPending && (
    //             <span className="absolute right-2">
    //               <LoaderIcon />
    //             </span>
    //           )}
    //         </DropdownMenuItem>
    //         <DropdownMenuSeparator />
    //         {user.isAdmin && (
    //           <>
    //             <DropdownMenuItem
    //               className="cursor-pointer"
    //               onSelect={() => router.push("/admin/users")}
    //             >
    //               <PersonIcon className="mr-2 h-4 w-4" />
    //               Manage Users
    //             </DropdownMenuItem>
    //             <DropdownMenuSeparator />
    //           </>
    //         )}
    //         <DropdownMenuItem
    //           className="cursor-pointer"
    //           onSelect={() => router.push(getDocumentPath(FAQ_DOCUMENT))}
    //         >
    //           <QuestionMarkIcon className="mr-2 h-4 w-4" />
    //           {FAQ_DOCUMENT.displayName}
    //         </DropdownMenuItem>
    //         <DropdownMenuSub>
    //           <DropdownMenuSubTrigger className="cursor-pointer">
    //             <FileTextIcon className="mr-2 h-4 w-4" />
    //             Policies
    //           </DropdownMenuSubTrigger>
    //           <DropdownMenuSubContent>
    //             {POLICY_PAGES.map((opt) => (
    //               <DropdownMenuItem
    //                 key={opt.id}
    //                 className="cursor-pointer"
    //                 onSelect={() => router.push(opt?.url)}
    //               >
    //                 {opt.displayName}
    //               </DropdownMenuItem>
    //             ))}
    //             {POLICY_DOCUMENTS.map((doc) => (
    //               <DropdownMenuItem
    //                 key={doc.id}
    //                 className="cursor-pointer"
    //                 onSelect={() => router.push(getDocumentPath(doc))}
    //               >
    //                 {doc.displayName}
    //               </DropdownMenuItem>
    //             ))}
    //           </DropdownMenuSubContent>
    //         </DropdownMenuSub>
    //         <DropdownMenuSeparator />

    //         <DropdownMenuItem
    //           className="cursor-pointer"
    //           onSelect={() => setTheme(theme === "dark" ? "light" : "dark")}
    //         >
    //           {`Toggle ${theme === "light" ? "dark" : "light"} mode`}
    //         </DropdownMenuItem>
    //         <DropdownMenuSeparator />
    //         <DropdownMenuItem
    //           className="cursor-pointer"
    //           onSelect={() => router.push("/reset-password")}
    //         >
    //           Reset Password
    //         </DropdownMenuItem>
    //         <DropdownMenuSeparator />
    //         <DropdownMenuItem asChild>
    //           <button
    //             type="button"
    //             className="w-full cursor-pointer"
    //             onClick={() => {
    //               signOut({
    //                 redirectTo: "/",
    //               });
    //             }}
    //           >
    //             Sign out
    //           </button>
    //         </DropdownMenuItem>
    //       </DropdownMenuContent>
    //     </DropdownMenu>
    //   </SidebarMenuItem>
    // </SidebarMenu>
    <div className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground h-10 p-2 flex align-items-center gap-3">
      <Image
        src={`https://avatar.vercel.sh/${user.email}`}
        alt={user.email ?? "User Avatar"}
        width={24}
        height={24}
        className="rounded-full"
      />
      <span className="truncate text-sm">{user?.email}</span>
    </div>
  );
}
