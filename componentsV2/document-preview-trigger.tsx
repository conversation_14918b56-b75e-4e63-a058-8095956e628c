import { cn } from "@/lib/utils";

interface DocumentPreviewTriggerProps {
  onClick: () => void;
  children: React.ReactNode;
  className?: string;
}

export function DocumentPreviewTrigger({ onClick, children, className }: DocumentPreviewTriggerProps) {
  return (
    <button 
      onClick={onClick}
      className={cn(
        "cursor-pointer group/preview relative",
        className
      )}
    >
      {children}
      <div className="absolute inset-0 flex flex-col items-center justify-end bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover/preview:opacity-100 transition-opacity rounded-md">
        <span className="text-white text-xs font-medium mb-2 px-2 py-1 bg-black/50 rounded-sm backdrop-blur-sm">
          Click to preview
        </span>
      </div>
    </button>
  );
} 