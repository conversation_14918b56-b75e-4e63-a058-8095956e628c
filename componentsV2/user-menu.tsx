import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
	LogOut,
	Settings,
	Home,
	User as UserIcon,
	FileText,
	CreditCard,
	Loader,
	Sun,
	Moon,
} from "lucide-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "./avatar";
import { signOut, useSession } from "next-auth/react";
import { POLICY_PAGES } from "@/lib/types";
import { PersonIcon } from "@radix-ui/react-icons";
import { useUser } from "@/contexts/UserContext";
import React from "react";
import { ThemeToggle } from "./theme-toggle";
import { useTheme } from "next-themes";

type UserMenuProps = {
	handleRoute?: (path: string) => void;
};

export const UserMenu = ({ handleRoute }: UserMenuProps) => {
	const router = useRouter();
	const user = useUser();
	const { data: session, status } = useSession();
	const [mounted, setMounted] = useState(false);
	const [email, setEmail] = useState<string | null>(null);
	const { theme, setTheme } = useTheme();

	const toggleTheme = () => {
		setTheme(theme === "dark" ? "light" : "dark");
	};

	useEffect(() => {
		setMounted(true);
	}, []);

	useEffect(() => {
		if (!mounted) return;
		if (status === "authenticated") {
			setEmail(session?.user?.email ?? null);
		} else {
			const localValue = localStorage.getItem("email");
			setEmail(localValue);
		}
	}, [status, session, mounted]);

	const [isPoliciesOpen, setIsPoliciesOpen] = useState(false); // State to manage dropdown visibility

	const togglePolicies = () => {
		setIsPoliciesOpen(!isPoliciesOpen); // Toggle visibility of Policies dropdown
	};

	if (!mounted || status === "loading") {
		return null;
	}
	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Avatar className="h-10 w-10 cursor-pointer ring-2 ring-transparent hover:ring-accent/30 transition-all duration-200">
					<AvatarFallback className="bg-[#8b5cf6] text-white">
						{user && user.email ? (
							user.email.charAt(0).toUpperCase()
						) : email ? (
							email.charAt(0).toUpperCase()
						) : (
							<UserIcon className="h-4 w-4" />
						)}
					</AvatarFallback>
				</Avatar>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				align="end"
				className="bg-white dark:bg-gray-900 shadow-sm min-w-[11rem] border border-gray-200 dark:border-gray-800 rounded-md py-1 z-[100]"
				sideOffset={5}
				side="bottom"
			>
				<DropdownMenuItem
					className="cursor-pointer font-bold py-2 px-4 text-sm text-gray-700 dark:text-gray-200 transition-colors hover:bg-primary hover:text-primary-foreground"
					onSelect={() => {
						if (handleRoute) {
							console.log("Yes");
							handleRoute("/account");
						} else {
							console.log("No");
						}
					}}
				>
					My Account
				</DropdownMenuItem>
				<DropdownMenuItem
					className="cursor-pointer py-2 px-4 text-sm text-gray-700 dark:text-gray-200 transition-colors hover:bg-primary hover:text-primary-foreground"
					onSelect={() => handleRoute?.("/account")}
				>
					<Settings className="h-4 w-4 mr-2" />
					Account Settings
				</DropdownMenuItem>
				<DropdownMenuItem
					className="cursor-pointer py-2 px-4 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
					onSelect={() => handleRoute?.("/subscription")}
				>
					<CreditCard className="h-4 w-4 mr-2" />
					Subscription
				</DropdownMenuItem>
				<DropdownMenuItem
					className="cursor-pointer py-2 px-4 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
					onSelect={toggleTheme}
				>
					{theme === "dark" ? (
						<Sun className="h-4 w-4 mr-2" />
					) : (
						<Moon className="h-4 w-4 mr-2" />
					)}
					{theme === "dark" ? "Light Mode" : "Dark Mode"}
				</DropdownMenuItem>

				<DropdownMenuSub>
					<DropdownMenuSubTrigger
						className="cursor-pointer py-2 px-4 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center"
						onClick={togglePolicies} // Toggle the visibility of Policies dropdown
					>
						<FileText className="h-4 w-4 mr-2" />
						Policies
					</DropdownMenuSubTrigger>
					{isPoliciesOpen && ( // Only show the sub-content when the state is true
						<DropdownMenuSubContent className="bg-white dark:bg-gray-900 shadow-sm min-w-[11rem] border border-gray-200 dark:border-gray-800 rounded-md py-1">
							{POLICY_PAGES?.map((opt) => (
								<DropdownMenuItem
									key={opt.id}
									className="cursor-pointer py-2 px-4 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
									onSelect={() => handleRoute?.(opt?.url)}
								>
									{opt.displayName}
								</DropdownMenuItem>
							))}
						</DropdownMenuSubContent>
					)}
				</DropdownMenuSub>

				<DropdownMenuItem
					className="cursor-pointer py-2 px-4 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
					onSelect={() => handleRoute?.("/")}
				>
					<Home className="h-4 w-4 mr-2" />
					Home
				</DropdownMenuItem>
				<DropdownMenuSeparator className="my-1 border-t border-gray-200 dark:border-gray-700" />
				{user?.isAdmin && (
					<>
						<DropdownMenuItem
							className="cursor-pointer hover:!bg-[#33466b] focus:!bg-[#33466b] hover:!text-white"
							onSelect={() => handleRoute?.("/admin/users")}
						>
							<PersonIcon className="h-4 w-4" />
							Manage Users
						</DropdownMenuItem>
						<DropdownMenuSeparator />
					</>
				)}
				<DropdownMenuItem
					className="cursor-pointer py-2 px-4 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
					onClick={() => {
						signOut({
							redirectTo: "/",
						});
					}}
				>
					<LogOut className="h-4 w-4 mr-2" color="currentColor" />
					Logout
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
};
