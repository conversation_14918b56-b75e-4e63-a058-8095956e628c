import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";

export function ChatSkeleton() {
	return (
		<div className="w-full h-dvh max-h-dvh max-w-full min-h-0 chat-background-light dark:chat-background-dark safari-height-fix">
			{/* Chat Header */}
			<header className="flex top-0 inset-x-0 py-4 items-center px-2 md:px-4 gap-2 md:z-[60] z-[60] h-[72px] chat-background-light dark:chat-background-dark">
				<div className="flex justify-between items-center w-full">
					<div className="flex items-center lg:gap-2 gap-0">
						{/* Page title */}
						<div className="flex items-center gap-2 min-w-0">
							<Skeleton className="h-6 w-24" />
							<span className="text-gray-400 dark:text-gray-600">/</span>
							<Skeleton className="h-4 w-32" />
						</div>

						{/* Header Controls */}
						<div className="flex items-center lg:gap-4 gap-2 min-w-0 flex-1 lg:ml-4 ml-0">
							<Skeleton className="h-6 w-6 rounded" />
						</div>
					</div>

					{/* Right side actions */}
					<div className="flex items-center justify-end lg:space-x-3 space-x-2">
						<Skeleton className="h-9 w-16 rounded-md" />
						<Skeleton className="h-9 w-9 rounded-md" />
						<Skeleton className="h-9 w-9 rounded-md" />
						<Skeleton className="h-9 w-9 rounded-md" />
						<Skeleton className="h-9 w-20 rounded-md" />
					</div>
				</div>
			</header>

			{/* Main Content */}
			<div className="flex flex-1 pr-4 pb-4" style={{ height: 'calc(100% - 72px)' }}>
				<div className="flex flex-1 flex-row max-h-full border rounded-lg border-gray-200 dark:border-slate-700">
					{/* Chat Sidebar */}
					<div className="flex flex-col overflow-hidden h-full w-1/4 max-w-[300px] border-r border-gray-200 dark:border-slate-700">
						<div className="p-4 shrink-0">
							{/* New Chat Button */}
							<div className="mb-4">
								<Skeleton className="h-10 w-full rounded-md" />
							</div>

							{/* Search Input */}
							<div className="relative">
								<Skeleton className="h-10 w-full rounded-md" />
							</div>
						</div>

						{/* Organization Buttons */}
						<div className="px-4 mb-2">
							<div className="flex w-full space-x-1.5">
								<Skeleton className="h-8 flex-1 rounded-md" />
								<Skeleton className="h-8 flex-1 rounded-md" />
							</div>
						</div>

						{/* Conversations List */}
						<div className="flex-1 min-h-0 relative overflow-y-auto custom-scrollbar">
							<div className="px-4 space-y-4">
								{/* Pinned Section */}
								<div className="space-y-2">
									<div className="flex items-center justify-between">
										<Skeleton className="h-4 w-16" />
										<Skeleton className="h-4 w-4" />
									</div>
									{Array.from({ length: 2 }, (_, i) => i).map((index) => (
										<div key={`pinned-${index}`} className="flex items-center gap-3 p-2 rounded-lg">
											<Skeleton className="h-3 w-3 rounded" />
											<div className="flex-1 space-y-1">
												<Skeleton className="h-4 w-full" />
												<Skeleton className="h-3 w-2/3" />
											</div>
										</div>
									))}
								</div>

								<Separator />

								{/* Recent Conversations */}
								<div className="space-y-2">
									<Skeleton className="h-4 w-20" />
									{Array.from({ length: 8 }, (_, i) => i).map((index) => (
										<div key={`conversation-${index}`} className="flex items-center gap-3 p-2 rounded-lg">
											<div className="flex-1 space-y-1">
												<div className="flex items-center gap-1">
													<Skeleton className="h-4 w-full" />
													<Skeleton className="h-3 w-8 rounded-full" />
												</div>
												<Skeleton className="h-3 w-1/2" />
											</div>
										</div>
									))}
								</div>
							</div>
						</div>
					</div>

					{/* Chat Panel */}
					<div className="flex-1 w-3/4 flex flex-col relative overflow-hidden custom-scrollbar h-full">
						{/* Messages Area */}
						<div className="flex flex-col flex-1 w-full min-h-0 relative">
							{/* Playbook Button */}
							<div className="flex items-end justify-end absolute lg:top-[15px] top-[80px] right-[30px] z-[40]">
								<Skeleton className="h-10 w-24 rounded-md" />
							</div>

							{/* Messages Content */}
							<div className="flex flex-col min-w-0 gap-8 flex-1 min-h-0 overflow-y-auto pt-3 relative pb-4">
								<div className="h-[72px]" />

								{/* Welcome Section */}
								<div className="w-full mx-auto sm:px-4 px-0 py-6 pt-0 flex flex-col overflow-y-auto">
									{/* Welcome Text */}
									<div className="text-center sticky md:top-[0px] top-[0px] sm:pt-[40px] pt-[0px] z-10">
										<Skeleton className="h-8 w-64 mx-auto mb-2" />
										<Skeleton className="h-5 w-80 mx-auto" />
									</div>

									{/* Welcome Cards */}
									<div className="flex flex-col lg:flex-row justify-center gap-4 mx-auto flex-wrap relative z-0">
										{Array.from({ length: 3 }, (_, i) => i).map((index) => (
											<div
												key={`welcome-card-${index}`}
												className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-100 dark:border-gray-700 shadow-sm flex-1 sm:max-w-[280px] max-w-[250px] mx-auto md:mx-0"
											>
												<div className="space-y-3">
													<Skeleton className="h-6 w-6 rounded" />
													<Skeleton className="h-5 w-full" />
													<div className="space-y-2">
														<Skeleton className="h-4 w-full" />
														<Skeleton className="h-4 w-3/4" />
													</div>
												</div>
											</div>
										))}
									</div>
								</div>
							</div>
						</div>

						{/* Input Area */}
						<form className="shrink-0">
							<div className="transition-all duration-300">
								<div className="max-w-4xl mx-auto px-4 pb-0">
									<div className="flex flex-col items-stretch w-full rounded-2xl pt-2 pb-2 max-h-[calc(100vh-100px)] justify-between relative">
										{/* Research Mode Toggle */}
										<div className="flex items-center justify-center gap-2 mb-4">
											<Skeleton className="h-10 w-32 rounded-md" />
											<Skeleton className="h-10 w-32 rounded-md" />
										</div>

										{/* Input Field */}
										<div className="relative">
											<Skeleton className="h-24 w-full rounded-lg" />
											<div className="absolute bottom-0 p-2 w-full flex flex-row justify-between z-10">
												<div className="flex gap-2">
													<Skeleton className="h-8 w-8 rounded" />
													<Skeleton className="h-8 w-8 rounded" />
													<Skeleton className="h-8 w-8 rounded" />
													<Skeleton className="h-8 w-8 rounded" />
												</div>
												<div className="flex items-center gap-2">
													<Skeleton className="h-8 w-8 rounded" />
													<Skeleton className="h-8 w-8 rounded" />
												</div>
											</div>
										</div>

										{/* Bottom Text */}
										<div className="flex items-center justify-center mt-2">
											<Skeleton className="h-3 w-96" />
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>

			{/* Fixed utility buttons */}
			<div className="fixed bottom-4 right-4 !z-50 flex gap-2">
				<Skeleton className="h-12 w-12 rounded-full" />
				<Skeleton className="h-12 w-12 rounded-full" />
				<Skeleton className="h-12 w-12 rounded-full" />
			</div>
		</div>
	);
}

export default ChatSkeleton;
