import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Plan } from "./data";
import {
  <PERSON><PERSON><PERSON><PERSON>eader,
  PlanCardFooter,
  PlanPricing,
  PlanFeatures,
} from "./plan-card";
import { Subscription } from "@/lib/db/schema";

interface PlanCardProps extends Plan {
  isUserPlan: boolean;
  isProcessing: boolean;
  billingCycle: "monthly" | "annual";
  onSubscribe: (
    tier: "free" | "premium" | "premium-yearly" | "enterprise"
  ) => void;
  activeSubscription?: Subscription;
  hideButtons?: boolean;
}

const PlanCard = ({
  id,
  name,
  description,
  price,
  monthlyEquivalent,
  popular = false,
  features,
  isUserPlan,
  isProcessing,
  billingCycle,
  onSubscribe,
  activeSubscription,
  hideButtons,
}: PlanCardProps) => {
  const isPremium = id === "premium" || id === "premium-yearly";
  const isPreview = id === "free";
  const isEnterprise = id === "enterprise";

  return (
    <Card
      className={`transition-all ${isUserPlan
        ? "border-primary ring-2 ring-[#270f5733] hover:-translate-y-1 hover:shadow-xl transition-all duration-300"
        : isPremium
          ? "border-primary border-2 ring-1 ring-[#270f5733] shadow-[0_10px_15px_-3px_#270f574d,0_4px_6px_-4px_#270f574d]"
          : isPreview
            ? "border border-iqidis-lightGray shadow-sm"
            : "border shadow-sm"
        } ${isPremium
          ? "hover:-translate-y-1 hover:shadow-xl transition-all duration-300"
          : ""
        } bg-white dark:bg-slate-800`}
    >
      <PlanCardHeader
        name={name}
        description={description}
        isPremium={isPremium}
        isEnterprise={isEnterprise}
        id={id}
      />

      <CardContent className="space-y-6">
        <PlanPricing
          price={price}
          billingCycle={billingCycle}
          monthlyEquivalent={monthlyEquivalent}
          isEnterprise={isEnterprise}
          id={id}
        />

        <PlanFeatures
          features={features}
          isPremium={isPremium}
          isPreview={isPreview}
          isEnterprise={isEnterprise}
        />
      </CardContent>

      <PlanCardFooter
        id={id}
        isProcessing={isProcessing}
        isUserPlan={isUserPlan}
        onSubscribe={onSubscribe}
        activeSubscription={activeSubscription}
        hideButtons={hideButtons}
        billingCycle={billingCycle}
      />
    </Card>
  );
};

export default PlanCard;
