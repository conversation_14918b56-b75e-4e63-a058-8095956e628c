
import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion";
import { FaqItem } from './data';
import { Button } from '@/components/ui/button';

interface FaqSectionProps {
  faqs: FaqItem[];
}

const FaqSection = ({ faqs }: FaqSectionProps) => {
  return (
    <div className="max-w-3xl mx-auto mb-16" id="faq-section">
      <Card className="bg-[#e4e0eb1a] shadow-sm border border-[#e4e0eb]">
        <CardHeader className="text-center">
          <CardTitle className="font-playfair text-2xl text-[#270f57] dark:text-[rgb(var(--base-navy))]">Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent className="px-6">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index + 1}`}>
                <AccordionTrigger className='text-[#270f57] dark:text-[rgb(var(--base-navy))]'>{faq.question}</AccordionTrigger>
                <AccordionContent className='text-[#231D30]'>
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
};

export default FaqSection;
