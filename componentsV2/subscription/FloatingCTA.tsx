import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CreditCard } from "lucide-react";

interface FloatingCTAProps {
  show: boolean;
}

const FloatingCTA = ({ show }: FloatingCTAProps) => {
  if (!show) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50 animate-fade-in">
      <Button
        variant="default"
        size="lg"
        className="px-6 h-12 shadow-lg"
        onClick={() => {
          const element = document.querySelector(".container");
          if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "start" });
          }
        }}
      >
        <CreditCard className="h-4 w-4 mr-2" />
        Upgrade Now
      </Button>
    </div>
  );
};

export default FloatingCTA;
