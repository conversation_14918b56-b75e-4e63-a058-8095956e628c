
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface BillingCycleSelectorProps {
  value: "monthly" | "annual";
  onChange: (value: string) => void;
}

const BillingCycleSelector = ({ value, onChange }: BillingCycleSelectorProps) => {
  return (
    <Tabs
      defaultValue="monthly"
      value={value}
      onValueChange={onChange}
      className="w-full max-w-xs mx-auto"
    >
      <TabsList className="grid w-full grid-cols-2 bg-[#e4e0eb] dark:bg-[#2c2a3a]">
        <TabsTrigger value="monthly" className="dark:text-gray-300 data-[state=active]:dark:bg-[#444158] dark:hover:bg-[#3a3751]">Monthly</TabsTrigger>
        <TabsTrigger value="annual" className="dark:text-gray-300 data-[state=active]:dark:bg-[#444158] dark:hover:bg-[#3a3751]">
          Annual
          <span className="ml-1.5 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded-full">
            Save 16%
          </span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

export default BillingCycleSelector;
