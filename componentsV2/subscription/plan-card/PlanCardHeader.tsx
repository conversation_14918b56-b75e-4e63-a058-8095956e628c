import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useSession } from "next-auth/react";

interface PlanCardHeaderProps {
  name: string;
  description: string;
  isPremium: boolean;
  isEnterprise: boolean;
  id: "free" | "premium" | "premium-yearly" | "enterprise";
}

const PlanCardHeader = ({
  name,
  description,
  isPremium,
  isEnterprise,
  id,
}: PlanCardHeaderProps) => {
  const { data: session } = useSession();

  // Determine if this is the user's current plan based on session data
  const isCurrentPlan = React.useMemo(() => {
    if (!session?.user) return false;
    // Direct match
    if (session.user.subscriptionTier === id) return true;

    return false;
  }, [session, id]);

  return (
    <CardHeader
      className={`pb-3 ${isPremium
        ? "bg-[#f9f8fb] rounded-t-lg dark:bg-gray-800/50"
        : isEnterprise
          ? "bg-gray-100/50 dark:bg-gray-800/50 rounded-t-lg"
          : ""
        }`}
    >
      <div className="flex flex-col space-y-2">
        <div className="flex justify-between items-center">
          <CardTitle
            className={`${isPremium ? "text-2xl font-playfair" : "text-xl"
              } text-[#270f57] dark:text-indigo-300`}
          >
            {name}
          </CardTitle>
          {isCurrentPlan && (
            <Badge
              variant="outline"
              className="bg-green-100 border-green-200 text-green-800 font-medium whitespace-nowrap dark:border-green-700 dark:text-green-400"
            >
              Current Plan
            </Badge>
          )}
        </div>
        <div>
          {isPremium ? (
            <p className="text-sm text-black dark:text-gray-300">
              {description}
            </p>
          ) : (
            <CardDescription>{description}</CardDescription>
          )}
        </div>
      </div>
    </CardHeader>
  );
};

export default PlanCardHeader;
