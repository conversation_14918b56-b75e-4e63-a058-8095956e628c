
import React from 'react';
import { PremiumFeature } from "@/componentsV2/subscription/PremiumFeature";
import { Feature } from '../data';
import { Diamond, Users, ShieldCheck } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface PlanFeaturesProps {
  features: {
    core: Feature[];
    collaboration?: Feature[];
    support?: Feature[];
  };
  isPremium: boolean;
  isPreview: boolean;
  isEnterprise: boolean;
}

const PlanFeatures = ({ features, isPremium, isPreview, isEnterprise }: PlanFeaturesProps) => {
  return (
    <div className="space-y-5">
      {/* Only show separator for non-enterprise plans */}
      {!isEnterprise && <Separator />}

      {/* Core features section with conditional heading */}
      <div className="mt-4">
        <h3 className="font-medium text-sm mb-3 flex text-[#270f57] dark:text-indigo-300">
          {isEnterprise ? (
            <>Features</>
          ) : (
            <>
              <Diamond className={`h-4 w-4 mr-2 mt-[3px] ${isPremium ? 'text-primary' : 'text-primary/70'} dark:text-indigo-300`} />
              {isPreview ? "All current Iqidis Core features, limited to:" : "Available Now"}
            </>
          )}
        </h3>
        {features.core.map((feature, index) => (
          <div key={`core-${index}`}>
            <PremiumFeature
              name={feature.name}
              premium={isPremium || isEnterprise}
              tooltip={feature.tooltip}
            />
          </div>
        ))}
      </div>

      {/* Only show collaboration/coming soon section for premium plan */}
      {isPremium && features.collaboration && (
        <div>
          <h3 className="font-medium text-sm mb-3 flex text-[#270f57] dark:text-indigo-300">
            <Users className="h-4 w-4 mr-2 text-primary dark:text-indigo-300" />
            Whats Coming Next
          </h3>
          {features.collaboration.map((feature, index) => (
            <div key={`collaboration-${index}`}>
              <PremiumFeature
                name={feature.name}
                premium={true}
                tooltip={feature.tooltip}
              />
            </div>
          ))}
        </div>
      )}

      {/* Only show support section for premium plan */}
      {isPremium && features.support && (
        <div>
          <h3 className="font-medium text-sm mb-3 flex items-center">
            <ShieldCheck className="h-4 w-4 mr-2 text-primary dark:text-indigo-300" />
            Support
          </h3>
          {features.support.map((feature, index) => (
            <div key={`support-${index}`}>
              <PremiumFeature
                name={feature.name}
                premium={true}
                tooltip={feature.tooltip}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PlanFeatures;
