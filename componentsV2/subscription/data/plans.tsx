import { ReactNode } from "react";

export interface Feature {
  name: string;
  included: boolean;
  tooltip?: string;
}

export interface PlanFeatures {
  core: Feature[];
  collaboration?: Feature[];
  support?: Feature[];
  enterprise?: Feature[];
}

export interface Plan {
  id: "free" | "premium" | "premium-yearly" | "enterprise";
  name: string;
  description: string;
  price: string;
  monthlyEquivalent?: string;
  popular?: boolean;
  features: PlanFeatures;
}

export interface UserSubscription {
  tier: string;
  daysLeft: number;
}

export const getSubscriptionPlans = (
  billingCycle: "monthly" | "annual"
): Record<string, Plan[]> => {
  const plans: Record<string, Plan[]> = {
    monthly: [
      {
        id: "free",
        name: "Free Preview",
        description: "Try Iqidis with limited access",
        price: "0",
        popular: false,
        features: {
          core: [
            { name: "10 queries per day", included: true },
            { name: "10 saved chats", included: true },
            { name: "150 MB total memory available", included: true },
            { name: "Limited support", included: true },
          ],
        },
      },
      {
        id: "premium",
        name: "Iqidis Core",
        description:
          "Lock in this price now for all current and future features. Pricing will increase soon.",
        price: "$249",
        popular: false,
        features: {
          core: [
            {
              name: "Unlimited AI queries",
              included: true,
              tooltip:
                "Run as many queries as you need, without any daily limits.",
            },
            {
              name: "Enhanced document analysis (PDF, Word, and more)",
              included: true,
              tooltip:
                "Extract and analyze information from documents in various formats.",
            },
            {
              name: "Research Mode – context-aware legal research with citations",
              included: true,
              tooltip:
                "Conduct legal research with proper context awareness and automatic citation.",
            },
            {
              name: "Iqidis Playbook – build, chain, and reuse prompts with custom logic",
              included: true,
              tooltip:
                "Create custom workflows with reusable prompts and logic.",
            },
            {
              name: "Iqidis Amplify – enhance any prompt with one click",
              included: true,
              tooltip:
                "Our AI technology enhances all your prompts for superior results with a single click.",
            },
            {
              name: "Citation-ready exports (PDF & Word)",
              included: true,
              tooltip:
                "Generate professional reports with proper legal citations in your preferred format.",
            },
            {
              name: "All future upgrades included at your current price",
              included: true,
              tooltip:
                "All future platform improvements are included in your subscription at no additional cost.",
            },
          ],
          collaboration: [
            {
              name: "Centralized document library",
              included: false,
              tooltip:
                "Coming soon: Organize and manage all your legal documents in one central location.",
            },
            {
              name: "Chat organization + saved matters",
              included: false,
              tooltip:
                "Coming soon: Keep your conversations organized by client, case, or topic.",
            },
            {
              name: "AI Drafting Assistant, with redlining and version control",
              included: false,
              tooltip:
                "Coming soon: Get AI-powered assistance for drafting legal documents with advanced features.",
            },
            {
              name: "Team collaboration tools",
              included: false,
              tooltip:
                "Coming soon: Work together with your team on documents and projects in real-time.",
            },
            {
              name: "Usage & performance analytics",
              included: false,
              tooltip:
                "Coming soon: Track usage patterns and performance metrics across your work.",
            },
            {
              name: "Billing & time tracking",
              included: false,
              tooltip:
                "Coming soon: Easily track billable hours and manage client billing.",
            },
            {
              name: "Advanced Research – shepardizing and more",
              included: false,
              tooltip:
                "Coming soon: Access advanced research tools including citation validation.",
            },
          ],
          support: [
            {
              name: "Priority email support",
              included: true,
              tooltip:
                "Get your questions answered and issues resolved with expedited support.",
            },
            {
              name: "Tailored onboarding available",
              included: true,
              tooltip:
                "Get personalized assistance to help you and your team get started quickly.",
            },
          ],
        },
      },
    ],
    annual: [
      {
        id: "free",
        name: "Free Preview",
        description: "Try Iqidis with limited access",
        price: "$0",
        popular: false,
        features: {
          core: [
            { name: "10 queries per day", included: true },
            { name: "10 saved chats", included: true },
            { name: "150 MB total memory available", included: true },
            { name: "Limited support", included: true },
          ],
        },
      },
      {
        id: "premium-yearly",
        name: "Iqidis Core",
        description:
          "Lock in this price now for all current and future features. Pricing will increase soon.",
        price: "$2,388",
        monthlyEquivalent: "$199/mo",
        popular: false,
        features: {
          core: [
            {
              name: "Unlimited AI queries",
              included: true,
              tooltip:
                "Run as many queries as you need, without any daily limits.",
            },
            {
              name: "Enhanced document analysis (PDF, Word, and more)",
              included: true,
              tooltip:
                "Extract and analyze information from documents in various formats.",
            },
            {
              name: "Research Mode – context-aware legal research with citations",
              included: true,
              tooltip:
                "Conduct legal research with proper context awareness and automatic citation.",
            },
            {
              name: "Iqidis Playbook – build, chain, and reuse prompts with custom logic",
              included: true,
              tooltip:
                "Create custom workflows with reusable prompts and logic.",
            },
            {
              name: "Iqidis Amplify – enhance any prompt with one click",
              included: true,
              tooltip:
                "Our AI technology enhances all your prompts for superior results with a single click.",
            },
            {
              name: "Citation-ready exports (PDF & Word)",
              included: true,
              tooltip:
                "Generate professional reports with proper legal citations in your preferred format.",
            },
            {
              name: "All future upgrades included at your current price",
              included: true,
              tooltip:
                "All future platform improvements are included in your subscription at no additional cost.",
            },
          ],
          collaboration: [
            {
              name: "Centralized document library",
              included: false,
              tooltip:
                "Coming soon: Organize and manage all your legal documents in one central location.",
            },
            {
              name: "Chat organization + saved matters",
              included: false,
              tooltip:
                "Coming soon: Keep your conversations organized by client, case, or topic.",
            },
            {
              name: "AI Drafting Assistant, with redlining and version control",
              included: false,
              tooltip:
                "Coming soon: Get AI-powered assistance for drafting legal documents with advanced features.",
            },
            {
              name: "Team collaboration tools",
              included: false,
              tooltip:
                "Coming soon: Work together with your team on documents and projects in real-time.",
            },
            {
              name: "Usage & performance analytics",
              included: false,
              tooltip:
                "Coming soon: Track usage patterns and performance metrics across your work.",
            },
            {
              name: "Billing & time tracking",
              included: false,
              tooltip:
                "Coming soon: Easily track billable hours and manage client billing.",
            },
            {
              name: "Advanced Research – shepardizing and more",
              included: false,
              tooltip:
                "Coming soon: Access advanced research tools including citation validation.",
            },
          ],
          support: [
            {
              name: "Priority email support",
              included: true,
              tooltip:
                "Get your questions answered and issues resolved with expedited support.",
            },
            {
              name: "Tailored onboarding available",
              included: true,
              tooltip:
                "Get personalized assistance to help you and your team get started quickly.",
            },
          ],
        },
      },
    ],
  };

  // Add Enterprise plan for both monthly and annual billing cycles
  const enterprisePlan = {
    id: "enterprise" as const,
    name: "Enterprise",
    description: "Custom solutions for teams and organizations",
    price: "Contact Sales",
    popular: false,
    features: {
      core: [
        { name: "Everything in Iqidis Core", included: true },
        {
          name: "Personal onboarding",
          included: true,
          tooltip:
            "Get a personalized onboarding experience to help you get started quickly.",
        },
        {
          name: "Multi-license and multi-team plans",
          included: true,
          tooltip:
            "Scale your subscription with multiple licenses for team members across your organization.",
        },
        {
          name: "Custom solutions",
          included: true,
          tooltip:
            "Work with our team to develop custom solutions tailored to your specific requirements.",
        },
      ],
    },
  };

  plans.monthly.push(enterprisePlan);
  plans.annual.push(enterprisePlan);

  return plans;
};
