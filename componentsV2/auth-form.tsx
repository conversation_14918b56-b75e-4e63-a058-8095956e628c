import { useState } from "react";
import Form from "next/form";
import Link from "next/link";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff, Lock, Mail } from "lucide-react";

export function AuthForm({
  action,
  children,
  defaultEmail = "",
  password = "",
  emailError = null,
  passwordError = null,
  setEmail,
  setPassword,
  showPassword,
  setShowPassword,
}: {
  action: NonNullable<
    string | ((formData: FormData) => void | Promise<void>) | undefined
  >;
  children: React.ReactNode;
  defaultEmail?: string;
  password?: string;
  emailError?: string | null;
  passwordError?: string | null;
  setEmail?: React.Dispatch<React.SetStateAction<string>>;
  setPassword?: React.Dispatch<React.SetStateAction<string>>;
  showPassword: boolean;
  setShowPassword: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  // Remove these state variables as they're now passed as props
  // const [email, setEmail] = useState(defaultEmail);
  // const [emailError, setEmailError] = useState<string | null>(null);
  // const [password, setPassword] = useState('');
  // const [passwordError, setPasswordError] = useState<string | null>(null);

  // // Update validateEmail to use the passed setEmailError if available
  // const validateEmail = (email: string) => {
  //   const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  //   if (!emailRegex.test(email)) {
  //     setEmail && setEmail("Please enter a valid email address.");
  //   } else {
  //     setEmail && setEmail("");
  //   }
  // };

  // const validatePassword = (password: string) => {
  //   if (password.length === 0) {
  //     setPasswordError && setPasswordError("Password is required");
  //   } else if (password.length < 8) {
  //     setPasswordError &&
  //       setPasswordError("Password must be at least 8 characters long.");
  //   } else {
  //     setPasswordError && setPasswordError("");
  //   }
  // };

  return (
    <Form action={action} className="flex flex-col gap-2 w-full">
      <div className="flex flex-col gap-1.5">
        <Label
          htmlFor="email"
          className="flex items-center text-sm font-medium text-slate-700 dark:text-slate-200"
        >
          <Mail size={16} className="mr-1.5 text-slate-500" />
          Email
        </Label>
      </div>

      <Input
        id="email"
        name="email"
        className={`frosted-input bg-[#f9f8fb] border ${emailError
            ? "border-rose-500 focus-visible:ring-rose-500"
            : "border-[#d6d1e0]"
          } dark:bg-slate-800/60`}
        type="email"
        placeholder="<EMAIL>"
        autoComplete="email"
        // autoFocus
        defaultValue={defaultEmail}
        onChange={(e) => setEmail && setEmail(e.target.value)}
      />
      {emailError && <p className="text-sm text-rose-500 mt-1">{emailError}</p>}

      <div className="flex flex-col gap-1.5">
        <div className="flex justify-between items-center">
          <Label
            htmlFor="password"
            className="flex items-center text-sm font-medium text-slate-700 dark:text-slate-200"
          >
            <Lock size={16} className="mr-1.5 text-slate-500" />
            Password
          </Label>
          <Link
            href="/forgot-password"
            className="text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm font-medium"
          >
            Forgot password?
          </Link>
        </div>
        <div className="relative">
          <Input
            id="password"
            name="password"
            className={`frosted-input bg-[#f9f8fb] border ${passwordError
                ? "border-rose-500 focus-visible:ring-rose-500"
                : "border-[#d6d1e0]"
              } dark:bg-slate-800/60`}
            type={showPassword ? "text" : "password"}
            placeholder="••••••••"
            autoComplete="current-password"
            value={password}
            onChange={(e) => {
              setPassword && setPassword(e.target.value);
              // validatePassword(e.target.value);
            }}
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            onClick={() => setShowPassword((prev) => !prev)}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>

        {passwordError && (
          <p className="text-sm text-rose-500 mt-1">{passwordError}</p>
        )}
      </div>

      {children}
    </Form>
  );
}
