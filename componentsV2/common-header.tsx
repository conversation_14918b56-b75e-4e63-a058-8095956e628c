"use client";

import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import { HomeIcon, Loader2, Minimize2, MoveDiagonal } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { LogoIqidis } from "./icons";
import { PageLoader } from "@/components/ui/page-loader";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { UserMenu } from "./user-menu";
import { NotificationsMenu } from "./notifications/NotificationsMenu";
import Link from "next/link";

// Helper function to get page name based on pathname
const getPageName = (pathname: string): string => {
  if (pathname === "/") return "Home";
  if (pathname.includes("/library")) return "Library";
  if (pathname.includes("/account")) return "Account";
  if (pathname.includes("/subscription")) return "Subscription";
  if (pathname.includes("/preferences")) return "Preferences";
  if (pathname.includes("/welcome")) return "Welcome";
  if (pathname.includes("/guides")) return "Guides";
  if (pathname.includes("/pricing")) return "Pricing";
  if (pathname.includes("/admin")) return "Admin";
  if (pathname.includes("/help")) return "Help";

  // Default fallback - capitalize first letter of path segment
  const segments = pathname.split("/").filter(Boolean);
  if (segments.length > 0) {
    return segments[segments.length - 1].charAt(0).toUpperCase() + segments[segments.length - 1].slice(1);
  }

  return "Page";
};

export function CommonHeader({ brandingOnly = false }) {
  const router = useRouter();
  const pathname = usePathname();
  const [showMenuButtons, setShowMenuButtons] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const handleShowMenuState = (status: boolean) => {
    setShowMenuButtons(status);
  };

  // Clear loading state when navigation completes
  useEffect(() => {
    // Only clear loading for non-subscription pages
    // For subscription pages, the SubscriptionPageContent will handle this
    if (!pathname.includes("/subscription") && !pathname.includes("/welcome")) {
      setIsLoading(false);
    }

    // Cleanup function
    return () => {
      if (
        !pathname.includes("/subscription") &&
        !pathname.includes("/welcome")
      ) {
        localStorage.removeItem("navigationInProgress");
      }
    };
  }, [pathname]);

  const currentPageName = getPageName(pathname);

  return (
    <>
      <header
        className="flex top-0 inset-x-0 py-4 items-center px-2 md:px-4 gap-2 md:z-[60] z-[60] h-[72px] chat-background-light dark:chat-background-dark"
      >
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center lg:gap-2 gap-0">
            {/* Page name display */}
            <div className="flex items-center gap-2 min-w-0">
              <p className="text-xl font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                {currentPageName}
              </p>
            </div>



            {!showMenuButtons && !brandingOnly && (
              <Button
                variant="ghost"
                className="hidden md:flex md:px-3 md:h-[36px] transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300"
                onClick={() => handleShowMenuState(true)}
              >
                <MoveDiagonal size={18} />
              </Button>
            )}
          </div>

          <div className="flex items-center justify-end lg:space-x-3 space-x-2">
            {!brandingOnly && (
              <>
                <NotificationsMenu />
              </>
            )}
          </div>
        </div>
      </header>

      {/* Replace the custom loader with PageLoader */}
      {isLoading && <PageLoader message="Loading..." />}
    </>
  );
}
