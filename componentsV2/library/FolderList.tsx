'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FolderPlus, Share2, Trash } from "lucide-react";
import { useState } from "react";
import { Row, Col, Dropdown, Spin, Typography, Tooltip } from "antd";
import { UploadButton } from "./Upload/UploadButton";
import { UploadModal } from "./Upload/UploadModal";
import { Navbar } from "./Navbar";
import { CreateFolderButton } from "./Folder/CreateFolderButton";
import { CreateFolderModal } from "./Folder/CreateFolderModal";
import { MoreHorizontal } from "lucide-react";
import { useMemoizedFn, useRequest } from "ahooks";
import { getFolders } from "./request/folders";
import { FolderEmptyIcon } from '@/componentsV2/icons'
import { DeleteFolderButton, DeleteFolderModal } from "./Folder/DeleteFolder";
import { usePathname, useRouter } from 'next/navigation';
import { PageLoader } from "@/components/ui/page-loader";

const { Text: TypographyText } = Typography

export function FolderList() {

  const [refresh, setRefresh] = useState(0)
  const router = useRouter();


  const refreshFolders = useMemoizedFn(() => {
    setRefresh(prev => prev + 1)
  })

  const { data: { folders, rootFolderId } = {}, loading } = useRequest(() => {
    return getFolders()
  }, {
    refreshDeps: [refresh]
  })

  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  // Intercept navigation to show loader
  const handleRoute = (path: string) => {
    // Set global loading state
    if (pathname !== path) {
      setIsLoading(true);
      router.push(path);
    }
  };

  return (
    <div className="flex flex-1 overflow-hidden h-full mr-4 mb-4">
      <div className="oveflow-hidden w-full flex flex-col chat-background-light dark:chat-background-dark border rounded-md border-gray-200 dark:border-gray-800 p-6"  >
        <div className="flex items-center justify-between mb-3 gap-3 flex-wrap">
          <div className="bg-white p-1 flex rounded-md w-fit libraryTabsDFS overflow-auto dark:bg-card dark:border dark:border-input">
            <Navbar active="Folders" handleRoute={handleRoute} />
          </div>
          <div className="flex gap-x-3">
            <CreateFolderButton />
            <UploadButton />
          </div>
        </div>

        <div className="flex-1 rounded-lg bg-white sm:p-5 p-3 dark:bg-card dark:border dark:border-input overflow-auto">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <Spin size="large" />
            </div>
          ) : !folders?.length ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-medium">
              <FolderEmptyIcon />
              <div className="text-lg mt-8 mb-5 text-black font-semibold dark:text-white">No Records Yet</div>
              {/* <p className="text-sm text-gray leading-6 max-w-[478px] text-center">This is a description. Here is some text to help you understand the situation.  This is a description. Here is some text to...</p> */}
            </div>
          ) : (
            <Row gutter={[
              { xs: 12, sm: 20 },  // horizontal gutter: 12px for <576px, 20px for ≥576px
              { xs: 12, sm: 20 }   // vertical gutter:   same breakpoints
            ]}>
              {folders.map((folder: any) => {
                return (
                  <Col
                    xs={12}
                    sm={6}
                    lg={4.8}
                    key={folder.folderId}
                  >
                    <div className="bg-white dark:bg-background dark:text-white sm:p-4 p-2 rounded-lg border cursor-pointer hover:bg-gray-200 dark:hover:bg-background h-full flex flex-col justify-between" onClick={(e) => {
                      const target = e.target as HTMLElement;
                      if (target.className && target.className.includes('ant-dropdown')) {
                        return;
                      }
                      handleRoute(`/library/folders/${folder.folderId}`)
                    }}>
                      {/* TODO hot area to small */}
                      <div className="flex items-center justify-between mb-0.5 dark:bg-background">
                        <TypographyText className="leading-[20px] text-sm font-semibold text-black dark:text-white" ellipsis={{ tooltip: folder.folderName }}>{folder.folderName}</TypographyText>
                        <Dropdown
                          arrow
                          menu={{
                            className: 'dark:bg-background w-[140px] [&>li:not(:last-child)]:border-b [&>li:not(:last-child)]:border-gray-light dark:border-background [&>li]:rounded-none [&>li]:border-dashed [&>li]:!py-2',
                            items: [
                              {
                                label: <CreateFolderButton mode="edit" folderId={folder.folderId} folderName={folder.folderName} folderDescription={folder.folderDescription}><span className="text-sm flex items-center gap-x-2 dark:text-white"><FolderPen className="size-4 text-gray-medium dark:text-white" />Edit</span></CreateFolderButton>,
                                key: 'rename',
                              },
                              {
                                label: <Tooltip title="Coming soon"><span onClick={e => {
                                  e.stopPropagation()
                                }} className="text-sm flex items-center gap-x-2 text-gray-medium"><Share2 className="size-4 text-gray-medium" />Share</span></Tooltip>,
                                key: 'share',
                                disabled: true,
                              },
                              {
                                label: <DeleteFolderButton folderId={folder.folderId} folderName={folder.folderName}><span className="text-sm flex items-center gap-x-2 text-function-error"><Trash className="size-4" />Delete</span></DeleteFolderButton>,
                                key: 'delete',
                              },
                            ],
                            onClick: (e) => {
                              e.domEvent.stopPropagation()
                            }
                          }}
                          trigger={['click']}
                        >
                          <div className="group cursor-pointer hover:bg-gray-200 p-0.5 rounded-sm" onClick={e => {
                            e.stopPropagation()
                          }}>
                            <MoreHorizontal className="size-4 group-hover:text-function-message" />
                          </div>
                        </Dropdown>
                      </div>
                      <TypographyText ellipsis={{ tooltip: folder.folderDescription }} className="text-xs text-gray-light mb-0.5">{folder.folderDescription}</TypographyText>
                      <div className="text-xs text-gray-medium">
                        {folder.documentCount} documents
                      </div>
                    </div>
                  </Col>
                )
              })}
              <Col
                xs={12}
                sm={6}
                lg={4.8}
              >
                <CreateFolderButton>
                  <div className="bg-white dark:bg-background p-4 rounded-lg border flex flex-col justify-between gap-y-3 sm:items-center cursor-pointer hover:bg-gray-200 !dark:hover:bg-black">
                    <FolderPlus className="size-6" />
                    <div className="text-black dark:text-white font-semibold text-sm">Create New Folder</div>
                  </div>
                </CreateFolderButton>
              </Col>
            </Row>
          )}
        </div>

        <UploadModal
          folderList={folders}
          onClose={(dirty) => {
            if (dirty) {
              refreshFolders()
            }
          }}
        />
        <CreateFolderModal
          parentId={rootFolderId}
          onConfirm={() => {
            refreshFolders()
          }}
        />
        <DeleteFolderModal onConfirm={() => {
          refreshFolders()
        }} />
        {isLoading && <PageLoader message="Loading..." />}
      </div>
    </div>
  );
};