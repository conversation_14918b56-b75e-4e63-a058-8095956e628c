"use client"
import { Mo<PERSON>, Button, Input, Form } from 'antd'
import { FolderDialogAtom } from './store'
import { useAtom } from 'jotai'
import { useMemoizedFn, useRequest } from 'ahooks'
import { useEffect, useState } from 'react'
import { checkFolderDuplicate, createFolder, renameFolder } from '../request/folders'
import { toast } from 'sonner'
import { Loader2 } from 'lucide-react'
const { Item: FormItem } = Form

const invalidChars = ['/', '\\', '?', '%', '*', ':', '|', '"', '<', '>']

export function CreateFolderModal(props: {
  parentId: string
  onConfirm?: (name: string, mode?: 'create' | 'edit', id?: string) => void 
}) {

  const [folderInfo, setFolderInfo] = useAtom(FolderDialogAtom)
  const [checkingDuplicate, setCheckingDuplicate] = useState(false)
  const [isButtonDisabled, setIsButtonDisabled] = useState(false)

  const closeModal = useMemoizedFn(() => {
    setFolderInfo({
      mode: 'create',
      id: undefined,
      folderName: '',
      folderDescription: undefined,
      open: false
    })
    form.resetFields()
  })

  const [form] = Form.useForm<{
    name: string
    description?: string
  }>()
  useEffect(() => {
    form.setFieldValue('name', folderInfo.folderName)
    form.setFieldValue('description', folderInfo.folderDescription)
  }, [folderInfo])

  const folderName = Form.useWatch('name', form)

  useEffect(() => {
    form.validateFields(['name']).then(() => {
      setIsButtonDisabled(false)
    }).catch(() => {
      setIsButtonDisabled(true)
    })
  }, [folderName])

  const { data: isDuplicate, runAsync: checkFolderNameDuplicate } = useRequest(async () => {
    if (!props.parentId || !folderName) {
      return false
    }
    setCheckingDuplicate(true)
    const [isDuplicate] = await Promise.all([checkFolderDuplicate(folderName.trim(), props.parentId, folderInfo.id), new Promise(resolve => setTimeout(resolve, 400))])
    setCheckingDuplicate(false)
    return isDuplicate
  }, {
    manual: true
  })

  const { runAsync: createFolderRequest, loading: creatingFolder } = useRequest(async () => {
    const folderName = form.getFieldValue('name')
    const description = form.getFieldValue('description')
    if (folderInfo.mode === 'create' && folderName) {
      return createFolder({ name: folderName.trim(), parentId: props.parentId, description, })
    }
    if (folderInfo.mode === 'edit' && folderInfo.id && folderName) {
      return renameFolder({ name: folderName.trim(), parentId: props.parentId, description }, folderInfo.id)
    }
  }, {
    manual: true,
  })

  return (
    <Modal
      centered
      title={folderInfo.mode === 'create' ?  "Create New Folder" : 'Rename Folder Name'}
      width={500}
      open={folderInfo.open}
      onCancel={closeModal}
      classNames={{
        body: '!pt-6 !pb-6 border-b border-t',
        header: '!pb-1.5',
      }}
      footer={
        <div className='flex justify-center gap-x-4 pt-2'>
          <Button color="primary" size='large' variant="outlined" onClick={closeModal}>Cancel</Button>
          <Button color="primary" size='large' variant="solid" loading={creatingFolder} disabled={!folderName || folderName.trim() === '' || isDuplicate || checkingDuplicate || isButtonDisabled}  onClick={async () => {
            try {
              await form.validateFields(['name'])
              await createFolderRequest()
              closeModal()
              toast.success(folderInfo.mode === 'create' ? 'Folder created successfully' : 'Folder renamed successfully')
              props.onConfirm?.(folderName, folderInfo.mode, folderInfo.id)
            } catch (error) {
              toast.error(typeof error === 'string' ? error : 'Failed to create folder')
            }
          }}>{folderInfo.mode === 'create' ? 'Create' : 'Rename'}</Button>
        </div>
      }
    >
      <Form
        form={form}
        component="div"
        className='min-h-[70px]'
        layout="vertical"
        requiredMark={false}
      >
        <FormItem
          name="name"
          label={<>Folder Name <span className='text-red-500'>*</span></>}
          required
          validateDebounce={300}
          validateFirst
          rules={[
            {
              max: 50,
              message: 'Folder name cannot exceed 50 characters'
            },
            {
              validator: async (_, value) => {
                if (!value) return;
                // Disallow: / \ ? % * : | " < > (Windows-safe)
                if (invalidChars.some(char => value.includes(char))) {
                  throw new Error('Folder name cannot contain the following characters: / \\ ? % * : | " < >');
                }
              }
            },
            {
              validator: async (_, value) => {
                if (!value) return;
                if (value.trim() === '') {
                  throw new Error('Folder name cannot consist of only whitespace characters');
                }
                if (value[0] === ' ') {
                  throw new Error('Folder name cannot start with whitespace characters');
                }
                if (value[value.length - 1] === ' ') {
                  throw new Error('Folder name cannot end with whitespace characters');
                }
              }
            },
            {
              validator: async (_, value) => {
                if (!value) return;
                try {
                  const isDuplicate = await checkFolderNameDuplicate();
                  if (isDuplicate) {
                    throw new Error('Folder name already exists');
                  }
                } finally {
                  setCheckingDuplicate(false);
                }
              }
            }
          ]}
          help={checkingDuplicate ? <span className='flex items-center gap-x-1'><Loader2 className='size-3 animate-spin' /> Checking duplicate...</span> : undefined}
        >
          <Input
            placeholder='Please input folder name'
          /> 
        </FormItem>
        <FormItem
          name="description"
          label="Folder Description"
          layout="vertical"
          rules={[
            {
              max: 100,
              message: 'Folder description cannot exceed 100 characters'
            }
          ]}
        >
          <Input placeholder='Please input folder description' />
        </FormItem>
      </Form>
    </Modal>
  )
}