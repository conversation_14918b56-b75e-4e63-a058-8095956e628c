import { Dispatch, SetStateAction, useMemo, useRef, useState, useEffect } from "react"
import { useDeepCompareEffect, useRequest } from "ahooks"
import { DocumentTypeFilter, DOCUMENT_TYPE_FILTER_ITEMS } from "../../DocumentTable/documentTypeFilter"
import { DownloadIcon, Search } from "lucide-react"
import { Input, InputRef, Select, Table } from "antd"
import { LibraryFilterAtom, LibrarySelectorDialogAtom } from "../store"
import { useAtom, useSetAtom } from "jotai"
import { Button } from "@/components/ui/button";
import { generateDocumentColumns } from "../../DocumentTable/documentColumns"
import type { Attachment } from "ai"
import { get, uniqBy } from "lodash-es"
import { generateUUID } from "@/lib/utils"
import { useTheme } from "next-themes"

export default function LibraryFolderView(props: {
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
}) {
  const setLibrarySelectorDialog = useSetAtom(LibrarySelectorDialogAtom)
  const { theme: themeMode } = useTheme()

  const fileNameRef = useRef<InputRef>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [selectedRowCache, setSelectedRowCache] = useState<any[]>([])
  const [libraryFilter, setLibraryFilter] = useAtom(LibraryFilterAtom)

  const [fileTypes, setFileTypes] = useState<string[]>([])
  const [fileName, setFileName] = useState<string>('')
  const [sort, setSort] = useState<[string, 'descend' | 'ascend'] | []>([])
  const [pagination, setPagination] = useState<{
    current?: number
    pageSize?: number
    total?: number
  }>({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  // Fetch folders if not already loaded
  const { data: foldersData } = useRequest(async () => {
    const queryString = new URLSearchParams({
      status: 'AVAILABLE',
    })
    const response = await fetch(`/api/folders?${queryString.toString()}`, {
      method: 'GET',
    })
    const data = await response.json()
    return data
  }, {
    onSuccess: (data) => {
      if (!libraryFilter.folders?.length) {
        setLibraryFilter(prev => ({
          ...prev,
          folders: data.folders?.map((folder: any) => ({
            folderId: folder.folderId,
            folderName: folder.folderName,
          })) ?? [],
          rootFolderId: data.rootFolder?.folderId,
          currentFolderId: data.rootFolder?.folderId,
        }))
      }
    }
  })

  const { loading: getDocumentsLoading, data: dataSource } = useRequest(async () => {
    const queryString = new URLSearchParams({
      current: pagination.current?.toString() ?? '1',
      pageSize: pagination.pageSize?.toString() ?? '10',
      status: 'AVAILABLE',
    })
    if (fileName) {
      queryString.append('filename', fileName)
    }

    if (sort.length === 2) {
      queryString.append('order', `${sort[0]}_${sort[1]}`)
    }

    if (libraryFilter.currentFolderId && libraryFilter.currentFolderId !== libraryFilter.rootFolderId) {
      queryString.append('folder', libraryFilter.currentFolderId)
    }

    if (fileTypes.length > 0) {
      const mimeTypes = fileTypes.flatMap(filter => {
        const item = DOCUMENT_TYPE_FILTER_ITEMS.find(item => item.key === filter)
        return item?.mimes ?? []
      })
      mimeTypes.forEach(mime => {
        queryString.append('fileType', mime)
      })
    }

    const response = await fetch(`/api/documents?${queryString.toString()}`, {
      method: 'GET',
    })

    const data = await response.json()
    setPagination({
      current: data.pagination.current,
      pageSize: data.pagination.pageSize,
      total: data.pagination.total,
    })
    return data.documents
  }, {
    debounceWait: 200,
    refreshDeps: [sort[0], sort[1], fileTypes, pagination.current, pagination.pageSize, fileName, libraryFilter.currentFolderId],
  })

  useEffect(() => {
    setPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    })
  }, [fileName, libraryFilter.currentFolderId, fileTypes])

  const columns = useMemo(() => generateDocumentColumns({
    sort,
    dataSource,
    setDataSource: () => { },
  }), [dataSource, sort])

  useDeepCompareEffect(() => {
    setSelectedRowKeys(props.attachments.filter(attachment => !!get(attachment, '_attachment_id')).map(attachment => get(attachment, '_attachment_id', '')))
  }, [props.attachments])

  return (
    <div className="flex flex-col gap-2 pt-2 px-6">
      <div className="grid md:grid-cols-2 gap-3">
        <div className="w-full">
          <Input
            placeholder="Search documents..."
            ref={fileNameRef}
            prefix={<Search className="size-4 text-gray-medium" />}
            allowClear
            onPressEnter={() => {
              setFileName(fileNameRef.current?.input?.value ?? '')
            }}
            onBlur={() => {
              setFileName(fileNameRef.current?.input?.value ?? '')
            }}
            onClear={() => {
              setFileName('')
            }}
            className={themeMode === 'dark' ? 'bg-gray-900 text-white border-gray-700' : ''}
          />
        </div>
        <div className="flex items-center gap-1 w-full">
          Folder:  <Select
            showSearch={true}
            filterOption={(input, option) =>
              (option?.folderName ?? '').toLowerCase().includes(input.toLowerCase())
            }
            className={`w-full ${themeMode === 'dark' ? 'bg-gray-900 text-white border-gray-700' : ''}`}
            options={[{
              folderName: 'All Documents',
              folderId: libraryFilter.rootFolderId,
            }, ...(libraryFilter.folders || [])]}
            fieldNames={{
              label: 'folderName',
              value: 'folderId',
            }}
            onChange={(value) => {
              setLibraryFilter(prev => ({
                ...prev,
                currentFolderId: value,
              }))
            }}
            value={libraryFilter.currentFolderId}
          ></Select>
          <DocumentTypeFilter theme={themeMode} typeFilters={fileTypes} setTypeFilters={setFileTypes} className={`border rounded-md ${themeMode === 'dark' ? 'border-gray-700 bg-gray-900 text-white' : 'border-[#D9D9D9]'}`} />
        </div>

      </div>
      <div className="flex justify-between">
        <div className="flex items-center gap-x-2">
          <span className={themeMode === 'dark' ? 'text-gray-300' : 'text-gray-medium'}>{pagination.total ?? '-'} documents</span>
        </div>
        <Button
          variant="outline"
          size="sm"
          disabled={selectedRowKeys.length === 0}
          className={`bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground disabled:bg-white disabled:text-gray-medium disabled:cursor-not-allowed ${themeMode === 'dark' ? 'bg-main text-white border-gray-700 hover:bg-main hover:text-white disabled:bg-main disabled:text-gray-500' : ''}`}
          onClick={() => {
            if (selectedRowKeys.length === 0) {
              return
            }
            props.setAttachments(uniqBy([...props.attachments, ...selectedRowKeys.filter(id => selectedRowCache.some((doc: any) => doc.id === id)).map(id => {
              const doc = selectedRowCache.find((doc: any) => doc.id === id)
              return {
                document_id: generateUUID(),
                _attachment_id: doc.id,
                contentType: doc.mime,
                name: doc.originalName,
                url: `https://iqidis-artifact.s3.us-east-1.amazonaws.com/${doc.storageKey}`,
              }
            })], '_attachment_id'))
            setSelectedRowKeys([])
            setLibrarySelectorDialog({
              open: false,
              viewVirtualPath: '',
            })
          }}
        >
          <DownloadIcon />
          Attach ({selectedRowKeys.length})
        </Button>
      </div>
      <div className="pt-1">
        <Table
          className="dashLibraryTable"
          loading={getDocumentsLoading}
          dataSource={dataSource}
          columns={columns.filter(column => ['originalName', 'mime', 'size', 'updatedAt'].includes(column.dataIndex))
            .map(column => ({
              ...column,
              width: column.dataIndex === 'originalName' ? 200 : column.dataIndex === 'mime' ? 80 : 100
            }))
          }

          size="small"
          rowSelection={{
            selectedRowKeys,
            type: 'checkbox',
            onChange: (selectedRowKeys) => {
              setSelectedRowKeys(selectedRowKeys as string[])
            },
            onSelectAll: (selected, selectedRows) => {
              if (selected) {
                setSelectedRowCache(prev => {
                  return uniqBy([...prev, ...selectedRows].filter(Boolean), 'id')
                })
              }
            },
            onSelect: (record, selected) => {
              if (selected) {
                setSelectedRowCache(prev => {
                  return uniqBy([...prev, record].filter(Boolean), 'id')
                })
              }
            },
            preserveSelectedRowKeys: true,
          }}
          scroll={{
            y: 500,
          }}
          rowKey="id"
          onChange={(pagination, filters, sorter) => {
            if (pagination.current && pagination.pageSize) {
              setPagination((prev: any) => {
                return {
                  ...prev,
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                }
              })
            }

            if (!(sorter instanceof Array)) {
              if (sorter.field && sorter.order) {
                setSort([String(sorter.field), sorter.order])
              } else {
                setSort([])
              }
            }

          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            pageSizeOptions: [10, 20, 50],
          }}
        />
      </div>
    </div>
  )
}