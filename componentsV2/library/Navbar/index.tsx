import { FileText, Folder<PERSON><PERSON>, Share2 } from "lucide-react";
import Link from 'next/link';
import { useEffect, useRef } from 'react';
import { Tooltip } from "antd";

export const tabs = [
  {
    type: 'All Documents',
    icon: <FileText className="w-[18px] h-[18px]" />,
    href: '/library'
  },
  // {
  //   type: 'Recent',
  //   icon: <Clock className="w-[18px] h-[18px]" />,
  //   disabled: true,
  //   href: '/library/recent'
  // },
  {
    type: 'Folders',
    icon: <FolderOpen className="w-[18px] h-[18px]" />,
    href: '/library/folders'
  },
  {
    type: 'Shared',
    icon: <Share2 className="w-[18px] h-[18px]" />,
    disabled: true,
    href: '/library/shared'
  }
]

type TabRefs = {
  [key: string]: HTMLElement | null;
}

export function Navbar(props: {active?: string, handleRoute?: (path: string) => void} = {}) {
  const { active = tabs[0].type, handleRoute } = props;
  const tabRefs = useRef<TabRefs>({});

  useEffect(() => {
    if (active && tabRefs.current[active]) {
      tabRefs.current[active]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      });
    }
  }, [active]);

  return (
    <div className="bg-white p-1 flex rounded-md w-full overflow-x-auto dark:bg-card">
      {tabs.map((tab) => (
        tab.disabled ? (
          <Tooltip
            title="Coming soon"
            placement="bottom"
            key={tab.type}
          >
          <div
            key={tab.type}
         ref={(el) => { tabRefs.current[tab.type] = el }}
            className={`flex gap-x-2 justify-center items-center px-4 py-3 ${active === tab.type ? 'text-[#8b5cf6] stroke-[#8b5cf6]' : 'text-gray-medium'} cursor-not-allowed`}
          >
            {tab.icon}
            <span className={`text-sm text-nowrap ${active === tab.type ? 'text-[#8b5cf6] font-semibold' : 'text-gray-medium font-medium'}`}>{tab.type}</span>
          </div>
          </Tooltip>
        ) : (
          <div
            key={tab.type}
            ref={(el) => { tabRefs.current[tab.type] = el }}
            // href={tab.href}
            className={`flex gap-x-2 justify-center items-center px-4 py- ${active === tab.type ? 'text-[#8b5cf6] dark:text-[#8b5cf6]' : 'text-gray-medium dark:text-white'} cursor-pointer `}
            onClick={() => handleRoute?.(tab.href)}
          >
            {tab.icon}
            <span className={`text-sm text-nowrap ${active === tab.type ? 'text-[#8b5cf6] font-semibold dark:text-[#8b5cf6]' : 'dark:text-white font-medium'}`}>{tab.type}</span>
          </div>
        )
      ))}
    </div>
  );
}