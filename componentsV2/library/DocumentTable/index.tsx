import { Table, TableProps } from "antd"
import { generateDocumentColumns } from "./documentColumns"
import "./overwrite.css"
import { useMemo } from "react"

interface DocumentTableProps {
  sort: [string, 'descend' | 'ascend'] | []
  setSort: (sort: [string, 'descend' | 'ascend'] | []) => void
  loading: boolean
  dataSource: any[]
  setDataSource: (dataSource: any[]) => void
  selectedRowKeys: string[]
  setSelectedRowKeys: (selectedRowKeys: string[]) => void
  pagination: {
    current?: number
    pageSize?: number
    total?: number
  }
  setPagination: (pagination: any) => any
  tableProps?: TableProps<any>
}

export const DocumentTable = (props: DocumentTableProps) => {

  const columns = useMemo(() => generateDocumentColumns({
    sort: props.sort,
    dataSource: props.dataSource,
    setDataSource: props.setDataSource,
  }), [props.dataSource, props.sort])

  return <Table
      className="h-full document-table"
      loading={props.loading}
      showSorterTooltip
      size="middle"
      rowSelection={{
        selectedRowKeys: props.selectedRowKeys,
        type: 'checkbox',
        onChange: (selectedRowKeys) => {
          props.setSelectedRowKeys(selectedRowKeys as string[])
        },
      }}
      columns={columns}
      rowKey="id"
      dataSource={props.dataSource}
      onChange={(pagination, filters, sorter) => {
        if (pagination.current && pagination.pageSize) {
          props.setPagination((prev: any) => {
            return {
              ...prev,
              current: pagination.current,
              pageSize: pagination.pageSize,
            }
          })
        }

        if (!(sorter instanceof Array)) {
          if ( sorter.field && sorter.order) {
            props.setSort([String(sorter.field), sorter.order])
          } else {
            props.setSort([])
          }
        }

      }}
      pagination={{
        current: props.pagination.current,
        pageSize: props.pagination.pageSize,
        total: props.pagination.total,
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 50],
      }}
      {...props.tableProps}
    />
}