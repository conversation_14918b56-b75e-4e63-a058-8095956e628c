.document-table .ant-table-body {
  scrollbar-width: thin;
  max-height: 100% !important;
  overflow: auto !important;
  /* Firefox */
}

html.dark .document-table .ant-table-body {
  border: 1px solid hsl(251 14% 28%) !important;
}

/* Chrome、Edge 滚动条美化 */
.document-table .ant-table-body::-webkit-scrollbar {
  width: 6px;
}

.document-table .ant-table-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.document-table .ant-table-body::-webkit-scrollbar-track {
  background-color: transparent;
}

.document-table .ant-table-container {
  min-width: 992px !important;
}

.document-table .ant-table-body {
  overflow: auto !important;
  max-height: calc(100vh - 365px) !important;
  border: 1px solid hsl(251 14%) !important;
}

html.dark .ant-table-container table th {
  background-color: rgb(75 85 99) !important;
  color: #ffffff !important;
}

html.dark .ant-table-tbody > tr.ant-table-placeholder {
  background: hsl(0 0% 7%);
  border: 1px solid hsl(251 14%) !important;
}

html.dark .ant-table-tbody > tr.ant-table-placeholder:hover,
.html.dark tr.ant-table-placeholder:hover > td {
  background: hsl(0 0% 7%) !important;
  border: 1px solid hsl(251 14%) !important;
}

html.dark .ant-table-tbody .ant-table-cell {
  border: 0px none;
}

html.dark .ant-table-tbody {
  background: #000 !important;
  color: #fff;
}

html.dark .ant-table-cell.ant-table-selection-column.ant-table-cell-row-hover,
html.dark .ant-table-cell.ant-table-cell-row-hover {
  background-color: hsl(0 0 10%) !important;
  color: #fff !important;
}

html.dark
  .ant-table-wrapper
  .ant-table-tbody
  .ant-table-row.ant-table-row-selected
  > .ant-table-cell {
  background-color: hsl(0 0 20%) !important;
}

html.dark .searchBarLibrary {
  background: #000 !important;
  border: 1px solid hsl(251 14% 28%) !important;
  color: #fff !important;
}

html.dark .ant-input-affix-wrapper > input.ant-input::placeholder {
  color: #fff !important;
  opacity: 1 !important;
}

html.dark .ant-typography {
  color: #fff;
}

html.dark .ant-select-dropdown.ant-select-dropdown-placement-bottomRight {
  background-color: #333333 !important;
  color: #fff;
}

html.dark .ant-select-item-option-content {
  color: #fff;
}

html.dark
  .ant-select-item.ant-select-item-option.ant-select-item-option-active.ant-select-item-option-selected {
  background: #000 !important;
  color: #fff !important;
}

html.dark
  .ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background: #000 !important;
  color: #fff !important;
}

html.dark .ant-select-dropdown {
  background-color: #333333 !important;
  color: #fff;
}

html.dark .ant-btn-color-dangerous.ant-btn-variant-outlined:disabled {
  background-color: transparent !important;
}

html.dark .ant-pagination-item-ellipsis {
  color: #fff !important;
}

html.dark .anticon.anticon-right,
html.dark .ant-select-arrow,
html.dark .anticon.anticon-left {
  color: #fff !important;
}

html.dark .ant-dropdown .ant-dropdown-menu {
  background: hsl(0 0 7%) !important;
}

/* .ant-table-wrapper .ant-table-tbody > tr.ant-table-placeholder */

.DashLibraryTable .ant-table-body {
  min-width: 992px !important;
}

html.dark .ant-pagination .ant-pagination-item-active a {
  color: #fff;
}

html.dark .ant-pagination .ant-pagination-item {
  border-color: transparent;
  background-color: transparent;
}

html.dark .ant-pagination .ant-pagination-item a {
  color: #fff;
}

html.dark .ant-pagination .ant-pagination-item-active {
  background: #3f2a65;
  border-color: #3f2a65;
}

html.dark .ant-select-selector {
  border-color: #3f2a65;
  background-color: #000 !important;
  color: #fff;
}

html.dark .ant-select-selector .ant-select-selection-item {
  color: #fff !important;
}

html.dark .ant-modal .ant-modal-content {
  background-color: hsl(0 0 7%) !important;
}

html.dark .ant-btn-variant-outlined:hover {
  background: hsl(0 0 7%) !important;
}

html.dark .ant-modal .ant-modal-content {
  background-color: hsl(0 0 7%) !important;
}

html.dark .ant-modal-mask {
  background-color: rgba(29, 29, 31, 0.8) !important;
}

html.dark .CrtNewFoldBtn {
  color: #fff !important;
  background-color: #3f2a65 !important;
  border-color: #3f2a65 !important;
}

html.dark .CrtNewFoldBtn:disabled,
html.dark .CrtNewFoldBtn:disabled:hover {
  background: #3f2a6577 !important;
  border-color: #3f2a6577 !important;
  color: #ffffff7f !important;
}

html.dark .CrtNewFoldBtn:hover {
  color: #fff !important;
  background-color: #523f7a !important;
  border-color: #523f7a !important;
}

html.dark .ant-input-outlined.ant-input-status-error:not(.ant-input-disabled) {
  background-color: hsl(0 0 7%);
}

html.dark
  .ant-dropdown
  .ant-dropdown-menu
  .ant-dropdown-menu-item-disabled:hover {
  background-color: hsl(0 0 7%) !important;
}

html.dark .ant-table-wrapper td.ant-table-column-sort {
  background-color: hsl(0 0 7%);
}
.document-table .ant-table-fixed-header {
  overflow: auto !important;
}

@media only screen and (max-width: 768px) {
  .document-table .ant-table-fixed-header {
    height: auto;
  }
}
