import { Check, Filter } from "lucide-react";
import { Dropdown } from "antd";
import React from "react";

export const DOCUMENT_TYPE_FILTER_ITEMS = [
	{
		key: "PDF",
		label: "PDF Files",
		mimes: ["application/pdf"],
	},
	{
		key: "DOCX",
		label: "DOC/DOCX Files",
		mimes: [
			"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			"application/msword",
		],
	},
	{
		key: "TXT",
		label: "TXT Files",
		mimes: ["text/plain"],
	},
	// {
	//   key: 'XLSX',
	//   label: 'XLS/XLSX Files',
	//   mimes: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'],
	// },
	// {
	//   key: 'PPTX',
	//   label: 'PPT/PPTX Files',
	//   mimes: ['application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.ms-powerpoint'],
	// },
	// {
	//   key: 'IMAGE',
	//   label: 'IMAGE Files',
	//   mimes: ['image/png', 'image/jpeg', 'image/jpg'],
	// },
];

export function DocumentTypeFilter(props: {
	className?: string;
	typeFilters: string[];
	setTypeFilters: React.Dispatch<React.SetStateAction<string[]>>;
	theme?: "light" | "dark" | string;
}) {
	const { typeFilters, setTypeFilters, theme = "light" } = props;

	return (
		<Dropdown
			arrow
			menu={{
				theme: theme === "dark" ? "dark" : "light",
				className:
					"min-w-[140px] w-fit [&>li:not(:last-child)]:border-b [&>li:not(:last-child)]:border-gray-light [&>li]:rounded-none [&>li]:border-dashed [&>li]:!py-2",
				// items: [
				//   {
				//     label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('application/pdf') ? 'text-[#8b5cf6]' : theme === 'dark' ? 'text-white' : 'text-black'}`}>
				//       PDF Files
				//       {typeFilters.includes('application/pdf') && <Check className="size-4" />}
				//     </span>,
				//     key: 'PDF',
				//   },
				//   {
				//     label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ? 'text-[#8b5cf6]' : theme === 'dark' ? 'text-white' : 'text-black'}`}>
				//       DOC/DOCX Files
				//       {typeFilters.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') && <Check className="size-4" />}
				//     </span>,
				//     key: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
				//   },
				//   {
				//     label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('text/plain') ? 'text-[#8b5cf6]' : theme === 'dark' ? 'text-white' : 'text-black'}`}>
				//       TXT Files
				//       {typeFilters.includes('text/plain') && <Check className="size-4" />}
				//     </span>,
				//     key: 'text/plain',
				//   },
				// ],
				items: DOCUMENT_TYPE_FILTER_ITEMS.map((item) => ({
					label: (
						<span
							className={`flex items-center gap-x-2 ${typeFilters.includes(item.key) ? "text-[#8b5cf6]" : theme === "dark" ? "text-[#fff]" : "text-black dark:text-white"}`}
						>
							{item.label}
							{typeFilters.includes(item.key) && <Check className="size-4" />}
						</span>
					),
					key: item.key,
				})),
				onClick: ({ key }) => {
					if (typeFilters.includes(key)) {
						setTypeFilters((prev: string[]) =>
							prev.filter((filter) => filter !== key),
						);
					} else {
						setTypeFilters((prev: string[]) => [...prev, key]);
					}
				},
			}}
			placement="bottomRight"
		>
			<div
				className={`flex justify-center items-center size-8 !basis-10 !min-w-[36px] bg-white dark:bg-background dark:border dark:border-input rounded-md cursor-pointer ${props.className} ${theme === "dark" ? "bg-gray-900" : "bg-white"}`}
			>
				<Filter
					className={`size-5 ${typeFilters.length > 0 ? "text-[#8b5cf6]" : theme === "dark" ? "text-[#fff]" : "text-gray-medium dark:text-white"}`}
				/>
			</div>
		</Dropdown>
	);
}
