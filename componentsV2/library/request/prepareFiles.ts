export async function prepareFiles(fileList: any[], folderId?: string | null, artifactSource?: string, metadata?: Record<string, any>) {
  const filesData = fileList.map((file: any) => {
    return {
      name: file.name,
      mime: file.type,
      size: file.size
    }
  })
  const response = await fetch('/api/document/prepare', {
    method: 'POST',
    body: JSON.stringify({ files: filesData, folderId, metadata, artifactSource})
  })

  if (response.ok) {
    return await response.json()
  }
  console.log('[request][prepareFile]: generate signed url failed')
  return Promise.reject('upload fail')
}