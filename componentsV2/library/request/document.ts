export async function getDownmentDownloadUrl(id: string) {
  const response = await fetch(`/api/document/${id}/download`, {
    method: 'GET',
  })
  const data = await response.json()
  return data.downloadUrl
}

export async function deleteDocuments(ids: string[]) {
  const response = await fetch(`/api/documents`, {
    method: 'DELETE',
    body: JSON.stringify({ ids })
  })
  const data = await response.json()
  return data.success
}

export async function favoriteDocument(id: string, isFavorite: boolean) {
  const response = await fetch(`/api/document/${id}`, {
    method: 'PATCH',
    body: JSON.stringify({ isFavorite })
  })
  const data = await response.json()
  return data.success
}

export async function deleteDocument(id: string) {
  const response = await fetch(`/api/document/${id}`, {
    method: 'DELETE',
  })
  const data = await response.json()
  return data.success
}

export async function getDocuments(ids: string[]) {
  const response = await fetch(`/api/documents`, {
    method: 'POST',
    body: JSON.stringify({ ids })
  })
  const data = await response.json()
  return data.documents
}

export async function getRecentlyDocuments(queryString?: string) {
  const response = await fetch(`/api/documents/recently${queryString ? `?${queryString}` : ''}`, {
    method: 'GET',
  })
  const data = await response.json()
  return data
}

export async function getFavoritesDocuments(queryString: string) {
  const response = await fetch(`/api/documents?${queryString}`, {
    method: 'GET',
  })
  const data = await response.json()
  return data
}

export async function moveDocuments(documentIds: string[], folderId: string) {
  const response = await fetch(`/api/documents/move`, {
    method: 'POST',
    body: JSON.stringify({ documentIds, folderId })
  })
  const data = await response.json()
  return data
}