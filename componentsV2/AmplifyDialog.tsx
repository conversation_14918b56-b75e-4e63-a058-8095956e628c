import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogClose,
} from "@/components/ui/dialog";
import { X, Refresh<PERSON>w, <PERSON>, <PERSON> } from "lucide-react";
import { toast } from "sonner";
import { SavePromptDialog } from "./SavePromptDialog";
import { LightningBoltIcon } from "./icons";

interface AmplifyDialogProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	originalInput: string;
	refinedPrompt: string;
	onUse: () => void;
	onRegenerate: () => void;
	onSave: (promptName: string, folderId: string) => void;
	isLoading: boolean;
}

export function AmplifyDialog({
	isOpen,
	onOpenChange,
	originalInput,
	refinedPrompt,
	onUse,
	onRegenerate,
	onSave,
	isLoading,
}: AmplifyDialogProps) {
	const [showSaveDialog, setShowSaveDialog] = useState(false);
	const [defaultPromptName, setDefaultPromptName] = useState("");

	// Set a default prompt name based on the first few words of the refined prompt
	useEffect(() => {
		if (refinedPrompt) {
			const words = refinedPrompt.split(" ").slice(0, 5).join(" ");
			setDefaultPromptName(
				words + (refinedPrompt.split(" ").length > 5 ? "..." : ""),
			);
		}
	}, [refinedPrompt]);

	// Handle save button click
	const handleSaveClick = () => {
		setShowSaveDialog(true);
	};

	return (
		<>
			<SavePromptDialog
				isOpen={showSaveDialog}
				onOpenChange={setShowSaveDialog}
				onSave={onSave}
				defaultPromptName={defaultPromptName}
				promptData={{
					title: defaultPromptName,
					content: refinedPrompt,
					folderId: "", // This will be selected in the dialog
					isFavorite: false,
				}}
			/>
			<Dialog open={isOpen} onOpenChange={onOpenChange}>
				<DialogContent className="sm:max-w-[500px] max-h-[80vh] flex flex-col">
					<DialogHeader className="flex flex-row justify-between space-y-0 pb-2 px-3">
						<DialogTitle className="text-base">
							<div className="flex flex-col">
								<div className="flex items-center gap-2">
									<div className="size-5 flex rounded-full justify-center bg-[rgb(var(--base-navy))]/10 dark:bg-[rgb(var(--base-navy))]/20">
										<div className="translate-y-[3px]">
											<LightningBoltIcon size={12} />
										</div>
									</div>
									<span className="text-[rgb(var(--base-navy))] dark:text-[rgb(var(--base-navy))]">
										Iqidis Amplify
									</span>
								</div>
								<span className="text-xs md:text-center text-left text-muted-foreground font-normal mt-1 md:ml-7 ml-0">
									Expand, refine, or elevate your prompts with Iqidis Amplify
								</span>
							</div>
						</DialogTitle>
						<DialogClose asChild>
							<Button
								variant="ghost"
								size="sm"
								className="h-8 w-8 p-0 rounded-full"
							>
								<X className="h-4 w-4" />
							</Button>
						</DialogClose>
					</DialogHeader>

					<div className="py-2 flex-1 overflow-auto flex flex-col px-6">
						<div className="mb-4">
							<h3 className="text-sm font-medium mb-1">Original Input:</h3>
							<div className="text-sm whitespace-pre-wrap bg-muted/30 p-3 rounded border max-h-[15vh] overflow-y-auto scrollbar-thin">
								{originalInput}
							</div>
						</div>

						<div className="flex-1 min-h-0">
							<h3 className="text-sm font-medium mb-1">Amplified Prompt:</h3>
							<div className="text-sm whitespace-pre-wrap bg-white dark:bg-zinc-800 p-3 rounded border h-[30vh] overflow-y-auto scrollbar-thin">
								{isLoading ? (
									<div className="flex items-center justify-center h-full py-4">
										<div className="h-5 w-5 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2"></div>
										<span className="text-sm text-muted-foreground">
											Generating suggestion...
										</span>
									</div>
								) : (
									refinedPrompt
								)}
							</div>
						</div>

						{/* Save Dialog - Rendered outside the current dialog to avoid z-index issues */}
					</div>

					<div className="flex justify-end gap-2 pt-2 mt-auto shrink-0 px-6">
						<Button
							variant="outline"
							size="sm"
							className="gap-1"
							onClick={handleSaveClick}
							disabled={isLoading}
						>
							<Star className="h-4 w-4" />
							Save
						</Button>
						<Button
							variant="outline"
							size="sm"
							className="gap-1"
							onClick={onRegenerate}
							disabled={isLoading}
						>
							<RefreshCw className="h-4 w-4" />
							Regenerate
						</Button>
						<Button
							variant="default"
							size="sm"
							className="gap-1 navy-button"
							onClick={onUse}
							disabled={isLoading}
						>
							<Check className="h-4 w-4" />
							Use
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</>
	);
}
