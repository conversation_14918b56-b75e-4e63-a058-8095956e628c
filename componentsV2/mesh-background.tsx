"use client";

import React from 'react';
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

const MeshBackground = () => {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div 
      className="fixed inset-0 -z-10"
      style={{
        background: `
          linear-gradient(to right, #8080800a 1px, transparent 1px),
          linear-gradient(to bottom, #8080800a 1px, transparent 1px)
        `,
        backgroundSize: '24px 24px'
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-tr from-white via-white/80 to-white/30 dark:from-gray-950 dark:via-gray-950/80 dark:to-gray-950/30" />
      <div className="absolute left-0 right-0 top-0 -z-10 m-auto h-[310px] w-full max-w-xs sm:max-w-2xl md:max-w-3xl lg:max-w-5xl xl:max-w-7xl">
        <div className="absolute inset-0 h-full w-full bg-gradient-radial-faint from-purple-500/20 to-transparent" />
      </div>
      <div 
        className="absolute inset-0 bg-[radial-gradient(circle_500px_at_50%_200px,var(--mesh-color),transparent)]"
        style={{
          '--mesh-color': resolvedTheme === 'dark' ? 'rgba(124, 58, 237, 0.15)' : 'rgba(139, 92, 246, 0.15)'
        } as React.CSSProperties}
      />
    </div>
  );
};

export default MeshBackground; 