"use client";

import { AnimatePresence, motion } from "framer-motion";
import { X } from "lucide-react";
import * as React from "react";
import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog";
import type { ChatPanelProps } from "./chat-panel";
import { ChatPanel } from "./chat-panel";

interface ChatFullscreenModalProps
    extends Omit<ChatPanelProps, "isFullscreen" | "onToggleFullscreen"> {
    isOpen: boolean;
    onClose: () => void;
}

export function ChatFullscreenModal({
    isOpen,
    onClose,
    ...chatPanelProps
}: ChatFullscreenModalProps) {
    // Prevent body scroll when modal is open and handle keyboard shortcuts
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = "hidden";

            // Handle keyboard shortcuts
            // const handleKeyDown = (event: KeyboardEvent) => {
            //     if (event.key === "Escape") {
            //         onClose();
            //     }
            // };

            // document.addEventListener("keydown", handleKeyDown);

            // return () => {
            //     document.removeEventListener("keydown", handleKeyDown);
            // };
        } else {
            document.body.style.overflow = "unset";
        }

        return () => {
            document.body.style.overflow = "unset";
        };
    }, [isOpen, onClose]);

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent
                className="max-w-none w-screen h-screen m-0 p-0 border-0 rounded-none bg-background flex flex-col z-[1000]"
                overlayClassName="bg-black/50 backdrop-blur-sm z-[999]"
            >
                <AnimatePresence mode="wait">
                    {isOpen && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.98, y: 20 }}
                            animate={{ opacity: 1, scale: 1, y: 0 }}
                            exit={{ opacity: 0, scale: 0.98, y: 20 }}
                            transition={{
                                type: "spring",
                                stiffness: 400,
                                damping: 40,
                                duration: 0.4,
                            }}
                            className="w-full h-full flex flex-col bg-background"
                        >
                            {/* Header with close button */}
                            <motion.div
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.1, duration: 0.3 }}
                                className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur-sm shrink-0 h-[72px]"
                            >
                                <div className="flex items-center gap-3">
                                    <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 rounded-full traffic-light-red"></div>
                                        <div className="w-3 h-3 rounded-full traffic-light-yellow"></div>
                                        <div className="w-3 h-3 rounded-full traffic-light-green"></div>
                                    </div>
                                    <div className="h-4 w-px bg-border"></div>
                                    <h2 className="text-lg font-semibold">
                                        {chatPanelProps.chatTitle || "AI Chat"}
                                    </h2>
                                </div>
                                <DialogClose asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 hover:bg-destructive/10 hover:text-destructive"
                                        onClick={onClose}
                                    >
                                        <X className="h-4 w-4" />
                                        <span className="sr-only">Close fullscreen</span>
                                    </Button>
                                </DialogClose>
                            </motion.div>

                            {/* Chat content */}
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.2, duration: 0.3 }}
                                className="flex-1 min-h-0 flex flex-col sm:p-6"
                            >
                                <div className="flex-1 min-h-0 flex flex-col">
                                    <ChatPanel
                                        {...chatPanelProps}
                                        isFullscreen={true}
                                        onToggleFullscreen={onClose}
                                    />
                                </div>
                            </motion.div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </DialogContent>
        </Dialog>
    );
}
