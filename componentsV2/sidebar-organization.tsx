"use client";

import { PinIcon } from "lucide-react";
import { } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { SidebarPinnedChats } from "@/componentsV2/sidebar-pinned-chats";
import type { ChatWithTags as Chat } from "@/lib/db/schema";

interface SidebarOrganizationProps {
	pinnedChats: Chat[];
	onPinChat: (chatId: string) => void;
	onUnpinChat: (chatId: string) => void;
	onReorderPins: (chatIds: string[]) => void;
	viewMode: "all" | "pinned" | "matters";
	setViewMode: (mode: "all" | "pinned" | "matters") => void;
	onDeleteChat: (chatId: string) => void;
	onBulkDelete: () => void;
	isLoading?: boolean;
}

export function SidebarOrganization({
	pinnedChats,
	onPinChat,
	onUnpin<PERSON>hat,
	onReorder<PERSON><PERSON>,
	viewMode,
	setViewMode,
	onDeleteChat,
	onBulkDelete,
	isLoading = false,
}: SidebarOrganizationProps) {

	if (isLoading) {
		return (
			<div className="mb-2">
				{/* Skeleton for view mode buttons */}
				<div className="flex items-center justify-between px-0.5">
					<div className="flex w-full space-x-1.5">
						<Skeleton className="flex-1 h-8 rounded-md" />
						<Skeleton className="flex-1 h-8 rounded-md" />
					</div>
				</div>

				{/* Skeleton for pinned section */}
				<div className="mb-4 space-y-2">
					{/* Skeleton for PINNED header */}
					<div className="px-1 py-1 flex items-center justify-between">
						<Skeleton className="h-4 w-16" />
						<div className="flex items-center gap-1">
							<Skeleton className="h-5 w-5 rounded" />
							<Skeleton className="h-5 w-5 rounded" />
						</div>
					</div>

					{/* Skeleton for pinned chats */}
					{Array.from({ length: 2 }, (_, i) => i).map((index) => (
						<div
							key={`pinned-skeleton-${index}`}
							className="flex items-center gap-3 p-2 rounded-lg"
						>
							<Skeleton className="h-4 w-4 rounded" />
							<div className="flex-1 space-y-1">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-3 w-2/3" />
							</div>
						</div>
					))}

					<Separator />

					{/* Skeleton for "Pinned chats will appear here" text */}
					<div className="px-2 py-2">
						<Skeleton className="h-3 w-36" />
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="mb-2">
			<div className="flex items-center justify-between px-0.5 pb-2">
				<div className="flex w-full space-x-1.5">
					<Button
						size="sm"
						variant="outline"
						className={`flex-1 h-8 px-1.5 transition-all duration-200 ${viewMode === "all"
							? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 hover:text-blue-800 dark:hover:text-blue-200 border border-blue-200 dark:border-blue-800"
							: "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-200 dark:border-gray-700"
							}`}
						onClick={() => setViewMode("all")}
					>
						<span className="flex items-center gap-1">All</span>
					</Button>
					<Button
						size="sm"
						variant="outline"
						className={`flex-1 h-8 px-1.5 transition-all duration-200 ${viewMode === "pinned"
							? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 hover:text-blue-800 dark:hover:text-blue-200 border border-blue-200 dark:border-blue-800"
							: "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-200 dark:border-gray-700"
							}`}
						onClick={() => setViewMode("pinned")}
					>
						<span className="flex items-center gap-1">
							<PinIcon
								className={`size-3.5 ${viewMode === "pinned" ? "text-blue-700 dark:text-blue-300" : "text-gray-500 dark:text-gray-400"}`}
							/>
							Pinned
						</span>
					</Button>
				</div>
			</div>

			{(viewMode === "all" || viewMode === "pinned") && (
				<SidebarPinnedChats
					chats={pinnedChats}
					onUnpin={onUnpinChat}
					onReorderPins={onReorderPins}
					onDelete={onDeleteChat}
					onBulkDelete={onBulkDelete}
					onPinChat={onPinChat}
					viewMode={viewMode}
					setViewMode={setViewMode}
				/>
			)}
		</div>
	);
}
