import React, { useState } from "react";
import { formatDistanceToNow } from "date-fns";
import { Notification } from "./types";
import { cn } from "@/lib/utils";
import { Bell, Newspaper, Info, User, ExternalLink } from "lucide-react";
import { useNotifications } from "@/contexts/NotificationsContext";
import Link from "next/link";

interface NotificationItemProps {
  notification: Notification;
  onClick?: () => void;
}

export const NotificationItem = ({ notification, onClick }: NotificationItemProps) => {
  const { markAsRead } = useNotifications();
  const { id, title, description, type, timestamp, isRead, link } = notification;
  const [expanded, setExpanded] = useState(false);

  const handleClick = () => {
    if (!isRead) {
      markAsRead(id);
    }
    if (onClick) {
      onClick();
    }
  };

  const getIcon = () => {
    switch (type) {
      case "news":
        return <Newspaper className="h-4 w-4 text-blue-500" />;
      case "update":
        return <Info className="h-4 w-4 text-amber-500" />;
      case "personal":
        return <User className="h-4 w-4 text-green-500" />;
      default:
        return <Bell className="h-4 w-4 text-iqidis-vividPurple" />;
    }
  };

  const formattedTime = formatDistanceToNow(new Date(timestamp), { addSuffix: true });

  const NotificationContent = () => {
    const charLimit = 100;
    const shouldTruncate = description.length > charLimit;
    const displayText = expanded ? description : description.slice(0, charLimit);

    return (
      <div
        className={cn(
          "flex items-start gap-3 p-3 hover:bg-[#441a9733] transition-colors rounded-md dark:bg-background cursor-pointer",
          !isRead && "bg-[#441a9719]"
        )}
        onClick={handleClick}
      >
        <div className="mt-0.5">{getIcon()}</div>
        <div className="flex-1">
          <div className="flex justify-between items-start">
            {link ? (
              <Link
                href={link}
                target="_blank"
                onClick={(e) => e.stopPropagation()}
                className={cn("text-sm font-medium hover:underline", !isRead && "font-semibold")}
              >
                {title}
              </Link>
            ) : (
              <h4 className={cn("text-sm font-medium", !isRead && "font-semibold")}>{title}</h4>
            )}
            {!isRead && (
              <span>
                <div className="h-2 w-2 rounded-full bg-[#270f57] dark:bg-[#8b5cf6]"></div>
              </span>
            )}
          </div>

          <p className="text-xs text-muted-foreground mt-1">
            {displayText}
            {shouldTruncate && !expanded && "... "}
            {shouldTruncate && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setExpanded((prev) => !prev);
                  if (!isRead) {
                    markAsRead(id);
                  }
                }}
                className="ml-1 text-primary underline text-xs dark:text-[#8b5cf6]"
              >
                {expanded ? "Read less" : "Read more"}
              </button>
            )}
          </p>

          <div className="flex justify-between items-center mt-2">
            <span className="text-xs text-muted-foreground">{formattedTime}</span>
            {link && (
              <Link href={link} target="_blank" onClick={(e) => e.stopPropagation()}>
                <ExternalLink className="h-3 w-3 text-muted-foreground hover:text-primary dark:hover:text-white" />
              </Link>
            )}
          </div>
        </div>
      </div>
    );
  };

  return <NotificationContent />;
};
