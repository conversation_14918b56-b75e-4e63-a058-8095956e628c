'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ThumbsUp, ThumbsDown, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { Logger } from '@/lib/utils/Logger';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

interface FeatureFeedbackProps {
  featureType: string;
  metadata?: Record<string, any>;
  buttonText?: string;
  title?: string;
  className?: string;
}

export function FeatureFeedback({
  featureType,
  metadata = {},
  buttonText = 'Share feedback',
  title = 'Feature Feedback',
  className = '',
}: FeatureFeedbackProps) {
  const [feedbackText, setFeedbackText] = useState('');
  const [feedbackRating, setFeedbackRating] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const dialogContentRef = useRef<HTMLDivElement>(null);

  // Prevent clicks inside the dialog from propagating to parent dialogs
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      e.stopPropagation();
    };

    const currentRef = dialogContentRef.current;
    if (currentRef) {
      currentRef.addEventListener('click', handleClick);
    }

    return () => {
      if (currentRef) {
        currentRef.removeEventListener('click', handleClick);
      }
    };
  }, [isOpen]);

  const submitFeedback = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (feedbackRating === null) {
      toast.error('Please select whether you liked this feature');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/user-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          featureType,
          rating: feedbackRating,
          feedbackText,
          metadata,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit feedback');
      }

      toast.success('Thank you for your feedback!');
      setIsOpen(false);
      setFeedbackText('');
      setFeedbackRating(null);
    } catch (error) {
      Logger.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className={`text-sm ${className}`}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent
        className="w-120 p-6 z-[9999]"
        ref={dialogContentRef}
        overlayClassName="z-[9998]"
      >
        <DialogTitle className="text-base mb-3">{title}</DialogTitle>
        <div className="flex justify-center gap-4 mb-4">
          <Button
            variant={feedbackRating === 1 ? "default" : "outline"}
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setFeedbackRating(1);
            }}
            className="flex items-center gap-2"
          >
            <ThumbsUp className="h-4 w-4" />
            <span>I like it</span>
          </Button>
          <Button
            variant={feedbackRating === 0 ? "default" : "outline"}
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setFeedbackRating(0);
            }}
            className="flex items-center gap-2"
          >
            <ThumbsDown className="h-4 w-4" />
            <span>Needs improvement</span>
          </Button>
        </div>
        <Textarea
          placeholder="Tell us what you think about this feature..."
          value={feedbackText}
          onChange={(e) => setFeedbackText(e.target.value)}
          className="mb-4"
          onClick={(e) => e.stopPropagation()}
        />
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsOpen(false);
            }}
            disabled={isSubmitting}
            size="sm"
          >
            Cancel
          </Button>
          <Button
            onClick={submitFeedback}
            disabled={isSubmitting || feedbackRating === null}
            size="sm"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Submitting...
              </>
            ) : (
              'Submit Feedback'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}





