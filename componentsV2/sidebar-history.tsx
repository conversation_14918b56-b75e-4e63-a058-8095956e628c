"use client";

import { isToday, isYesterday, subMonths, subWeeks } from "date-fns";
import Link from "next/link";
import { useParams, usePathname, useRouter } from "next/navigation";
import type { User } from "next-auth";
import { memo, useEffect, useState, useMemo, useRef } from "react";
import { toast } from "sonner";
import useSWR, { mutate } from "swr";
import {
	ArrowRight as ArrowRightIcon,
	MoreVerticalIcon,
	PinIcon,
	TagIcon,
} from "lucide-react";
import { DndProvider, useDrag } from "react-dnd";
import { ItemTypes } from "@/lib/dnd-types";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Badge } from "@/components/ui/badge";
import { useTagManagement } from "@/hooks/use-tag-management";
import { TagManagementDialog } from "@/componentsV2/tag-management-dialog";

import {
	CheckCircleFillIcon,
	GlobeIcon,
	LockIcon,
	MoreH<PERSON>zontalIcon,
	ShareIcon,
	TrashIcon,
	PenIcon,
} from "@/componentsV2/icons";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuPortal,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// Removed sidebar imports - using regular HTML elements instead
import type { ChatWithTags as Chat } from "@/lib/db/schema";
import { fetcher } from "@/lib/utils";
import { useChatVisibility } from "@/hooks/use-chat-visibility";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { SidebarOrganization } from "@/componentsV2/sidebar-organization";
import { BulkDeleteDialog } from "@/componentsV2/bulk-delete-dialog";
import { logEvent } from "@/lib/analytics/events-client";
import { ChatEvent } from "@/lib/analytics/event-types";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";

type GroupedChats = {
	today: Chat[];
	yesterday: Chat[];
	lastWeek: Chat[];
	lastMonth: Chat[];
	older: Chat[];
};

interface Matter {
	id: string;
	name: string;
	chats: Chat[];
}

interface PinnedChat {
	chatId: string;
	position: number;
	chat: Chat;
}

interface SidebarData {
	chats: Chat[];
	folders: Matter[]; // We'll keep using "matters" in our component
	pinnedChats: PinnedChat[];
}

type DragItem = {
	type: string;
	id: string;
	source: "history" | "pinned" | "folder";
	folderId?: string;
	index?: number;
};

const handleRename = async (chatId: string, newTitle: string) => {
	try {
		const response = await fetch(`/api/chat`, {
			method: "PATCH",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({ id: chatId, title: newTitle }),
		});

		if (response.ok) {
			// Only update UI and revalidate if backend update was successful
			mutate("/api/chat-org/sidebar-data");
			return true;
		} else {
			throw new Error("Failed to rename chat");
		}
	} catch (error) {
		toast.error("Failed to rename chat.");
		return false; // Return failure status to the caller
	}
};

const PureChatItem = ({
	chat,
	isActive,
	onDelete,
	onPinChat,
	onUnpinChat,
	onBulkDelete,
	pinnedChats,
	onManageTags,
}: {
	chat: Chat;
	isActive: boolean;
	onDelete: (chatId: string) => void;
	onPinChat: (chatId: string) => void;
	onUnpinChat: (chatId: string) => void;
	onBulkDelete: () => void;
	pinnedChats: PinnedChat[];
	onManageTags?: () => void;
}) => {
	const { visibilityType, setVisibilityType } = useChatVisibility({
		chatId: chat.id,
		initialVisibility: chat.visibility,
	});
	const router = useRouter();

	// Check if the current chat is pinned
	const isPinned = useMemo(
		() => pinnedChats.some((pinnedChat) => pinnedChat.chatId === chat.id),
		[chat.id, pinnedChats],
	);

	const [isEditing, setIsEditing] = useState(false);
	const [displayTitle, setDisplayTitle] = useState(chat.title);

	const handleSubmitRename = async (e: React.FormEvent) => {
		e.preventDefault();
		if (displayTitle && displayTitle !== chat.title) {
			const success = await handleRename(chat.id, displayTitle);
			if (!success) {
				setDisplayTitle(chat.title);
			}
		}
		setIsEditing(false);
	};

	const formatTime = (dateString: Date | string) => {
		const now = new Date();
		const date = new Date(dateString);
		const diffInMs = now.getTime() - date.getTime();
		const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
		const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
		const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

		if (diffInMinutes < 1) {
			return "just now";
		} else if (diffInMinutes < 60) {
			return `${diffInMinutes} min${diffInMinutes === 1 ? "" : "s"} ago`;
		} else if (diffInHours < 24) {
			return `${diffInHours} hr${diffInHours === 1 ? "" : "s"} ago`;
		} else if (diffInDays < 30) {
			return `${diffInDays} day${diffInDays === 1 ? "" : "s"} ago`;
		} else {
			// More than a month, show actual date and time
			return date.toLocaleDateString("en-US", {
				month: "short",
				day: "numeric",
				year: "numeric",
				hour: "2-digit",
				minute: "2-digit",
				hour12: true,
			});
		}
	};

	return (
		<div
			className={`relative group ${
				isEditing
					? "border-2 border-primary rounded-md"
					: "after:content-[''] after:absolute after:inset-x-[5%] after:bottom-0 after:h-[0.5px] after:bg-gradient-to-r after:from-transparent after:via-[rgb(var(--base-navy))] after:to-transparent after:opacity-10 after:blur-[0.5px] last:after:hidden"
			}`}
		>
			<div
				className={`h-auto pt-1.5 pb-1 pl-1 flex items-start w-full transition-colors duration-200 rounded-md mx-1 ${
					isActive
						? "bg-gray-100 dark:bg-gray-800"
						: "hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-sidebar-accent-foreground"
				}`}
			>
				{isEditing ? (
					<form onSubmit={handleSubmitRename} className="flex-1">
						<input
							type="text"
							value={displayTitle}
							onChange={(e) => setDisplayTitle(e.target.value)}
							onBlur={handleSubmitRename}
							className="w-full bg-transparent outline-none pl-1"
						/>
					</form>
				) : (
					<div className="flex w-full pr-8">
						<Link
							href={`/chat/${chat.id}`}
							className="min-w-0 flex-1 px-2 pb-1.5"
						>
							<div className="flex flex-col gap-0.5">
								<div className="flex items-center gap-1 min-w-0">
									{isPinned && (
										<PinIcon className="size-3 shrink-0 text-primary dark:text-white" />
									)}
									<span className="truncate text-sm font-semibold text-black dark:text-white min-w-0 flex-shrink">
										{chat.isLoading ? (
											<div className="flex items-center gap-2">
												<div className="animate-spin h-3 w-3 border-2 border-primary border-t-transparent rounded-full"></div>
												<span>{displayTitle}</span>
											</div>
										) : (
											displayTitle
										)}
									</span>
								</div>
								<span className="text-[11px] text-sidebar-foreground/40 block truncate leading-tight">
									{formatTime(chat.updatedAt || chat.createdAt)}
								</span>
								{chat.tags && chat.tags.length > 0 && (
									<div className="flex gap-1 flex-wrap mt-1">
										{chat.tags.map((tag) => (
											<Badge
												key={tag.name}
												className="px-1.5 py-0 text-[10px] h-4 rounded-lg truncate max-w-[60px] border-0 font-normal"
												style={{
													backgroundColor: `${tag.color}20`,
													color: tag.color,
													borderColor: tag.color,
												}}
											>
												<span className="truncate">{tag.name}</span>
											</Badge>
										))}
									</div>
								)}
							</div>
						</Link>
					</div>
				)}
			</div>

			{!chat.isLoading && (
				<DropdownMenu modal={true}>
					<DropdownMenuTrigger asChild>
						<button
							type="button"
							className="absolute right-2 top-1/2 -translate-y-1/2 grid place-items-center m-auto text-sidebar-foreground/70 outline-none transition-[opacity,background-color] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus:ring-2 data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground opacity-0 group-hover:opacity-100"
						>
							<MoreVerticalIcon className="size-4" />
							<span className="sr-only">More</span>
						</button>
					</DropdownMenuTrigger>

					<DropdownMenuContent side="bottom" align="end">
						<DropdownMenuSub>
							<DropdownMenuSubTrigger className="cursor-pointer">
								<ShareIcon />
								<span>Share</span>
							</DropdownMenuSubTrigger>
							<DropdownMenuPortal>
								<DropdownMenuSubContent>
									<DropdownMenuItem
										className="cursor-pointer flex-row justify-between"
										onClick={() => {
											setVisibilityType("private");
										}}
									>
										<div className="flex flex-row gap-2 items-center">
											<LockIcon size={12} />
											<span>Private</span>
										</div>
										{visibilityType === "private" ? (
											<CheckCircleFillIcon />
										) : null}
									</DropdownMenuItem>
									<DropdownMenuItem
										className="cursor-pointer flex-row justify-between"
										onClick={() => {
											setVisibilityType("public");
										}}
									>
										<div className="flex flex-row gap-2 items-center">
											<GlobeIcon />
											<span>Shareable</span>
										</div>
										{visibilityType === "public" ? (
											<CheckCircleFillIcon />
										) : null}
									</DropdownMenuItem>
								</DropdownMenuSubContent>
							</DropdownMenuPortal>
						</DropdownMenuSub>

						<DropdownMenuItem
							className="cursor-pointer"
							onClick={() =>
								isPinned ? onUnpinChat(chat.id) : onPinChat(chat.id)
							}
						>
							<PinIcon />
							<span>{isPinned ? "Unpin" : "Pin"}</span>
						</DropdownMenuItem>

						{/* Add Manage Tags option */}
						{onManageTags && (
							<DropdownMenuItem
								className="cursor-pointer"
								onClick={(e) => {
									e.stopPropagation();
									onManageTags();
								}}
							>
								<TagIcon />
								<span>Tags</span>
							</DropdownMenuItem>
						)}

						<DropdownMenuItem
							className="cursor-pointer"
							onClick={() => {
								setIsEditing(true);
							}}
						>
							<PenIcon />
							<span>Rename</span>
						</DropdownMenuItem>

						<DropdownMenuItem
							className="cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500"
							onSelect={() => onDelete(chat.id)}
						>
							<TrashIcon />
							<span>Delete</span>
						</DropdownMenuItem>

						<DropdownMenuItem
							className="cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500"
							onClick={() => onBulkDelete()}
						>
							<TrashIcon />
							<span>Bulk Delete</span>
						</DropdownMenuItem>

						<Tooltip>
							<TooltipTrigger asChild>
								<DropdownMenuItem
									className="cursor-pointer"
									onClick={() => {
										router.push(`/chat/transfer/${chat.id}`);
									}}
								>
									<ArrowRightIcon />
									<span>Transfer</span>
								</DropdownMenuItem>
							</TooltipTrigger>
							<TooltipContent side="left" className="max-w-[200px]">
								Transfer this chat to a new one if it is getting too long
							</TooltipContent>
						</Tooltip>
					</DropdownMenuContent>
				</DropdownMenu>
			)}
		</div>
	);
};

export const ChatItem = memo(function ChatItem({
	chat,
	isActive,
	onDelete,
	onPinChat,
	onUnpinChat,
	onBulkDelete,
	pinnedChats,
	onAddToFolder,
	onManageTags,
}: {
	chat: Chat;
	isActive: boolean;
	onDelete: (chatId: string) => void;
	onPinChat: (chatId: string) => void;
	onUnpinChat: (chatId: string) => void;
	onBulkDelete: () => void;
	pinnedChats: PinnedChat[];
	onAddToFolder?: (chatId: string, folderId: string) => void;
	onManageTags?: () => void;
}) {
	const isPinned = pinnedChats.some((p) => p.chatId === chat.id);
	const ref = useRef<HTMLDivElement>(null);

	// Set up drag
	const [{ isDragging }, drag] = useDrag({
		type: ItemTypes.CHAT,
		item: {
			type: ItemTypes.CHAT,
			id: chat.id,
			source: isPinned ? "pinned" : "history",
		},
		collect: (monitor) => ({
			isDragging: monitor.isDragging(),
		}),
	});

	// Apply the drag ref to our element
	drag(ref);

	return (
		<div ref={ref} className={`${isDragging ? "opacity-50" : "opacity-100"}`}>
			<PureChatItem
				chat={chat}
				isActive={isActive}
				onDelete={onDelete}
				onPinChat={onPinChat}
				onUnpinChat={onUnpinChat}
				onBulkDelete={onBulkDelete}
				pinnedChats={pinnedChats}
				onManageTags={onManageTags}
			/>
		</div>
	);
});

export function SidebarHistory({
	user,
	searchQuery,
}: {
	user: User | undefined;
	searchQuery: string;
}) {
	const { id } = useParams();
	const pathname = usePathname();
	const {
		data: sidebarData,
		isLoading,
		mutate,
	} = useSWR<SidebarData>(user ? "/api/chat-org/sidebar-data" : null, fetcher, {
		fallbackData: { chats: [], folders: [], pinnedChats: [] },
		revalidateOnFocus: true,
		dedupingInterval: 5000, // Only revalidate after 5 seconds
	});

	// Add tag management hook
	const {
		isDialogOpen,
		currentChat,
		openTagDialog,
		closeTagDialog,
		handleSaveTags,
		isLoading: isTagsLoading,
	} = useTagManagement({
		onUpdateTags: async (chatId, tags) => {
			// Update local state first
			mutate((currentData) => {
				if (!currentData) return currentData;

				return {
					...currentData,
					chats: currentData.chats.map((chat) =>
						chat.id === chatId ? { ...chat, tags } : chat,
					),
				};
			}, false);

			// Then trigger global revalidation
			mutate();
		},
	});

	// Add this effect to revalidate when the pathname changes
	useEffect(() => {
		// Preserve loading chat items during revalidation
		const loadingChats =
			sidebarData?.chats?.filter((chat) => chat.isLoading) || [];

		if (loadingChats.length > 0) {
			// If we have loading chats, do a controlled mutation
			mutate(
				async () => {
					// Fetch fresh data
					const response = await fetch("/api/chat-org/sidebar-data");
					const freshData = await response.json();

					// Get current timestamp and calculate one minute ago
					const now = new Date();
					const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

					// Filter loading chats:
					// 1. Keep only those that don't exist in fresh data
					// 2. Keep only those created less than a minute ago
					const recentLoadingChats = loadingChats.filter(
						(loadingChat) =>
							!freshData.chats.some(
								(chat: Chat) => chat.id === loadingChat.id,
							) && new Date(loadingChat.createdAt) > oneMinuteAgo,
					);

					return {
						...freshData,
						chats: [...recentLoadingChats, ...freshData.chats],
					};
				},
				{ revalidate: false },
			);
		} else {
			// No loading chats, just do a normal revalidation
			mutate();
		}
	}, [pathname, mutate, sidebarData]);

	const filteredHistory = useMemo(() => {
		if (!sidebarData?.chats) return [];
		if (!searchQuery) return sidebarData.chats;
		return sidebarData.chats.filter((chat) =>
			chat.title.toLowerCase().includes(searchQuery.toLowerCase()),
		);
	}, [sidebarData?.chats, searchQuery]);

	// Remove the local state and useEffect that was causing the infinite loop
	// Instead, access pinnedChats and matters directly from sidebarData
	const pinnedChats = useMemo(
		() => sidebarData?.pinnedChats?.map((item) => item.chat) || [],
		[sidebarData?.pinnedChats],
	);

	const matters = useMemo(
		() => sidebarData?.folders || [],
		[sidebarData?.folders],
	);

	const [deleteId, setDeleteId] = useState<string | null>(null);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
	const router = useRouter();
	const handleDelete = async () => {
		const deletePromise = fetch(`/api/chat?id=${deleteId}`, {
			method: "DELETE",
		});

		toast.promise(deletePromise, {
			loading: "Deleting chat...",
			success: () => {
				mutate((sidebarData) => {
					if (sidebarData) {
						return {
							...sidebarData,
							chats: sidebarData.chats.filter((h) => h.id !== id),
							pinnedChats: sidebarData.pinnedChats.filter(
								(p) => p.chatId !== id,
							),
						};
					}
				});

				logEvent(ChatEvent.DELETE_CHAT, {
					chatId: deleteId,
				});
				return "Chat deleted successfully";
			},
			error: "Failed to delete chat",
		});

		setShowDeleteDialog(false);

		if (deleteId === id) {
			router.push("/");
		}
	};

	const handleBulkDelete = async (chatIds: string[]) => {
		try {
			const response = await fetch("/api/chat/bulk-delete", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ chatIds }),
			});

			if (!response.ok) {
				throw new Error("Failed to delete chats");
			}

			// Update the UI by removing the deleted chats
			mutate((sidebarData) => {
				if (sidebarData) {
					return {
						...sidebarData,
						chats: sidebarData.chats.filter((h) => !chatIds.includes(h.id)),
						pinnedChats: sidebarData.pinnedChats.filter(
							(p) => !chatIds.includes(p.chatId),
						),
					};
				}
			});

			// If current chat is deleted, redirect to home
			if (id && typeof id === "string" && chatIds.includes(id)) {
				router.push("/");
			}
		} catch (error) {
			toast.error("Failed to delete chats");
			throw error;
		}
	};

	// Add these state variables and handlers to SidebarHistory
	const handlePinChat = async (chatId: string) => {
		const chatToPin = filteredHistory.find((chat) => chat.id === chatId);
		if (!chatToPin) return;

		// Check if already pinned
		if (pinnedChats.some((chat) => chat.id === chatId)) {
			toast.info("Chat is already pinned");
			return;
		}

		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				// Create a simple pinnedChat entry for the UI
				const newPinnedChat = {
					chatId: chatToPin.id,
					position: (currentData.pinnedChats?.length || 0) + 1,
					chat: chatToPin,
				};

				return {
					...currentData,
					pinnedChats: [...(currentData.pinnedChats || []), newPinnedChat],
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch("/api/chat-org/pinned-chats", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ chatId }),
			});

			if (!response.ok) throw new Error("Failed to pin chat");

			// Revalidate to get actual data
			mutate();
			// toast.success("Chat pinned successfully");
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to pin chat");
		}
	};

	const handleUnpinChat = async (chatId: string) => {
		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				return {
					...currentData,
					pinnedChats: currentData.pinnedChats.filter(
						(pinnedChat) => pinnedChat.chatId !== chatId,
					),
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch(
				`/api/chat-org/pinned-chats?chatId=${chatId}`,
				{
					method: "DELETE",
				},
			);

			if (!response.ok) throw new Error("Failed to unpin chat");

			// Revalidate to get actual data
			mutate();
			// toast.success("Chat unpinned successfully");
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to unpin chat");
		}
	};

	const handleReorderPins = async (chatIds: string[]) => {
		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				// Create a new array of pinnedChats in the desired order
				// Use filter first to ensure we only work with valid entries
				const reorderedPinnedChats = chatIds
					.map((id) => {
						const pinnedChat = currentData.pinnedChats.find(
							(pinnedChat) => pinnedChat.chatId === id,
						);
						return pinnedChat ? { ...pinnedChat } : null;
					})
					.filter((chat): chat is PinnedChat => chat !== null); // Type guard to remove nulls

				return {
					...currentData,
					pinnedChats: reorderedPinnedChats,
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch("/api/chat-org/pinned-chats", {
				method: "PATCH",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ chatIds }),
			});

			if (!response.ok) throw new Error("Failed to reorder pinned chats");

			// Revalidate to get actual data
			mutate();
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to reorder pinned chats");
		}
	};

	// Matter handlers
	const handleAddMatter = async (name: string) => {
		if (!name.trim()) return;

		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				// Create a temporary matter with minimal required fields
				const newMatter = {
					id: `temp-${Date.now()}`,
					name,
					chats: [],
				};

				return {
					...currentData,
					folders: [...currentData.folders, newMatter],
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch("/api/chat-org/folders", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ name }),
			});

			if (!response.ok) throw new Error("Failed to create matter");

			// Revalidate to get actual data
			mutate();
			// toast.success(`Matter "${name}" created successfully`);
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to create matter");
		}
	};

	const handleRenameMatter = async (matterId: string, newName: string) => {
		if (!newName.trim()) return;

		// Find the current matter name
		const matter = matters.find((m) => m.id === matterId);
		if (!matter) return;

		const oldName = matter.name;

		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				return {
					...currentData,
					folders: currentData.folders.map((matter) =>
						matter.id === matterId ? { ...matter, name: newName } : matter,
					),
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch("/api/chat-org/folders", {
				method: "PATCH",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ oldName, newName }),
			});

			if (!response.ok) throw new Error("Failed to rename matter");

			// Revalidate to get actual data
			mutate();
			// toast.success(`Matter renamed to "${newName}" successfully`);
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to rename matter");
		}
	};

	const handleDeleteMatter = async (matterId: string) => {
		// Find the matter name
		const matter = matters.find((m) => m.id === matterId);
		if (!matter) return;

		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				return {
					...currentData,
					folders: currentData.folders.filter(
						(matter) => matter.id !== matterId,
					),
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch(
				`/api/chat-org/folders?name=${encodeURIComponent(matter.name)}`,
				{
					method: "DELETE",
				},
			);

			if (!response.ok) throw new Error("Failed to delete matter");

			// Revalidate to get actual data
			mutate();
			// toast.success("Matter deleted successfully");
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to delete matter");
		}
	};

	const handleAddChatToMatter = async (matterId: string, chatId: string) => {
		// Find the matter name and chat
		const matter = matters.find((m) => m.id === matterId);
		if (!matter) return;

		const chatToAdd = filteredHistory.find((chat) => chat.id === chatId);
		if (!chatToAdd) return;

		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				return {
					...currentData,
					folders: currentData.folders.map((matter) => {
						if (matter.id === matterId) {
							return {
								...matter,
								chats: [...matter.chats, chatToAdd],
							};
						}
						return matter;
					}),
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch("/api/chat-org/folders/chats", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ chatId, folderName: matter.name }),
			});

			if (!response.ok) throw new Error("Failed to add chat to matter");

			// Revalidate to get actual data
			mutate();
			// toast.success("Chat added to matter successfully");
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to add chat to matter");
		}
	};

	const handleRemoveChatFromMatter = async (
		matterId: string,
		chatId: string,
	) => {
		// Find the matter name
		const matter = matters.find((m) => m.id === matterId);
		if (!matter) return;

		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				return {
					...currentData,
					folders: currentData.folders.map((matter) => {
						if (matter.id === matterId) {
							return {
								...matter,
								chats: matter.chats.filter((chat) => chat.id !== chatId),
							};
						}
						return matter;
					}),
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch(
				`/api/chat-org/folders/chats?chatId=${encodeURIComponent(chatId)}&folderName=${encodeURIComponent(matter.name)}`,
				{
					method: "DELETE",
				},
			);

			if (!response.ok) throw new Error("Failed to remove chat from matter");

			// Revalidate to get actual data
			mutate();
			// toast.success("Chat removed from matter successfully");
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to remove chat from matter");
		}
	};

	const handleReorderChatsInMatter = async (
		matterId: string,
		chatIds: string[],
	) => {
		// Find the folder name
		const matter = matters.find((m) => m.id === matterId);
		if (!matter) return;

		// Optimistically update using mutate
		mutate(
			(currentData) => {
				if (!currentData) return currentData;

				return {
					...currentData,
					folders: currentData.folders.map((matter) => {
						if (matter.id === matterId) {
							// Create a map of chats by ID for quick lookup
							const chatMap = new Map(
								matter.chats.map((chat) => [chat.id, chat]),
							);

							// Create a new array of chats in the desired order
							const reorderedChats = chatIds
								.map((id) => chatMap.get(id))
								.filter((chat): chat is Chat => chat !== undefined);

							return {
								...matter,
								chats: reorderedChats,
							};
						}
						return matter;
					}),
				};
			},
			false, // Don't revalidate immediately
		);

		// Call API
		try {
			const response = await fetch("/api/chat-org/folders/chats", {
				method: "PATCH",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ chatIds, folderName: matter.name }),
			});

			if (!response.ok) throw new Error("Failed to reorder chats in matter");

			// Revalidate to get actual data
			mutate();
		} catch (error) {
			// Revalidate to revert optimistic update
			mutate();
			toast.error("Failed to reorder chats in matter");
		}
	};

	// Add this state variable near the other state variables in SidebarHistory
	const [viewMode, setViewMode] = useState<"all" | "pinned" | "matters">("all");

	// Check loading state first - show skeleton while loading
	if (isLoading) {
		return (
			<div className="space-y-4">
				{/* Pinned Section */}
				<div className="space-y-2">
					<Skeleton className="h-4 w-16" />
					{Array.from({ length: 2 }, (_, i) => i).map((index) => (
						<div
							key={`pinned-${index}`}
							className="flex items-center gap-3 p-2 rounded-lg"
						>
							<Skeleton className="h-4 w-4 rounded" />
							<div className="flex-1 space-y-1">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-3 w-2/3" />
							</div>
						</div>
					))}
				</div>

				<Separator />

				{/* Recent Conversations */}
				<div className="space-y-2">
					<Skeleton className="h-4 w-20" />
					{Array.from({ length: 6 }, (_, i) => i).map((index) => (
						<div
							key={`conversation-${index}`}
							className="flex items-center gap-3 p-2 rounded-lg"
						>
							<div className="flex-1 space-y-1">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-3 w-1/2" />
							</div>
						</div>
					))}
				</div>
			</div>
		);
	}

	// After loading is complete, check for empty states
	if (filteredHistory?.length === 0 && searchQuery) {
		return (
			<div>
				<div>
					<div className="px-1 text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2">
						No chats found matching &ldquo;{searchQuery}&rdquo;
					</div>
				</div>
			</div>
		);
	}

	if (filteredHistory?.length === 0) {
		return (
			<div>
				<div>
					<div className="px-1 text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2">
						Your conversations will appear here once you start chatting!
					</div>
				</div>
			</div>
		);
	}

	const groupChatsByDate = (chats: Chat[]): GroupedChats => {
		const now = new Date();
		const oneWeekAgo = subWeeks(now, 1);
		const oneMonthAgo = subMonths(now, 1);

		// First sort all chats by updatedAt (or createdAt if updatedAt is not available)
		const sortedChats = [...chats].sort((a, b) => {
			const dateA = a.updatedAt || a.createdAt;
			const dateB = b.updatedAt || b.createdAt;
			return new Date(dateB).getTime() - new Date(dateA).getTime();
		});

		return sortedChats.reduce(
			(groups, chat) => {
				const chatDate = new Date(chat.updatedAt || chat.createdAt);

				if (isToday(chatDate)) {
					groups.today.push(chat);
				} else if (isYesterday(chatDate)) {
					groups.yesterday.push(chat);
				} else if (chatDate > oneWeekAgo) {
					groups.lastWeek.push(chat);
				} else if (chatDate > oneMonthAgo) {
					groups.lastMonth.push(chat);
				} else {
					groups.older.push(chat);
				}

				return groups;
			},
			{
				today: [],
				yesterday: [],
				lastWeek: [],
				lastMonth: [],
				older: [],
			} as GroupedChats,
		);
	};

	return (
		<DndProvider backend={HTML5Backend}>
			<>
				<div>
					{user && (
						<SidebarOrganization
							pinnedChats={pinnedChats}
							onPinChat={handlePinChat}
							onUnpinChat={handleUnpinChat}
							onReorderPins={handleReorderPins}
							viewMode={viewMode}
							setViewMode={setViewMode}
							onDeleteChat={(chatId) => {
								setDeleteId(chatId);
								setShowDeleteDialog(true);
							}}
							onBulkDelete={() => setShowBulkDeleteDialog(true)}
							isLoading={isLoading}
						/>
					)}
					{/* Then your existing grouped chats rendering */}
					{viewMode === "all" &&
						filteredHistory &&
						(() => {
							const groupedChats = groupChatsByDate(filteredHistory);

							return (
								<>
									{groupedChats.today.length > 0 && (
										<>
											<div className="p-1 text-xs text-sidebar-foreground/50">
												Today
											</div>
											{groupedChats.today.map((chat) => (
												<ChatItem
													key={chat.id}
													chat={chat}
													isActive={chat.id === id}
													onDelete={(chatId) => {
														setDeleteId(chatId);
														setShowDeleteDialog(true);
													}}
													onPinChat={handlePinChat}
													onUnpinChat={handleUnpinChat}
													onBulkDelete={() => setShowBulkDeleteDialog(true)}
													pinnedChats={sidebarData?.pinnedChats || []}
													onAddToFolder={(chatId, folderId) => {
														const matter = matters.find(
															(m) => m.id === folderId,
														);
														if (matter) {
															handleAddChatToMatter(folderId, chatId);
														}
													}}
													onManageTags={() => openTagDialog(chat)}
												/>
											))}
										</>
									)}

									{groupedChats.yesterday.length > 0 && (
										<>
											<div className="p-1 text-xs text-sidebar-foreground/50 mt-0">
												Yesterday
											</div>
											{groupedChats.yesterday.map((chat) => (
												<ChatItem
													key={chat.id}
													chat={chat}
													isActive={chat.id === id}
													onDelete={(chatId) => {
														setDeleteId(chatId);
														setShowDeleteDialog(true);
													}}
													onPinChat={handlePinChat}
													onUnpinChat={handleUnpinChat}
													onBulkDelete={() => setShowBulkDeleteDialog(true)}
													pinnedChats={sidebarData?.pinnedChats || []}
													onAddToFolder={(chatId, folderId) => {
														const matter = matters.find(
															(m) => m.id === folderId,
														);
														if (matter) {
															handleAddChatToMatter(folderId, chatId);
														}
													}}
													onManageTags={() => openTagDialog(chat)}
												/>
											))}
										</>
									)}

									{groupedChats.lastWeek.length > 0 && (
										<>
											<div className="p-1 text-xs text-sidebar-foreground/50 mt-3">
												Last 14 days
											</div>
											{groupedChats.lastWeek.map((chat) => (
												<ChatItem
													key={chat.id}
													chat={chat}
													isActive={chat.id === id}
													onDelete={(chatId) => {
														setDeleteId(chatId);
														setShowDeleteDialog(true);
													}}
													onPinChat={handlePinChat}
													onUnpinChat={handleUnpinChat}
													onBulkDelete={() => setShowBulkDeleteDialog(true)}
													pinnedChats={sidebarData?.pinnedChats || []}
													onAddToFolder={(chatId, folderId) => {
														const matter = matters.find(
															(m) => m.id === folderId,
														);
														if (matter) {
															handleAddChatToMatter(folderId, chatId);
														}
													}}
													onManageTags={() => openTagDialog(chat)}
												/>
											))}
										</>
									)}

									{groupedChats.lastMonth.length > 0 && (
										<>
											<div className="p-1 text-xs text-sidebar-foreground/50 mt-3">
												Last 30 days
											</div>
											{groupedChats.lastMonth.map((chat) => (
												<ChatItem
													key={chat.id}
													chat={chat}
													isActive={chat.id === id}
													onDelete={(chatId) => {
														setDeleteId(chatId);
														setShowDeleteDialog(true);
													}}
													onPinChat={handlePinChat}
													onUnpinChat={handleUnpinChat}
													onBulkDelete={() => setShowBulkDeleteDialog(true)}
													pinnedChats={sidebarData?.pinnedChats || []}
													onAddToFolder={(chatId, folderId) => {
														const matter = matters.find(
															(m) => m.id === folderId,
														);
														if (matter) {
															handleAddChatToMatter(folderId, chatId);
														}
													}}
													onManageTags={() => openTagDialog(chat)}
												/>
											))}
										</>
									)}

									{groupedChats.older.length > 0 && (
										<>
											<div className="p-1 text-xs text-sidebar-foreground/50 mt-3">
												Older
											</div>
											{groupedChats.older.map((chat) => (
												<ChatItem
													key={chat.id}
													chat={chat}
													isActive={chat.id === id}
													onDelete={(chatId) => {
														setDeleteId(chatId);
														setShowDeleteDialog(true);
													}}
													onPinChat={handlePinChat}
													onUnpinChat={handleUnpinChat}
													onBulkDelete={() => setShowBulkDeleteDialog(true)}
													pinnedChats={sidebarData?.pinnedChats || []}
													onAddToFolder={(chatId, folderId) => {
														const matter = matters.find(
															(m) => m.id === folderId,
														);
														if (matter) {
															handleAddChatToMatter(folderId, chatId);
														}
													}}
													onManageTags={() => openTagDialog(chat)}
												/>
											))}
										</>
									)}
								</>
							);
						})()}
				</div>
				<AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
					<AlertDialogContent>
						<AlertDialogHeader>
							<AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
							<AlertDialogDescription>
								This action cannot be undone. This will permanently delete your
								chat and remove it from our database.
							</AlertDialogDescription>
						</AlertDialogHeader>
						<AlertDialogFooter>
							<AlertDialogCancel>Cancel</AlertDialogCancel>
							<AlertDialogAction
								onClick={handleDelete}
								className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
							>
								Continue
							</AlertDialogAction>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialog>
				<BulkDeleteDialog
					isOpen={showBulkDeleteDialog}
					onClose={() => setShowBulkDeleteDialog(false)}
					chats={filteredHistory}
					onDelete={handleBulkDelete}
				/>

				{/* Add tag management dialog */}
				{isDialogOpen && currentChat && (
					<TagManagementDialog
						open={isDialogOpen}
						onOpenChange={(open) => {
							if (!open) closeTagDialog();
						}}
						chat={currentChat}
						allChats={sidebarData?.chats || []}
						onSave={handleSaveTags}
						isLoading={isTagsLoading}
						lightweightDialog={true}
						anchorElement={document.activeElement as HTMLElement}
					/>
				)}
			</>
		</DndProvider>
	);
}
