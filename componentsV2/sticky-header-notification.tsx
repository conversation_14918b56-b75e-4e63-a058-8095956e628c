"use client"
import { format } from "date-fns"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

type Props = {
    label: string
    date?: Date | null
}
export function StickyHeaderNotification({ label, date }: Props) {
    const router = useRouter()
    const [isOpen, setIsOpen] = useState(true)
    const fomattedDate = date ? format(new Date(date), 'yyyy-MM-dd HH:mm:ss') : ''
    const handleClose = () => {
        setIsOpen(false)
    }

    if (!isOpen) {
        return null
    }

    const handleClick = () => {
        router.push('/subscription')
    }
    return (
        <>
            <div
                id="sticky-banner"
                className="fixed top-0 start-0 z-50 flex justify-between w-full p-4 border-b border-gray-200 bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
            >
                <div className="flex items-center mx-auto justify-between">
                    <p className="flex items-center justify-between text-sm font-normal text-gray-500 dark:text-gray-400">
                        <span className="mr-2">
                            {`${label} ${fomattedDate}`}
                        </span>
                        <Button onClick={handleClick}>Upgrade Plan</Button>
                    </p>
                </div>

                <div className="flex items-center">
                    <button
                        onClick={handleClose}
                        data-dismiss-target="#sticky-banner"
                        type="button"
                        className="shrink-0 inline-flex justify-center w-7 h-7 items-center text-gray-400 hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 dark:hover:bg-gray-600 dark:hover:text-white"
                    >
                        <svg
                            className="w-3 h-3"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 14 14"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                            />
                        </svg>
                        <span className="sr-only">Close banner</span>
                    </button>
                </div>
            </div>
        </>
    )
}
