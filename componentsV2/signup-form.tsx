import Form from "next/form";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { IFormData } from "@/app/(auth)/signup/page";
import { Eye, EyeOff } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { CONTACT } from "@/lib/constants";
import { Button } from "@/components/ui/button";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { AlertCircle } from "lucide-react";

export function SignupForm({
	action,
	children,
	formData,
	setFormData,
	passwordError,
	firstNameError,
	lastNameError,
	emailError,
	companyError,
	teamsizeError,
	confirmPasswordError,
	onEmailChange,
	passwordCriteria, // Add this prop
	setReferrerCode,
	referrerCode,
	referralCodeError,
	fetchReferralCode,
}: {
	action: NonNullable<
		string | ((formData: FormData) => void | Promise<void>) | undefined
	>;
	children: React.ReactNode;
	formData: IFormData;
	setFormData: React.Dispatch<React.SetStateAction<IFormData>>;
	passwordError: string;
	firstNameError?: string;
	lastNameError?: string;
	emailError?: string;
	companyError?: string;
	teamsizeError?: string;
	confirmPasswordError?: string;
	onEmailChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
	passwordCriteria?: {
		minLength: boolean;
		hasUppercase: boolean;
		hasLowercase: boolean;
		hasNumber: boolean;
		hasSpecial: boolean;
	};
	referrerCode?: string;
	setReferrerCode: React.Dispatch<React.SetStateAction<string>>;
	referralCodeError?: string;
	fetchReferralCode: (code: string) => Promise<void>;
}) {
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirm, setShowConfirm] = useState(false);
	const [showReferralCode, setShowReferralCode] = useState(false);
	const referralInputRef = useRef<HTMLDivElement>(null);

	// Add heardFrom options
	const heardFromOptions = [
		// { value: "", label: "Select an option" },
		{ value: "search", label: "Search Engine (Google, Bing, etc.)" },
		{ value: "social", label: "Social Media" },
		{ value: "linkedin", label: "LinkedIn" },
		{ value: "friend", label: "Friend or Colleague" },
		{ value: "legal_publication", label: "Legal Publication" },
		{ value: "conference", label: "Conference or Event" },
		{ value: "advertisement", label: "Advertisement" },
		{ value: "other", label: "Other (please specify)" },
	];

	// Add state for custom "heard from" input
	const [showCustomHeardFrom, setShowCustomHeardFrom] = useState(false);

	useEffect(() => {
		if ("" !== referrerCode) {
			setShowReferralCode(true);
		}
	}, [referrerCode]);
	// Add click outside handler that only closes when referral code is empty
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			// Check if the click target is a link or inside a link
			const isLink = (event.target as HTMLElement).closest("a") !== null;

			if (
				!isLink && // Don't close if clicking on a link
				referralInputRef.current &&
				!referralInputRef.current.contains(event.target as Node) &&
				(!referrerCode || referrerCode.trim() === "")
			) {
				setShowReferralCode(false);
			}
		};

		if (showReferralCode) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showReferralCode, referrerCode]);

	// Update useEffect to show/hide custom input based on selection
	useEffect(() => {
		setShowCustomHeardFrom(formData?.heardFrom === "other");
	}, [formData?.heardFrom]);

	// const handleChange = (
	//   e: React.ChangeEvent<
	//     HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
	//   >
	// ) => {
	//   const { name, value, type } = e.target;
	//   const checked = (e.target as HTMLInputElement).checked;

	//   // Only update checkbox values if the input is actually a checkbox
	//   if (type === "checkbox") {
	//     setFormData((prev: IFormData) => ({
	//       ...prev,
	//       [name]: checked,
	//     }));
	//   } else {
	//     // For non-checkbox inputs, just update the value
	//     setFormData((prev: IFormData) => ({
	//       ...prev,
	//       [name]: value,
	//     }));
	//   }
	// };

	function handleChange(
		e: React.ChangeEvent<
			HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
		>,
	) {
		const { name, value, type } = e.target;
		const checked = (e.target as HTMLInputElement).checked;
		setFormData((prev) => ({
			...prev,
			[name]: type === "checkbox" ? checked : value,
		}));
	}
	return (
		<Form action={action} className="flex flex-col gap-3">
			<div className="flex flex-row gap-3">
				<div className="flex flex-col gap-1.5 flex-1">
					<Label
						htmlFor="firstname"
						className="text-sm font-medium text-slate-700 dark:text-slate-200"
					>
						First Name <span className="text-red-500">*</span>
					</Label>

					<Input
						id="firstname"
						name="firstname"
						className={`frosted-input bg-[#f9f8fb] border ${
							firstNameError
								? "border-rose-500 focus-visible:ring-rose-500"
								: "border-[#d6d1e0]"
						} dark:bg-slate-800/60`}
						type="text"
						placeholder="John"
						required
						value={formData?.firstname}
						onChange={handleChange}
					/>
					{firstNameError && (
						<p className="text-sm text-rose-500 mt-1">{firstNameError}</p>
					)}
				</div>
				<div className="flex flex-col gap-1.5 flex-1">
					<Label
						htmlFor="lastname"
						className="text-sm font-medium text-slate-700 dark:text-slate-200"
					>
						Last Name <span className="text-red-500">*</span>
					</Label>

					<Input
						id="lastname"
						name="lastname"
						className={`frosted-input bg-[#f9f8fb] border ${
							lastNameError
								? "border-rose-500 focus-visible:ring-rose-500"
								: "border-[#d6d1e0]"
						} dark:bg-slate-800/60`}
						type="text"
						placeholder="Doe"
						required
						value={formData?.lastname}
						onChange={handleChange}
					/>
					{lastNameError && (
						<p className="text-sm text-rose-500 mt-1">{lastNameError}</p>
					)}
				</div>
			</div>
			<div className="flex flex-col gap-1">
				<Label
					htmlFor="email"
					className="text-sm font-medium text-slate-700 dark:text-slate-200"
				>
					Email Address <span className="text-red-500">*</span>
				</Label>

				<Input
					id="email"
					name="email"
					className={`frosted-input bg-[#f9f8fb] border ${
						emailError
							? "border-rose-500 focus-visible:ring-rose-500"
							: "border-[#d6d1e0]"
					} dark:bg-slate-800/60`}
					type="email"
					placeholder="<EMAIL>"
					autoComplete="email"
					required
					value={formData?.email}
					onChange={onEmailChange || handleChange}
				/>
				{emailError && (
					<p className="text-sm text-rose-500 mt-1">{emailError}</p>
				)}
			</div>
			<div className="flex sm:flex-row flex-col gap-3">
				<div className="flex flex-col gap-1.5 flex-1">
					<Label
						htmlFor="company"
						className="text-sm font-medium text-slate-700 dark:text-slate-200"
					>
						Organization <span className="text-red-500">*</span>
					</Label>

					<Input
						id="company"
						name="company"
						className={`frosted-input bg-[#f9f8fb] border ${
							companyError
								? "border-rose-500 focus-visible:ring-rose-500"
								: "border-[#d6d1e0]"
						} dark:bg-slate-800/60`}
						type="text"
						placeholder="Amicus LLP"
						required
						value={formData?.company}
						onChange={handleChange}
					/>
					{companyError && (
						<p className="text-sm text-rose-500 mt-1">{companyError}</p>
					)}
				</div>
				<div className="flex flex-col gap-1.5 flex-1">
					<Label
						htmlFor="teamsize"
						className="text-sm font-medium text-slate-700 dark:text-slate-200"
					>
						Team Size<span className="text-red-500">*</span>
					</Label>

					<Input
						id="teamsize"
						name="teamsize"
						className={`h-10 rounded-lg bg-[#f9f8fb] dark:bg-slate-800/60 border ${
							teamsizeError
								? "border-rose-500 focus-visible:ring-rose-500"
								: "border-[#d6d1e0]"
						} [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none `}
						type="text"
						inputMode="numeric"
						pattern="[0-9]*"
						min="1"
						max="1000"
						placeholder="e.g., 50"
						required
						value={formData?.teamsize}
						onChange={(e) => {
							const value = e.target.value.replace(/[^0-9]/g, "");
							if (
								value === "" ||
								(parseInt(value) >= 1 && parseInt(value) <= 1000)
							) {
								setFormData((prev) => ({
									...prev,
									teamsize: value,
								}));
							}
						}}
					/>
					{teamsizeError && (
						<p className="text-sm text-rose-500 mt-1">{teamsizeError}</p>
					)}
				</div>
			</div>
			<div className="flex sm:flex-row flex-col gap-3">
				<div className="flex flex-col gap-1.5 flex-1">
					<Label
						htmlFor="password"
						className="text-sm font-medium text-slate-700 dark:text-slate-200"
					>
						Password <span className="text-red-500">*</span>
					</Label>
					<div className="relative">
						<Input
							id="password"
							name="password"
							className={`frosted-input bg-[#f9f8fb] border ${
								// Only show red border for password-specific errors, not match errors
								passwordError && !passwordError.includes("match")
									? "border-rose-500 focus-visible:ring-rose-500"
									: "border-[#d6d1e0]"
							} pr-10 dark:bg-slate-800/60`}
							type={showPassword ? "text" : "password"}
							placeholder="••••••••"
							required
							value={formData?.password}
							onChange={handleChange}
						/>
						<button
							type="button"
							className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 transition-colors hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 flex items-center justify-center "
							onClick={() => setShowPassword((prev) => !prev)}
						>
							{showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
						</button>
					</div>
					{/* Only show password-specific errors, not match errors */}
					{passwordError && !passwordError.includes("match") && (
						<p className="text-sm text-rose-500 mt-1">{passwordError}</p>
					)}
					{/* Password criteria section */}
					{formData?.password?.length > 0 && (
						<TooltipProvider delayDuration={0}>
							<Tooltip>
								<TooltipTrigger asChild>
									<div className="flex items-center mt-1 text-xs">
										<AlertCircle
											size={14}
											className={`mr-1 ${
												Object.values(passwordCriteria || {}).every(Boolean)
													? "text-green-500"
													: "text-amber-500"
											}`}
										/>
										<span>
											{Object.values(passwordCriteria || {}).every(Boolean)
												? "Password meets all requirements"
												: "Password requirements"}
										</span>
									</div>
								</TooltipTrigger>
								<TooltipContent side="right" className="w-64 p-2">
									<ul className="space-y-1 text-xs">
										<li
											className={`flex items-center ${
												passwordCriteria?.minLength
													? "text-green-500"
													: "text-muted-foreground"
											}`}
										>
											<span className="mr-1">
												{passwordCriteria?.minLength ? "✓" : "○"}
											</span>
											At least 8 characters
										</li>
										<li
											className={`flex items-center ${
												passwordCriteria?.hasUppercase
													? "text-green-500"
													: "text-muted-foreground"
											}`}
										>
											<span className="mr-1">
												{passwordCriteria?.hasUppercase ? "✓" : "○"}
											</span>
											One uppercase letter
										</li>
										<li
											className={`flex items-center ${
												passwordCriteria?.hasLowercase
													? "text-green-500"
													: "text-muted-foreground"
											}`}
										>
											<span className="mr-1">
												{passwordCriteria?.hasLowercase ? "✓" : "○"}
											</span>
											One lowercase letter
										</li>
										<li
											className={`flex items-center ${
												passwordCriteria?.hasNumber
													? "text-green-500"
													: "text-muted-foreground"
											}`}
										>
											<span className="mr-1">
												{passwordCriteria?.hasNumber ? "✓" : "○"}
											</span>
											One number
										</li>
										<li
											className={`flex items-center ${
												passwordCriteria?.hasSpecial
													? "text-green-500"
													: "text-muted-foreground"
											}`}
										>
											<span className="mr-1">
												{passwordCriteria?.hasSpecial ? "✓" : "○"}
											</span>
											One special character
										</li>
									</ul>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					)}
				</div>
				<div className="flex flex-col gap-1.5 flex-1">
					<Label
						htmlFor="confirmPassword"
						className="text-sm font-medium text-slate-700 dark:text-slate-200"
					>
						Confirm Password <span className="text-red-500">*</span>
					</Label>
					<div className="relative">
						<Input
							id="confirmPassword"
							name="confirmPassword"
							className={`frosted-input bg-[#f9f8fb] dark:bg-slate-800/60 border ${
								confirmPasswordError ||
								(passwordError && passwordError.includes("match"))
									? "border-rose-500 focus-visible:ring-rose-500"
									: "border-[#d6d1e0]"
							} pr-10`}
							type={showConfirm ? "text" : "password"}
							placeholder="••••••••"
							required
							value={formData?.confirmPassword}
							onChange={handleChange}
						/>
						<button
							type="button"
							className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 transition-colors hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 flex items-center justify-center"
							onClick={() => setShowConfirm((prev) => !prev)}
						>
							{showConfirm ? <EyeOff size={18} /> : <Eye size={18} />}
						</button>
					</div>
					{/* Show either confirmPasswordError or password match error */}
					{(confirmPasswordError ||
						(passwordError && passwordError.includes("match"))) && (
						<p className="text-sm text-rose-500 mt-1">
							{confirmPasswordError || passwordError}
						</p>
					)}
				</div>
			</div>
			<div className="flex flex-col gap-1.5">
				<Label
					htmlFor="heardFrom"
					className="text-sm font-medium text-slate-700 dark:text-slate-200"
				>
					How did you hear about us?
				</Label>

				<div className="relative">
					<select
						id="heardFrom"
						name="heardFrom"
						className={`frosted-input  bg-[#f9f8fb] border border-[#d6d1e0] rounded-md h-10 px-3 w-full appearance-none text-sm font-normal font-sans text-slate-700 dark:text-slate-200 dark:bg-slate-800/60 dark:border-[#d6d1e0] focus:outline-none dark:focus:outline-none focus:border-[rgb(var(--base-navy))]  dark:focus:border-[#d6d1e0]`}
						value={formData?.heardFrom}
						onChange={handleChange}
					>
						<option value="">Select an option</option>
						{heardFromOptions.map((option) => (
							<option
								key={option.value}
								value={option.value}
								className="font-sans text-sm dark:hover:bg-[#3D434F] dark:bg-[#232A37]"
							>
								{option.label}
							</option>
						))}
					</select>
					<div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
						<svg
							className="h-4 w-4 text-slate-500"
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 20 20"
							fill="currentColor"
							aria-hidden="true"
						>
							<path
								fillRule="evenodd"
								d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
								clipRule="evenodd"
							/>
						</svg>
					</div>
				</div>

				{/* Show custom input field when "Other" is selected */}
				{showCustomHeardFrom && (
					<div className="mt-2">
						<Input
							id="customHeardFrom"
							name="customHeardFrom"
							className="frosted-input bg-[#f9f8fb] border border-[#d6d1e0] dark:bg-slate-800/60"
							type="text"
							placeholder="Please specify how you heard about us"
							value={formData?.customHeardFrom || ""}
							onChange={handleChange}
						/>
					</div>
				)}
			</div>
			<div>
				<div className="relative h-10">
					<div
						className={`transition-all ease-in-out origin-left ${showReferralCode ? "w-full" : "w-auto"}`}
						style={{ transitionDuration: "1100ms" }}
					>
						{!showReferralCode ? (
							<button
								type="button"
								onClick={() => setShowReferralCode(true)}
								className="text-indigo-600 hover:text-indigo-800 text-sm font-medium transition-colors duration-200 underline-offset-2 mt-2"
							>
								Have a referral code?
							</button>
						) : (
							<div
								ref={referralInputRef}
								className="animate-in fade-in"
								style={{ animationDuration: "1100ms" }}
							>
								<Input
									id="referralCode"
									name="referralCode"
									value={referrerCode}
									onChange={(e) => setReferrerCode(e.target.value)}
									onBlur={(e) => fetchReferralCode(e.target.value.trim())}
									placeholder="Enter referral code"
									className={`dark:focus:bg-slate-800 dark:bg-slate-800 dark:border-white ${referralCodeError ? "border-red-500" : ""} transition-all`}
									autoFocus
								/>
								{referralCodeError && (
									<p
										className="text-red-500 text-sm mt-1 animate-in fade-in mb-1"
										style={{
											animationDuration: "800ms",
											animationDelay: "200ms",
										}}
									>
										{referralCodeError}
									</p>
								)}
							</div>
						)}
					</div>
				</div>
			</div>
			<div className="flex flex-col gap-2 py-1">
				<p className="text-xs text-slate-600 dark:text-slate-400 text-center mt-2">
					By signing up, you agree to our&nbsp;
					<Link
						href="/signup/terms-of-service"
						target="_blank"
						className="text-indigo-600 dark:text-indigo-400 hover:underline"
					>
						Terms of Use
					</Link>
					&nbsp;and&nbsp;
					<Link
						href="/signup/privacy-policy"
						target="_blank"
						className="text-indigo-600 dark:text-indigo-400 hover:underline"
					>
						Privacy Policy
					</Link>
					.
				</p>
			</div>
			{children}

			<p className="text-xs text-slate-600 dark:text-slate-400 text-center mt-2">
				If you have any questions or need multiple licenses - email us at &nbsp;
				<a href={`mailto:${CONTACT.EMAIL}`} className="text-blue-600 underline">
					<EMAIL>
				</a>
			</p>
		</Form>
	);
}
