import {
	useEffect,
	useRef,
	useState,
	type RefObject,
	type Dispatch,
	type SetStateAction,
} from "react";

export function useScrollToBottom<T extends HTMLElement>(): [
	RefObject<T>,
	RefObject<T>,
	boolean,
	Dispatch<SetStateAction<boolean>>,
	(forceImmediate?: boolean) => void, // Added scrollToBottom function to the return value
] {
	const containerRef = useRef<T>(null);
	const endRef = useRef<T>(null);
	const userScrolledRef = useRef(false);
	const initialScrollDoneRef = useRef(false); // Track if initial scroll has happened
	const [isScrolledUp, setIsScrolledUp] = useState(false);

	// Function to scroll to the bottom of the container
	const scrollToBottom = (forceImmediate = false) => {
		const container = containerRef.current;
		const end = endRef.current;

		if (container && end) {
			// Use a more reliable scrolling method for mobile
			const scrollOptions: ScrollIntoViewOptions = {
				behavior: forceImmediate ? "auto" : "smooth",
				block: "end",
			};

			// Try multiple scroll methods for better compatibility
			try {
				// Method 1: Using scrollIntoView
				end.scrollIntoView(scrollOptions);

				// Method 2: Direct scrollTop manipulation (more reliable on mobile)
				if (forceImmediate) {
					container.scrollTop = container.scrollHeight;
				}

				// Method 3: Using scrollTo (fallback)
				container.scrollTo({
					top: container.scrollHeight,
					behavior: forceImmediate ? "auto" : "smooth",
				});

				// Reset scroll state
				userScrolledRef.current = false;
				setIsScrolledUp(false);
			} catch (error) {
				console.error("Scroll error:", error);
				// Fallback to basic scroll
				container.scrollTop = container.scrollHeight;
			}
		}
	};

	useEffect(() => {
		const container = containerRef.current;
		const end = endRef.current;

		if (container && end) {
			// Initial scroll on mount
			// if (!initialScrollDoneRef.current) {
			//   scrollToBottom(true);
			//   initialScrollDoneRef.current = true;

			//   // Additional scroll after a short delay to catch any late-loading content
			//   setTimeout(() => scrollToBottom(true), 100);
			//   setTimeout(() => scrollToBottom(true), 500); // Extra delay for mobile
			// }

			const handleScroll = () => {
				if (!container) return;
				const { scrollTop, scrollHeight, clientHeight } = container;

				// Consider scrolled up if we're more than 100px from bottom
				const scrolledUp = scrollHeight - scrollTop - clientHeight > 100;

				userScrolledRef.current = scrolledUp;
				setIsScrolledUp(scrolledUp);
			};

			container.addEventListener("scroll", handleScroll);

			// Enhanced mutation observer for better mobile support
			const observer = new MutationObserver(() => {
				// if (!userScrolledRef.current) {
				//   // Use requestAnimationFrame for smoother scrolling
				//   // requestAnimationFrame(() => {
				//   //   scrollToBottom();
				//   // });
				// }
				if (!container) return;

				const messages = container.querySelectorAll("[data-role]");
				if (!messages.length) return;

				const lastMessage = messages[messages.length - 1];
				const role = lastMessage.getAttribute("data-role");

				if (role === "user") {
					requestAnimationFrame(() => {
						scrollToBottom(true);
					});
				}
			});

			observer.observe(container, {
				childList: true,
				subtree: true,
				characterData: true,
				attributes: true,
			});

			return () => {
				container.removeEventListener("scroll", handleScroll);
				observer.disconnect();
			};
		}
	}, []);

	return [containerRef, endRef, isScrolledUp, setIsScrolledUp, scrollToBottom];
}
