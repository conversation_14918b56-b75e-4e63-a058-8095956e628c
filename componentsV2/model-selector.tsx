"use client";

import { startTransition, useMemo, useOptimistic, useState } from "react";

import { saveChatModelId } from "@/app/(chat)/actions";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { models } from "@/lib/ai/models";
import { logEvent } from "@/lib/analytics/events-client";
import { ChatEvent } from "@/lib/analytics/event-types";
import { useUser } from "@/contexts/UserContext";
import { cn } from "@/lib/utils";
import { CheckCircleFillIcon, ChevronDownIcon } from "./icons";

export function ModelSelector({
  selectedModelId,
  className,
  onModelSelect,
  chatId,
}: {
  selectedModelId: string;
  className?: string;
  onModelSelect?: (modelId: string) => void;
  chatId?: string;
}) {
  const [open, setOpen] = useState(false);
  const [optimisticModelId, setOptimisticModelId] =
    useOptimistic(selectedModelId);
  const user = useUser();

  const selectedModel = useMemo(
    () => models.find((model) => model.id === optimisticModelId),
    [optimisticModelId]
  );

  const visibleModels = useMemo(
    () => models.filter((model) => !model.hidden),
    []
  );

  const handleModelSelect = async (model: (typeof models)[0]) => {
    setOpen(false);

    // Log the Model Change event
    logEvent(ChatEvent.MODEL_CHANGE, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      previousModel: optimisticModelId,
      newModel: model.id,
      chatId: chatId || null,
    });

    document.cookie = `model-id=${model.id}; path=/; max-age=31536000`;

    // Update optimistic state
    setOptimisticModelId(model.id);

    if (chatId) {
      await saveChatModelId({ chatId, modelId: model.id });
    }

    if (onModelSelect) {
      onModelSelect(model.id);
    }
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        asChild
        className={cn(
          "w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground focus:outline-none focus-visible:outline-none",
          className
        )}>
        <Button
          variant="outline"
          className={cn("!mb-0 min-w-fit items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 py-2 px-2 sm:px-3 h-8 sm:h-[36px] transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 w-fit shrink-0", className)}
          onMouseDown={(e) => {
            // Prevent focus on mouse click
            e.preventDefault();
          }}
          tabIndex={-1}>
          <div className="flex items-center gap-1 sm:gap-2">
            <div className="text-green-500">
              <CheckCircleFillIcon />
            </div>
            <span className="font-medium text-xs sm:text-sm whitespace-nowrap">{selectedModel?.label}</span>
            <ChevronDownIcon />
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        side="top"
        className="sm:min-w-[300px] min-w-[200px]">
        {visibleModels.map((model) => (
          <DropdownMenuItem
            key={model.id}
            onSelect={() => startTransition(() => handleModelSelect(model))}
            className="gap-4 group/item flex flex-row justify-between items-center privateIconDropdown"
            data-active={model.id === optimisticModelId}>
            <div className="flex flex-col gap-1 items-start">
              <div className="flex items-center">{model.label}</div>
              {model.description && (
                <div className="text-xs text-muted-foreground whitespace-pre-line privateDesc">
                  {model.description}
                </div>
              )}
            </div>
            <div className="text-green-500 opacity-0 group-data-[active=true]/item:opacity-100">
              <CheckCircleFillIcon />
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
