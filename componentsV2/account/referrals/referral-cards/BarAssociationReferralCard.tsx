import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Building, Award, Check } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function BarAssociationReferralCard() {
  return (
    <Card className="flex flex-col h-full hover:-translate-y-1 hover:shadow-md transition-all duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-xl flex items-center gap-2">
            <div className="p-1.5 bg-secondary rounded-md">
              <Building className="h-5 w-5 text-primary" />
            </div>
            <span>Bar Association</span>
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 flex-1 flex flex-col">
        <div className="text-2xl font-playfair font-bold flex items-center gap-2">
          Custom{" "}
          <span className="text-sm font-normal text-muted-foreground">
            rewards
          </span>
        </div>

        <div className="space-y-3 flex-1">
          <p className="text-sm font-medium">
            We also reward users who introduce Iqidis to organizations
          </p>
          <ul className="text-sm text-muted-foreground space-y-2 pt-2">
            {[
              "Bar associations",
              "CLE program directors",
              "Practice group networks or firm-wide rollouts",
            ].map((feature, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="rounded-full p-1 mt-0.5 bg-green-100">
                  <Check className="h-3 w-3 text-green-600" />
                </div>
                <span>{feature}</span>
              </li>
            ))}
          </ul>

          <div className="pt-2">
            <p className="text-sm font-medium">Custom rewards may include:</p>
            <ul className="text-sm text-muted-foreground space-y-2 pt-2">
              {[
                "Cash bonuses",
                "Free months",
                "Lifetime feature access",
                "Public recognition",
              ].map((feature, index) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="rounded-full p-1 mt-0.5 bg-green-100">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="mt-auto pt-2">
          <Button variant="outline" className="w-full">
            Coming Soon
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
