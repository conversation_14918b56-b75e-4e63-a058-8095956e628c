import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Co<PERSON>, Check, Mail, Share2, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { Logger } from "@/lib/utils/Logger";
import { User } from "@/lib/db/schema";
import Link from "next/link";

export default function ReferralLinkCard({
  user,
  referralLink,
  isLoading,
}: {
  user: Partial<User>;
  referralLink: string;
  isLoading: boolean;
}) {
  const [copied, setCopied] = useState("");

  // Mock referral link - in a real app, this would come from an API
  // const referralLink = `https://iqidis.ai/ref/johnsmith123`;

  const referralCode = user?.referralCode ?? "";

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    setCopied(type);
    toast.success(`${type} copied to clipboard`);

    setTimeout(() => {
      setCopied("");
    }, 2000);
  };

  // const subject = "Check out Iqidis AI for legal professionals";
  // const body = `I've been using Iqidis AI to help with my legal work and thought you might find it useful. Sign up with my referral link: ${referralLink}`;

  const subject = "Check out Iqidis AI for legal professionals";
  const body = `
Hi there,

I've been using Iqidis AI to help with my legal work and thought you might find it useful.

👉 Sign up with my referral link: ${referralLink}

`.trim();

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load all required data
        // await Promise.all([getActivePlans(), getActiveSubscription()]);
      } catch (error) {
        Logger.error("Failed to load subscription data", error);
      } finally {
        // Remove loading state from localStorage
        localStorage.removeItem("subscriptionPageLoading");
        // setPageLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <Card className="h-full" data-referral-link-card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Share2 className="h-5 w-5 text-primary" />
          Your Referral Link
        </CardTitle>
        <CardDescription>
          Share this link with colleagues to earn rewards
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="referral-link" className="text-sm font-medium">
            Referral Link
          </label>
          <div className="flex gap-2">
            <Input
              id="referral-link"
              value={referralLink}
              readOnly
              className="flex-1"
            />
            <Button
              variant="outline"
              size="icon"
              onClick={() => copyToClipboard(referralLink, "Link")}
              aria-label="Copy referral link"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : copied === "Link" ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <label htmlFor="referral-code" className="text-sm font-medium">
            Referral Code
          </label>
          <div className="flex gap-2">
            <Input
              id="referral-code"
              value={referralCode}
              readOnly
              className="flex-1"
            />
            <Button
              variant="outline"
              size="icon"
              onClick={() => copyToClipboard(referralCode, "Code")}
              aria-label="Copy referral code"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : copied === "Code" ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        <Button
          className="w-full gap-2"
          variant="secondary"
          //   onClick={() => {
          //     window.location.href = `mailto:?subject=${encodeURIComponent(
          //       subject
          //     )}&body=${encodeURIComponent(body)}`;
          //   }}
          // >
          onClick={() => {
            window.location.href = `mailto:?subject=${encodeURIComponent(
              subject
            )}&body=${encodeURIComponent(body)}`;
          }}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <>
              <Mail className="h-4 w-4" />
              Share via Email
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
