
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { ExternalLink, CreditCard, PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter } from "next/navigation";

interface PaymentMethod {
  id: string;
  card: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  } | null;
  isDefault: boolean;
}

interface PaymentMethodCardProps {
  tier: "free" | "premium" | "premium-yearly" | "enterprise";
}

export function PaymentMethodCard({ tier }: PaymentMethodCardProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { data: session } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (tier !== "free") {
      fetchPaymentMethods();
    }
  }, [tier]);

  const fetchPaymentMethods = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/stripe/payment-methods');
      const data = await response.json();

      if (response.ok) {
        setPaymentMethods(data.paymentMethods);
      } else {
        console.error("Failed to fetch payment methods:", data.error);
      }
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleManageBilling = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/stripe/customer-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('Failed to create portal session');
      }

      const { url } = await response.json();
      window.location.href = url;
    } catch (error) {
      console.error('Error opening customer portal:', error);
      toast.error("Could not open payment portal. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  const formatCardBrand = (brand: string) => {
    const brands: Record<string, string> = {
      'visa': 'VISA',
      'mastercard': 'MC',
      'amex': 'AMEX',
      'discover': 'DISC',
      'diners': 'DINERS',
      'jcb': 'JCB',
      'unionpay': 'UP'
    };

    return brands[brand.toLowerCase()] || brand.toUpperCase();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment Method</CardTitle>
        <CardDescription>Manage your payment details</CardDescription>
      </CardHeader>
      <CardContent>
        {tier === "free" ? (
          <div className="text-center py-4">
            <p className="text-muted-foreground">No payment method required on Free plan</p>
          </div>
        ) : isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
          </div>
        ) : paymentMethods.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-muted-foreground">No payment methods found</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={handleManageBilling}
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Payment Method
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {paymentMethods.map((method) => (
              <div key={method.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-14 bg-muted rounded flex items-center justify-center">
                    <span className="font-semibold text-xs">
                      {method.card ? formatCardBrand(method.card.brand) : 'CARD'}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">
                      •••• {method.card?.last4 || '****'}
                      {method.isDefault && <span className="ml-2 text-xs text-green-500">Default</span>}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Expires {method.card?.expMonth || 'MM'}/{method.card?.expYear || 'YYYY'}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleManageBilling}
                >
                  Update
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="border-t pt-4 flex flex-col items-start">
        <p className="text-sm text-muted-foreground mb-2">Need help with billing?</p>
        <Button variant="link" className="h-auto p-0 text-primary dark:text-indigo-400" asChild>
          <a href="mailto:<EMAIL>" className="flex items-center">
            <span>Contact Billing Support</span>
            <ExternalLink className="h-3 w-3 ml-1" />
          </a>
        </Button>
      </CardFooter>
    </Card>
  );
}
