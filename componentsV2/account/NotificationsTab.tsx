
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Bell, Mail, BarChart, Save } from "lucide-react";
import { toast } from "sonner";

export default function NotificationsTab() {
  const [isLoading, setIsLoading] = useState(false);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [usageReports, setUsageReports] = useState(false);
  const [marketingCommunications, setMarketingCommunications] = useState(false);
  const [newFeatures, setNewFeatures] = useState(false);
  const [usageAlerts, setUsageAlerts] = useState(false);


  const handleSavePreferences = async () => {
    setIsLoading(true);
    try {
      // This would connect to your API
      console.log("Notification preferences:", { emailNotifications, usageReports });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success("Notification preferences updated successfully!");
    } catch (error) {
      console.error("Error updating notification preferences:", error);
      toast.error("Failed to update notification preferences. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Email Notifications</CardTitle>
          <CardDescription>
            Choose what updates you want to receive via email
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-sm font-medium">Account Updates</h4>
                <p className="text-sm text-muted-foreground">Receive important updates about your account</p>
              </div>
              <Switch
                checked={emailNotifications}
                onCheckedChange={setEmailNotifications}
                aria-label="Toggle email notifications"
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-sm font-medium">Usage Reports</h4>
                <p className="text-sm text-muted-foreground">Get monthly usage summaries via email</p>
              </div>
              <Switch
                checked={usageReports}
                onCheckedChange={setUsageReports}
                aria-label="Toggle usage reports"
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-sm font-medium">Marketing Communications</h4>
                <p className="text-sm text-muted-foreground">Receive information about new features and offers</p>
              </div>
              <Switch
                checked={marketingCommunications}
                onCheckedChange={setMarketingCommunications}
                aria-label="Toggle marketing emails"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>In-App Notifications</CardTitle>
          <CardDescription>
            Configure what notifications appear in-app
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-sm font-medium">New Features</h4>
                <p className="text-sm text-muted-foreground">Get notified about new platform features</p>
              </div>
              <Switch
                checked={newFeatures}
                onCheckedChange={setNewFeatures}
                aria-label="Toggle feature notifications"
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-sm font-medium">Usage Alerts</h4>
                <p className="text-sm text-muted-foreground">Get notified when approaching usage limits</p>
              </div>
              <Switch
                checked={usageAlerts}
                onCheckedChange={setUsageAlerts}
                aria-label="Toggle usage alerts"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button
          onClick={handleSavePreferences}
          disabled={isLoading}
          className="w-full sm:w-auto"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              <span>Saving...</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              <span>Save Preferences</span>
            </div>
          )}
        </Button>
      </div>
    </div>
  );
}
