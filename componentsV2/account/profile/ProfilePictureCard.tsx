
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { UserRound, Upload, Plus } from "lucide-react";
import { useState, useRef } from "react";
import { toast } from "sonner";

interface ProfilePictureCardProps {
  userData: {
    firstName: string;
    lastName: string;
    avatarUrl: string;
  };
  onAvatarUpdate: (newAvatarUrl: string) => Promise<void>;
}

export function ProfilePictureCard({ userData, onAvatarUpdate }: ProfilePictureCardProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Dismiss any existing toasts before showing new ones
    toast.dismiss();

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image must be less than 5MB');
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/profile/update', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload image');
      }

      const { avatarUrl } = await response.json();

      // Don't show a toast here, let the parent component handle it
      await onAvatarUpdate(avatarUrl);
      // Remove the toast.success call from here
    } catch (error) {
      console.error('Upload error:', error);
      toast.dismiss(); // Dismiss all previous toasts
      toast.error(error instanceof Error ? error.message : 'Failed to update profile picture');
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const hasAvatar = !!userData.avatarUrl;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Picture</CardTitle>
        <CardDescription>How you appear to other users</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center space-y-4">
        <div className="relative">
          <div className="h-32 w-32 rounded-full bg-muted flex items-center justify-center overflow-hidden border-2 border-border">
            {userData.avatarUrl ? (
              <img
                src={userData.avatarUrl}
                alt={`${userData.firstName} ${userData.lastName}`}
                className="h-full w-full object-cover"
              />
            ) : (
              <UserRound className="h-16 w-16 text-muted-foreground" />
            )}
          </div>
        </div>
        <div className="flex flex-col gap-2 w-full items-center">
          <input
            type="file"
            ref={fileInputRef}
            accept="image/*"
            className="hidden"
            onChange={handleFileChange}
          />
          <Button
            variant="outline"
            onClick={handleButtonClick}
            disabled={isUploading}
          >
            {isUploading ? 'Uploading...' : hasAvatar ? 'Change Photo' : 'Add Photo'}
            {!isUploading && (
              hasAvatar ?
                <Upload className="ml-2 h-4 w-4" /> :
                <Plus className="ml-2 h-4 w-4" />
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
