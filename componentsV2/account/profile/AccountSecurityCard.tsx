import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>le,
	CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
	Too<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>ider,
	TooltipTrigger,
	Tooltip,
} from "@/components/ui/tooltip";

export function AccountSecurityCard() {
	const router = useRouter();

	const { data: session, status } = useSession();

	return (
		<Card className="mt-6">
			<CardHeader>
				<CardTitle>Account Security</CardTitle>
				<CardDescription>
					Manage your password and security settings
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4 p-4 sm:p-6">
				<div className="flex items-center justify-between mobileColumn">
					<div className="space-y-1">
						<h4 className="text-sm font-medium">Password</h4>
						<p className="text-sm text-muted-foreground">
							Change your password
						</p>
					</div>
					{/* <span
            title={
              session?.user?.heardFrom === "google-login"
                ? "You have logged in with Google, password reset is not available."
                : ""
            }> */}
					<TooltipProvider delayDuration={0}>
						<Tooltip>
							<TooltipTrigger asChild>
								{session?.user?.heardFrom == "google-login" ? (
									<Button
										variant="outline"
										size="sm"
										className="opacity-50 hover:bg-transparent cursor-not-allowed"
										// disabled
									>
										Update
									</Button>
								) : (
									<Button
										variant="outline"
										onClick={() => router.push("/reset-password")}
										size="sm"
									>
										Update
									</Button>
								)}
							</TooltipTrigger>
							<TooltipContent side="bottom" sideOffset={5} className="z-[9999]">
								{session?.user?.heardFrom === "google-login"
									? "You have logged in with Google, password reset is not available."
									: ""}
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
					{/* </span> */}
				</div>
			</CardContent>
		</Card>
	);
}
