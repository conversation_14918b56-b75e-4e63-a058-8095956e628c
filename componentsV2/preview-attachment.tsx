import type { Attachment } from "ai";
import { LoaderIcon, CrossIcon } from "./icons";
import {
	FileTextIcon,
	ImageIcon,
	FileVideoIcon,
	FileAudioIcon,
	FileIcon,
	CheckIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { useMemo, useState, useEffect } from "react";
import { FilePreviewModal } from "./file-preview-modal";
import { DocumentPreviewTrigger } from "./document-preview-trigger";

const inferContentType = (url?: string, name?: string): string => {
	const source = (name || url || "").toLowerCase();
	const extension = source.split(".").pop() || "";

	const contentTypes: Record<string, string> = {
		pdf: "application/pdf",
		jpg: "image/jpg",
		jpeg: "image/jpeg",
		png: "image/png",
		gif: "image/gif",
		webp: "image/webp",
		svg: "image/svg+xml",
		mp4: "video/mp4",
		mp3: "audio/mp3",
		wav: "audio/wav",
		doc: "application/msword",
		docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		xls: "application/vnd.ms-excel",
		xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		ppt: "application/vnd.ms-powerpoint",
		pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
	};

	return contentTypes[extension] || "";
};

// Extend the Attachment type to include database fields
interface ExtendedAttachment extends Attachment {
	id?: string; // Database ID
}

export const PreviewAttachment = ({
	attachment,
	isUploading = false,
	onDelete,
	showSuccessIndicator = false,
	disablePreview = false,
}: {
	attachment: ExtendedAttachment;
	isUploading?: boolean;
	onDelete?: () => void;
	showSuccessIndicator?: boolean;
	disablePreview?: boolean;
}) => {
	const [showSuccess, setShowSuccess] = useState(false);
	const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
	const [imgError, setImgError] = useState(false);

	// Show success indicator briefly when upload completes
	useEffect(() => {
		if (showSuccessIndicator && !isUploading) {
			setShowSuccess(true);
			const timer = setTimeout(() => setShowSuccess(false), 2000);
			return () => clearTimeout(timer);
		}
	}, [isUploading, showSuccessIndicator]);

	const { name, url, contentType: rawContentType } = attachment;

	// if contentType is not provided, infer it from name or url
	const contentType = rawContentType || inferContentType(url, name);

	// Check if URL is a protected URL (S3 or other private storage)
	const isProtectedUrl = useMemo(() => {
		if (!url) return false;
		return (
			url.includes("s3.amazonaws.com") ||
			url.includes("amazonaws.com") ||
			url.includes("blob.vercel-storage.com")
		);
	}, [url]);

	const getFileIcon = (type?: string) => {
		if (!type)
			return <FileIcon className="w-8 h-8 text-gray-600 dark:text-white" />;

		if (type.startsWith("image"))
			return <ImageIcon className="w-8 h-8 text-blue-500" />;
		if (type.startsWith("video"))
			return <FileVideoIcon className="w-8 h-8 text-red-500" />;
		if (type.startsWith("audio"))
			return <FileAudioIcon className="w-8 h-8 text-green-500" />;
		if (type === "application/pdf")
			return <FileTextIcon className="w-8 h-8 text-black-500" />;

		return <FileIcon className="w-8 h-8 text-gray-600 dark:text-white" />;
	};

	// Extract attachment ID logic to a single place
	const attachmentId = attachment._attachment_id || attachment.id;
	const isPreviewable = !disablePreview && !!attachmentId;

	// Simplify preview URL logic
	const previewUrl = attachment._attachment_id
		? `/library/artifact/${attachment._attachment_id}/preview`
		: attachmentId || "";

	const handleFileClick = () => {
		if (attachmentId) {
			setIsPreviewModalOpen(true);
		}
	};

	const renderContent = () => (
		<>
			{onDelete && !isUploading && !showSuccess && (
				<Button
					variant="ghost"
					size="icon"
					className="absolute top-0 right-0 z-20 m-1 size-6 p-1 bg-background/80 hover:bg-destructive hover:text-destructive-foreground"
					onClick={(e) => {
						e.preventDefault();
						e.stopPropagation();
						onDelete();
					}}
				>
					<CrossIcon size={12} />
				</Button>
			)}

			{url ? (
				contentType.startsWith("image") && !isProtectedUrl && !imgError ? (
					// Only try to render image if it's not a protected URL
					// eslint-disable-next-line @next/next/no-img-element
					<img
						key={url}
						src={url}
						alt={name ?? "An image attachment"}
						className="rounded-md size-full object-cover"
						onError={() => {
							// If image fails to load, fall back to icon
							console.warn("Image failed to load:", url);
							setImgError(true);
						}}
					/>
				) : (
					getFileIcon(contentType)
				)
			) : (
				<FileIcon className="w-8 h-8 text-gray-600 dark:text-white" />
			)}

			{isUploading && (
				<div className="animate-spin absolute text-zinc-500">
					<LoaderIcon color="#000000" />
				</div>
			)}

			{showSuccess && (
				<div className="absolute inset-0 flex items-center justify-center bg-green-500/20 rounded-md">
					<div className="bg-green-500 text-white rounded-full p-1">
						<CheckIcon size={16} />
					</div>
				</div>
			)}
		</>
	);

	return (
		<>
			<Tooltip>
				<TooltipTrigger asChild>
					<div className="flex flex-col gap-2 flex-shrink-0">
						{isPreviewable ? (
							<DocumentPreviewTrigger
								onClick={handleFileClick}
								className="w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center flex-shrink-0"
							>
								{renderContent()}
							</DocumentPreviewTrigger>
						) : (
							<div className="w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center flex-shrink-0">
								{renderContent()}
							</div>
						)}
						<div
							className="text-xs text-zinc-500 max-w-20 truncate"
							title={name}
						>
							{name}
						</div>
					</div>
				</TooltipTrigger>
				<TooltipContent>
					{name || "Unnamed attachment"}
					{isUploading && " (uploading...)"}
					{showSuccess && " (upload complete)"}
				</TooltipContent>
			</Tooltip>

			<FilePreviewModal
				isOpen={isPreviewModalOpen}
				onClose={() => setIsPreviewModalOpen(false)}
				fileUrl={previewUrl}
				fileName={name || "Untitled"}
			/>
		</>
	);
};
