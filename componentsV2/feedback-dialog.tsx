'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { PaperclipIcon, X as CloseIcon } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { toast } from 'sonner';

interface FeedbackDialogProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'problem' | 'feature';
  userEmail?: string;
}

export function FeedbackDialog({
  isOpen,
  onClose,
  type,
  userEmail = 'Not logged in'
}: FeedbackDialogProps) {
  const [feedback, setFeedback] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [chatUrl, setChatUrl] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const pathname = usePathname();

  const MAX_FILES = 5;
  const MAX_TOTAL_SIZE = 4.5 * 1024 * 1024; // 4.5MB total
  const MAX_FILE_SIZE = 4.5 * 1024 * 1024; // 4.5MB per file

  const getCurrentTotalSize = () => {
    return attachments.reduce((total, file) => total + file.size, 0);
  };

  useEffect(() => {
    if (isOpen) {
      setFeedback('');
      setAttachments([]);
      setChatUrl(pathname.includes('/chat/') ? window.location.origin + pathname : '');
    }
  }, [isOpen, pathname]);

  const handleSubmit = async () => {
    if (!feedback.trim()) {
      toast.error('Please provide feedback before submitting');
      return;
    }

    try {
      setIsSubmitting(true);

      const formData = new FormData();
      formData.append('type', type);
      formData.append('feedback', feedback);
      formData.append('chatUrl', chatUrl);

      // Append each attachment
      attachments.forEach(file => {
        formData.append('attachments', file);
      });

      const response = await fetch('/api/feedback', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to submit feedback');
      }

      toast.success(
        type === 'problem'
          ? 'Problem report submitted successfully'
          : 'Feature request submitted successfully'
      );

      onClose();
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      const currentTotalSize = getCurrentTotalSize();

      // Filter files by individual size
      const validFiles = newFiles.filter(file => {
        if (file.size > MAX_FILE_SIZE) {
          toast.error(`File ${file.name} exceeds 4.5MB limit`);
          return false;
        }
        return true;
      });

      // Check total size
      const newTotalSize = validFiles.reduce((sum, file) => sum + file.size, currentTotalSize);
      if (newTotalSize > MAX_TOTAL_SIZE) {
        toast.error('Total file size cannot exceed 4.5MB');
        return;
      }

      // Check file count
      if (attachments.length + validFiles.length > MAX_FILES) {
        toast.error(`Maximum ${MAX_FILES} files allowed`);
        return;
      }

      setAttachments(currentFiles => [...currentFiles, ...validFiles]);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  const removeAttachment = (index: number) => {
    setAttachments(currentFiles => currentFiles.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const totalSize = getCurrentTotalSize();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] w-[95vw] min-h-[400px] md:min-h-[500px] p-8">
        <DialogHeader className="text-left px-0">
          <DialogTitle className="text-xl">
            {type === 'problem' ? 'Report a Problem' : 'Request a Feature'}
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-6 flex-grow">
          <div className="grid gap-3">
            <Label htmlFor="feedback" className="text-lg">
              Your {type === 'problem' ? 'Problem Report' : 'Feature Request'}
            </Label>
            <Textarea
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder={type === 'problem' ? 'Describe the problem you encountered...' : 'Describe the feature you would like...'}
              className="min-h-[200px] text-base"
            />
          </div>
          {type === 'problem' && (
            <div className="grid gap-2">
              <Label htmlFor="chatLink" className="text-lg">Chat Link</Label>
              <Input
                id="chatLink"
                value={chatUrl}
                onChange={(e) => setChatUrl(e.target.value)}
                placeholder="Enter chat URL if applicable"
                className="text-base"
              />
            </div>
          )}
          <div className="grid gap-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="attachments" className="text-lg">Attachments</Label>
            </div>
            <div className="flex flex-col gap-3">
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById('file-upload')?.click()}
                  disabled={attachments.length >= MAX_FILES || totalSize >= MAX_TOTAL_SIZE}
                >
                  <PaperclipIcon className="h-4 w-4 mr-2" />
                  Add Files
                </Button>
                <input
                  id="file-upload"
                  type="file"
                  multiple
                  accept="image/*,.pdf,.doc,.docx"
                  className="hidden"
                  onChange={handleFileChange}
                />
              </div>
              {attachments.length > 0 && (
                <div className="flex flex-col gap-2">
                  {attachments.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 rounded-md bg-muted w-full overflow-hidden"
                    >
                      <div className="flex items-center gap-2 min-w-0 flex-1 mr-2 overflow-hidden">
                        <div className="min-w-0 flex-1 overflow-hidden">
                          <span
                            className="block text-sm w-full leading-tight"
                            title={file.name}
                            style={{
                              wordBreak: 'break-all',
                              overflowWrap: 'break-word',
                              hyphens: 'auto'
                            }}
                          >
                            {file.name}
                          </span>
                        </div>
                        <span className="text-xs text-muted-foreground whitespace-nowrap flex-shrink-0">
                          ({formatFileSize(file.size)})
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttachment(index)}
                        className="h-6 w-6 p-0 flex-shrink-0"
                      >
                        <CloseIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex justify-end gap-2 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="px-6"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="px-6"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Sending...' : 'Send Feedback'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
