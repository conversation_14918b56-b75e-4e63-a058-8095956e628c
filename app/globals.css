@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  --text-input-color-light: #fefcff;
  --text-input-color-dark: #2e2a36;
  --title-color: 0, 4, 53;
  /* #000435 in RGB format */
}

/* Scrollbar Hide Utility */
.scrollbar-hide {
  -ms-overflow-style: none !important;
  /* IE and Edge */
  scrollbar-width: none !important;
  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari and Opera */
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-border-pulse {
    animation: border-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-shimmer {
    position: relative;
    overflow: hidden;
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .text-shimmer {
    position: relative;
    background-image: linear-gradient(
      90deg,
      hsl(var(--foreground) / 0.8) 0%,
      hsl(var(--foreground)) 20%,
      hsl(var(--foreground) / 0.8) 40%
    );
    color: transparent;
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: text-shimmer 2.5s linear infinite;
  }

  .animate-dots {
    display: inline-flex;
    align-items: center;
  }

  .animate-dots span {
    animation: dots-fade 1.4s infinite ease-in-out both;
  }

  .animate-dots span:nth-child(1) {
    animation-delay: -0.32s;
  }

  .animate-dots span:nth-child(2) {
    animation-delay: -0.16s;
  }

  @keyframes shimmer {
    0% {
      background-position: -100% 0;
      opacity: 0.8;
    }

    50% {
      opacity: 1;
    }

    100% {
      background-position: 100% 0;
      opacity: 0.8;
    }
  }

  @keyframes dots-fade {
    0%,
    80%,
    100% {
      opacity: 0.4;
      transform: scale(0.8);
    }

    40% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  @keyframes pulse-subtle {
    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.8;
    }
  }

  @keyframes border-pulse {
    0%,
    100% {
      border-color: hsl(var(--primary));
    }

    50% {
      border-color: hsl(var(--primary) / 0.5);
    }
  }

  @keyframes bounce {
    0%,
    100% {
      transform: translateY(0);
    }

    50% {
      transform: translateY(-5px);
    }
  }

  .scale-102 {
    transform: scale(1.02);
  }

  @keyframes text-shimmer {
    0% {
      background-position: 0% center;
    }

    100% {
      background-position: 200% center;
    }
  }

  .animate-spin-slow {
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .animate-flow-slow {
    animation: flow-slow 18s linear infinite;
  }

  .animate-flow-pause {
    animation-play-state: paused;
  }

  @keyframes flow-slow {
    0% {
      transform: translateY(-50%);
    }

    100% {
      transform: translateY(0);
    }
  }

  .chat-background-light {
    background-color: var(--chat-background-color);
    background-attachment: fixed;
  }

  .chat-background-dark {
    background-color: var(--chat-background-color);
    background-attachment: fixed;
  }

  .assistant-message-light {
    /* background-color: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 8px; */
    /* padding: 1.25rem; */
    position: relative;
    /* box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 5px 15px rgba(0, 0, 0, 0.08),
      0 0 1px rgba(0, 0, 0, 0.2); */
    /* transition: transform 0.3s ease-out, box-shadow 0.3s ease-out; */
    overflow: visible;
    z-index: 1;
  }

  .assistant-message-dark {
    /* background-color: var(--assistant-message-bg);
    border: 1px solid var(--header-border-dark);
    border-radius: 8px; */
    /* padding: 1.25rem; */
    position: relative;
    /* box-shadow: 0 4px 12px var(--header-shadow-dark),
      0 8px 24px rgba(0, 0, 0, 0.2), 0 0 1px var(--header-border-dark);
    transition: transform 0.3s ease-out, box-shadow 0.3s ease-out; */
    overflow: visible;
    z-index: 1;
  }

  .user-message-light {
    background-color: rgb(var(--base-navy));
    color: white;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  .user-message-dark {
    background-color: rgb(var(--base-navy));
    color: black;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15),
      0 1px 2px rgba(255, 255, 255, 0.03);
    position: relative;
    transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  /* Hover effect with enhanced shadow and subtle animation */
  /* .assistant-message-light:hover,
  .assistant-message-dark:hover {
    transform: translateY(-2px) scale(1.005);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 10px 25px rgba(0, 0, 0, 0.07);
    z-index: 2;
    Ensure it appears above content below
  } */

  /* .dark .assistant-message-light:hover,
  .dark .assistant-message-dark:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 12px 30px rgba(0, 0, 0, 0.25);
    z-index: 2;
    Ensure it appears above content below
  } */

  /* User message hover effects */
  /* .user-message-light:hover,
  .user-message-dark:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(var(--base-navy), 0.3),
      0 3px 6px rgba(0, 0, 0, 0.1);
  }

  .dark .user-message-light:hover,
  .dark .user-message-dark:hover {
    box-shadow: 0 6px 12px rgba(var(--base-navy), 0.5),
      0 3px 6px rgba(0, 0, 0, 0.3);
  } */

  /* Remove the inner glow effect on user messages as it's not needed with the new background */
  .user-message-light::after,
  .dark .user-message-light::after {
    content: none;
  }

  .textarea-animate {
    transition: transform 0.2s ease-in-out;
  }

  .textarea-animate:focus-within {
    transform: scale(1.01);
  }

  .action-section-background {
    background-color: var(--chat-background-color);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 1rem;
    transition: all 0.2s ease-in-out;
  }

  .dark .action-section-background {
    background-color: var(--chat-background-color);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .action-section-background:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .dark .action-section-background:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}

@layer base {
  :root {
    /* --background: 255 255% 100%; */
    --background: 260 30% 98%;
    --foreground: 251 14% 11%;
    /* --card: 252 24% 96%; */
    --card: 0 0% 100%;
    --card-foreground: 251 14% 11%;
    --popover: 252 24% 96%;
    --popover-foreground: 251 14% 11%;
    /* --primary: 220, 35%, 31%; */
    --primary: 261, 41%, 28%;
    --primary-foreground: 252 24% 96%;
    --secondary: 252 24% 96%;
    --secondary-foreground: 251 14% 11%;
    --muted: 252 24% 96%;
    --muted-foreground: 251 14% 45%;
    --accent: 252 24% 96%;
    --accent-foreground: 251 14% 11%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 252 24% 96%;
    --border: 260 24% 91%;
    --input: 260 24% 91%;
    --ring: 251 14% 11%;
    --chart-1: 260 24% 91%;
    --chart-2: 260 33% 82%;
    --chart-3: 260 24% 91%;
    --chart-4: 260 33% 82%;
    --chart-5: 260 24% 91%;
    --radius: 0.5rem;
    --sidebar-background: 252 24% 96%;
    --sidebar-foreground: 251 14% 35%;
    --sidebar-primary: #ffffff;
    --sidebar-primary-foreground: 252 24% 96%;
    --sidebar-accent: #fcfcfc;
    --sidebar-accent-foreground: 251 14% 11%;
    --sidebar-border: 260 24% 91%;
    --sidebar-ring: #f4f4f5;
    --base-navy: 51, 69, 105;
    /* #334569  Using mild navy for now */
    --chat-background-color: #ffffff;
    --chat-dot-color: rgba(0, 0, 0, 0.15);
    --assistant-message-bg: #fefcff;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 252 24% 96%;
    --card: 0 0% 7%;
    --card-foreground: 252 24% 96%;
    --popover: 0 0% 5%;
    --popover-foreground: 252 24% 96%;
    --primary: 248 10% 19%;
    --primary-foreground: 252 24% 96%;
    --secondary: 251 14% 28%;
    --secondary-foreground: 252 24% 96%;
    --muted: 251 14% 28%;
    --muted-foreground: 251 14% 65%;
    --accent: 251 14% 28%;
    --accent-foreground: 252 24% 96%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 252 24% 96%;
    --border: 251 14% 28%;
    --input: 251 14% 28%;
    --ring: 252 24% 96%;
    --chart-1: 251 14% 35%;
    --chart-2: 251 14% 28%;
    --chart-3: 248 10% 19%;
    --chart-4: 251 14% 43%;
    --chart-5: 251 14% 35%;
    --sidebar-background: 0 0% 0%;
    --sidebar-foreground: 252 24% 96%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 252 24% 96%;
    --sidebar-accent: 0 0% 7%;
    --sidebar-accent-foreground: 252 24% 96%;
    --sidebar-border: 251 14% 28%;
    --sidebar-ring: 251 14% 35%;
    --base-navy: 234, 238, 245;
    /* #d8d0e4 */
    --chat-background-color: #000000;
    --assistant-message-bg: #1a1a1a;
    --header-bg-dark: rgba(0, 0, 0, 0.5);
    --header-gradient-dark: rgba(10, 10, 10, 0.7);
    --header-gradient-dark-end: rgba(0, 0, 0, 0.5);
    --header-border-dark: rgba(255, 255, 255, 0.03);
    --header-shadow-dark: rgba(0, 0, 0, 0.5);
    --header-highlight-dark: rgba(255, 255, 255, 0.05);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  @font-face {
    font-family: "geist";
    font-style: normal;
    font-weight: 100 900;
    src: url(/fonts/geist.woff2) format("woff2");
  }

  @font-face {
    font-family: "geist-mono";
    font-style: normal;
    font-weight: 100 900;
    src: url(/fonts/geist-mono.woff2) format("woff2");
  }
}

.skeleton {
  * {
    pointer-events: none !important;
  }

  *[class^="text-"] {
    color: transparent;
    @apply rounded-md bg-foreground/20 select-none animate-pulse;
  }

  .skeleton-bg {
    @apply bg-foreground/10;
  }

  .skeleton-div {
    @apply bg-foreground/20 animate-pulse;
  }
}

.ProseMirror {
  outline: none;
}

.cm-editor,
.cm-gutters {
  @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
  @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
  @apply bg-transparent !important;
}

.cm-activeLine {
  @apply rounded-r-sm !important;
}

.cm-lineNumbers {
  @apply min-w-7;
}

.cm-foldGutter {
  @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
  @apply rounded-l-sm !important;
}

.suggestion-highlight {
  @apply bg-blue-200 hover:bg-blue-300 dark:hover:bg-blue-400/50 dark:text-blue-50 dark:bg-blue-500/40;
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  border: 2px solid transparent;
  background-clip: padding-box;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  @apply bg-transparent;
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: theme("colors.gray.300") theme("colors.gray.100");
}

.dark .custom-scrollbar {
  scrollbar-color: theme("colors.gray.600") theme("colors.gray.800");
}

/* Paper with Curved Folded Corner Style */
.paper-folded-container {
  position: relative;
  background: var(--paper-bg-color, #ffffff);
  border-radius: 8px;
  overflow: hidden;
  border: none !important;
  box-shadow: 0 3px 8px 0px rgba(0, 0, 0, 0.08);
}

.dark .paper-folded-container {
  --paper-bg-color: rgba(40, 38, 44, 0.8);
  box-shadow: 0 3px 8px 0px rgba(0, 0, 0, 0.25);
}

.paper-folded-container:hover {
  box-shadow: 0 4px 12px 0px rgba(0, 0, 0, 0.12);
}

.dark .paper-folded-container:hover {
  box-shadow: 0 4px 12px 0px rgba(0, 0, 0, 0.35);
}

/* Remove all the fold-related pseudo-elements */
.paper-folded-container::after,
.dark .paper-folded-container::after,
.paper-folded-container:hover::after,
.dark .paper-folded-container:hover::after,
.paper-folded-container::before {
  content: none;
}

/* div[data-radix-popper-content-wrapper] {
  z-index: 1001 !important;
  left: 100px !important;
} */

/* Tooltip custom styling */
[data-radix-popper-content-wrapper] [role="tooltip"] {
  background-color: #4d4d4d !important;
  color: white !important;
  z-index: 9999 !important;
  max-width: 200px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.dark [data-radix-popper-content-wrapper] [role="tooltip"] {
  background-color: #333333 !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* A4 paper effect */
.assistant-message-light::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.08) 0%,
    rgba(0, 0, 0, 0.04) 40%,
    rgba(0, 0, 0, 0.01) 80%,
    transparent 100%
  );
  z-index: 0;
}

.dark .assistant-message-light::before {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.07) 0%,
    rgba(0, 0, 0, 0.1) 60%,
    transparent 100%
  );
}

/* Subtle lined paper effect */
.assistant-message-light .markdown-wrapper {
  position: relative;
}

.assistant-message-light .markdown-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.02) 1px,
    transparent 1px
  );
  background-size: 100% 22px;
  opacity: 0.3;
  z-index: -1;
}

.dark .assistant-message-light .markdown-wrapper::before,
.assistant-message-dark .markdown-wrapper::before {
  background-image: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.03) 1px,
    transparent 1px
  );
  opacity: 0.15;
}

.dark .user-message-light,
.user-message-dark {
  background-color: #433d52;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  position: relative;
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Paper fold effect in top right corner */
.assistant-message-light::before,
.assistant-message-dark::before {
  content: none;
}

.dark .assistant-message-light::before,
.dark .assistant-message-dark::before {
  content: none;
}

/* Subtle lined paper effect */
.assistant-message-light::after,
.assistant-message-dark::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.02) 1px,
    transparent 1px
  );
  background-size: 100% 21px;
  opacity: 0.4;
  z-index: -1;
  border-radius: inherit;
}

.dark .assistant-message-light::after,
.dark .assistant-message-dark::after {
  background-image: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.03) 1px,
    transparent 1px
  );
  opacity: 0.25;
}

/* Add custom shadow hover effect for purple buttons */
.navy-button {
  background-color: rgb(var(--base-navy));
  color: white;
  transition: all 0.2s ease;
}

.navy-button:hover {
  background-color: rgb(var(--base-navy)) !important;
  box-shadow: 0 4px 8px rgba(var(--base-navy), 0.3);
  transform: translateY(-1px);
}

.dark .navy-button {
  color: black;
  background-color: rgb(var(--base-navy));
}

.dark .navy-button:hover {
  background-color: rgb(var(--base-navy)) !important;
  box-shadow: 0 4px 8px rgba(var(--base-navy), 0.4);
  transform: translateY(-1px);
}

.actionsBtns {
  animation-name: fadeIn;
  animation-duration: 2s;
  animation-timing-function: ease;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0px);
  }
}

.SearchModal {
  top: 100px !important;
  transform: translate(-50%, 0px) !important;
  padding: 12px;
}

/* Enhanced button class for special buttons like Research Mode and Enhanced Doc Reader */
.enhanced-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 1rem 0 0.75rem; /* Adjust left/right padding */
  height: 2.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  border: 1px solid;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  padding-right: 3.25rem; /* Increase right padding to make more room for indicator */
}

/* Light mode - default state (OFF) */
.enhanced-button {
  background-color: white;
  color: rgb(var(--base-navy));
  border-color: rgb(209, 213, 219);
}

/* Light mode - active state (ON) */
.enhanced-button[aria-pressed="true"] {
  background-color: rgb(var(--base-navy));
  color: white;
  border-color: rgb(var(--base-navy));
  font-weight: 600;
}

/* Add indicator for OFF state in light mode */
.enhanced-button:not([aria-pressed="true"])::after {
  content: "OFF";
  position: absolute;
  right: 0.75rem;
  font-size: 0.65rem;
  font-weight: bold;
  color: #9ca3af;
  background-color: #f3f4f6;
  padding: 0.15rem 0.4rem;
  border-radius: 0.25rem;
  margin-left: 0.75rem; /* Add margin to separate from text */
}

/* Dark mode - default state (OFF) */
.dark .enhanced-button {
  background-color: rgba(39, 39, 42, 0.3);
  color: rgb(212, 212, 216);
  border-color: rgba(63, 63, 70, 0.5);
}

/* Dark mode - active state (ON) */
.dark .enhanced-button[aria-pressed="true"] {
  background-color: rgb(var(--base-navy));
  color: black;
  border-color: rgb(var(--base-navy));
  font-weight: 600;
}

/* Add indicator for OFF state in dark mode */
.dark .enhanced-button:not([aria-pressed="true"])::after {
  content: "OFF";
  position: absolute;
  right: 0.75rem;
  font-size: 0.65rem;
  font-weight: bold;
  color: #71717a;
  background-color: #27272a;
  padding: 0.15rem 0.4rem;
  border-radius: 0.25rem;
  margin-left: 0.75rem; /* Add margin to separate from text */
}

/* Add indicator for ON state in both light and dark modes */
.enhanced-button[aria-pressed="true"]::after {
  content: "ON";
  position: absolute;
  right: 0.75rem;
  font-size: 0.65rem;
  font-weight: bold;
  color: rgb(var(--base-navy));
  background-color: white;
  padding: 0.15rem 0.4rem;
  border-radius: 0.25rem;
  margin-left: 0.75rem; /* Add margin to separate from text */
}

/* Ensure dark mode ON indicator has proper contrast */
.dark .enhanced-button[aria-pressed="true"]::after {
  color: #fff;
  background-color: #8b5cf6;
}

/* Glassmorphic effect */
/* .glassmorphic-header {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background-clip: padding-box;
} */

/* Sidebar history item hover and active styles for light theme */
/* [data-sidebar="menu-button"]:hover,
[data-sidebar="menu-button"][data-active="true"] {
  background-color: rgba(0, 0, 0, 0.05) !important;
} */

/* Dark mode sidebar item hover/active styles */
/* .dark [data-sidebar="menu-button"]:hover,
.dark [data-sidebar="menu-button"][data-active="true"] {
  background-color: rgba(255, 255, 255, 0.08) !important;
} */

/* Higher z-index for Guide, Request a Feature, and Report a Problem buttons */
.fixed.bottom-4.right-4.z-50.flex.gap-2 {
  z-index: 9999;
}

/* Ensure the gradient overlay in chat container is properly themed */
.dark .absolute.inset-x-0.top-0.h-16.z-0.bg-gradient-to-b {
  --tw-gradient-from: var(--chat-background-color)
    var(--tw-gradient-from-position);
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.dark body {
  background-color: var(--chat-background-color);
}

/* Hide browser's password reveal icon */
input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear,
input[type="password"]::-webkit-contacts-auto-fill-button,
input[type="password"]::-webkit-credentials-auto-fill-button {
  display: none !important;
}

/* For Webkit browsers (Chrome, Safari) */
input[type="password"]::-webkit-inner-spin-button,
input[type="password"]::-webkit-outer-spin-button,
input[type="password"]::-webkit-search-cancel-button,
input[type="password"]::-webkit-search-decoration,
input[type="password"]::-webkit-search-results-button,
input[type="password"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
  margin: 0;
}

/* Glassmorphic buttons container for chat input */
.glassmorphic-buttons {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.dark .glassmorphic-buttons {
  background: rgba(0, 0, 0, 0);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Custom toast styling */
.document-loaded-toast {
  width: 100%;
  min-width: 300px;
  padding: 8px 4px;
}

.document-loaded-toast-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  align-items: center;
  text-align: center;
}

.document-loaded-toast-message {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 2px;
}

.document-loaded-toast-button {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  background-color: rgb(var(--base-navy));
  color: white;
  transition: all 0.2s ease;
}

.document-loaded-toast-button:hover {
  background-color: rgb(var(--base-navy)) !important;
  box-shadow: 0 4px 8px rgba(var(--base-navy), 0.3);
  transform: translateY(-1px);
}

.dark .document-loaded-toast-button {
  color: black;
  background-color: rgb(var(--base-navy));
}

.dark .document-loaded-toast-button:hover {
  background-color: rgb(var(--base-navy)) !important;
  box-shadow: 0 4px 8px rgba(var(--base-navy), 0.4);
  transform: translateY(-1px);
}

/* Ensure buttons are still visible on hover */
.glassmorphic-buttons:hover {
  background: rgba(255, 255, 255, 0.85);
}

.dark .glassmorphic-buttons:hover {
  background: rgba(0, 0, 0, 0);
}

.to-accent\/80 {
  --tw-gradient-to: hsl(260 70% 35% / 0.8) var(--tw-gradient-to-position);
}

.from-primary {
  --tw-gradient-from: hsl(260 70% 20%) var(--tw-gradient-from-position);
  --tw-gradient-stops: hsl(260 70% 20%), hsl(260 70% 35% / 0.8);
}

.bg-gradient-to-r {
  background-image: linear-gradient(
    to right,
    hsl(260 70% 20%),
    hsl(260 70% 35% / 0.8)
  );
}

.bg-unlock-iqidis-core {
  background-image: linear-gradient(to right, #1a1f2c, #394151, #6d7a8c);
}

.hideScrollBar::-webkit-scrollbar {
  display: none !important;
  /* Hides the scrollbar */
}

.hideScrollBar {
  -ms-overflow-style: none !important;
  /* Hides scrollbar in IE and Edge */
  scrollbar-width: none !important;
  /* Hides scrollbar in Firefox */
}

.privateIconDropdown:focus > *,
.privateIconDropdown:hover > * {
  color: #fff;
}

.privateIconDropdown:focus .privateDesc,
.privateIconDropdown:hover .privateDesc {
  color: #fff;
}

.animate-scroll-left {
  animation: scroll-left linear infinite;
  will-change: transform;
  animation-duration: 120s;
}

/* Add scroll animations for testimonials */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

/* Add a utility class for title styling */
.title-gradient {
  background-image: linear-gradient(
    to right,
    rgb(var(--title-color)),
    rgb(var(--title-color) / 0.8)
  );
  background-clip: text;
  color: transparent;
}

.homeBtnTextHide {
  margin-left: 5px;
}

.proText {
  display: none;
}

.bg-mesh {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23142159' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.searchBarLibrary {
  width: 220px;
}

.file-upload-modal .ant-upload-wrapper .ant-upload-list .ant-upload-list-item {
  height: auto !important;
}

.file-upload-modal
  .ant-upload-wrapper
  .ant-upload-list
  .ant-upload-list-item
  .ant-upload-list-item-name {
  white-space: normal !important;
  width: 100% !important;
}

html.dark .anticon.anticon-close.ant-modal-close-icon svg,
html.dark .ant-upload-icon .anticon,
html.dark .ant-upload-list-item-name {
  color: #fff !important;
}

html.dark .ant-upload-list.ant-upload-list-text {
  background-color: hsl(0 0 7%);
}

html.dark .ant-modal .ant-modal-content {
  background-color: hsl(0 0 7%) !important;
}

html.dark
  .ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover {
  background: hsl(0 0 7%) !important;
}

html.dark .ant-modal-header {
  background-color: hsl(0 0 7%);
}

html.dark .anticon.anticon-loading.anticon-spin svg,
html.dark .ant-empty-description {
  color: #fff !important;
}

html.dark .ant-btn-color-dangerous.ant-btn-variant-solid:disabled {
  color: #fff !important;
}

html.dark .ant-table-cell:hover {
  background: #4b5563 !important;
}

html.dark .ant-select-arrow {
  color: #fff !important;
}

html.dark .anticon.anticon-caret-up.ant-table-column-sorter-up,
html.dark .anticon.anticon-caret-down.ant-table-column-sorter-down {
  color: #ffffff5c;
}

html.dark .ant-table-wrapper .ant-table-column-sorter-down.active,
html.dark .ant-table-wrapper .ant-table-column-sorter-up.active {
  color: #fff !important;
}

html.dark
  .ant-table-wrapper
  .ant-table-thead
  th.ant-table-column-has-sorters:hover::before {
  background-color: #fff !important;
}

.dashLibraryTable .ant-table-container {
  min-width: 992px !important;
}

.dashLibraryTable .ant-table {
  overflow: scroll !important;
}

.fileNameDashLibrary .ant-upload-list-item-name {
  white-space: normal !important;
  word-break: break-word !important;
}

.fileNameDashLibrary .ant-upload-list-item.ant-upload-list-item-undefined,
.fileNameDashLibrary .ant-upload-list-item.ant-upload-list-item-uploading,
.fileNameDashLibrary .ant-upload-list-item.ant-upload-list-item-done {
  height: 100% !important;
}

.library-selector-modal .ant-modal-header {
  padding: 15px 24px !important;
}

/* html.dark .library-selector-modal .ant-modal-header {
  background-color: #1f1f1f !important;
} */

.library-selector-modal .ant-modal-content {
  padding: 0px 0px 24px 0px !important;
}

.library-selector-modal .DragImageBox {
  padding: 0px 24px !important;
}

.ArrowLeftContainer svg {
  width: 2rem !important;
}

html.dark .UploadFolderSelect .ant-select-selector {
  background: hsl(0 0 7%) !important;
  color: #fff !important;
}

html.dark .UploadFolderSelect .ant-select-selector .ant-select-selection-item,
html.dark .LibDocdisBtn {
  color: #fff !important;
}

html.dark .LibDocdisBtn:disabled {
  opacity: 0.7;
}

html.dark .ant-btn-color-dangerous.ant-btn-variant-outlined {
  background-color: #ff4d4f !important;
}

html.dark .ant-modal-mask {
  background-color: rgba(29, 29, 31, 0.8) !important;
}

html.dark .ant-pagination .ant-pagination-disabled .ant-pagination-item-link {
  opacity: 0.5;
}

html.dark .ant-pagination-item-ellipsis {
  color: #fff !important;
}

html.dark .ant-select .ant-select-arrow,
html.dark .ant-select-item-option-content {
  color: #fff !important;
}

html.dark
  .ant-select-item.ant-select-item-option.ant-select-item-option-active.ant-select-item-option-selected,
html.dark
  .ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #000 !important;
  color: #fff !important;
}

html.dark .ant-select-dropdown {
  background-color: #333 !important;
}

.recentFavFileName {
  max-width: 300px !important;
}

.dark .proActTabs[aria-selected="true"] {
  color: white !important;
}

html.dark .darkModedisabledBtn:disabled {
  border-color: rgb(106 106 123) !important;
}

html.dark .darkModedisabledBtn:disabled svg,
html.dark .darkModedisabledBtn:disabled span {
  color: rgb(106 106 123) !important;
}

.chatContainer {
  max-height: calc(100vh - 90px);
}

html.dark .ant-pagination .ant-pagination-item-active {
  background: #3f2a65;
  border-color: #3f2a65;
}

html.dark .ant-pagination .ant-pagination-item-active a {
  color: #fff;
}

html.dark .ant-select-selector {
  border-color: #3f2a65;
  background-color: #000 !important;
  color: #fff;
}

@media screen and (max-width: 1560px) {
  main.playbook-open {
    padding: 0px 10px !important;
  }
}

@media screen and (max-width: 1275px) {
  .headerOneBox {
    max-width: 50% !important;
  }
}

@media screen and (max-width: 1200px) {
  .hide1200 {
    display: none;
  }

  .margin1200 {
    margin-left: 8px !important;
  }

  .ant-upload-wrapper
    .ant-upload-list
    .ant-upload-list-item
    .ant-upload-list-item-actions
    .ant-upload-list-item-action {
    opacity: 1 !important;
  }
}

@media screen and (max-width: 992px) {
  .welcomeContainer {
    height: calc(100vh - 210px);
    overflow: auto;
    width: 100%;
    max-width: 100% !important;
  }

  .dark .enhanced-button[aria-pressed="true"]:hover {
    background-color: #fff;
    color: #000;
  }

  .dark .enhanced-button:hover:not([aria-pressed="true"]) {
    background-color: rgba(39, 39, 42, 0.3);
    color: rgb(212, 212, 216);
    border-color: rgba(63, 63, 70, 0.5);
  }

  .dark .enhanced-button[aria-pressed="true"] {
    background-color: #fff;
    color: #000;
  }

  .assistant-message-light:hover,
  .assistant-message-dark:hover,
  .user-message-light:hover,
  .user-message-dark:hover {
    transform: translateY(0px) scale(1) !important;
    box-shadow: none !important;
  }
}

@media screen and (max-width: 960px) {
  .updatePlanText {
    display: none;
  }

  .proText {
    display: block;
  }

  .mobileHide {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  .headerChat {
    /* height: 116px !important; */
    /* flex-direction: column; */
    gap: 0px;
  }

  .SearchModal {
    top: 20px !important;
  }

  .libraryTabsDFS {
    width: 100%;
  }

  header > div {
    width: 100%;
  }

  /* .chatContainer {
    padding-top: 86px !important;
  } */

  .playbookBtn {
    right: 10px;
    width: fit-content;
    padding: 0px 0px;
    z-index: 40 !important;
  }

  .welcomeContainer {
    height: 70vh;
    overflow: scroll;
    padding-top: 12px;
    width: 100%;
  }

  [data-playbook="drawer"] {
    max-width: 610px !important;
    width: 100%;
  }

  .MobileTag {
    max-width: 95%;
    left: 50% !important;
    right: 12px !important;
    width: 100%;
    transform: translateX(-50%);
    pointer-events: fill;
  }

  .library-selector-modal .ant-modal-header {
    padding: 15px 12px !important;
  }
}

@media screen and (max-width: 640px) {
  main.playbook-open {
    padding: 0px !important;
  }

  [data-playbook="drawer"] {
    width: 100% !important;
  }

  .drawerMobile {
    width: 96%;
    left: 50%;
    transform: translateX(-50%);
  }

  .searchBarLibrary {
    width: 100%;
  }

  .file-upload-modal {
    width: 100% !important;
    margin: 8px auto !important;
  }

  .ant-modal {
    top: 20px !important;
    padding-bottom: 10px !important;
  }

  .ant-modal-wrap {
    overflow: hidden;
  }

  /* .ant-modal .ant-modal-content {
    padding: 15px !important;
  }

  .library-selector-modal .ant-modal-header{

  } */

  /* div[data-radix-popper-content-wrapper] {
  left:0px !important;
} */
}

@media screen and (max-width: 560px) {
  .policyDropdown + div[data-radix-popper-content-wrapper] {
    left: 97% !important;
    top: 15% !important;
  }
}

@media screen and (max-width: 576px) {
  .ant-pagination .ant-pagination-options {
    display: block !important;
  }

  .recentFavFileName {
    max-width: 290px !important;
  }
}

@media screen and (max-width: 420px) {
  .mobileColumn {
    flex-direction: column;
    gap: 20px;
    align-items: start;
  }

  .allFolderOption {
    column-gap: 8px !important;
  }

  .allFolderOption button {
    padding: 0px 10px;
  }
}

@media screen and (max-width: 360px) {
  .homeBtnTextHide {
    display: none;
  }

  .policeTermHeading {
    font-size: 1.4rem;
  }

  .hideInSmall {
    display: none;
  }

  .PadLessSmall {
    padding: 0px 0px 0px 8px;
  }
  .recentFavFileName {
    max-width: 150px !important;
  }
}

@media screen and (max-height: 770px) and (max-width: 640px) {
  .welcomeText {
    padding-top: 140px;
  }
}

@media screen and (max-height: 800px) {
  .signContainer {
    overflow: auto !important;
  }

  .signScoll {
    align-items: start !important;
  }

  .signScoll > article {
    margin-block: 10px !important;
    padding-block: 20px !important;
  }
}

@media screen and (max-height: 678px) {
  .resetContainer {
    align-items: start !important;
  }

  .resetContainer > div {
    margin-block: 10px !important;
    padding-block: 20px !important;
  }
}

@media screen and (max-width: 450px) {
  .signBox {
    padding: 1.5rem 8px !important;
  }

  .signBoxChild {
    padding: 2rem 10px !important;
  }
}

.word-break-auto-phrase,
.word-break-auto-phrase * {
  word-break: auto-phrase;
}

/* Fix playbook button positioning for Safari */
@supports (-webkit-touch-callout: none) {
  .playbookBtn {
    position: fixed;
    top: 72px;
    /* Align with header height */
    z-index: 40;
  }
}

/* Adding Safari-specific viewport height fix */
@supports (-webkit-touch-callout: none) {
  .h-dvh,
  .h-screen {
    height: -webkit-fill-available;
  }

  .min-h-0 {
    min-height: -webkit-fill-available;
  }
}

/* Safari detection and fixes */
.safari-height-fix {
  max-height: calc(100% - 2px);
  height: 100%;
}

@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance: none) {
    .safari-height-fix {
      height: -webkit-fill-available !important;
    }

    .chatContainer {
      max-height: calc(100vh - 72px);
      height: calc(100% - 72px);
    }

    /* Fix playbook positioning in Safari */
    [data-playbook="drawer"] {
      height: 100% !important;
      position: fixed !important;
    }
  }
}
.blur-overlay {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 60px;
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.3s ease;
  z-index: 1;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}
.blur-overlay.hidden {
  opacity: 0;
}

.markdown-content-main-hide {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.userPromptHover:hover .userPromptTime {
  opacity: 1;
}

html.dark .ant-input-affix-wrapper .ant-input-clear-icon {
  color: rgba(255, 255, 255, 0.25);
}

html.dark .ant-input-affix-wrapper .ant-input-clear-icon:hover {
  color: rgba(255, 255, 255, 0.45);
}

@media screen and (max-height: 1200px) and (max-width: 1024px) {
  .smallScreenWelcomeText {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    top: 70px !important;
    width: 100%;
  }

  .welcomeBoxes {
    margin-top: 180px !important;
  }

  .drawerMobile {
    max-height: calc(100vh - 82px) !important;
    height: 88%;
  }
}

/* Model selector button styling - completely remove focus styles */

.markdown-content ul ul {
  list-style-type: circle;
}

.traffic-light-red {
  background-color: #ef4444 !important;
}

.traffic-light-yellow {
  background-color: #eab308 !important;
}

.traffic-light-green {
  background-color: #16a34a !important;
}

/* Fix textarea focus styles */
textarea:focus {
  outline: none !important;
  box-shadow: 0 0 0 1px rgb(var(--base-navy)) !important;
  border-color: rgb(var(--base-navy)) !important;
}

/* Removing any blur overlay from covering the input */
.blur-overlay {
  pointer-events: none;
  z-index: 0 !important; /* Ensure it stays below the input */
}

/* Fix for sidebar width in collapsed icon mode */
.group[data-state="collapsed"][data-collapsible="icon"] > .relative.h-svh {
  width: var(--sidebar-width-icon) !important;
}

/* Ensure the fixed sidebar also maintains correct width */
.group[data-state="collapsed"][data-collapsible="icon"]
  .fixed[data-sidebar="sidebar"] {
  width: var(--sidebar-width-icon) !important;
}
/* Markdown Table Styling - Clean & Modern */
.markdown-content .reportTable {
  position: relative;
  width: 100%;
  overflow-x: auto;
  margin: 1.5rem 0;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  contain: layout;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.markdown-content .reportTable table {
  border-collapse: collapse;
  width: 100%;
  min-width: 100%;
  font-size: 0.9375rem;
}

.markdown-content .reportTable table th {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f9fafb;
  color: #111827;
  font-weight: 600;
  text-align: left;
  padding: 0.875rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.markdown-content .reportTable table td {
  padding: 0.875rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  color: #374151;
  min-width: 120px;
}

/* Simple hover effect */
.markdown-content .reportTable table tr:hover td {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Last row styling */
.markdown-content .reportTable table tr:last-child td {
  border-bottom: none;
}

/* Clean scrollbar */
.markdown-content .reportTable::-webkit-scrollbar {
  height: 6px;
}

.markdown-content .reportTable::-webkit-scrollbar-track {
  background: transparent;
}

.markdown-content .reportTable::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

.markdown-content .reportTable::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* Dark mode styling */
html.dark .markdown-content .reportTable {
  background-color: #1f2937;
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

html.dark .markdown-content .reportTable table th {
  background-color: #111827;
  color: #f3f4f6;
  border-color: rgba(255, 255, 255, 0.1);
}

html.dark .markdown-content .reportTable table td {
  border-color: rgba(255, 255, 255, 0.05);
  color: #d1d5db;
}

html.dark .markdown-content .reportTable table tr:hover td {
  background-color: rgba(255, 255, 255, 0.03);
}

html.dark .markdown-content .reportTable::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

html.dark .markdown-content .reportTable::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}
