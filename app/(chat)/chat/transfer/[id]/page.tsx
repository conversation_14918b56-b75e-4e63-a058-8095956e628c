import { redirect } from 'next/navigation';
import { auth } from '@/app/(auth)/auth';
import { getChatById } from '@/lib/db/queries';
import { ChatTransferUI } from '@/componentsV2/chat-transfer-ui';

export default async function ChatTransferPage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const session = await auth();
  if (!session?.user) {
    return redirect('/login');
  }

  const { id } = await params;

  const chat = await getChatById({ id });
  if (!chat || chat.userId !== session.user.id) {
    return redirect('/');
  }

  return <ChatTransferUI chatId={id} userId={session.user.id} />;
}
