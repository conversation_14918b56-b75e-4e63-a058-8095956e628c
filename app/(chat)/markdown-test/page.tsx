import { cookies } from "next/headers";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";
import { auth } from "@/app/(auth)/auth";
import { Chat } from "@/componentsV2/chat";
import { ChatSkeleton } from "@/componentsV2/chat-skeleton";
import { DataStreamHandler } from "@/componentsV2/data-stream-handler";
import { DEFAULT_MODEL_NAME, models } from "@/lib/ai/models";
import { PLANS } from "@/lib/constants";
import {
	getChatById,
	getMessagesWithAttachmentsByChatId,
	saveChat,
} from "@/lib/db/queries";
import { convertToUIMessages } from "@/lib/utils";

// Hardcoded chat ID for markdown testing - consistent across environments
const HARDCODED_CHAT_ID = "a1b2c3d4-e5f6-7890-abcd-ef1234567890";

export default async function Page() {
	const session = await auth();

	// Admin-only access
	if (!session || !session.user || !session.user.isAdmin) {
		return notFound();
	}

	const userEmail = session?.user?.email || "Not logged in";

	let isSubscriptionAboutToExpire = false;
	let matchedDays: number | undefined;
	let label = UPGRADE_PLAN_MESSAGES.END_OF_PLAN_MESSAGE;
	const isFreeTier = session?.user?.subscriptionTier === PLANS.FREE_PLAN;

	if (session?.user?.id && !isFreeTier) {
		const endDate = session?.user?.subscriptionEndDate;

		if (endDate) {
			const subscriptionEndDate = format(endDate, "yyyy-MM-dd HH:mm:ss");
			const todayFormatted = format(new Date(), "yyyy-MM-dd");

			let daysBeforeEndList = [7, 4, 0];
			if (session.user.subscriptionTier === PLANS.PAID_PLAN_TRIAL) {
				daysBeforeEndList = [4, 0];
			}
			for (const days of daysBeforeEndList) {
				const dateBeforeEnd = subDays(subscriptionEndDate, days);
				if (format(dateBeforeEnd, "yyyy-MM-dd") === todayFormatted) {
					matchedDays = days;
					break;
				}
			}
			if (matchedDays !== undefined) {
				isSubscriptionAboutToExpire = true;

				switch (matchedDays) {
					case 7:
						label = UPGRADE_PLAN_MESSAGES.SEVEN_DAY_REMAIN_MESSAGE;
						break;
					case 4:
						label = UPGRADE_PLAN_MESSAGES.FOUR_DAY_REMAIN_MESAGE;
						break;
					case 0:
						label = UPGRADE_PLAN_MESSAGES.ZERO_DAY_REMAIN_MESSAGE;
						break;
					default:
						UPGRADE_PLAN_MESSAGES.END_OF_PLAN_MESSAGE;
				}
			}
		}
	}

	return (
		<>
			<Script
				src="https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js"
				strategy="beforeInteractive"
			/>
			<UserProvider user={session?.user}>
				<PendoProvider user={session?.user}>
					<SentryProvider user={session?.user}>
						<GlobalPlaybookProvider>
							<div className="flex flex-col">
								{isSubscriptionAboutToExpire && (
									<StickyHeaderNotification label={label} />
								)}
								<SidebarProvider defaultOpen={false}>
									<div className="flex flex-1 overflow-hidden w-screen h-screen">
										<AppSidebar user={session?.user as any} />
										<SidebarInset className="w-full flex-1 h-full">
											<TooltipProvider>
												<main className="flex flex-1 overflow-hidden dark:chat-background-dark chat-background-light">
													<Suspense fallback={<ChatSkeleton />}>
														<ChatContent id={HARDCODED_CHAT_ID} />
													</Suspense>
													<div className="fixed bottom-4 right-4 !z-50 flex gap-2 transition-opacity duration-300">
														<HelpButton />
														<RequestFeatureButton userEmail={userEmail} />
														<ReportProblemButton userEmail={userEmail} />
													</div>
												</main>
											</TooltipProvider>
										</SidebarInset>
									</div>
								</SidebarProvider>
							</div>
						</GlobalPlaybookProvider>
					</SentryProvider>
				</PendoProvider>
			</UserProvider>
		</>
	);
}

async function ChatContent({ id }: { id: string }) {
	console.log("ChatContent called with hardcoded ID:", id);

	const session = await auth();

	if (!session || !session.user) {
		return redirect(`/login?returnUrl=/admin/markdown-test`);
	}

	let chat = await getChatById({ id });

	// If chat doesn't exist, create it with the same hardcoded ID
	if (!chat) {
		console.log("Creating markdown test chat with hardcoded ID...");
		try {
			await saveChat({
				id: id, // Use the same hardcoded ID
				userId: session.user.id!,
				title: "Markdown Test Chat",
				visibility: "public", // Set default visibility to public
			});
			chat = await getChatById({ id });
			console.log("Created chat successfully");
		} catch (error) {
			console.error("Failed to create chat:", error);
			return notFound();
		}
	}

	if (!chat) {
		return notFound();
	}

	const messagesFromDb = await getMessagesWithAttachmentsByChatId({
		id,
	});

	const cookieStore = await cookies();
	const modelIdFromCookie = cookieStore.get("model-id")?.value;
	const selectedModelId =
		chat.modelId ||
		models.find((model) => model.id === modelIdFromCookie)?.id ||
		DEFAULT_MODEL_NAME;
	const isFreeActiveSubscription =
		session?.user?.subscriptionTier === PLANS.FREE_PLAN;

	return (
		<>
			<Chat
				id={chat.id}
				initialMessages={convertToUIMessages(messagesFromDb)}
				selectedModelId={selectedModelId}
				selectedVisibilityType={chat.visibility}
				isReadonly={true} // Always readonly for testing
				title={chat.title}
				isFreeActiveSubscription={isFreeActiveSubscription}
			/>
			<DataStreamHandler id={id} />
		</>
	);
}
