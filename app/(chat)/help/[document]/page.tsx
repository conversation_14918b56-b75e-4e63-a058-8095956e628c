'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { notFound } from 'next/navigation';
import { VALID_DOCUMENTS } from '@/lib/types';
import { LoaderIcon } from '@/components/icons';

export default function HelpPage() {
  const params = useParams();
  const documentId = params.document as string;
  const [isLoading, setIsLoading] = useState(true);

  // Find the document from our valid documents list
  const document = VALID_DOCUMENTS.find(doc => doc.id === documentId);

  useEffect(() => {
    const validateDocument = async () => {
      // If document doesn't exist in our valid documents, return 404
      if (!document) {
        return notFound();
      }

      try {
        const res = await fetch(`/api/help?document=${documentId}`);
        if (!res.ok) {
          return notFound();
        }
      } catch (error) {
        return notFound();
      } finally {
        setIsLoading(false);
      }
    };

    validateDocument();
  }, [documentId, document]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin">
          <LoaderIcon />
        </div>
      </div>
    );
  }

  // If document not found in VALID_DOCUMENTS, return 404
  if (!document) {
    return notFound();
  }

  return (
    <div className="flex flex-col items-center min-h-screen p-4">
      <h1 className="text-2xl font-bold mb-4">{document.displayName}</h1>
      <div className="w-full max-w-4xl bg-white rounded-lg shadow-lg dark:bg-zinc-900">
        <iframe
          src={`/api/help?document=${documentId}`}
          className="w-full h-[calc(100vh-8rem)] rounded-lg"
          title={document.displayName}
        />
      </div>
    </div>
  );
}
