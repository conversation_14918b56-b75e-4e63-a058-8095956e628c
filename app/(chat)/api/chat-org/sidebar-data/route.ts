import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { getSidebarData } from "@/lib/db/chatOrgQueries";
import { Logger } from "@/lib/utils/Logger";

/**
 * GET handler for retrieving all data needed for the sidebar
 * This includes:
 * - Chats with their tags
 * - Folders with their associated chats
 * - Pinned chats
 */
export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const sidebarData = await getSidebarData(session.user.id);
    return NextResponse.json(sidebarData);
  } catch (error) {
    Logger.error("Error fetching sidebar data:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}