import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import {
  getFoldersByUserId,
  createFolder,
  updateFolder,
  deleteFolder,
} from "@/lib/db/chatOrgQueries";
import { Logger } from "@/lib/utils/Logger";

/**
 * GET handler for retrieving all folders for the current user
 */
export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const folders = await getFoldersByUserId(session.user.id);
    return NextResponse.json(folders);
  } catch (error) {
    Logger.error("Error fetching chat folders:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * POST handler for creating a new folder
 * Body: { name: string }
 */
export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { name } = await request.json();
    if (!name) {
      return new Response("Name is required", { status: 400 });
    }

    try {
      const folder = await createFolder({
        name,
        userId: session.user.id,
      });

      return NextResponse.json(folder, { status: 201 });
    } catch (error: any) {
      // Check for specific error types
      if (error.message === "DUPLICATE_FOLDER_NAME") {
        return new Response("A folder with this name already exists. Please choose a different name", {
          status: 409 // Conflict status code
        });
      }

      // Re-throw other errors to be caught by the outer catch block
      throw error;
    }
  } catch (error) {
    Logger.error("Error creating chat folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * PATCH handler for updating a folder's name
 * Body: { oldName: string, newName: string }
 */
export async function PATCH(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { oldName, newName } = await request.json();
    if (!oldName || !newName) {
      return new Response("Old name and new name are required", { status: 400 });
    }

    try {
      const folder = await updateFolder({
        oldName,
        newName,
        userId: session.user.id,
      });

      if (!folder) {
        return new Response("Folder not found or you don't have permission to update it", { status: 404 });
      }

      return NextResponse.json(folder);
    } catch (error: any) {
      if (error.message === "DUPLICATE_FOLDER_NAME") {
        return new Response("A folder with this name already exists. Please choose a different name", {
          status: 409 // Conflict status code
        });
      }
      throw error;
    }
  } catch (error) {
    Logger.error("Error updating chat folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * DELETE handler for deleting a folder
 * Query params:
 * - name: string
 */
export async function DELETE(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const name = searchParams.get("name");
    
    if (!name) {
      return new Response("Folder name is required", { status: 400 });
    }

    const folder = await deleteFolder({
      name,
      userId: session.user.id,
    });

    if (!folder) {
      return new Response("Folder not found or you don't have permission to delete it", { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error deleting chat folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
