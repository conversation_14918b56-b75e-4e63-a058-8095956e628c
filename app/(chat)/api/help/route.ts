import { NextResponse } from "next/server";
import { Logger } from "@/lib/utils/Logger";
import { VALID_DOCUMENTS } from "@/lib/types";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const documentType = searchParams.get('document');

    // Validate document type and get the document
    const document = VALID_DOCUMENTS.find(doc => doc.id === documentType);
    if (!document) {
      return new NextResponse('Document not found', { status: 404 });
    }

    const response = await fetch(document.url);
    if (!response.ok) {
      return new NextResponse('Document not found', { status: 404 });
    }

    // Stream the PDF content
    const pdfContent = await response.blob();

    // Return the PDF with appropriate headers
    return new NextResponse(pdfContent, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${document.id}.pdf"`,
      },
    });

  } catch (error) {
    Logger.error('Error serving help document:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
