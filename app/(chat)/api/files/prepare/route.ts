// app/api/document/prepare/route.ts
import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { Logger } from "@/lib/utils/Logger";
import { generateUUID, ALLOWED_PROMPT_FILE_TYPES } from "@/lib/utils";
import {
  S3Client,
  CreateMultipartUploadCommand,
  PutObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import CryptoJS from "crypto-js";


// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

// Function to get file extension from MIME type
function getExtensionFromMimeType(mimeType: string): string {
  const mimeToExt: Record<string, string> = {
    "application/pdf": ".pdf",
    "image/jpeg": ".jpg",
    "image/png": ".png",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      ".docx",
    "application/msword": ".doc",
  };

  return mimeToExt[mimeType] || "";
}

// Function to generate S3 presigned URL
async function generateS3PresignedUrl(
  documentId: string,
  contentType: string
): Promise<string> {
  try {
    const bucketName =
      process.env.AWS_S3_BUCKET_NAME_PREFIX + "-" + process.env.VERCEL_ENV ||
      "";

    Logger.debug("S3 bucket name:", bucketName);

    const extension = getExtensionFromMimeType(contentType);
    const key = `uploads/${documentId}${extension}`; // S3 object key with extension

    Logger.debug("Creating S3 command with params:", {
      documentId,
      contentType,
      key,
      bucketName,
    });

    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      ContentType: contentType,
    });

    // Generate presigned URL valid for 15 minutes
    Logger.debug("Generating presigned URL");
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 900 });

    Logger.debug("Successfully generated presigned URL");
    return signedUrl;
  } catch (error) {
    Logger.error("Error generating S3 presigned URL:", {
      error,
      documentId,
      contentType,
    });
    throw error; // Re-throw to handle in the calling function
  }
}

// Add this function to validate the signature
function validateSignature(signature: string, timestamp: string): boolean {
  // Check if timestamp is recent (within 5 minutes)
  const requestTime = parseInt(timestamp);
  const currentTime = Date.now();
  if (isNaN(requestTime) || currentTime - requestTime > 5 * 60 * 1000) {
    return false;
  }

  // Validate signature
  const signString = `${timestamp}`;
  Logger.info("Validating signature with params:", {
    signString,
    signature,
    secret: process.env.NEXT_PUBLIC_SIGNATURE_SECRET,
  });
  const expectedSignature = CryptoJS.HmacSHA256(
    signString,
    process.env.NEXT_PUBLIC_SIGNATURE_SECRET || ""
  ).toString(CryptoJS.enc.Hex);

  return signature === expectedSignature;
}

export async function POST(request: Request) {
  // Get auth headers
  const signature = request.headers.get("Authorization");
  const timestamp = request.headers.get("x-requested-time");

  // Validate signature if provided
  if (signature && timestamp) {
    if (!validateSignature(signature, timestamp)) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }
  } else {
    // Fall back to session auth
    const session = await auth();
    if (!session?.user || !session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
  }

  try {
    const body = await request.json();
    const { files, channel, partitionId, chatId } = body;

    // Make sure the validation is consistent
    // Validate file types
    for (const file of files) {
      if (!file.mime || !ALLOWED_PROMPT_FILE_TYPES[file.mime as keyof typeof ALLOWED_PROMPT_FILE_TYPES]) {
        Logger.warn("Invalid file type attempted upload", {
          mimeType: file.mime,
          fileName: file.metadata?.name,
        });
        return NextResponse.json(
          { error: "Unsupported file type. Only PDF, DOCX, DOC, and TXT files are allowed." },
          { status: 400 }
        );
      }
    }

    // Process each file to generate signed URLs
    const responses = await Promise.all(
      files.map(async (file: any) => {
        const documentId = generateUUID();
        const multipart = file.multipart === true;

        if (multipart) {
          // Create multipart upload
          const bucketName =
            process.env.AWS_S3_BUCKET_NAME_PREFIX +
              "-" +
              process.env.VERCEL_ENV || "your-bucket-name";
          const extension = getExtensionFromMimeType(file.mime);
          const key = `uploads/${documentId}${extension}`;

          const createCommand = new CreateMultipartUploadCommand({
            Bucket: bucketName,
            Key: key,
            ContentType: file.mime,
          });

          const { UploadId } = await s3Client.send(createCommand);

          // Return multipart upload details
          return {
            documentId,
            uploadId: UploadId,
            multipart: true,
            key,
            bucket: bucketName,
          };
        } else {
          // Standard presigned URL
          const uploadUrl = await generateS3PresignedUrl(documentId, file.mime);

          return {
            url: uploadUrl,
            documentId,
            multipart: false,
          };
        }
      })
    );
    return NextResponse.json({
      data: responses,
    });
  } catch (error) {
    Logger.error("Error in document prepare", error);
    return NextResponse.json(
      { error: "Failed to prepare document upload" },
      { status: 500 }
    );
  }
}
