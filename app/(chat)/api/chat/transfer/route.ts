import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { 
  getChatById, 
  getMessagesWithAttachmentsByChatId,
  saveChat, 
  saveMessages,
  saveMessageDocuments
} from '@/lib/db/queries';
import { generateUUID } from '@/lib/utils';
import { Logger } from '@/lib/utils/Logger';
import { SummaryService } from '@/lib/services/SummaryService';
import { DEFAULT_MODEL_NAME } from '@/lib/ai/models';
import { generateTitleFromUserMessage } from '@/app/(chat)/actions';
import { DEFAULT_CHAT_VISIBILITY } from '@/lib/types';
import { checkChatCreationLimit } from '@/lib/db/chatUsage';

// GET endpoint to fetch chat data and generate summary
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
  }

  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get original chat
    const chat = await getChatById({ id: chatId });
    if (!chat || chat.userId !== session.user.id) {
      return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
    }

    // Get messages with attachments
    const messagesWithAttachments = await getMessagesWithAttachmentsByChatId({
      id: chatId,
    });

    // Extract conversation for summary generation
    const conversationText = messagesWithAttachments
      .map(msg => `${msg.role.toUpperCase()}: ${typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)}`)
      .join('\n\n');
    
    // Generate summary using the SummaryService
    let summary = '';
    try {
      summary = await SummaryService.generateSummary(conversationText);
    } catch (error) {
      Logger.error('Error generating summary', error);
      summary = generatePlaceholderSummary(messagesWithAttachments);
    }

    // Extract unique attachments
    const attachments = messagesWithAttachments.reduce((acc, msg) => {
      if (msg.experimental_attachments && msg.experimental_attachments.length > 0) {
        msg.experimental_attachments.forEach(attachment => {
          if (!acc.some(a => a.id === attachment.id)) {
            acc.push(attachment);
          }
        });
      }
      return acc;
    }, [] as any[]);

    return NextResponse.json({ 
      chat,
      messages: messagesWithAttachments,
      summary,
      attachments
    });
  } catch (error) {
    Logger.error('Error fetching chat data for transfer', error);
    return NextResponse.json({ error: 'Failed to fetch chat data' }, { status: 500 });
  }
}

// POST endpoint to create new chat with summary
export async function POST(request: Request) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { summary, originalChatId, selectedAttachments, modelId } = await request.json();
    Logger.info('POST: Transfer request received', { summary, originalChatId, selectedAttachments, modelId });

    if (!summary || !originalChatId) {
      return NextResponse.json({ error: 'Summary and original chat ID are required' }, { status: 400 });
    }

    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    const isPremiumUser = (session.user.isAdmin || session.user.subscriptionTier !== 'free')
    // Check if user can create a new chat
    if (!isPremiumUser) {
      const canCreateChat = await checkChatCreationLimit(userId);
      if (!canCreateChat) {
        return NextResponse.json({ 
          error: 'CHAT_LIMIT_EXCEEDED',
          message: 'You\'ve reached your chat limit. Please delete some chats before creating a new one.'
        }, { status: 429 });
      }
    }

    // Create new chat
    const newChatId = generateUUID();
    // Limit summary length to 400 characters for title generation
    const truncatedSummary = summary.substring(0, 400);
    const title = await generateTitleFromUserMessage({
      message: {role: 'user', content: truncatedSummary}
    });
    await saveChat({
      id: newChatId,
      userId,
      title,
      modelId: modelId || DEFAULT_MODEL_NAME,
      visibility: DEFAULT_CHAT_VISIBILITY,
    });

    // Create first message with summary
    const userMessageId = generateUUID();
    await saveMessages({
      messages: [
        {
          id: userMessageId,
          chatId: newChatId,
          role: 'user',
          content: summary,
          createdAt: new Date(),
          metadata: {}, // Add metadata field
        }
      ]
    });

    // Save document attachments if any
    if (selectedAttachments && selectedAttachments.length > 0) {
      await saveMessageDocuments({
        messageId: userMessageId,
        documentIds: selectedAttachments.map((a: any) => a.id)
      });
    }

    return NextResponse.json({ 
      success: true,
      chatId: newChatId
    });
  } catch (error) {
    Logger.error('Error creating new chat from summary', error);
    return NextResponse.json({ error: 'Failed to create new chat' }, { status: 500 });
  }
}

// Fallback function to generate a simple summary if the model fails
function generatePlaceholderSummary(messages: any[]) {
  const userMessages = messages.filter(m => m.role === 'user');
  const assistantMessages = messages.filter(m => m.role === 'assistant');
  
  return `# Chat Summary

## Key Topics Discussed:
${userMessages.slice(0, 5).map((msg, i) => {
  const content = typeof msg.content === 'string' 
    ? msg.content.substring(0, 100) 
    : 'Complex message ' + (i+1);
  return `- ${content}${content.length >= 100 ? '...' : ''}`;
}).join('\n')}

This is a continuation of our previous conversation. I've summarized the key points above.`;
}
