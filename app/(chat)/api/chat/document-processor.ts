import { Logger } from "@/lib/utils/Logger";
import { countTokens } from "@/lib/services/llmService";
import {
  saveMessageDocuments,
  createSourceDocumentIfNotExists,
  sourceDocumentExists,
  storePendingDocumentAssociation,
} from "@/lib/db/queries";
import { generateUUID, expandMimeType } from "@/lib/utils";
import { extractTextFromPdf } from "@/lib/services/pdf";
import {
  extractTextFromPdfBuffer,
  extractTextFromDocx,
  extractTextFromWordURL,
  extractTextFromDoc,
   extractTextFromTxt 
} from "@/lib/services/document";

import {
  processPdfViaFargate,
  waitForFargateJobCompletion,
} from "@/lib/utils/fargate";
import { measureExecutionTime } from "@/lib/utils/performance";
import { genSignature } from "@/lib/utils/auth";
import type { Attachment } from "ai";
import { ServerErrorEvent, ServerSuccessEvent } from "@/lib/analytics/event-types";
import { serializeError } from "@/lib/utils/errorUtils";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";
import { db } from "@/lib/db";
import { sourceDocuments } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { retryOperation } from "@/lib/utils/retry";
import { logServerEvent } from "@/lib/analytics/events-server";
import { RequestTimer } from "@/lib/utils/requestTimer";

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

/* -----------------------------------------------------------------------------
 * Document Processing
 * ---------------------------------------------------------------------------*/

/**
 * Formats file text for inclusion in system prompt
 */
function formatFilesText(files: [string, string, boolean][]): string {
  if (files.length === 0) return "";

  return files
    .map(([filename, content, isFromLastMessage]) => {
      const sourceLabel = isFromLastMessage ? "ATTACHMENT_FROM_CURRENT_QUERY" : "ATTACHMENT_FROM_PREVIOUS_INTERACTION";
      return `--- File (${sourceLabel}): ${filename}\n${content}\n File End ---\n\n`;
    })
    .join("\n\n");
}

const convertS3UrlToBuffer = async (url: string): Promise<Buffer> => {
  let buffer;

  // Check if this is a Vercel Blob URL
  if (
    url.includes("vercel-storage.com") ||
    url.includes("blob.vercel-storage.com")
  ) {
    // Handle Vercel Blob URL with a direct fetch
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch from Vercel Blob: ${response.status}`);
    }
    buffer = Buffer.from(await response.arrayBuffer());
    return buffer;
  }

  // Original S3 handling
  const urlObj = new URL(url);
  const bucketName = urlObj.hostname.split(".")[0];
  const key = urlObj.pathname.substring(1); // Remove leading slash
 Logger.info("Fetching from S3",{"key": key, "bucketName": bucketName, "url": urlObj});
  // Fetching object directly from S3
  const command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
  });

  const s3Response = await s3Client.send(command);

  // Convert stream to buffer
  const chunks = [];
  for await (const chunk of s3Response.Body as Readable) {
    chunks.push(chunk);
  }
  buffer = Buffer.concat(chunks);
  return buffer;
};

/**
 * Extracts text from file attachments
 */
async function extractTextFromAttachment(
  url: string,
  contentType: string,
  documentId: string
): Promise<string> {
  // First check if we already have extracted text in the database
  const existingDoc = await db
    .select()
    .from(sourceDocuments)
    .where(eq(sourceDocuments.id, documentId))
    .limit(1)
    .execute()
    .then((rows) => rows[0]);

  if (existingDoc?.extractedText) {
    Logger.info("Using pre-extracted text from database", { documentId });
    return existingDoc.extractedText;
  }

  // Fall back to extraction if not in database
  const buffer = await convertS3UrlToBuffer(url);

  // Expand normalized MIME type back to full form for processing
  const expandedContentType = expandMimeType(contentType);

  // Handle different file types
  if (expandedContentType === "application/pdf" || url.toLowerCase().endsWith(".pdf")) {
    return await extractTextFromPdfBuffer(buffer);
  } else if (
    expandedContentType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
    url.toLowerCase().endsWith(".docx")
  ) {
    return await extractTextFromDocx(buffer);
  } else if (
    expandedContentType === "application/msword" ||
    url.toLowerCase().endsWith(".doc")
  ) {
    // Use the correct function for .doc files
    try {
      Logger.info("Extracting text from DOC file", { documentId });
      
      return await extractTextFromDoc(buffer);
    } catch (error) {
      Logger.error("Error extracting text from DOC file", { error, documentId });
      return "";
    }
  } else if (
    expandedContentType === "text/plain" ||
    url.toLowerCase().endsWith(".txt")
  ) {
    try {
      Logger.info("Extracting text from TXT file", { documentId });
      return await extractTextFromTxt(buffer);
    } catch (error) {
      Logger.error("Error extracting text from TXT file", { error, documentId });
      return "";
    }
  }
  return "";
}

/**
 * Processes document attachments
 */
async function processDocumentAttachments(
  attachments: any[],
  userId: string,
  chatId: string,
  ragEnabled: boolean,
  isFromLastMessage: boolean = false
): Promise<[string, string, boolean][]> {
  const filesText: [string, string, boolean][] = [];
  // Track processed document IDs to avoid duplicates
  const processedDocumentIds = new Set<string>();

  for (const attachment of attachments) {
    const { url, contentType, name, size } = attachment;
    const documentId = attachment.document_id;

    // Skip if we've already processed this document in this request
    if (processedDocumentIds.has(documentId)) {
      Logger.info("Skipping already processed document", { documentId, name });
      continue;
    }

    processedDocumentIds.add(documentId);
    Logger.debug("Processing attachment", {
      url,
      contentType,
      name,
      documentId,
    });

    try {
      if (!ragEnabled) {
        try {
          const fileText = await extractTextFromAttachment(
            url,
            contentType,
            documentId
          );
          if (fileText) filesText.push([name, fileText, isFromLastMessage]);
        } catch (extractError) {
          Logger.error("Failed to extract text from document, continuing with other documents", {
            error: extractError,
            documentId,
            name
          });
          // Add a placeholder for failed extraction
          filesText.push([name, `[Could not extract text from ${name}]`, isFromLastMessage]);
        }
      }

      await handleSourceDocument(
        documentId,
        name,
        url,
        size,
        userId,
        chatId,
        ragEnabled,
        isFromLastMessage
      ).catch(error => {
        Logger.error("Error in handleSourceDocument, continuing with other documents", {
          error,
          documentId,
          name
        });
      });
    } catch (attachmentError) {
      Logger.error("Error processing attachment, continuing with other documents", {
        error: attachmentError,
        documentId,
        name
      });
    }
  }

  return filesText;
}

/**
 * Handles source document creation and processing
 */
async function handleSourceDocument(
  documentId: string,
  filename: string,
  url: string,
  fileSize: number,
  userId: string,
  chatId: string,
  ragEnabled: boolean,
  isFromLastMessage: boolean = false
): Promise<void> {
  Logger.debug("RAG enabled:", ragEnabled);
  Logger.debug("Checking if source document exists", { documentId });

  try {
    if (await sourceDocumentExists({ documentId })) {
      Logger.info("Source document already exists", { documentId });
    } else {
      Logger.info("Creating source document", { documentId });
      await createSourceDocumentIfNotExists({
        documentId,
        filename,
        url,
        fileSize,
        userId,
        chatId,
      });
    }

    // Only process via Fargate if RAG is enabled AND it's from the last message
    if (ragEnabled && isFromLastMessage) {
      try {
        const jobResponse = await processPdfViaFargate(documentId, url);
        const jobId = jobResponse.job_id;
        const jobStatus = await waitForFargateJobCompletion(jobId, documentId);

        if (jobStatus) {
          // Job completed successfully
          const result = {
            page_count: jobStatus.total_pages,
            job_id: jobId,
          };
          Logger.info("PDF processing result", { documentId, result });
        }
      } catch (fargateError) {
        Logger.error("PDF processing failed but continuing", {
          documentId,
          error: fargateError,
        });
        // Continue execution even if processing fails
      }
    }
  } catch (error) {
    Logger.error("Error in handleSourceDocument", { error, documentId });
    // Don't rethrow the error to prevent it from affecting other documents
  }
}

/**
 * Prepares messages by removing file attachments
 */
async function prepareMessagesWithoutFiles(
  fixedMessages: any[]
): Promise<any[]> {
  return await Promise.all(
    fixedMessages.map(async (msg: any) => {
      if (msg.experimental_attachments) {
        const { experimental_attachments, ...rest } = msg;
        return rest;
      }
      return msg;
    })
  );
}

/**
 * Main function to process documents and extract text
 */
async function processDocuments(
  fixedMessages: any[],
  userId: string,
  chatId: string,
  ragEnabled: boolean,
  requestId?: string
): Promise<{ filesText: [string, string, boolean][]; filesTextToInclude: string, analyticsTags: { [key: string]: any } }> {
  const filesText: [string, string, boolean][] = [];
  let filesTextToInclude = "";
  let analyticsTags = {"attachment_count": 0};
  
  RequestTimer.markEvent( "doc-processing-started", requestId);
  try {
    // Find the last user message
    const userMessages = fixedMessages.filter((msg) => msg.role === "user");
    const lastUserMessageIndex = userMessages.length - 1;
    let currentUserMessageIndex = 0;

    // Process messages to handle attachments
    for (const msg of fixedMessages) {
      if (msg.role === "user") {
        const isLastUserMessage =
          currentUserMessageIndex === lastUserMessageIndex;
        currentUserMessageIndex++;

        if (
          msg.experimental_attachments &&
          msg.experimental_attachments.length > 0
        ) {
          try {
            analyticsTags["attachment_count"] += msg.experimental_attachments.length;
            // Update document_id in attachments before processing
            msg.experimental_attachments.forEach((attachment: any) => {
              if (!attachment.document_id || !(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(attachment.document_id)) ) {
                attachment.document_id = generateUUID();
                Logger.warn("Generated document_id for attachment", attachment.url);
              }
            });

            const extractedTexts = await processDocumentAttachments(
              msg.experimental_attachments,
              userId,
              chatId,
              ragEnabled,
              isLastUserMessage // Pass true only for the last user message
            );
            filesText.push(...extractedTexts);
          } catch (attachmentError) {
            Logger.error("Error processing attachments but continuing", {
              error: attachmentError,
              messageIndex: currentUserMessageIndex
            });
          }
        }
      }
    }

    if (filesText.length > 0) {
      filesTextToInclude = formatFilesText(filesText);
      Logger.info("Files text token count:", countTokens(filesTextToInclude));
    }
  } catch (error) {
    Logger.error("Error in processDocuments but returning partial results", { error });
    // Even if there's an error, we'll return whatever we've processed so far
  }
  
  RequestTimer.markEvent( "doc-processing-finished", requestId);
  return { filesText, filesTextToInclude, analyticsTags };
}

/**
 * Safely associates documents with a message, with retry logic
 */
async function safelyAssociateDocuments(
  messageId: string,
  documentIds: string[]
): Promise<boolean> {
  if (!documentIds.length) return true;

  try {
    return await retryOperation(async () => {
      // Use direct database query instead of fetch for server-side code
      await saveMessageDocuments({ messageId, documentIds });
      return true;
    }, 3);
  } catch (error) {
    Logger.error("Failed to associate documents with message after retries", {
      error,
      messageId,
      documentCount: documentIds.length,
    });
    return false;
  }
}

const deleteFile = async (attachment: Attachment, chatId: string) => {
  try {
    Logger.info("Client: Removing file from upload queue", {
      name: attachment.name,
      documentId: attachment.document_id,
    });

    // If the file has a document_id and has been confirmed (not pending),
    // we need to delete it from the server
    if (attachment.document_id && !attachment.pendingConfirm) {
      Logger.info("Client: Deleting confirmed file from server", {
        url: attachment.url,
        documentId: attachment.document_id,
      });

      const response = await fetch(`/api/files/upload`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: attachment.url,
          documentId: attachment.document_id,
          chatId,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => "Unknown error");
        Logger.error("Client: Delete request failed", {
          status: response.status,
          statusText: response.statusText,
          errorText,
        });
        throw new Error(`Failed to delete file: ${response.statusText}`);
      }

      const result = await response.json();
      Logger.info("Client: File deletion from server successful", result);
    } else {
      // For pending files, we just remove them from the UI
      Logger.info("Client: Removing pending file from UI only", {
        name: attachment.name,
        pendingConfirm: attachment.pendingConfirm,
      });
    }
    return true;
  } catch (error) {
    Logger.error("Client: Delete error", error);
    logServerEvent(ServerErrorEvent.FILE_DELETE_FAILED, {
      error: serializeError(error),
      chatId,
    });
    return false;
  }
};

// Export the document processor utility object
export const documentProcessor = {
  extractTextFromAttachment,
  processDocuments,
  prepareMessagesWithoutFiles,
  saveMessageDocuments: async (messageId: string, documentIds: string[]) => {
    if (!documentIds.length) return true;

    try {
      // Try to save message documents directly
      await saveMessageDocuments({ messageId, documentIds });
      return true;
    } catch (error) {
      Logger.error("Failed to associate documents with message", {
        error,
        messageId,
        documentCount: documentIds.length,
      });

      // Store for server-side retry
      await storePendingDocumentAssociation({ messageId, documentIds });
      return false;
    }
  },
  deleteFile,
  convertS3UrlToBuffer,
};
