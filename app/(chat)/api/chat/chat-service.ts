import { convertToCoreMessages } from "ai";
import { NextRequest } from "next/server";
import { LRUCache } from "lru-cache";
import { auth } from "@/app/(auth)/auth";
import { Logger } from "@/lib/utils/Logger";
import { DEFAULT_CHAT_VISIBILITY } from "@/lib/types";
import { User as NextUser } from "next-auth";
import { logServerEvent } from "@/lib/analytics/events-server";
import { ServerErrorEvent, ServerSuccessEvent } from "@/lib/analytics/event-types";
import { serializeError } from "@/lib/utils/errorUtils";
import { RequestTimer } from "@/lib/utils/requestTimer";

import {
  getChatById,
  saveChat,
  saveMessages,
  updateChatTitleById,
  deleteChatById,
  updateChatTimestamp,
  getUserPreferences,
} from "@/lib/db/queries";

import { createStreamRecordInDb, updateLatestStreamForChat } from "@/lib/db/streamQueries";
import { StreamStatus } from "@/lib/db/schema";

import { checkUsageLimits } from "@/lib/db/chatUsage";

import { generateTitleFromUserMessage, trackUserFirstMessage } from "../../actions";

import {
  convertMessagesForLLMs,
} from "@/lib/services/llmService";

import {
  generateUUID,
  getMostRecentUserMessage,
  getDocumentIdsFromLastUserMessage,
  fixMessageAttachments,
} from "@/lib/utils";

import { documentProcessor } from "./document-processor";
import { measureExecutionTime } from "@/lib/utils/performance";
import { RAGProcessor } from "@/lib/services/ragV1";
import { SearchService } from "@/lib/services/rag/SearchService";
import { QueryRewriteService } from "@/lib/services/rag/QueryRewriteService";
import { PerplexityService } from "@/lib/services/rag/PerplexityService";
import { ChainOfThoughtService } from "@/lib/services/chainOfThoughtService";
import { Model } from "@/lib/ai/models";
import type { Message } from "@/lib/db/schema";

/* -----------------------------------------------------------------------------
 * Types
 * ---------------------------------------------------------------------------*/

export type ChatRequestData = {
  includeInternet?: boolean;
  ragEnabled?: boolean;
  useLegacyModel?: boolean;
  [key: string]: any;
};

export type ChatRequest = {
  id: string;
  messages: any[];
  modelId: string;
  data?: ChatRequestData;
  useLegacyModel?: boolean;
  [key: string]: any;
};

export type ChatMessagingOptions = {
  chatId: string;
  messages: any[];
  userId: string;
  model: Model;
  provider: any;
  includeInternet: boolean;
  ragEnabled: boolean;
  session: { user: NextUser };
};

// Adding a preferences cache with a reasonable TTL
const preferencesCache = new LRUCache<string, any>({
  max: 100,
  ttl: 1000 * 60 * 15, // Cache preferences for 15 minutes
});

/* -----------------------------------------------------------------------------
 * Main Functions
 * ---------------------------------------------------------------------------*/

/**
 * Main handler for chat messaging
 */
export async function handleChatMessaging({
  chatId,
  messages,
  userId,
  model,
  provider,
  includeInternet = false,
  ragEnabled = false,
  session,
  data, // Add data parameter
  requestId,
}: {
  chatId: string;
  messages: any[];
  userId: string;
  model: Model;
  provider: any;
  includeInternet?: boolean;
  ragEnabled?: boolean;
  session: { user: NextUser };
  data?: ChatRequestData; // Add data type
  requestId?: string;
}): Promise<Response> {
  // Get chat first
  const chat = await measureExecutionTime(getChatById, "getChatById", {
    id: chatId,
  });

  // Check usage limits
  const usageLimitResult = await measureExecutionTime(
    checkUsageLimits,
    "checkUsageLimits",
    session.user,
    chat,
    chatId
  );

  // If usage is restricted, return error response
  if (usageLimitResult.restricted) {
    logServerEvent(ServerSuccessEvent.CHAT_REQUEST_SERVED, {
      chatId,
      userId,
      userEmail : session.user.email,
      modelId: model.id,
      // includeInternet,
      // ragEnabled,
      // useLegacyModel:data?.useLegacyModel ?? false,
      messageId:data?.messageId,
      responseType: "USAGE_LIMIT_EXCEEDED",
      requestId,
    });
    updateLatestStreamForChat(chatId, StreamStatus.INTERRUPTED);
    return new Response(
      JSON.stringify({
        error: {
          type: "USAGE_LIMIT_EXCEEDED",
          uiMessage: usageLimitResult.errorMessage,
        },
      }),
      {
        status: 429,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  // Process message attachments
  const fixedMessages = fixMessageAttachments(messages);

  // Convert messages to core messages for processing
  const coreMessages = convertToCoreMessages(fixedMessages);
  const userMessage = getMostRecentUserMessage(coreMessages);

  // Extract command text from user message
  let commandText = "";
  if (typeof userMessage?.content === "string") {
    commandText = userMessage.content.trim();
  } else if (Array.isArray(userMessage?.content)) {
    commandText = userMessage.content
      .filter((c: any) => c.type === "text")
      .map((c: any) => c.text)
      .join(" ")
      .trim();
  }

  if (!chat) {
    if (userMessage) {
      trackUserFirstMessage(userId, userMessage)
    }
    const title = await measureExecutionTime(
      generateTitleFromUserMessage,
      "generateTitleFromUserMessage",
      { message: userMessage }
    );

    await measureExecutionTime(saveChat, "saveChat", {
      id: chatId,
      userId,
      title,
      modelId: model.id,
      visibility: DEFAULT_CHAT_VISIBILITY,
    });
  } else {
    // Update the timestamp for existing chat - only once per request
    Logger.info("Updating timestamp for existing chat", { id: chatId });
    await updateChatTimestamp({ id: chatId });
  }
  // Generate IDs for messages or use client-provided ID if available
  const userMessageId = data?.messageId || generateUUID();
  const finalMessageId = generateUUID();
  
  // Process messages without file attachments
  const messagesWithoutFiles =
    await documentProcessor.prepareMessagesWithoutFiles(fixedMessages);
  const coreMessagesWithoutFiles = convertToCoreMessages(messagesWithoutFiles);

  Logger.info("Using message IDs:", {
    userMessageId,
    finalMessageId,
    isClientProvided: !!data?.messageId,
  });

  // Save user message
  await measureExecutionTime(saveMessages, "saveMessages", {
    messages: [
      {
        ...userMessage,
        id: userMessageId,
        createdAt: new Date(),
        chatId,
        experimental_attachments: userMessage?.experimental_attachments ?? [],
      },
    ],
  });
  
  createStreamRecordInDb({
    chatId,
    messageId: userMessageId, // user's msg id as opposed to response msg id
    userId,
  });
  
  RequestTimer.markEvent( "doc-processing-started", requestId);
  // Process file attachments and extract text
  const { filesText, filesTextToInclude, analyticsTags } =
    await documentProcessor.processDocuments(
      fixedMessages,
      userId,
      chatId,
      ragEnabled,
      requestId
    );
  // Log document processing completion event
  logServerEvent(ServerSuccessEvent.DOCUMENT_PROCESSING_COMPLETED, {
    chatId,
    userId,
    userEmail: session.user.email,
    modelId: model.id,
    messageId: data?.messageId,
    requestId,
    elapsedTimeSeconds: RequestTimer.getElapsedTimeBetween("doc-processing-finished", "doc-processing-started", requestId),
    ...analyticsTags,
  });

  // Convert messages for LLM processing
  const llmMessages = await convertMessagesForLLMs(coreMessagesWithoutFiles);

  // Save entry to 'messageDocument' table for document persistence
  // Only do this once per request
  const documentIds = getDocumentIdsFromLastUserMessage(fixedMessages);
  if (documentIds.length > 0) {
    // Process document associations in the background to avoid blocking the response
    (async () => {
      try {
        await documentProcessor.saveMessageDocuments(
          userMessageId,
          documentIds
        );
        Logger.info("Successfully associated documents with message", {
          messageId: userMessageId,
          documentCount: documentIds.length,
        });
      } catch (error) {
        Logger.error("Failed to associate documents with message", {
          error,
          messageId: userMessageId,
          documentCount: documentIds.length,
        });
        // Server-side code can't use localStorage, so we'll log the error
        // and implement a server-side retry mechanism

        // Store pending associations in a database table or use a queue system
        // For now, we'll just log the error and continue
      }
    })();
  }

  const hasFiles = fixedMessages.some(
    (msg) =>
      msg.experimental_attachments && msg.experimental_attachments.length > 0
  );
  Logger.debug("Has files:", hasFiles);

  // Handle RAG and image processing
  let processedImages: Array<{
    mime_type: string;
    data: string;
    url: string;
  }> = [];

  if (ragEnabled) {
    try {
      RequestTimer.markEvent( "rag-started", requestId);
      Logger.debug("Include internet:", includeInternet);
      Logger.debug("RAG enabled search:", ragEnabled);
      const relevantChunks = await SearchService.searchEmbeddings(
        commandText,
        chatId
      );
      Logger.debug("Found relevant chunks:", relevantChunks.length);

      const imageChunks =
        relevantChunks.length > 0
          ? RAGProcessor.extractImageChunks(relevantChunks)
          : [];
      Logger.debug("Found image chunks:", imageChunks.length);

      processedImages =
        imageChunks.length > 0
          ? await RAGProcessor.processImageChunks(imageChunks)
          : [];
      Logger.debug("Processed images:", processedImages.length);
      RequestTimer.markEvent( "rag-finished", requestId);
      // Log RAG processing completion event
      logServerEvent(ServerSuccessEvent.RAG_PROCESSING_COMPLETED, {
        chatId,
        userId,
        userEmail: session.user.email,
        modelId: model.id,
        messageId: data?.messageId,
        requestId,
        elapsedTimeSeconds: RequestTimer.getElapsedTimeBetween("rag-finished", "rag-started", requestId),
        ...analyticsTags,
      });

    } catch (error) {
      Logger.error("Error in RAG pipeline", error);
      logServerEvent(
        ServerErrorEvent.CHAT_ROUTE_ERROR,
        {
          chatId,
          userId,
          errorMessage: "Error in RAG pipeline",
          error: serializeError(error),
          requestId,
        },
        true
      );
      updateLatestStreamForChat(chatId, StreamStatus.FAILED);
      return new Response("Error processing your query", { status: 500 });
    }
  }

  // Internet search integration
  let perplexityAnswer: {
    mainContent: string;
    citations: string[];
    formattedContent: string;
  } = {
    mainContent: "",
    citations: [],
    formattedContent: "",
  };

  let rewrittenQuery = "";
  let chainOfThoughtsPromise: Promise<string> | null = null;

  if (includeInternet) {
    const chatHistory = llmMessages
      .map((msg) => `${msg.role}: ${msg.content}`)
      .join("\n");

    rewrittenQuery = await QueryRewriteService.rewriteQuery(
      commandText,
      chatHistory,
      processedImages,
      filesTextToInclude
    );
    perplexityAnswer = await PerplexityService.getAnswer(rewrittenQuery);
  } else {
    // Start chain of thoughts generation in parallel
    chainOfThoughtsPromise = measureExecutionTime(
      ChainOfThoughtService.generateChainOfThought,
      "generateChainOfThought",
      commandText
    );
  }

  // Build system prompt
  let preferences;
  const cachedPreferences = preferencesCache.get(userId);

  if (cachedPreferences) {
    preferences = cachedPreferences;
    Logger.debug("Using cached user preferences", { userId });
  } else {
    preferences = await measureExecutionTime(
      getUserPreferences,
      "getUserPreferences",
      userId
    );
    // Cache the preferences for future requests
    preferencesCache.set(userId, preferences);
  }

  // Get provider's system prompt
  const baseSystemPrompt = provider.getSystemPrompt
    ? provider.getSystemPrompt(model, preferences)
    : "";
    
  logServerEvent(ServerSuccessEvent.PROMPT_PREPARATION_COMPLETED, {
    chatId,
    userId,
    userEmail: session.user.email,
    modelId: model.id,
    messageId: data?.messageId,
    requestId,
    elapsedTimeSeconds: RequestTimer.getElapsedTime(requestId),
    ...analyticsTags,
  });
  // Stream response from provider
  return provider.streamResponse(
    llmMessages,
    commandText,
    baseSystemPrompt,
    userMessageId,
    finalMessageId,
    chatId,
    rewrittenQuery,
    chainOfThoughtsPromise,
    perplexityAnswer,
    processedImages,
    filesTextToInclude,
    userId,
    requestId,
    session.user.email,
    userId
      ? async (
          msgId: string,
          chatId: string,
          content: string,
          metadata: any
        ) => {
          // If we have a chain of thoughts promise, resolve it here
          const resolvedChainOfThoughts = chainOfThoughtsPromise
            ? await chainOfThoughtsPromise
            : undefined;

          await saveMessageWithErrorHandling(
            msgId,
            chatId,
            content,
            userId,
            resolvedChainOfThoughts,
            metadata
          );
        }
      : undefined
  );
}

/**
 * PATCH Handler for updating chat title
 */
export async function updateChatTitle(request: NextRequest): Promise<Response> {
  const { id, title } = await request.json();
  if (!id || !title) {
    return new Response("Missing required fields", { status: 400 });
  }

  const session = await auth();
  if (!session?.user?.id) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const chat = await getChatById({ id });
    if (chat.userId !== session.user.id) {
      return new Response("Unauthorized", { status: 401 });
    }
    await updateChatTitleById({ id, title });
    return new Response("Chat renamed", { status: 200 });
  } catch (error) {
    console.error(`Error renaming chat ${id}:`, error);
    return new Response("An error occurred while renaming chat", {
      status: 500,
    });
  }
}

/**
 * DELETE Handler for chat deletion
 */
export async function deleteChat(request: NextRequest): Promise<Response> {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");
  if (!id) {
    return new Response("Not Found", { status: 404 });
  }

  const session = await auth();
  if (!session?.user?.id) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const chat = await getChatById({ id });
    if (chat.userId !== session.user.id) {
      return new Response("Unauthorized", { status: 401 });
    }
    await deleteChatById({ id });
    return new Response("Chat deleted", { status: 200 });
  } catch (error) {
    Logger.error(`Error deleting chat ${id}:`, error);
    logServerEvent(
      ServerErrorEvent.CHAT_DELETE_ERROR,
      {
        chatId: id,
        userId: session.user.id,
        errorMessage: "Failed to delete chat",
        error: serializeError(error),
      },
      true
    );
    return new Response("An error occurred while processing your request", {
      status: 500,
    });
  }
}

/**
 * Save message handler with error logging
 */
export async function saveMessageWithErrorHandling(
  finalMessageId: string,
  chatId: string,
  content: string,
  userId: string,
  chainOfThoughts?: string,
  metadata?: any
): Promise<void> {
  try {
    await saveMessages({
      messages: [
        {
          id: finalMessageId,
          chatId,
          role: "assistant",
          content,
          createdAt: new Date(),
          metadata: {
            ...(metadata || {}),
            internetResults: metadata?.internetResults,
            relevantImages: metadata?.relevantImages,
            rewrittenQuery: metadata?.rewrittenQuery,
            chainOfThoughts: chainOfThoughts,
          } as Message["metadata"],
        },
      ],
    });
    // Fire and forget - update stream status to COMPLETED
    updateLatestStreamForChat(chatId, StreamStatus.COMPLETED);
  } catch (error) {
    logServerEvent(ServerErrorEvent.CHAT_ROUTE_ERROR, {
      chatId,
      userId,
      errorMessage: "Failed to save chat",
      error: serializeError(error),
    });
    Logger.error("Failed to save chat:", error);
  }
}
