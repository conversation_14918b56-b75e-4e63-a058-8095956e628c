import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { Logger } from '@/lib/utils/Logger';
import { db } from '@/lib/db/db';
import { stream, StreamStatus, message } from '@/lib/db/schema';
import { eq, desc, and, gt } from 'drizzle-orm';
import { getChatById } from '@/lib/db/queries';

export async function POST(request: NextRequest) {
  try {
    // Get the session
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body with user message details
    const { chatId, userMessageTimestamp } = await request.json();
    
    if (!chatId || !userMessageTimestamp) {
      return NextResponse.json({ error: 'Chat ID and user message timestamp are required' }, { status: 400 });
    }

    // Get the chat to verify ownership
    const chat = await getChatById({ id: chatId });
    if (!chat) {
      return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
    }

    // Verify the user has access to this chat
    if (chat.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Find the most recent stream for this chat
    const [latestStream] = await db
      .select()
      .from(stream)
      .where(eq(stream.chatId, chatId))
      .orderBy(desc(stream.createdAt))
      .limit(1);

    // If no stream found, return no pending streams
    if (!latestStream) {
      return NextResponse.json({ status: 'no_pending_streams' });
    }

    const userMessageDate = new Date(userMessageTimestamp);
    const streamCreatedDate = new Date(latestStream.createdAt);

    // If stream is older than the user message timestamp
    if (streamCreatedDate < userMessageDate) {
      // If stream is in PENDING status and older than 5 minutes, mark as TIMEOUT
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      if (latestStream.status === StreamStatus.PENDING && streamCreatedDate < fiveMinutesAgo) {
        await db
          .update(stream)
          .set({
            status: StreamStatus.TIMEOUT,
            updatedAt: new Date(),
          })
          .where(eq(stream.id, latestStream.id));
      }
      
      return NextResponse.json({ status: 'no_pending_streams' });
    }

    // Stream is found and is newer than user message timestamp
    // Process stream based on status
    if (
      latestStream.status === StreamStatus.FAILED || 
      latestStream.status === StreamStatus.INTERRUPTED ||
      latestStream.status === StreamStatus.TIMEOUT
    ) {
      return NextResponse.json({ status: 'failed' });
    }

    if (
      latestStream.status === StreamStatus.PENDING || 
      latestStream.status === StreamStatus.STREAMING
    ) {
      return NextResponse.json({ status: 'processing' });
    }

    if (latestStream.status === StreamStatus.COMPLETED) {
      // Query for the latest assistant message in the chat
      const [assistantResponse] = await db
        .select()
        .from(message)
        .where(and(
          eq(message.chatId, chatId),
          eq(message.role, 'assistant'),
          gt(message.createdAt, userMessageDate)
        ))
        .orderBy(desc(message.createdAt))
        .limit(1);

      // If assistant message exists AND is newer than the user message timestamp
      if (assistantResponse) {
        return NextResponse.json({
          status: 'completed',
          message: assistantResponse,
        });
      } else {
        // No assistant message found after user message
        return NextResponse.json({ status: 'no_pending_streams' });
      }
    }

    // Fallback for unexpected stream status
    return NextResponse.json({ status: 'no_pending_streams' });
  } catch (error) {
    Logger.error('Error in resume chat API', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// // Keep the GET method for backward compatibility
// export async function GET(request: NextRequest) {
//   try {
//     // Get the session
//     const session = await auth();
//     if (!session || !session.user) {
//       return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//     }

//     // Get the chat ID from the query params
//     const searchParams = request.nextUrl.searchParams;
//     const chatId = searchParams.get('chatId');

//     if (!chatId) {
//       return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
//     }

//     // Get the chat to verify ownership
//     const chat = await getChatById({ id: chatId });
//     if (!chat) {
//       return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
//     }

//     // Verify the user has access to this chat
//     if (chat.userId !== session.user.id) {
//       return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
//     }

//     // Find the most recent stream for this chat
//     const [latestStream] = await db
//       .select()
//       .from(stream)
//       .where(eq(stream.chatId, chatId))
//       .orderBy(desc(stream.createdAt))
//       .limit(1);

//     // If no stream found or stream is already completed, return no pending streams
//     if (!latestStream || latestStream.status === StreamStatus.COMPLETED) {
//       return NextResponse.json({ status: 'no_pending_streams' });
//     }

//     // If stream is pending but older than 5 minutes, mark as failed and return timeout
//     const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
//     if (
//       latestStream.status === StreamStatus.PENDING && 
//       new Date(latestStream.createdAt) < fiveMinutesAgo
//     ) {
//       // Update stream status to FAILED
//       await db
//         .update(stream)
//         .set({
//           status: StreamStatus.FAILED,
//           updatedAt: new Date(),
//         })
//         .where(eq(stream.id, latestStream.id));

//       return NextResponse.json({ status: 'timeout' });
//     }

//     // If stream is still pending and recent, check if we have a message
//     // First, get the most recent user message
//     const [lastUserMessage] = await db
//       .select()
//       .from(message)
//       .where(and(
//         eq(message.chatId, chatId),
//         eq(message.role, 'user')
//       ))
//       .orderBy(desc(message.createdAt))
//       .limit(1);

//     if (!lastUserMessage) {
//       return NextResponse.json({ status: 'processing' });
//     }

//     // Check if there's an assistant message after the last user message
//     const [assistantResponse] = await db
//       .select()
//       .from(message)
//       .where(and(
//         eq(message.chatId, chatId),
//         eq(message.role, 'assistant'),
//       ))
//       .orderBy(desc(message.createdAt))
//       .limit(1);

//     // If we found an assistant message and it's newer than the user message,
//     // return it
//     if (
//       assistantResponse && 
//       new Date(assistantResponse.createdAt) > new Date(lastUserMessage.createdAt)
//     ) {
//       return NextResponse.json({
//         status: 'completed',
//         message: assistantResponse,
//       });
//     }

//     // If we're still here, the stream is still processing
//     return NextResponse.json({ status: 'processing' });
//   } catch (error) {
//     Logger.error('Error in resume chat API', { error });
//     return NextResponse.json(
//       { error: 'Internal server error' },
//       { status: 500 }
//     );
//   }
// }
