import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { Logger } from "@/lib/utils/Logger";
import { CitationValidationService } from "@/lib/services/cite-check/CitationValidationService";
import { SummaryService } from '@/lib/services/SummaryService';
import { updateMessageWithCitationValidation } from "@/lib/db/citationQueries";
import { getChatById, getMessagesWithAttachmentsByChatId } from "@/lib/db/queries";
import { sql } from "drizzle-orm";

export async function POST(request: Request) {
  try {
    // Authenticate the user
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const { messageId, chatId } = await request.json();

    // Validate required parameters
    if (!messageId || !chatId) {
      return NextResponse.json(
        { error: "Message ID and Chat ID are required" },
        { status: 400 }
      );
    }

    // Verify the chat belongs to the user
    const chat = await getChatById({ id: chatId });
    if (!chat || chat.userId !== session.user.id) {
      return NextResponse.json(
        { error: "Chat not found or access denied" },
        { status: 404 }
      );
    }
    Logger.info(`Fetching messages for chat ${chatId} and message ${messageId}`);
    // Get all messages with attachments for this chat
    const allMessages = await getMessagesWithAttachmentsByChatId({
      id: chatId,
    });

    // Find the target message and preceding user message
    let assistantMessage = null;
    let userMessage = null;
    let previousMessages = [];
    
    // Sort messages by creation time to ensure proper order
    const sortedMessages = [...allMessages].sort(
      (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
    
    // Find the target message index
    const targetIndex = sortedMessages.findIndex(msg => msg.id === messageId);
    
    if (targetIndex === -1) {
      return NextResponse.json(
        { error: "Target message not found in chat" },
        { status: 404 }
      );
    }
    
    assistantMessage = sortedMessages[targetIndex];
    
    // Verify it's an assistant message
    if (assistantMessage.role !== "assistant") {
      return NextResponse.json(
        { error: "Target message is not an assistant message" },
        { status: 400 }
      );
    }
    
    // Find the preceding user message
    for (let i = targetIndex - 1; i >= 0; i--) {
      if (sortedMessages[i].role === "user") {
        userMessage = sortedMessages[i];
        break;
      }
    }
    
    if (!userMessage) {
      return NextResponse.json(
        { error: "No preceding user message found" },
        { status: 404 }
      );
    }
    
    // Get all messages before the target message for context
    previousMessages = sortedMessages.slice(0, targetIndex);

    const conversationText = previousMessages
      .map(msg => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        return `${msg.role.toUpperCase()}: ${content}`;
      })
      .join('\n\n');

    Logger.info("Preparing summary with previous messages:");
    // Generate conversation summary
    const conversationSummary = await SummaryService.generateSummary(conversationText);
    Logger.info("Conversation summary generated successfully\n", conversationSummary);
    // Extract internet search results if available
    const internetResults = assistantMessage.metadata?.internetResults;
    
    Logger.info("Calling CitationValidationService ...");
    // Validate citations
    const validationResult = await CitationValidationService.validateCitationsWithPerplexity(
      conversationSummary,
      typeof userMessage.content === 'string' ? userMessage.content : JSON.stringify(userMessage.content),
      typeof assistantMessage.content === 'string' ? assistantMessage.content : JSON.stringify(assistantMessage.content),
      internetResults ? {
        mainContent: internetResults.mainContent,
        citations: internetResults.citations
      } : undefined
    );
    Logger.info("Citation validation completed:", validationResult);

    // Update the message with citation validation results
    await updateMessageWithCitationValidation({
      messageId,
      userId: session.user.id,
      citationValidation: JSON.stringify(validationResult),
    });

    return NextResponse.json({ 
      validationResult,
      success: true 
    });
  } catch (error) {
    Logger.error("Error in citation validation:", error);
    return NextResponse.json(
      { error: "Failed to validate citations" },
      { status: 500 }
    );
  }
}
