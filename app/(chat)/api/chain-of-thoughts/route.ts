import { NextRequest, NextResponse } from "next/server";
import { ChainOfThoughtService } from "@/lib/services/chainOfThoughtService";
import { Logger } from "@/lib/utils/Logger";

export async function POST(req: NextRequest) {
  try {
    Logger.debug("Chain of Thoughts API: Request received");
    
    const { 
      query, 
      chatId, 
      chatHistory = ""
    } = await req.json();

    if (!query) {
      Logger.error("Chain of Thoughts API: Query parameter is missing");
      return NextResponse.json(
        { error: "Query parameter is required" },
        { status: 400 }
      );
    }

    Logger.debug("Chain of Thoughts API: Processing query", { 
      query, 
      chatId,
      chatHistoryLength: chatHistory.length,
    });

    const chainOfThought = await ChainOfThoughtService.generateChainOfThought(
      query,
      chatHistory
    );

    if (!chainOfThought || typeof chainOfThought !== 'string') {
      Logger.error("Chain of Thoughts API: Received invalid chain of thought", { chainOfThought });
      return NextResponse.json(
        { error: "Failed to generate a valid chain of thought" },
        { status: 500 }
      );
    }

    Logger.debug("Chain of Thoughts API: Successfully generated chain of thought", { 
      original: query.substring(0, 100) + "...",
      chainOfThought: chainOfThought.substring(0, 100) + "..."
    });

    return NextResponse.json({ chainOfThought });
  } catch (error) {
    Logger.error("Error in chain of thoughts API:", error);
    return NextResponse.json(
      { error: "Failed to generate chain of thoughts" },
      { status: 500 }
    );
  }
} 