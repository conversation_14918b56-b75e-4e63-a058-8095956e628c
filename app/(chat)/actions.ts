"use server";

import { type CoreUserMessage, generateText } from "ai";
import { cookies } from "next/headers";

import { customModel } from "@/lib/ai";
import {
  deleteMessagesByChatIdAfterTimestamp,
  getMessageById,
  updateChatVisibilityById,
  updateChatModelById,
  userhasChat,
} from "@/lib/db/queries";
import { VisibilityType } from "@/components/visibility-selector";
import { Logger } from "@/lib/utils/Logger";
import { logServerEvent } from "@/lib/analytics/events-server";
import { ChatEvent, ServerErrorEvent } from "@/lib/analytics/event-types";

export async function saveChatModelId({
  chatId,
  modelId,
}: {
  chatId: string;
  modelId: string;
}) {
  await updateChatModelById({ id: chatId, modelId });
}

export async function generateTitleFromUserMessage({
  message,
}: {
  message: {
    role: string;
    content: any;
  };
}) {
  try {
    const { text: title } = await generateText({
      model: customModel("gpt-4o-mini"),
      system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,
      prompt: JSON.stringify(message),
    });

    return title;
  } catch (error) {
    // Fallback to a simple heuristic approach
    Logger.warn("Title generation failed with gpt:", error);    
    // Log server error event
    logServerEvent(ServerErrorEvent.GPT_API_ERROR, {
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      context: "generateTitleFromUserMessage"
    }, false);
    try {
      // Extract text content from message
      let messageText = '';
      if (typeof message.content === 'string') {
        messageText = message.content;
      } else if (Array.isArray(message.content)) {
        messageText = message.content
          .filter(item => item.type === 'text')
          .map(item => item.text)
          .join(' ');
      }
      
      // Simple heuristic: Use first sentence or first N characters
      let title = '';
      
      // Try to get first sentence (up to 80 chars)
      const firstSentenceMatch = messageText.match(/^[^.!?]+[.!?]/);
      if (firstSentenceMatch && firstSentenceMatch[0].length <= 80) {
        title = firstSentenceMatch[0].trim();
      } else {
        // Otherwise take first 60 chars and add ellipsis if needed
        title = messageText.substring(0, 60).trim();
        if (messageText.length > 60) title += '...';
      }
      
      // Fallback if we couldn't extract anything meaningful
      return title || "New Chat";
    } catch (fallbackError) {
      Logger.error("Title generation failed:", fallbackError);
      return "New Chat";
    }
  }
}

export async function deleteTrailingMessages({ id }: { id: string }) {
  const [message] = await getMessageById({ id });

  await deleteMessagesByChatIdAfterTimestamp({
    chatId: message.chatId,
    timestamp: message.createdAt,
  });
}

export async function updateChatVisibility({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: VisibilityType;
}) {
  await updateChatVisibilityById({ chatId, visibility });
}


export async function trackUserFirstMessage(userId: string, message: {
  role: string;
  content: any;
}) {
  try {
    const hasChat = await userhasChat(userId)
    if (!hasChat) {
      let messageText = '';
      if (typeof message.content === 'string') {
        messageText = message.content;
      } else if (Array.isArray(message.content)) {
        messageText = message.content
          .filter(item => item.type === 'text')
          .map(item => item.text)
          .join(' ');
      }
      if (messageText) {
        logServerEvent(ChatEvent.USER_FIRST_MESSAGE, {
          userId,
          role: message.role,
          message: messageText
        });
      }
    }
  } catch (error) {
    Logger.error("Event Tracking Error", error);
  }
}