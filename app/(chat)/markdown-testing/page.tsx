import { cookies } from "next/headers";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";
import { auth } from "@/app/(auth)/auth";
import { Chat } from "@/componentsV2/chat";
import { ChatSkeleton } from "@/componentsV2/chat-skeleton";
import { DataStreamHandler } from "@/componentsV2/data-stream-handler";
import { DEFAULT_MODEL_NAME, models } from "@/lib/ai/models";
import { PLANS } from "@/lib/constants";
import {
	getChatById,
	getMessagesWithAttachmentsByChatId,
	saveChat,
} from "@/lib/db/queries";
import { convertToUIMessages } from "@/lib/utils";

// Hardcoded chat ID for markdown testing - consistent across environments
const HARDCODED_CHAT_ID = "a1b2c3d4-e5f6-7890-abcd-ef1234567890";

export default async function Page() {
	const session = await auth();

	// Admin-only access
	if (!session || !session.user || !session.user.isAdmin) {
		return notFound();
	}

	return (
		<Suspense fallback={<ChatSkeleton />}>
			<ChatContent id={HARDCODED_CHAT_ID} />
		</Suspense>
	);
}

async function ChatContent({ id }: { id: string }) {
	console.log("ChatContent called with hardcoded ID:", id);

	const session = await auth();

	if (!session || !session.user) {
		return redirect(`/login?returnUrl=/markdown-testing`);
	}

	let chat = await getChatById({ id });

	// If chat doesn't exist, create it with the same hardcoded ID
	if (!chat) {
		console.log("Creating markdown test chat with hardcoded ID...");
		try {
			await saveChat({
				id: id, // Use the same hardcoded ID
				userId: session.user.id!,
				title: "Markdown Test Chat",
				visibility: "public", // Set default visibility to public
			});
			chat = await getChatById({ id });
			console.log("Created chat successfully");
		} catch (error) {
			console.error("Failed to create chat:", error);
			return notFound();
		}
	}

	if (!chat) {
		return notFound();
	}

	const messagesFromDb = await getMessagesWithAttachmentsByChatId({
		id,
	});

	const cookieStore = await cookies();
	const modelIdFromCookie = cookieStore.get("model-id")?.value;
	const selectedModelId =
		chat.modelId ||
		models.find((model) => model.id === modelIdFromCookie)?.id ||
		DEFAULT_MODEL_NAME;
	const isFreeActiveSubscription =
		session?.user?.subscriptionTier === PLANS.FREE_PLAN;

	return (
		<>
			<Chat
				id={chat.id}
				initialMessages={convertToUIMessages(messagesFromDb)}
				selectedModelId={selectedModelId}
				selectedVisibilityType={chat.visibility}
				isReadonly={true} // Always readonly for testing
				title={chat.title}
				isFreeActiveSubscription={isFreeActiveSubscription}
			/>
			<DataStreamHandler id={id} />
		</>
	);
}
