import { cookies } from 'next/headers';
import { Suspense } from 'react';
import { auth } from '@/app/(auth)/auth';
import { Chat } from '@/componentsV2/chat';
import { ChatSkeleton } from '@/componentsV2/chat-skeleton';
import { DataStreamHandler } from '@/componentsV2/data-stream-handler';
import To<PERSON>Handler from '@/componentsV2/toast-handle';
import { DEFAULT_MODEL_NAME, models } from '@/lib/ai/models';
import { PLANS } from '@/lib/constants';
import { DEFAULT_CHAT_VISIBILITY } from '@/lib/types';
import { generateUUID } from '@/lib/utils';

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const id = generateUUID();

  const cookieStore = await cookies();
  const modelIdFromCookie = cookieStore.get('model-id')?.value;
  const session = await auth();

  const modelIdMapping: Record<string, string> = {
    'gemini-2.0-flash-001': 'iqidis-everyday',
    'gemini-2.0-flash-thinking-exp': 'iqidis-advanced',
  };

  const mappedModelId =
    modelIdFromCookie && modelIdMapping[modelIdFromCookie]
      ? modelIdMapping[modelIdFromCookie]
      : modelIdFromCookie;

  const selectedModelId =
    models.find((model) => model.id === mappedModelId)?.id ||
    DEFAULT_MODEL_NAME;

  const isFreeActiveSubscription =
    session?.user?.subscriptionTier === PLANS.FREE_PLAN;

  const params = await searchParams;
  const isTrial = params.isTrial === 'true';
  const isPremium = params.isPremium === 'true';

  return (
    <Suspense fallback={<ChatSkeleton />}>
      <>
        <ToastHandler showTrialToast={isTrial} showPremiumToast={isPremium} />
        <Chat
          key={id}
          id={id}
          initialMessages={[]}
          selectedModelId={selectedModelId}
          selectedVisibilityType={DEFAULT_CHAT_VISIBILITY}
          isReadonly={false}
          isFreeActiveSubscription={isFreeActiveSubscription}
        />
        <DataStreamHandler id={id} />
      </>
    </Suspense>
  );
}
