import Script from "next/script";
import { TooltipProvider } from "@/components/ui/tooltip";
import { GlobalPlaybookProvider } from "@/componentsV2/global-playbook-provider";
import { HelpButton } from "@/componentsV2/help-button";
import { ReportProblemButton } from "@/componentsV2/report-problem-button";
import { RequestFeatureButton } from "@/componentsV2/request-feature-button";
import { StickyHeaderNotification } from "@/componentsV2/sticky-header-notification";
import { format, subDays } from "date-fns";
import { PendoProvider } from "@/app/pendo-provider";
import { SentryProvider } from "@/app/sentry-provider";
import { AppSidebar } from "@/componentsV2/app-sidebar";
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { UserProvider } from "@/contexts/UserContext";

import { PLANS, UPGRADE_PLAN_MESSAGES } from "@/lib/constants";
import { auth } from "../(auth)/auth";

export const experimental_ppr = true;

export default async function Layout({
	children,
}: {
	children: React.ReactNode;
}) {
	const [session] = await Promise.all([auth()]);
	const userEmail = session?.user?.email || "Not logged in";

	let isSubscriptionAboutToExpire = false;
	let matchedDays: number | undefined;
	let label = UPGRADE_PLAN_MESSAGES.END_OF_PLAN_MESSAGE;
	const isFreeTier = session?.user?.subscriptionTier === PLANS.FREE_PLAN;

	if (session?.user?.id && !isFreeTier) {
		const endDate = session?.user?.subscriptionEndDate;

		if (endDate) {
			const subscriptionEndDate = format(endDate, "yyyy-MM-dd HH:mm:ss");
			const todayFormatted = format(new Date(), "yyyy-MM-dd");

			let daysBeforeEndList = [7, 4, 0];
			if (session.user.subscriptionTier === PLANS.PAID_PLAN_TRIAL) {
				daysBeforeEndList = [4, 0];
			}
			for (const days of daysBeforeEndList) {
				const dateBeforeEnd = subDays(subscriptionEndDate, days);
				if (format(dateBeforeEnd, "yyyy-MM-dd") === todayFormatted) {
					matchedDays = days;
					break;
				}
			}
			if (matchedDays !== undefined) {
				isSubscriptionAboutToExpire = true;

				switch (matchedDays) {
					case 7:
						label = UPGRADE_PLAN_MESSAGES.SEVEN_DAY_REMAIN_MESSAGE;
						break;
					case 4:
						label = UPGRADE_PLAN_MESSAGES.FOUR_DAY_REMAIN_MESAGE;
						break;
					case 0:
						label = UPGRADE_PLAN_MESSAGES.ZERO_DAY_REMAIN_MESSAGE;
						break;
					default:
						UPGRADE_PLAN_MESSAGES.END_OF_PLAN_MESSAGE;
				}
			}
		}
	}

	return (
		<>
			<Script
				src="https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js"
				strategy="beforeInteractive"
			/>
			<UserProvider user={session?.user}>
				<PendoProvider user={session?.user}>
					<SentryProvider user={session?.user}>
						<GlobalPlaybookProvider>
							<div className="flex flex-col">
								{isSubscriptionAboutToExpire && (
									<StickyHeaderNotification label={label} />
								)}
								<SidebarProvider defaultOpen={false}>
									<div className="flex flex-1 overflow-hidden w-screen h-screen">
										<AppSidebar user={session?.user as any} />
										<SidebarInset className="w-full flex-1 h-full">
											<TooltipProvider>
												<main className="flex flex-1 overflow-hidden dark:chat-background-dark chat-background-light">
													{children}
													<div className="fixed bottom-4 right-4 !z-50 flex gap-2 transition-opacity duration-300">
														<HelpButton />
														<RequestFeatureButton userEmail={userEmail} />
														<ReportProblemButton userEmail={userEmail} />
													</div>
												</main>
											</TooltipProvider>
										</SidebarInset>
									</div>
								</SidebarProvider>
							</div>
						</GlobalPlaybookProvider>
					</SentryProvider>
				</PendoProvider>
			</UserProvider>
		</>
	);
}
