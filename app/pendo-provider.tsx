'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Script from 'next/script';

declare global {
  interface Window {
    pendo: any;
  }
}

export function PendoScript() {
  const pendoKey = process.env.NEXT_PUBLIC_PENDO_KEY || '';
  return (
    <Script
      id="pendo-script"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{
        __html: `
        (function(apiKey){
            (function(p,e,n,d,o){var v,w,x,y,z;o=p[d]=p[d]||{};o._q=o._q||[];
            v=['initialize','identify','updateOptions','pageLoad','track'];for(w=0,x=v.length;w<x;++w)(function(m){
                o[m]=o[m]||function(){o._q[m===v[0]?'unshift':'push']([m].concat([].slice.call(arguments,0)));};})(v[w]);
                y=e.createElement(n);y.async=!0;y.src='https://cdn.pendo.io/agent/static/'+apiKey+'/pendo.js';
                z=e.getElementsByTagName(n)[0];z.parentNode.insertBefore(y,z);})(window,document,'script','pendo');
                
            // Initialize immediately after loading
            window.pendo.initialize({
              visitor: {
                id: 'TEMP-' + Math.random().toString(36).substr(2, 9)
              },
              account: {
                id: 'TEMP-ACCOUNT'
              }
            });
        })('${pendoKey}');
        `
      }}
    />
  );
}

interface PendoProviderProps {
  children: React.ReactNode;
  user?: {
    id?: string;
    email?: string | null;
    name?: string | null;
    isAdmin?: boolean;
  } | null;
}

export function PendoProvider({ children, user }: PendoProviderProps) {
  const pathname = usePathname();
  const router = useRouter();

  // Initialize Pendo when component mounts and when pathname changes
  useEffect(() => {
    if (typeof window === 'undefined' || !window.pendo) return;

    const visitorData = user ? {
      id: user.email,
      user_id: user.id,
      full_name: user.name,
      role: user.isAdmin ? 'admin' : 'user',
      signedIn: true
    } : {
      id: 'ANONYMOUS-' + Math.random().toString(36).substr(2, 9),
      signedIn: false
    };

    const accountData = user ? {
      id: user.email,
      tier: user.isAdmin ? 'admin' : 'regular',
    } : {
      id: 'ANONYMOUS-ACCOUNT',
      tier: 'anonymous'
    };

    if (typeof window.pendo.isReady === 'function' && !window.pendo.isReady()) {
      // First time initialization
      window.pendo.initialize({
        visitor: visitorData,
        account: accountData
      });
    } else {
      // Update identity for existing instance
      window.pendo.identify({
        visitor: visitorData,
        account: accountData
      });
    }

  }, [pathname, user]); // Dependencies updated to use user prop

  return <>{children}</>;
}
