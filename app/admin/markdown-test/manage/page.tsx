"use client";

import { Eye, Plus, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

// Same hardcoded chat ID as the main page
const HARDCODED_CHAT_ID = "a1b2c3d4-e5f6-7890-abcd-ef1234567890";

interface Message {
	id: string;
	role: "user" | "assistant";
	content: string;
	createdAt: string;
}

export default function MarkdownTestManagePage() {
	const { data: session, status } = useSession();
	const router = useRouter();
	const [messages, setMessages] = useState<Message[]>([]);
	const [newMessage, setNewMessage] = useState("");
	const [newRole, setNewRole] = useState<"user" | "assistant">("assistant");
	const [loading, setLoading] = useState(false);

	// Check admin access
	useEffect(() => {
		if (status === "loading") return;

		if (!session?.user) {
			router.push("/login");
			return;
		}

		if (!session.user.isAdmin) {
			router.push("/"); // Redirect non-admins
			return;
		}

		loadMessages();
	}, [session, status, router]);

	const loadMessages = async () => {
		try {
			const response = await fetch(`/api/chat/${HARDCODED_CHAT_ID}/messages`);
			if (response.ok) {
				const data = await response.json();
				setMessages(data);
			}
		} catch (error) {
			console.error("Failed to load messages:", error);
		}
	};

	const addMessage = async () => {
		if (!newMessage.trim()) return;

		setLoading(true);
		try {
			const response = await fetch(`/api/chat/${HARDCODED_CHAT_ID}/messages`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					role: newRole,
					content: newMessage.trim(),
				}),
			});

			if (response.ok) {
				setNewMessage("");
				await loadMessages();
			} else {
				console.error("Failed to add message");
			}
		} catch (error) {
			console.error("Error adding message:", error);
		} finally {
			setLoading(false);
		}
	};

	const deleteMessage = async (messageId: string) => {
		if (!confirm("Are you sure you want to delete this message?")) return;

		try {
			const response = await fetch(
				`/api/chat/${HARDCODED_CHAT_ID}/messages/${messageId}`,
				{
					method: "DELETE",
				},
			);

			if (response.ok) {
				await loadMessages();
			} else {
				console.error("Failed to delete message");
			}
		} catch (error) {
			console.error("Error deleting message:", error);
		}
	};

	const clearAllMessages = async () => {
		if (
			!confirm(
				"Are you sure you want to clear ALL messages? This cannot be undone.",
			)
		)
			return;

		try {
			const response = await fetch(`/api/chat/${HARDCODED_CHAT_ID}/messages`, {
				method: "DELETE",
			});

			if (response.ok) {
				await loadMessages();
			} else {
				console.error("Failed to clear messages");
			}
		} catch (error) {
			console.error("Error clearing messages:", error);
		}
	};

	if (status === "loading") {
		return <div className="p-8">Loading...</div>;
	}

	if (!session?.user?.isAdmin) {
		return <div className="p-8">Access denied. Admin only.</div>;
	}

	return (
		<div className="container mx-auto p-8 max-w-4xl">
			<div className="flex items-center justify-between mb-6">
				<h1 className="text-3xl font-bold">Markdown Test Chat Management</h1>
				<div className="flex gap-2">
					<Button
						variant="outline"
						onClick={() => router.push("/admin/markdown-test")}
					>
						<Eye className="h-4 w-4 mr-2" />
						View Chat
					</Button>
					<Button
						variant="destructive"
						onClick={clearAllMessages}
						disabled={messages.length === 0}
					>
						Clear All Messages
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Add Message Section */}
				<Card className="p-6">
					<h2 className="text-xl font-semibold mb-4">Add New Message</h2>

					<div className="space-y-4">
						<div>
							<Label htmlFor="role">Role</Label>
							<div className="flex gap-2 mt-1">
								<Button
									variant={newRole === "assistant" ? "default" : "outline"}
									onClick={() => setNewRole("assistant")}
									size="sm"
								>
									Assistant
								</Button>
								<Button
									variant={newRole === "user" ? "default" : "outline"}
									onClick={() => setNewRole("user")}
									size="sm"
								>
									User
								</Button>
							</div>
						</div>

						<div>
							<Label htmlFor="content">Message Content</Label>
							<Textarea
								id="content"
								placeholder="Enter markdown content here..."
								value={newMessage}
								onChange={(e) => setNewMessage(e.target.value)}
								className="min-h-[200px] font-mono"
							/>
						</div>

						<Button
							onClick={addMessage}
							disabled={!newMessage.trim() || loading}
							className="w-full"
						>
							<Plus className="h-4 w-4 mr-2" />
							{loading ? "Adding..." : "Add Message"}
						</Button>
					</div>
				</Card>

				{/* Messages List */}
				<Card className="p-6">
					<h2 className="text-xl font-semibold mb-4">
						Current Messages ({messages.length})
					</h2>

					<div className="space-y-3 max-h-[600px] overflow-y-auto">
						{messages.length === 0 ? (
							<p className="text-muted-foreground text-center py-8">
								No messages yet. Add some messages to test markdown rendering.
							</p>
						) : (
							messages.map((message, index) => (
								<div
									key={message.id}
									className="border rounded-lg p-3 relative group"
								>
									<div className="flex items-center justify-between mb-2">
										<div className="flex items-center gap-2">
											<span className="text-xs font-medium">#{index + 1}</span>
											<span
												className={`text-xs px-2 py-1 rounded-full ${
													message.role === "assistant"
														? "bg-blue-100 text-blue-800"
														: "bg-green-100 text-green-800"
												}`}
											>
												{message.role}
											</span>
										</div>
										<Button
											variant="ghost"
											size="icon"
											onClick={() => deleteMessage(message.id)}
											className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6"
										>
											<Trash2 className="h-3 w-3" />
										</Button>
									</div>
									<div className="text-sm font-mono whitespace-pre-wrap break-words">
										{message.content.length > 150
											? message.content.substring(0, 150) + "..."
											: message.content}
									</div>
								</div>
							))
						)}
					</div>
				</Card>
			</div>

			<div className="mt-6 p-4 bg-muted rounded-lg">
				<h3 className="font-semibold mb-2">Instructions:</h3>
				<ul className="text-sm space-y-1 text-muted-foreground">
					<li>• Add messages with markdown content to test rendering</li>
					<li>
						• Use "Assistant" role for AI responses, "User&quot; role for user
						messages
					</li>
					<li>
						• Click "View Chat" to see how the messages render in the actual
						chat interface
					</li>
					<li>
						• Messages are synced across environments for consistent testing
					</li>
				</ul>
			</div>
		</div>
	);
}
