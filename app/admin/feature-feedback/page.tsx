"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { DownloadIcon, Loader2 } from "lucide-react";
import { Search } from "lucide-react";
import { Label } from "@/components/ui/label";
import { SessionProvider, useSession } from "next-auth/react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "antd";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ThumbsUp, ThumbsDown } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AntdMultiSelect } from "@/components/ui/multi-select";

// Wrapper component with SessionProvider
export default function FeedbackManagementPage() {
  return (
    <SessionProvider>
      <FeedbackManagement />
    </SessionProvider>
  );
}

interface FeedbackEntry {
  id: string;
  userId: string;
  userEmail: string;
  featureType: string;
  rating: number;
  feedbackText: string | null;
  metadata: any;
  createdAt: string;
}

function FeedbackManagement() {
  const { data: session } = useSession();
  const router = useRouter();
  const [feedback, setFeedback] = useState<FeedbackEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  
  // Filters
  const [featureTypeFilter, setFeatureTypeFilter] = useState<string[]>(["all"]);
  const [ratingFilter, setRatingFilter] = useState<string>("all");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [searchQuery, setSearchQuery] = useState<string>("");
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Unique feature types for filter dropdown
  const featureTypes = useMemo(() => {
    const types = [...new Set(feedback.map(item => item.featureType))];
    return types.sort();
  }, [feedback]);

  // Filter feedback based on selected filters
  const filteredFeedback = useMemo(() => {
    return feedback.filter(item => {
      // Feature type filter
      // Check if "all" is selected OR if the item's featureType is in the filter
      if (!featureTypeFilter.includes("all") && !featureTypeFilter.includes(item.featureType)) {
        return false;
      }
      
      // Rating filter
      if (ratingFilter !== "all" && item.rating !== parseInt(ratingFilter)) {
        return false;
      }
      
      // Date range filter
      if (startDate && new Date(item.createdAt) < startDate) {
        return false;
      }
      if (endDate) {
        const endOfDay = new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        if (new Date(item.createdAt) > endOfDay) {
          return false;
        }
      }
      
      // Search query (match email or feedback text)
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesEmail = item.userEmail.toLowerCase().includes(query);
        const matchesFeedback = item.feedbackText?.toLowerCase().toLowerCase().includes(query) || false;
        if (!matchesEmail && !matchesFeedback) {
          return false;
        }
      }
      
      return true;
    });
  }, [feedback, featureTypeFilter, ratingFilter, startDate, endDate, searchQuery]);

  // Calculate metrics
  const metrics = useMemo(() => {
    const total = filteredFeedback.length;
    const positive = filteredFeedback.filter(item => item.rating === 1).length;
    const negative = filteredFeedback.filter(item => item.rating === 0).length;
    const averageRating = total > 0 ? positive / total : 0;
    
    return {
      total,
      positive,
      negative,
      averageRating: (averageRating * 100).toFixed(1) + "%",
    };
  }, [filteredFeedback]);

  // Pagination
  const paginatedFeedback = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredFeedback.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredFeedback, currentPage, itemsPerPage]);
  
  const totalPages = Math.ceil(filteredFeedback.length / itemsPerPage);

  useEffect(() => {
    fetchFeedback();
  }, []);

  useEffect(() => {
    setCurrentPage(1);
  }, [featureTypeFilter, ratingFilter, startDate, endDate, searchQuery]);

  const fetchFeedback = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/user-feedback");
      
      if (!response.ok) {
        if (response.status === 401) {
          router.push("/");
          return;
        }
        throw new Error("Failed to fetch feedback data");
      }
      
      const data = await response.json();
      // Sort by date (newest first)
      setFeedback(data.sort((a: FeedbackEntry, b: FeedbackEntry) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ));
    } catch (error) {
      toast.error("Failed to load feedback data");
    } finally {
      setIsLoading(false);
    }
  };

  const exportToCSV = async () => {
    try {
      setIsExporting(true);

      // Create CSV content
      const headers = ["Feature Type", "Rating", "Feedback", "User Email", "Date"];
      const csvContent = [
        headers.join(","),
        ...filteredFeedback.map((item) =>
          [
            item.featureType,
            item.rating === 1 ? "Positive" : "Negative",
            item.feedbackText ? `"${item.feedbackText.replace(/"/g, '""')}"` : "",
            item.userEmail,
            new Date(item.createdAt).toLocaleString(),
          ].join(",")
        ),
      ].join("\n");

      // Create blob and download
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "feature-feedback.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("Feedback data exported successfully");
    } catch (error) {
      toast.error("Failed to export feedback data");
    } finally {
      setIsExporting(false);
    }
  };

  // Check if user is admin
  // This logic runs client-side. Consider server-side redirect for better security on admin pages.
  if (session && session.user && !(session.user as any).isAdmin) {
    router.push("/");
    return null;
  }

  return (
    <div className="container mx-auto py-6 px-8 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button
            title="Go Back"
            variant="outline"
            onClick={() => router.back()}
            >
           ← Back
          </Button>
          <h1 className="text-2xl font-bold">Feature Feedback Management</h1>
        </div>
          
        <Button
          onClick={exportToCSV}
          disabled={isExporting || filteredFeedback.length === 0}
          className="flex items-center gap-2"
        >
          {isExporting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <DownloadIcon className="h-4 w-4" />
          )}
          Export to CSV
        </Button>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Positive Feedback</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center">
            <div className="text-2xl font-bold">{metrics.positive}</div>
            <ThumbsUp className="ml-2 h-5 w-5 text-green-500" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Negative Feedback</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center">
            <div className="text-2xl font-bold">{metrics.negative}</div>
            <ThumbsDown className="ml-2 h-5 w-5 text-red-500" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Satisfaction Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.averageRating}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
          <Label htmlFor="feature-type">Feature Type</Label>
          <AntdMultiSelect
            value={featureTypeFilter}
            onChange={setFeatureTypeFilter}
            options={[
              { value: "all", label: "All" },
              ...featureTypes.map(type => ({ value: type, label: type }))
            ]}
            className="w-full"
            placeholder="Select feature types..."
          />
        </div>
        
        <div>
          <Label htmlFor="rating">Rating</Label>
          <Select
            value={ratingFilter}
            onValueChange={setRatingFilter} 
          >
            <SelectTrigger id="rating" className="h-8">
              <SelectValue placeholder="All ratings" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All ratings</SelectItem>
              <SelectItem value="1">Positive</SelectItem>
              <SelectItem value="0">Negative</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label>Start Date</Label>
          <DatePicker 
            onChange={(date) => setStartDate(date ? date.toDate() : undefined)}
            placeholder="From"
            className="w-full"
          />
        </div>
        
        <div>
          <Label>End Date</Label>
          <DatePicker 
            onChange={(date) => setEndDate(date ? date.toDate() : undefined)}
            placeholder="To"
            className="w-full"
          />
        </div>
        
        <div>
          <Label htmlFor="search">Search</Label>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              id="search"
              placeholder="Search email or feedback..."
              className="pl-8 h-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Feedback Table */}
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Feature Type</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead className="w-[300px]">Feedback</TableHead>
                  <TableHead>User Email</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Metadata</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedFeedback.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No feedback found matching the current filters
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedFeedback.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.featureType}</TableCell>
                      <TableCell>
                        {item.rating === 1 ? (
                          <ThumbsUp className="h-5 w-5 text-green-500" />
                        ) : (
                          <ThumbsDown className="h-5 w-5 text-red-500" />
                        )}
                      </TableCell>
                      <TableCell className="max-w-[300px]">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="truncate block w-full">
                                {item.feedbackText || "—"}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[400px] break-words">
                              <p className="text-sm">{item.feedbackText || "—"}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell>{item.userEmail}</TableCell>
                      <TableCell>
                        {new Date(item.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="max-w-[150px]">
                        {item.metadata ? (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="truncate block w-full text-xs text-muted-foreground">
                                  {Object.keys(item.metadata).length > 0 ? 
                                    `{${Object.keys(item.metadata).join(', ')}}` : 
                                    "—"}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent side="left" className="max-w-[400px] max-h-[300px] overflow-auto">
                                <pre className="text-xs whitespace-pre-wrap">
                                  {JSON.stringify(item.metadata, null, 2)}
                                </pre>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : (
                          "—"
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                {Math.min(currentPage * itemsPerPage, filteredFeedback.length)} of{" "}
                {filteredFeedback.length} entries
              </div>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                >
                  First
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                >
                  Last
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
