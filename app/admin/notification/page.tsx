"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, Search, ArrowLeft } from "lucide-react";
import { createNotification } from "@/lib/actions/notifications";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export default function SendNotificationPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [sendToAll, setSendToAll] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [formData, setFormData] = useState({
    type: "",
    title: "",
    description: "",
    link: "",
  });

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch("/api/admin/users");
        const data = await response.json();
        setUsers(data);
      } catch (error) {
        console.error("Error fetching users:", error);
        toast.error("Failed to load users");
      } finally {
        setIsLoadingUsers(false);
      }
    };

    fetchUsers();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Get all user IDs if sending to all users
      const userIds = sendToAll ? users.map((user) => user.id) : selectedUsers;

      if (userIds.length === 0) {
        toast.error("Please select at least one user");
        return;
      }

      await createNotification({
        type: formData.type,
        title: formData.title,
        description: formData.description,
        userIds,
        link: formData.link,
      });

      toast.success("Notification sent successfully");
      router.push("/admin/users"); // Redirect to notifications list
    } catch (error) {
      console.error("Error sending notification:", error);
      toast.error("Failed to send notification");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleTypeChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      type: value,
    }));
  };

  const filteredUsers = users.filter(
    (user) =>
      `${user.firstName} ${user.lastName}`
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleUserSelect = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background/95 p-4">
      <div className="w-full max-w-2xl">
        <Card className="border-none shadow-lg">
          <CardHeader className="space-y-1 pb-4">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
                className="hover:bg-accent/50"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <CardTitle className="text-2xl font-bold">
                  Send Notification
                </CardTitle>
                <CardDescription>
                  Create and send notifications to specific users or all users
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title" className="text-sm font-medium">
                      Title
                    </Label>
                    <Input
                      id="title"
                      name="title"
                      placeholder="Enter notification title"
                      required
                      className="w-full"
                      value={formData.title}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type" className="text-sm font-medium">
                      Notification Type
                    </Label>
                    <Select
                      name="type"
                      required
                      value={formData.type}
                      onValueChange={handleTypeChange}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="news">News</SelectItem>
                        <SelectItem value="update">Update</SelectItem>
                        <SelectItem value="personal">Personal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="text-sm font-medium">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Enter notification description"
                    required
                    className="min-h-[80px] resize-none"
                    value={formData.description}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="link" className="text-sm font-medium">
                      Link (Optional)
                    </Label>
                    <Input
                      id="link"
                      name="link"
                      placeholder="Enter notification link"
                      className="w-full"
                      value={formData.link}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Send to</Label>
                    <div className="flex items-center space-x-2 bg-accent/50 p-3 rounded-lg h-[40px]">
                      <Switch
                        checked={sendToAll}
                        onCheckedChange={setSendToAll}
                      />
                      <Label
                        htmlFor="send-to-all"
                        className="text-sm font-medium"
                      >
                        All users
                      </Label>
                    </div>
                  </div>
                </div>

                {!sendToAll && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Select Users</Label>
                    <div className="border rounded-lg overflow-hidden">
                      <div className="p-2 border-b bg-accent/50">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search users..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-8 bg-background"
                          />
                        </div>
                      </div>
                      <ScrollArea className="h-[150px]">
                        {isLoadingUsers ? (
                          <div className="flex items-center justify-center h-[150px]">
                            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                          </div>
                        ) : filteredUsers.length > 0 ? (
                          <div className="p-2 space-y-1">
                            {filteredUsers.map((user) => (
                              <div
                                key={user.id}
                                className="flex items-center space-x-2 p-2 hover:bg-accent/50 rounded-md transition-colors"
                              >
                                <Checkbox
                                  id={user.id}
                                  checked={selectedUsers.includes(user.id)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      handleUserSelect(user.id);
                                    } else {
                                      setSelectedUsers((prev) =>
                                        prev.filter((id) => id !== user.id)
                                      );
                                    }
                                  }}
                                />
                                <Label
                                  htmlFor={user.id}
                                  className="flex-1 cursor-pointer"
                                >
                                  <div className="font-medium">
                                    {user.firstName} {user.lastName}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {user.email}
                                  </div>
                                </Label>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="p-4 text-center text-sm text-muted-foreground">
                            No users found
                          </div>
                        )}
                      </ScrollArea>
                    </div>
                    {selectedUsers.length > 0 && (
                      <p className="text-sm text-muted-foreground">
                        {selectedUsers.length} user
                        {selectedUsers.length !== 1 ? "s" : ""} selected
                      </p>
                    )}
                  </div>
                )}
              </div>

              <Button
                type="submit"
                className="w-full bg-[#270f57] hover:bg-[#270f57]/90"
                disabled={
                  isLoading || (!sendToAll && selectedUsers.length === 0)
                }
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  "Send Notification"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
