'use client';

import { useState, useEffect, Suspense, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { z } from 'zod';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Eye, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { Loader2 } from 'lucide-react';
import { LogoSignup } from '@/components/icons';
import { useTheme } from 'next-themes';
import MeshBackground from '@/components/mesh-background';
import { motion } from 'framer-motion';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertCircle } from "lucide-react";

const FloatingParticles = () => {
  const { resolvedTheme } = useTheme();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions
    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);
    
    // Create particles
    const particleCount = 50;
    const particles: {
      x: number;
      y: number;
      size: number;
      speedX: number;
      speedY: number;
      opacity: number;
      color: string;
    }[] = [];
    
    // Animation function
    const animate = () => {
      // Implementation would go here
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', setCanvasDimensions);
    };
  }, [resolvedTheme]);
  
  return (
    <canvas 
      ref={canvasRef} 
      className="absolute inset-0 pointer-events-none z-0 h-full"
      aria-hidden="true"
    />
  );
};

const passwordSchema = z.object({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(100, 'Password is too long')
    .refine(val => /[A-Z]/.test(val), {
      message: 'Password must contain at least one uppercase letter',
    })
    .refine(val => /[a-z]/.test(val), {
      message: 'Password must contain at least one lowercase letter',
    })
    .refine(val => /[0-9]/.test(val), {
      message: 'Password must contain at least one number',
    })
    .refine(val => /[^A-Za-z0-9]/.test(val), {
      message: 'Password must contain at least one special character',
    })
});

const ResetPasswordForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Add password criteria state
  const [passwordCriteria, setPasswordCriteria] = useState({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecial: false,
  });

  // Get email and token from URL parameters
  const email = searchParams.get('email');
  const token = searchParams.get('token');

  useEffect(() => {
    setMounted(true);
    // Redirect if email or token is missing
    if (!email || !token) {
     
      toast.error('Invalid reset link');
      router.push('/login');
    }
  }, [email, token, router]);

  // Password validation
  useEffect(() => {
    if (password) {
      setPasswordCriteria({
        minLength: password.length >= 8,
        hasUppercase: /[A-Z]/.test(password),
        hasLowercase: /[a-z]/.test(password),
        hasNumber: /[0-9]/.test(password),
        hasSpecial: /[^A-Za-z0-9]/.test(password),
      });
    }
  }, [password]);

  // Confirm password validation
  useEffect(() => {
    if (confirmPassword && password !== confirmPassword) {
      setConfirmPasswordError('Passwords do not match');
    } else {
      setConfirmPasswordError('');
    }
  }, [confirmPassword, password]);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
    
      toast.error('Passwords do not match');
      return;
    }

    // Validate password meets all criteria
    const allCriteriaMet = Object.values(passwordCriteria).every(Boolean);
    if (!allCriteriaMet) {
      
      toast.error('Password does not meet all requirements');
      return;
    }

    try {
      // Validate password
      passwordSchema.parse({ password });
      
      setLoading(true);

      const response = await fetch('/api/auth/forgot-password/reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          email,
          newPassword: password,
          token
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to reset password');
      }
   

      toast.success('Password successfully updated');
      setTimeout(() => {
        router.push('/login');
      }, 750);
    } catch (error) {
      if (error instanceof z.ZodError) {
       
        toast.error(error.errors[0].message);
      } else if (error instanceof Error) {
    
        toast.error(error.message);
      } else {
     
        toast.error('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!mounted) return null;

  const isFormValid = 
    password && 
    confirmPassword && 
    password === confirmPassword && 
    passwordCriteria.minLength &&
    passwordCriteria.hasUppercase &&
    passwordCriteria.hasLowercase &&
    passwordCriteria.hasNumber &&
    passwordCriteria.hasSpecial;

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background relative overflow-hidden">
      <MeshBackground />
      <FloatingParticles />
      
      <motion.div 
        className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-8 bg-white/70 dark:bg-slate-800/70 backdrop-blur-md border border-white/30 dark:border-gray-700 shadow-lg p-8 z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <div className="flex flex-col items-center justify-center gap-2 text-center">
          <motion.div 
            className="mb-2"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <LogoSignup
              size={140}
              mixBlendMode={resolvedTheme === "dark" ? "lighten" : "multiply"}
              isDark={resolvedTheme === "dark"}
            />
          </motion.div>
          
          <motion.h3 
            className="text-2xl font-semibold font-playfair dark:text-zinc-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            Reset Password
          </motion.h3>
          <motion.p 
            className="text-sm text-muted-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Enter your new password below
          </motion.p>
        </div>
        
        <motion.form 
          onSubmit={handleResetPassword} 
          className="flex flex-col gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <div className="flex flex-col gap-1.5">
            <Label htmlFor="password">New Password</Label>
            <div className="relative">
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter new password"
                className={`w-full p-3 border rounded bg-card/80 text-card-foreground dark:bg-gray-800/60 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:outline-none transition-all duration-200 pr-10 ${
                  passwordError ? "border-rose-500 focus:ring-rose-500" : ""
                }`}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                minLength={8}
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
            
            {/* Password criteria section */}
            {password.length > 0 && (
              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center mt-1 text-xs">
                      <AlertCircle 
                        size={14} 
                        className={`mr-1 ${
                          Object.values(passwordCriteria).every(Boolean)
                            ? "text-green-500"
                            : "text-amber-500"
                        }`} 
                      />
                      <span>
                        {Object.values(passwordCriteria).every(Boolean)
                          ? "Password meets all requirements"
                          : "Password requirements"}
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="w-64 p-2">
                    <ul className="space-y-1 text-xs">
                      <li className={`flex items-center ${passwordCriteria.minLength ? "text-green-500" : "text-muted-foreground"}`}>
                        <span className="mr-1">{passwordCriteria.minLength ? "✓" : "○"}</span>
                        At least 8 characters
                      </li>
                      <li className={`flex items-center ${passwordCriteria.hasUppercase ? "text-green-500" : "text-muted-foreground"}`}>
                        <span className="mr-1">{passwordCriteria.hasUppercase ? "✓" : "○"}</span>
                        One uppercase letter
                      </li>
                      <li className={`flex items-center ${passwordCriteria.hasLowercase ? "text-green-500" : "text-muted-foreground"}`}>
                        <span className="mr-1">{passwordCriteria.hasLowercase ? "✓" : "○"}</span>
                        One lowercase letter
                      </li>
                      <li className={`flex items-center ${passwordCriteria.hasNumber ? "text-green-500" : "text-muted-foreground"}`}>
                        <span className="mr-1">{passwordCriteria.hasNumber ? "✓" : "○"}</span>
                        One number
                      </li>
                      <li className={`flex items-center ${passwordCriteria.hasSpecial ? "text-green-500" : "text-muted-foreground"}`}>
                        <span className="mr-1">{passwordCriteria.hasSpecial ? "✓" : "○"}</span>
                        One special character
                      </li>
                    </ul>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          
          <div className="flex flex-col gap-1.5">
            <Label htmlFor="confirmPassword">Confirm New Password</Label>
            <div className="relative">
              <input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm new password"
                className={`w-full p-3 border rounded bg-card/80 text-card-foreground dark:bg-gray-800/60 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:outline-none transition-all duration-200 pr-10 ${
                  confirmPasswordError ? "border-rose-500 focus:ring-rose-500" : ""
                }`}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                minLength={8}
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                aria-label={showConfirmPassword ? "Hide password" : "Show password"}
              >
                {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
            {confirmPasswordError && (
              <p className="text-sm text-rose-500 mt-1">{confirmPasswordError}</p>
            )}
          </div>
          
          <motion.button
            type="submit"
            className="mt-4 w-full bg-primary text-primary-foreground py-3 px-4 rounded hover:bg-primary hover:shadow-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary disabled:hover:shadow-none disabled:hover:text-primary-foreground font-medium dark:bg-white
              dark:text-purple-800
              dark:border
              dark:border-purple-300
              dark:hover:bg-purple-100 dark:hover:text-purple-800"
            disabled={loading || !isFormValid}
            whileHover={{ scale: loading || !isFormValid ? 1 : 1.02 }}
            whileTap={{ scale: loading || !isFormValid ? 1 : 0.98 }}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </div>
            ) : (
              "Reset Password"
            )}
          </motion.button>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <Link href="/login" className="flex items-center justify-center text-sm text-muted-foreground hover:text-foreground mt-2 transition-colors">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Login
            </Link>
          </motion.div>
        </motion.form>
      </motion.div>
    </div>
  );
};

export default function ForgotPasswordResetPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ResetPasswordForm />
    </Suspense>
  );
}
