"use server";

import { z } from "zod";

import {
  addR<PERSON><PERSON>ralE<PERSON>ry,
  createUser,
  ensureUserHasSubscription,
  getPlanByName,
  getUser,
  getUserByReferralCode,
  subscribe,
} from "@/lib/db/queries";

import { signIn } from "./auth";
import { PLANS } from "@/lib/constants";
import { sendSlackMessage } from "@/lib/analytics/slack";
import { db } from "@/lib/db";
import { userPreferences } from "@/lib/db/schema";
import { EmailService } from "@/lib/email/service";
import { generateUUID } from "@/lib/utils";
import { identifyUser } from "@/lib/mixpanel/server/mixpanelServer";

const authFormSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

const signupSchema = z.object({
  firstname: z.string(),
  lastname: z.string(),
  email: z.string().email(),
  password: z.string().min(8),
  confirmPassword: z.string().min(8),
  company: z.string(),
  teamsize: z.string(),
  referralCode: z.string(),
  referrerId: z.string().optional(),
  heardFrom: z.string().optional(), // Add this field
});

export interface LoginActionState {
  status:
    | "idle"
    | "in_progress"
    | "success"
    | "failed"
    | "invalid_data"
    | "user_not_found";
  subscriptionTier?: string;
  isAdmin?: boolean;
  userId?: string;
  userEmail?: string;
}

export const login = async (
  _: LoginActionState,
  formData: FormData
): Promise<LoginActionState> => {
  try {
    const validatedData = authFormSchema.parse({
      email: formData.get("email"),
      password: formData.get("password"),
    });
    const [user] = await getUser(validatedData.email);

    // Check if user exists
    if (!user) {
      return { status: "user_not_found" } as LoginActionState;
    }

    if (user && !user.isEmailVerified) {
      return { status: "invalid_data" } as LoginActionState;
    }
    await signIn("credentials", {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });
    await ensureUserHasSubscription(user.id);

    identifyUser(user.id, { $email: user.email, userId: user.id });
    return {
      status: "success",
      subscriptionTier: user?.subscriptionTier ?? "",
      isAdmin: user?.isAdmin,
      userId: user.id,
      userEmail: user.email,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: "invalid_data" };
    }

    return { status: "failed" };
  }
};

export interface RegisterActionState {
  status:
    | "idle"
    | "in_progress"
    | "success"
    | "failed"
    | "user_exists"
    | "invalid_data"
    | "unauthorized";
  message?: string;
}

export const register = async (
  state: RegisterActionState,
  formData: FormData
): Promise<RegisterActionState> => {
  try {
    // Immediately return with unauthorized status for regular user registration
    return {
      status: "unauthorized",
      message: "User registration is restricted to administrators only.",
    };

    // Commenting out old registration logic
    /*
    const validatedData = registerSchema.parse({
      email: formData.get('email'),
      password: formData.get('password'),
    });

    const [user] = await getUser(validatedData.email);

    if (user) {
      return { status: 'user_exists' } as RegisterActionState;
    }
    await createUser(validatedData.email, validatedData.password);
    await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });

    return { status: 'success' };
    */
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: "invalid_data" };
    }
    return { status: "failed" };
  }
};

export const signup = async (
  _: RegisterActionState,
  formData: FormData
): Promise<RegisterActionState> => {
  try {
    const formReferrerCode = formData.get("referrerCode")?.toString();
    const referralCodeUUID = await generateUUID();

    // Validate referral code if provided
    if (formReferrerCode && formReferrerCode.trim() !== "") {
      const referrer = await getUserByReferralCode(formReferrerCode);
      if (!referrer) {
        return {
          status: "invalid_data",
          message: "Invalid referral code",
        };
      }
    }

    const validatedData = signupSchema.parse({
      firstname: formData.get("firstname"),
      lastname: formData.get("lastname"),
      email: formData.get("email"),
      password: formData.get("password"),
      confirmPassword: formData.get("confirmPassword"),
      company: formData.get("company"),
      teamsize: formData.get("teamsize"),
      referralCode: referralCodeUUID,
      referrerId: formReferrerCode,
      heardFrom: formData.get("heardFrom"),
    });

    if (validatedData.password !== validatedData.confirmPassword) {
      return { status: "unauthorized" };
    }
    const [user] = await getUser(validatedData.email);
    if (user) {
      return { status: "user_exists" } as RegisterActionState;
    }

    const referrerId = formReferrerCode
      ? await getUserByReferralCode(formReferrerCode)
      : undefined;

    const heardFrom = validatedData.heardFrom;

    const insertedUsers = await createUser(
      validatedData.email,
      validatedData.referralCode,
      validatedData.password,
      referrerId,
      validatedData.firstname,
      validatedData.lastname,
      validatedData.company,
      validatedData.teamsize,
      undefined, // description field
      false, // isAdmin
      heardFrom
    );

    // add referral table entry
    if (referrerId && insertedUsers?.length > 0) {
      await addReferralEntry(referrerId, insertedUsers[0].id);
    }

    if (insertedUsers && insertedUsers.length > 0) {
      //Activating 14 days free trial plan
      const [plan] = await getPlanByName(PLANS.FREE_PLAN);

      await subscribe(insertedUsers[0].id, plan.id);

      // Also create initial preferences
      await db.insert(userPreferences).values({
        userId: insertedUsers[0].id,
        fullName: `${validatedData.firstname} ${validatedData.lastname}`.trim(),
        firmName: validatedData.company,
        email: validatedData.email,
      });

      // Add user to Mailchimp immediately after signup
      await EmailService.sendWelcomeEmail(
        validatedData.email,
        validatedData.firstname,
        validatedData.lastname,
        {
          company: validatedData.company,
          teamsize: validatedData.teamsize,
          source: "signup",
        }
      );

      await sendSlackMessage(insertedUsers[0].id, insertedUsers[0].email);
    }
    return { status: "success" };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: "invalid_data" };
    }
    return { status: "failed" };
  }
};

export async function socialLogin(socialProvider: string) {
    await signIn(socialProvider, { callbackUrl: "/welcome" });
}
