'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useActionState, useEffect, useState } from 'react';
import { toast } from 'sonner';

import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';

import { register, type RegisterActionState } from '../actions';
import { logEvent} from "@/lib/analytics/events-client";
import { AuthEvent } from '@/lib/analytics/event-types';

export default function Register() {
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);

  const [state, formAction] = useActionState<RegisterActionState, FormData>(
    register,
    {
      status: 'idle',
    },
  );

  useEffect(() => {
    if (state.status === 'user_exists') {
      toast.dismiss();
      toast.error('Account already exists');
    } else if (state.status === 'failed') {
      toast.dismiss();
      toast.error('Failed to create account');
    } else if (state.status === 'invalid_data') {
      toast.dismiss();
      toast.error('Failed validating your submission!');
    } else if (state.status === 'success') {
      toast.dismiss();
      toast.success('Account created successfully');
      logEvent(AuthEvent.SIGN_UP, {hours: new Date().getUTCHours()})
      setIsSuccessful(true);
      router.refresh();
    }
  }, [state, router]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    formAction(formData);
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-12 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Registration Restricted</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            User registration is restricted to administrator approval only. Please contact an administrator to create an account.
          </p>
          <div className="mt-4">
            <Link
              href="/login"
              className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
            >
              Return to Login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
