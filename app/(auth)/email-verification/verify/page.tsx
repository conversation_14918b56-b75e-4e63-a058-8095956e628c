'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import { LogoIqidis } from '@/components/icons'
import { useTheme } from 'next-themes'
import { logEvent } from '@/lib/analytics/events-client'
import { AuthEvent } from '@/lib/analytics/event-types'

const EmailVerification = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const { resolvedTheme } = useTheme()

    // Get email and token from URL parameters
    const email = searchParams.get('email')
    const token = searchParams.get('token')

    // Redirect if email or token is missing
    useEffect(() => {
        if (!email || !token) {
            toast.dismiss();
            toast.error('Invalid reset link')
            router.push('/login')
        } else {
            updateUserData()
        }
    }, [email, token, router])

    const updateUserData = async () => {
        const response = await fetch('/api/auth/email-verification/verify', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email,
                token,
            }),
        })

        if (!response.ok) {
            const data = await response.json()
            throw new Error(data.error || 'Failed to verify email')
        }
        toast.dismiss();
        toast.success('Email verified successfully')
        logEvent(AuthEvent.VERIFY_EMAIL, {email})
        setTimeout(() => {
            router.push('/login?isFreemium=true')
        }, 750)
    }

    return (
        <div className="flex h-dvh w-screen items-center justify-center bg-background">
            <div className="flex flex-col items-center gap-6">
                <div className="relative flex items-center justify-center">
                    Your email address is being verified. Please wait,
                    redirecting to the login page...
                </div>
                <div className="animate-spin-slow">
                    <div className="relative flex items-center justify-center">
                        <LogoIqidis
                            size={150}
                            mixBlendMode={
                                resolvedTheme === 'dark'
                                    ? 'lighten'
                                    : 'multiply'
                            }
                            isDark={resolvedTheme === 'dark'}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default function EmailVerificationPage() {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <EmailVerification />
        </Suspense>
    )
}
