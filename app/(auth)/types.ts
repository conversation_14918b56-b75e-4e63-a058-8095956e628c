import { DefaultSession } from "next-auth";

declare module "next-auth" {
  interface User {
    userId?: string;
    userEmail?: string;
    isAdmin: boolean;
    subscriptionTier: string;
    subscriptionEndDate: Date | null;
    stripeCustomerId?: string;
    firstname?: string;
    lastname?: string;
    company?: string;
    avatarUrl?: string;
    isAdminManaged?: boolean;
    isExtendedTrialUser?: boolean;
    // Additional fields for free tier users
    totalChats?: number;
    dailyMessageCount?: number;
    minutesUntilReset?: number;
    memoryUsageMB?: number;
    heardFrom?:string;
  }

  interface Session extends DefaultSession {
    user?: User;
  }
}
