"use client";

import Link from "next/link";
import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import {
	useActionState,
	useEffect,
	useState,
	useCallback,
	Suspense,
	useRef,
} from "react";
import { toast } from "sonner";
import { useTheme } from "next-themes";

import { SignupForm } from "@/components/signup-form";
import { SubmitButton } from "@/components/submit-button";
import { TestimonialCarousel } from "@/components/testimonial-carousel";
import { CONTACT, TESTIMONIALS } from "@/lib/constants";

import { signup, type RegisterActionState } from "../actions";
import { LogoIqidis, LogoSignup } from "@/components/icons";
import { logEvent } from "@/lib/analytics/events-client";
import { AuthEvent } from "@/lib/analytics/event-types";
import { Button } from "@/components/ui/button";
import { signIn } from "next-auth/react";

export interface IFormData {
	firstname: string;
	lastname: string;
	email: string;
	password: string;
	confirmPassword: string;
	company: string;
	teamsize: string;
	subscribe: boolean;
	referralCode?: string;
	heardFrom?: string;
	customHeardFrom?: string; // Add this field
}

const initialFormaData = {
	firstname: "",
	lastname: "",
	email: "",
	password: "",
	confirmPassword: "",
	company: "",
	teamsize: "",
	subscribe: false,
	heardFrom: "",
	customHeardFrom: "", // Add this field with empty default
};

function SignUpForm() {
	const router = useRouter();
	const { resolvedTheme } = useTheme();
	const [signUpFormData, setSignupFormData] =
		useState<IFormData>(initialFormaData);
	const [isSuccessful, setIsSuccessful] = useState(false);
	const [isRedirecting, setIsRedirecting] = useState(false);
	const [passwordError, setPasswordError] = useState<string>("");
	const [firstNameError, setFirstNameError] = useState<string>("");
	const [lastNameError, setLastNameError] = useState<string>("");
	const [emailError, setEmailError] = useState<string>("");
	const [companyError, setCompanyError] = useState<string>("");
	const [teamsizeError, setTeamsizeError] = useState<string>("");
	const [confirmPasswordError, setConfirmPasswordError] = useState<string>("");
	const [referrerCode, setReferrerCode] = useState("");
	const successToastShown = useRef(false);
	const searchParams = useSearchParams();
	const paramsReferrerCode = searchParams.get("ref");

	// Add a new state to track form submissions
	const [submissionCount, setSubmissionCount] = useState(0);

	// Add this to your state declarations
	const [passwordCriteria, setPasswordCriteria] = useState({
		minLength: false,
		hasUppercase: false,
		hasLowercase: false,
		hasNumber: false,
		hasSpecial: false,
	});

	// Add a new state for referral code error
	const [referralCodeError, setReferralCodeError] = useState<string>("");

	const [state, formAction] = useActionState<RegisterActionState, FormData>(
		signup,
		{
			status: "idle",
		},
	);

	const sendEmail = useCallback(async () => {
		const response = await fetch("/api/auth/email-verification/request", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify({ email: signUpFormData.email }),
		});

		if (!response.ok) {
			const data = await response.json();
			throw new Error(data.error || "Failed to send reset instructions");
		}
		setIsRedirecting(true);
		toast.success("Verification instructions have been sent to your email");
		setTimeout(() => {
			router.push("/login");
		}, 2000);
	}, [signUpFormData.email, router]);

	useEffect(() => {
		setSignupFormData((prev) => ({
			...prev,
			heardFrom: prev.heardFrom,
		}));
		// Only process state changes when submission count changes (form was submitted)
		if (submissionCount > 0) {
			switch (state.status) {
				case "unauthorized":
					toast.error("Passwords do not match!");
					// Ensure checkboxes remain unchecked on error
					break;
				case "failed":
					toast.error("Failed to create account. Please try again.");
					// Ensure checkboxes remain unchecked on error
					break;
				case "user_exists":
					toast.error("An account with this email already exists");
					// Ensure checkboxes remain unchecked on error
					break;
				case "invalid_data":
					// Check if there's a specific message about referral code
					if (state.message && state.message.includes("referral code")) {
						toast.error(state.message);
						setReferralCodeError("Invalid referral code");
					} else {
						toast.error("Please check your information and try again");
					}
					// Ensure checkboxes remain unchecked on error
					break;
				case "success":
					if (!successToastShown.current) {
						successToastShown.current = true;
					}
					setIsSuccessful(true);
					sendEmail();
					setSignupFormData(initialFormaData);
					// Reset checkbox states on success
					break;
			}
		}
	}, [state.status, state.message, submissionCount, sendEmail]);

	useEffect(() => {
		if (signUpFormData.password && signUpFormData.confirmPassword) {
			if (signUpFormData.password !== signUpFormData.confirmPassword) {
				setPasswordError("Passwords do not match");
			} else {
				setPasswordError("");
			}
		} else {
			setPasswordError("");
		}
	}, [signUpFormData.password, signUpFormData.confirmPassword]);

	useEffect(() => {
		if (signUpFormData.firstname.trim() !== "") {
			if (!/^[A-Za-z\s-']+$/.test(signUpFormData.firstname.trim())) {
				setFirstNameError("First name should contain only letters");
			} else {
				setFirstNameError("");
			}
		} else {
			setFirstNameError("");
		}
	}, [signUpFormData.firstname]);

	useEffect(() => {
		if (signUpFormData.lastname.trim() !== "") {
			if (!/^[A-Za-z\s-']+$/.test(signUpFormData.lastname.trim())) {
				setLastNameError("Last name should contain only letters");
			} else {
				setLastNameError("");
			}
		} else {
			setLastNameError("");
		}
	}, [signUpFormData.lastname]);

	// Email validation
	useEffect(() => {
		if (signUpFormData.email.trim() !== "") {
			if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(signUpFormData.email.trim())) {
				setEmailError("Please enter a valid email address");
			} else {
				setEmailError("");
			}
		} else {
			setEmailError("");
		}
	}, [signUpFormData.email]);

	// Company validation
	useEffect(() => {
		if (signUpFormData.company.trim() !== "") {
			if (signUpFormData.company.trim().length < 2) {
				setCompanyError("Company name must be at least 2 characters");
			} else {
				setCompanyError("");
			}
		} else {
			setCompanyError("");
		}
	}, [signUpFormData.company]);

	// Team size validation
	useEffect(() => {
		if (signUpFormData.teamsize.trim() !== "") {
			if (!/^\d+$/.test(signUpFormData.teamsize.trim())) {
				setTeamsizeError("Team size must be a number");
			} else if (parseInt(signUpFormData.teamsize.trim()) < 1) {
				setTeamsizeError("Team size must be at least 1");
			} else {
				setTeamsizeError("");
			}
		} else {
			setTeamsizeError("");
		}
	}, [signUpFormData.teamsize]);

	// Password validation
	useEffect(() => {
		if (signUpFormData.password) {
			setPasswordCriteria({
				minLength: signUpFormData.password.length >= 8,
				hasUppercase: /[A-Z]/.test(signUpFormData.password),
				hasLowercase: /[a-z]/.test(signUpFormData.password),
				hasNumber: /[0-9]/.test(signUpFormData.password),
				hasSpecial: /[^A-Za-z0-9]/.test(signUpFormData.password),
			});
		}
	}, [signUpFormData.password]);

	// Confirm password validation - separate from password length validation
	useEffect(() => {
		if (
			signUpFormData.confirmPassword &&
			signUpFormData.password !== signUpFormData.confirmPassword
		) {
			setConfirmPasswordError("Passwords do not match");
		} else {
			setConfirmPasswordError("");
		}
	}, [signUpFormData.confirmPassword, signUpFormData.password]);

	// Reset the error shown flag when the user changes any form field
	const [errorShown, setErrorShown] = useState(false);

	useEffect(() => {
		setErrorShown(false);
	}, [signUpFormData]);

	useEffect(() => {
		setReferrerCode(paramsReferrerCode ? paramsReferrerCode : "");
	}, [paramsReferrerCode]);

	// Add effect to clear referral code error when referral code changes
	useEffect(() => {
		if (referrerCode === "") {
			setReferralCodeError("");
		}
	}, [referrerCode]);

	const handleSubmit = async (formData: FormData) => {
		// Increment submission count to trigger the useEffect
		setSubmissionCount((prev) => prev + 1);

		const firstname = formData.get("firstname") as string;
		const lastname = formData.get("lastname") as string;
		const password = formData.get("password") as string;
		const confirmPassword = formData.get("confirmPassword") as string;
		const heardFrom = formData.get("heardFrom") as string;
		const customHeardFrom = formData.get("customHeardFrom") as string;

		console.log("Form submission - heardFrom:", heardFrom);
		console.log("Form submission - customHeardFrom:", customHeardFrom);

		// Validate names contain only letters
		if (!/^[A-Za-z\s-']+$/.test(firstname.trim())) {
			toast.error("First name should contain only letters");
			return;
		}

		if (!/^[A-Za-z\s-']+$/.test(lastname.trim())) {
			toast.error("Last name should contain only letters");
			return;
		}

		// Validate password meets all criteria
		const allCriteriaMet =
			password.length >= 8 &&
			/[A-Z]/.test(password) &&
			/[a-z]/.test(password) &&
			/[0-9]/.test(password) &&
			/[^A-Za-z0-9]/.test(password);

		if (!allCriteriaMet) {
			toast.error("Password does not meet all requirements");
			return;
		}

		// Validate passwords match
		if (password !== confirmPassword) {
			toast.error("Passwords do not match!");

			// Reset only password fields
			setSignupFormData((prev) => ({
				...prev,
				password: "",
				confirmPassword: "",
			}));
			return;
		}

		// Combine heardFrom and customHeardFrom if "other" is selected
		if (heardFrom === "other" && customHeardFrom) {
			formData.set("heardFrom", `other:${customHeardFrom}`);
			console.log("Updated heardFrom value:", formData.get("heardFrom"));
		}

		const newFormData = {
			firstname: formData.get("firstname") as string,
			lastname: formData.get("lastname") as string,
			email: formData.get("email") as string,
			password: formData.get("password") as string,
			confirmPassword: formData.get("confirmPassword") as string,
			company: formData.get("company") as string,
			teamsize: formData.get("teamsize") as string,
			subscribe: (formData.get("subscribe") as string) !== null,
			heardFrom: formData.get("heardFrom") as string,
			customHeardFrom: formData.get("customHeardFrom") as string,
		};

		setSignupFormData(newFormData);

		if (referrerCode) {
			formData.set("referrerCode", referrerCode);
		}

		// Reset error states before submission
		setReferralCodeError("");

		formAction(formData);
		logEvent(AuthEvent.SIGN_UP, {
			firstname: newFormData.firstname,
			lastname: newFormData.lastname,
			email: newFormData.email,
			company: newFormData.company,
			teamsize: newFormData.teamsize,
			subscribe: newFormData.subscribe,
			heardFrom: newFormData.heardFrom,
			customHeardFrom: newFormData.customHeardFrom,
		});
	};

	const fetchReferralCode = useCallback(async (code: string) => {
    try {
      const response = await fetch("/api/referral/user-by-referral-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ referralCode: code }),
      });
      if (!response.ok) {
        throw new Error("Failed to fetch referral code");
      }

      const isValid = await response.json();
      setReferralCodeError(isValid ? "Invalid referral code" : "");
    } catch (error) {
      setReferralCodeError("Invalid referral code");
      console.error("Error fetching referral code:", error);
    }
  }, []);

	// Create a function to reset the form state when typing in the email field
	const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		// Update the form data as usual
		const { name, value } = e.target;
		setSignupFormData((prev) => ({
			...prev,
			[name]: value,
		}));

		// If there was an error state, reset submission count to avoid showing toasts
		if (state.status !== "idle" && state.status !== "success") {
			// Reset submission count to 0 so the useEffect for toast messages won't trigger
			setSubmissionCount(0);
			// Reset error shown flag
			setErrorShown(false);
		}
	};

	const isFormValid =
		signUpFormData.firstname.trim() !== "" &&
		/^[A-Za-z\s-']+$/.test(signUpFormData.firstname.trim()) &&
		signUpFormData.lastname.trim() !== "" &&
		/^[A-Za-z\s-']+$/.test(signUpFormData.lastname.trim()) &&
		/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(signUpFormData.email.trim()) &&
		signUpFormData.company.trim() !== "" &&
		signUpFormData.teamsize.trim() !== "" &&
		passwordCriteria.minLength &&
		passwordCriteria.hasUppercase &&
		passwordCriteria.hasLowercase &&
		passwordCriteria.hasNumber &&
		passwordCriteria.hasSpecial &&
		signUpFormData.confirmPassword.trim() !== "" &&
		signUpFormData.password === signUpFormData.confirmPassword;
	// Removed nonAffiliationChecked condition

	if (isRedirecting) {
		return (
			<main className="relative flex md:h-dvh h-auto w-screen md:overflow-hidden overflow-auto">
				<section className="w-full md:w-1/2 flex items-center justify-center p-6 signBox signScoll">
					<article className="relative w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-6 bg-white dark:bg-slate-800 p-8 border border-gray-200 dark:border-gray-700 shadow-[0_8px_30px_rgba(0,0,0,0.12)] dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] animate-fade-in">
						<header className="flex flex-col items-center justify-center gap-2 text-center">
							<div className="mb-2">
								<LogoSignup
									size={140}
									mixBlendMode={
										resolvedTheme === "dark" ? "lighten" : "multiply"
									}
									isDark={resolvedTheme === "dark"}
								/>
							</div>
							<h1 className="text-4xl font-bold font-playfair text-[#4a2a75]">
								Verify Your Email
							</h1>
							<p className="text-slate-600 dark:text-slate-400 mt-2">
								Please verify your email address to complete account creation.
							</p>
						</header>
						<div className="animate-spin-slow flex justify-center">
							<div className="relative flex items-center justify-center">
								<LogoIqidis
									size={100}
									mixBlendMode={
										resolvedTheme === "dark" ? "lighten" : "multiply"
									}
									isDark={resolvedTheme === "dark"}
								/>
							</div>
						</div>
					</article>
				</section>

				<aside className="hidden md:flex md:w-1/2 bg-indigo-50 dark:bg-slate-900/50 flex-col items-center justify-center p-6 overflow-hidden">
					<div className="size-full max-w-lg">
						<TestimonialCarousel fullHeight={true} />
					</div>
				</aside>
			</main>
		);
	}

	return (
    <main className="relative flex h-full xl:overflow-hidden overflow-auto signContainer">
      {/* Left side - Signup form */}
      <section className="w-full md:w-1/2 min-h-screen flex items-center justify-center p-6 bg-[#f9f8fb] dark:bg-slate-800 signBox signScoll">
        <article className="relative w-full max-w-md overflow-hidden flex flex-col gap-6 dark:bg-slate-800 lg:p-8 md:p-6 p-4 dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] animate-fade-in bg-white/70 backdrop-blur-md border border-white/30 shadow-lg rounded-lg">
          <header className="flex flex-col items-center justify-center gap-1 text-center mb-2">
            <h1 className="text-4xl font-bold font-playfair from-primary to-accent/80 text-transparent bg-clip-text bg-gradient-to-r dark:text-[rgb(var(--base-navy))]">
              Start Your Iqidis Journey
            </h1>
          </header>

          <SignupForm
            action={handleSubmit}
            formData={signUpFormData}
            setFormData={setSignupFormData}
            passwordError={passwordError}
            firstNameError={firstNameError}
            lastNameError={lastNameError}
            emailError={emailError}
            companyError={companyError}
            teamsizeError={teamsizeError}
            confirmPasswordError={confirmPasswordError}
            onEmailChange={handleEmailChange}
            passwordCriteria={passwordCriteria}
            setReferrerCode={setReferrerCode}
            referrerCode={referrerCode}
            referralCodeError={referralCodeError}
            fetchReferralCode={fetchReferralCode}
          >
            <div className="flex flex-col gap-2 mt-2">
              <SubmitButton
                disabled={!isFormValid}
                isSuccessful={isSuccessful}
                className="bg-primary text-white font-medium py-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-200 
                dark:bg-white
                dark:text-purple-800
                dark:border
                dark:border-purple-300
                dark:hover:bg-purple-100"
              >
                {`Create Account ${referrerCode ? "With Referral" : ""}`}
              </SubmitButton>
              <div className="flex items-center justify-between">
                <hr className="flex-grow border-gray-300 dark:border-gray-700" />
                <span className="mx-2 text-gray-500 dark:text-gray-400">
                  or
                </span>
                <hr className="flex-grow border-gray-300 dark:border-gray-700" />
              </div>
              <Button
			  	type="button"
                onClick={() => {
                  if (referrerCode) {
                    localStorage.setItem("referralCode", referrerCode);
                  }
                  signIn("google", { callbackUrl: "/welcome" });
                }}
                variant={"secondary"}
                size={"sm"}
                disabled={referralCodeError !== ""}
              >
                <svg
                  className="mr-2 -ml-1 w-5 h-5"
                  viewBox="0 0 533.5 544.3"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill="#4285F4"
                    d="M533.5 278.4c0-18.5-1.5-37.1-4.7-55H272.1v104h146.9c-6.3 33.9-25 62.7-53.3 82.2l85.8 66.6c50.2-46.2 82-114.4 82-197.8z"
                  />
                  <path
                    fill="#34A853"
                    d="M272.1 544.3c71.7 0 131.8-23.8 175.7-64.9l-85.8-66.6c-23.9 16.1-54.4 25.6-89.9 25.6-69.1 0-127.6-46.6-148.6-109.1H35.2v68.4C78.9 479.9 168.1 544.3 272.1 544.3z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M123.5 328.3c-10.5-30.9-10.5-64 0-94.9V165H35.2C12.8 209.4 0 261.2 0 316.1s12.8 106.7 35.2 151.1l88.3-68.9z"
                  />
                  <path
                    fill="#EA4335"
                    d="M272.1 107.2c39.2 0 74.3 13.5 101.9 40.2l76.4-76.4C401.8 24.5 343.7 0 272.1 0 168.1 0 78.9 64.4 35.2 165l88.3 68.4c21-62.5 79.5-109.1 148.6-109.1z"
                  />
                </svg>
                Continue With Google
              </Button>
              <nav className="flex justify-center items-center text-sm mt-1">
                <span className="text-slate-600 dark:text-slate-400">
                  Already have an account?
                </span>
                <Link
                  href="/login"
                  className="text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium ml-1"
                >
                  Sign in
                </Link>
              </nav>
            </div>
          </SignupForm>
        </article>
      </section>
      {/* Right side - Testimonials */}
      <aside className="hidden md:flex md:w-1/2 bg-indigo-50 dark:bg-slate-900/50 flex-col items-center justify-center p-6 overflow-hidden">
        <div className="size-full max-w-lg">
          <TestimonialCarousel fullHeight={true} />
        </div>
      </aside>
    </main>
  );
}

// Main page component with Suspense
export default function Page() {
	return (
		<Suspense
			fallback={
				<div className="flex h-dvh w-screen items-center justify-center bg-background">
					<div className="flex flex-col items-center gap-6">
						<div className="animate-spin-slow">
							<div className="relative flex items-center justify-center">
								<LogoIqidis size={150} />
							</div>
						</div>
					</div>
				</div>
			}
		>
			<SignUpForm />
		</Suspense>
	);
}
