'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession, SessionProvider } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, AlertCircle } from 'lucide-react'
import { Logger } from '@/lib/utils/Logger'
import { toast } from 'sonner'

type Subscription = {
  id: string
  status: string
  planName: string
  startDate: string
  endDate: string | null
  autoRenew: boolean
  billingCycle: string | null
  isActive: boolean
  stripeSubscriptionStatus: string
  createdAt: string
  isAdminManaged: boolean
}

export default function SubscriptionDetailsPage() {
  return (
    <SessionProvider>
      <SubscriptionDetailsContent />
    </SessionProvider>
  )
}

function SubscriptionDetailsContent() {
  const router = useRouter()
  const { data: session } = useSession()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [cancelLoading, setCancelLoading] = useState(false)
  const [cancellingSubscriptionId, setCancellingSubscriptionId] = useState<string | null>(null)
  const [cancellationComplete, setCancellationComplete] = useState(false)

  useEffect(() => {
    const fetchSubscriptions = async () => {
      if (!session?.user?.id) return
      
      try {
        const res = await fetch('/api/query/get-user-subscriptions')
        if (!res.ok) {
          throw new Error('Failed to fetch subscription history')
        }
        
        const data = await res.json()
        // Sort subscriptions by creation date (newest first)
        const sortedData = [...data].sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        console.log('Subscription data:', sortedData)
        setSubscriptions(sortedData)
      } catch (error) {
        Logger.error('Error fetching subscriptions', error)
        setError(error instanceof Error ? error.message : 'Failed to load subscription details')
      } finally {
        setLoading(false)
      }
    }
    
    fetchSubscriptions()
  }, [session])

  const checkCancellationStatus = async (subscriptionId: string) => {
    try {
      const response = await fetch(`/api/stripe/check-cancellation?subscription_id=${subscriptionId}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to check cancellation status')
      }
      
      const data = await response.json()
      
      if (data.isCanceled) {
        setCancellationComplete(true)
        setCancellingSubscriptionId(null)
        
        // Refresh subscription data
        const updatedRes = await fetch('/api/query/get-user-subscriptions')
        if (updatedRes.ok) {
          const updatedData = await updatedRes.json()
          // Sort subscriptions by creation date (newest first)
          const sortedData = [...updatedData].sort((a, b) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          )
          setSubscriptions(sortedData)
        }
        
        return true
      }
      
      return false
    } catch (error) {
      Logger.error('Error checking cancellation:', error)
      setError(error instanceof Error ? error.message : 'Failed to verify cancellation')
      setCancellingSubscriptionId(null)
      return true // Stop polling on error
    }
  }

  const handleCancel = async () => {
    if (!session?.user?.id) return
    
    setCancelLoading(true)
    try {
      const subscriptionIdResponse = await fetch('/api/query/get-current-plan-stripe-subscription-id', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      })
      
      if (!subscriptionIdResponse.ok) {
        throw new Error('Failed to fetch subscription ID')
      }
      
      const stripeSubscriptionId = await subscriptionIdResponse.json()
      Logger.info('stripeSubscriptionId', stripeSubscriptionId)
      if (!stripeSubscriptionId) {
        throw new Error('Stripe Subscription ID not found')
      }
      
      const res = await fetch('/api/stripe/cancel-subscription', {
        method: 'POST',
        body: JSON.stringify({ subscriptionId: stripeSubscriptionId }),
        headers: { 'Content-Type': 'application/json' },
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to cancel subscription')
      }
      
      await res.json()
      
      // Start polling for cancellation status
      setCancellingSubscriptionId(stripeSubscriptionId)
      
      // Poll every 1 second until cancellation is confirmed
      const pollInterval = setInterval(async () => {
        const shouldStop = await checkCancellationStatus(stripeSubscriptionId)
        if (shouldStop) clearInterval(pollInterval)
      }, 1000)
      
      // Initial check
      checkCancellationStatus(stripeSubscriptionId)
      
      // Clear interval after 30 seconds as a safety measure
      setTimeout(() => clearInterval(pollInterval), 30000)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to cancel subscription')
    } finally {
      setCancelLoading(false)
    }
  }

  const handleUpgrade = () => {
    router.push('/subscription')
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getActiveSubscription = () => {
    return subscriptions.find(sub => sub.isActive)
  }

  const activeSubscription = getActiveSubscription()

  // Add debug log to check the subscription status
  if (activeSubscription) {
    console.log('Active subscription status:', {
      status: activeSubscription.status,
      planName: activeSubscription.planName,
      stripeSubscriptionStatus: activeSubscription.stripeSubscriptionStatus
    })
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" onClick={() => router.push('/account')}>
          <ArrowLeft className="h-4 w-4 mr-2" /> Back to Account
        </Button>
        <h1 className="text-2xl font-bold">Subscription Details</h1>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <span>{error}</span>
        </div>
      )}
      
      {loading ? (
        <div className="text-center py-8">Loading subscription details...</div>
      ) : (
        <>
          <div className="mb-8">
            <Card>
              <CardHeader>
                <CardTitle>Current Subscription</CardTitle>
                <CardDescription>Your active subscription details</CardDescription>
              </CardHeader>
              <CardContent>
                {activeSubscription ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Plan:</span>
                      <span className="font-bold">{activeSubscription.planName}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Status:</span>
                      <Badge variant={activeSubscription.status === 'active' ? 'default' : 'outline'}>
                        {activeSubscription.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Started:</span>
                      <span>{formatDate(activeSubscription.startDate)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Renews/Expires:</span>
                      <span>{formatDate(activeSubscription.endDate)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Auto-renew:</span>
                      <span>{activeSubscription.autoRenew ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Billing cycle:</span>
                      <span>{activeSubscription.billingCycle || 'N/A'}</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p>You don&apos;t have an active subscription.</p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-end space-x-4">
                {activeSubscription && 
                 activeSubscription.status === 'active' && 
                 activeSubscription.planName !== 'free' && 
                 !activeSubscription.isAdminManaged && 
                 activeSubscription.stripeSubscriptionStatus !== 'cancelled' &&
                 activeSubscription.stripeSubscriptionStatus !== 'canceled_at_period_end' ? (
                  <Button 
                    variant="destructive" 
                    onClick={handleCancel}
                    disabled={cancelLoading}
                  >
                    {cancelLoading ? 'Cancelling...' : 'Cancel Subscription'}
                  </Button>
                ) : null}

                {(!activeSubscription || activeSubscription.planName === 'free') && 
                 (!activeSubscription || !activeSubscription.isAdminManaged) && (
                  <Button onClick={handleUpgrade}>
                    Upgrade Plan
                  </Button>
                )}

                {activeSubscription?.isAdminManaged && (
                  <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded mb-4">
                    <p className="text-sm">
                      Your subscription is managed by an administrator. Please contact support for any changes.
                    </p>
                  </div>
                )}
              </CardFooter>
            </Card>
          </div>
          
          <div>
            <h2 className="text-xl font-semibold mb-4">Subscription History</h2>
            {subscriptions.length > 0 ? (
              <div className="space-y-4">
                {subscriptions.map((sub) => (
                  <Card key={sub.id} className={sub.isActive ? 'border-blue-500' : ''}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">{sub.planName}</CardTitle>
                        <Badge variant={sub.isActive ? 'default' : 'outline'}>
                          {sub.stripeSubscriptionStatus === 'canceled_at_period_end' ? 'Cancelled at period end' : sub.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>Started: {formatDate(sub.startDate)}</div>
                        <div>Ended/Renews: {formatDate(sub.endDate)}</div>
                        <div>Auto-renew: {sub.autoRenew ? 'Yes' : 'No'}</div>
                        <div>Billing: {sub.billingCycle || 'N/A'}</div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 bg-gray-50 rounded-lg">
                <p>No subscription history found.</p>
              </div>
            )}
          </div>
        </>
      )}
      {cancellingSubscriptionId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-xl font-bold mb-4">Processing Cancellation</h3>
            <div className="flex items-center justify-center mb-4">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
            </div>
            <p className="text-center">
              We&apos;re processing your cancellation request. This may take a few moments...
            </p>
          </div>
        </div>
      )}

      {cancellationComplete && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <h2 className="text-xl font-bold mb-2">Subscription Cancelled!</h2>
          <p>Your subscription has been cancelled and will remain active until the end of the current billing period.</p>
          <button 
            onClick={() => setCancellationComplete(false)} 
            className="mt-2 bg-green-500 text-white px-4 py-2 rounded"
          >
            Dismiss
          </button>
        </div>
      )}
    </div>
  )
}


