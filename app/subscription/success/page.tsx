'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { use } from 'react';
import { Logger } from '@/lib/utils/Logger';
import { PageLoader } from "@/components/ui/page-loader";

function LoadingState() {
  return <PageLoader message="Setting up your subscription..." />;
}

function SubscriptionSuccessContent() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParamsPromise = useSearchParams();
  
  // Use React's use() to unwrap the Promise
  const searchParams = useSearchParams();
  
  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    const isTrial = searchParams.get('isTrial') === 'true';
    const isPremium = searchParams.get('isPremium') === 'true';
    
    if (!sessionId) {
      setError('Missing session information');
      setLoading(false);
      return;
    }
    
    const checkSubscriptionStatus = async () => {
      try {
        const response = await fetch(`/api/stripe/check-subscription?session_id=${sessionId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to check subscription status');
        }
        
        const data = await response.json();
        
        // If checkout is complete and subscription is active in our database
        if (data.checkoutStatus === 'complete' && data.subscription?.status === 'active') {
          // Redirect to dashboard with success message
          let redirectUrl = '/?subscription=active';
          if (isTrial) redirectUrl = `/?isTrial=${isTrial}`;
          if (isPremium) redirectUrl = `/?isPremium=${isPremium}`;
          
          window.location.href = redirectUrl;
          return true;
        }
        
        return false;
      } catch (error) {
        Logger.error('Error checking subscription:', error);
        setError(error instanceof Error ? error.message : 'Failed to verify subscription');
        setLoading(false);
        return true; // Stop polling on error
      }
    };
    
    // Poll every 2 seconds until subscription is active or error occurs
    const pollInterval = setInterval(async () => {
      const shouldStop = await checkSubscriptionStatus();
      if (shouldStop) clearInterval(pollInterval);
    }, 2000);
    
    // Initial check
    checkSubscriptionStatus();
    
    // Cleanup
    return () => clearInterval(pollInterval);
  }, [searchParams, router]);
  
  return (
    <div className="container mx-auto py-10 text-center">
      <div>
        <h1 className="text-3xl font-bold mb-4">Subscription Successful!</h1>
        {loading ? (
          <>
            <p className="mb-6">Setting up your subscription...</p>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          </>
        ) : error ? (
          <p className="text-red-500 mb-6">{error}</p>
        ) : (
          <p className="mb-6">Your account has been upgraded. Redirecting to dashboard...</p>
        )}
      </div>
    </div>
  );
}

export default function SubscriptionSuccessPage() {
  return (
    <Suspense fallback={<LoadingState />}>
      <SubscriptionSuccessContent />
    </Suspense>
  );
}
