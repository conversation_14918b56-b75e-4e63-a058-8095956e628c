'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON> } from 'next/navigation'
import { useSession, SessionProvider } from 'next-auth/react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, AlertCircle } from 'lucide-react'
import { Logger } from '@/lib/utils/Logger'

type Subscription = {
  id: string
  status: string
  planName: string
  startDate: string
  endDate: string | null
  autoRenew: boolean
  billingCycle: string | null
  isActive: boolean
}

export default function SubscriptionManagementPage() {
  return (
    <SessionProvider>
      <SubscriptionContent />
    </SessionProvider>
  )
}

function SubscriptionContent() {
  const router = useRouter()
  const { data: session } = useSession()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [cancelSuccess, setCancelSuccess] = useState(false)

  useEffect(() => {
    const fetchSubscriptions = async () => {
      if (!session?.user?.id) return
      
      try {
        const res = await fetch('/api/query/get-user-subscriptions')
        if (!res.ok) {
          throw new Error('Failed to fetch subscription history')
        }
        
        const data = await res.json()
        setSubscriptions(data)
      } catch (error) {
        Logger.error('Error fetching subscriptions', error)
        setError(error instanceof Error ? error.message : 'Failed to load subscription details')
      } finally {
        setLoading(false)
      }
    }
    
    fetchSubscriptions()
  }, [session])

  const handleCancel = async (subscriptionId: string) => {
    if (!session?.user?.id) return
    
    try {
      const res = await fetch('/api/stripe/cancel-subscription', {
        method: 'POST',
        body: JSON.stringify({ subscriptionId }),
        headers: { 'Content-Type': 'application/json' },
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to cancel subscription')
      }
      
      await res.json()
      
      // Refresh subscription data
      const updatedRes = await fetch('/api/query/get-user-subscriptions')
      if (updatedRes.ok) {
        const updatedData = await updatedRes.json()
        setSubscriptions(updatedData)
      }
      
      setCancelSuccess(true)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to cancel subscription')
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getActiveSubscription = () => {
    return subscriptions.find(sub => sub.isActive)
  }

  const activeSubscription = getActiveSubscription()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" onClick={() => router.push('/account')}>
          <ArrowLeft className="h-4 w-4 mr-2" /> Back to Account
        </Button>
        <h1 className="text-2xl font-bold">Subscription Management</h1>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <span>{error}</span>
        </div>
      )}
      
      {cancelSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <h2 className="text-xl font-bold mb-2">Subscription Cancelled!</h2>
          <p>Your subscription has been cancelled and will remain active until the end of the current billing period.</p>
        </div>
      )}
      
      {loading ? (
        <div className="text-center py-8">Loading subscription details...</div>
      ) : (
        <>
          <div className="mb-8">
            <Card>
              <CardHeader>
                <CardTitle>Current Subscription</CardTitle>
                <CardDescription>Your active subscription details</CardDescription>
              </CardHeader>
              <CardContent>
                {activeSubscription ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Plan:</span>
                      <span className="font-bold">{activeSubscription.planName}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Status:</span>
                      <Badge variant={activeSubscription.status === 'active' ? 'default' : 'outline'}>
                        {activeSubscription.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Started:</span>
                      <span>{formatDate(activeSubscription.startDate)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Renews/Expires:</span>
                      <span>{formatDate(activeSubscription.endDate)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Auto-renew:</span>
                      <span>{activeSubscription.autoRenew ? 'Yes' : 'No'}</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p>You don&apos;t have an active subscription.</p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-end space-x-4">
                {activeSubscription && activeSubscription.status === 'active' && activeSubscription.planName !== 'free' ? (
                  <Button 
                    variant="destructive" 
                    onClick={() => handleCancel(activeSubscription.id)}
                    disabled={!activeSubscription.autoRenew || activeSubscription.planName === 'free'}
                  >
                    {!activeSubscription.autoRenew ? 'Already Cancelled' : 'Cancel Subscription'}
                  </Button>
                ) : null}
                <Button onClick={() => router.push('/subscription')}>
                  {activeSubscription && activeSubscription.planName !== 'free' ? 'Change Plan' : 'Subscribe'}
                </Button>
              </CardFooter>
            </Card>
          </div>
          
          <div>
            <h2 className="text-xl font-semibold mb-4">Subscription History</h2>
            {subscriptions.length > 0 ? (
              <div className="space-y-4">
                {subscriptions.map((sub) => (
                  <Card key={sub.id} className={sub.isActive ? 'border-blue-500' : ''}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">{sub.planName}</CardTitle>
                        <Badge variant={sub.isActive ? 'default' : 'outline'}>
                          {sub.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>Started: {formatDate(sub.startDate)}</div>
                        <div>Ended/Renews: {formatDate(sub.endDate)}</div>
                        <div>Auto-renew: {sub.autoRenew ? 'Yes' : 'No'}</div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 bg-gray-50 rounded-lg">
                <p>No subscription history found.</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}
