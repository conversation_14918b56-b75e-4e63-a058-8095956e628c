'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { Logger } from '@/lib/utils/Logger'

export function PaymentMethods() {
  const [loading, setLoading] = useState(false)
  const { data: session } = useSession()
  const userEmail = session?.user?.email || ''
  
  Logger.info('userEmail', userEmail);

  const handleManagePaymentMethods = async () => {
    try {
      setLoading(true)

      // Now proceed to customer portal
      const response = await fetch('/api/stripe/customer-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (!response.ok) {
        throw new Error('Failed to create portal session')
      }
      
      const { url } = await response.json()
      window.location.href = url
    } catch (error) {
      Logger.error('Error opening customer portal:', error)
      toast.error("Could not open payment methods. Please try again later.")
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment Methods</CardTitle>
        <CardDescription>Manage your saved payment methods</CardDescription>
      </CardHeader>
      <CardContent>
        <Button 
          onClick={handleManagePaymentMethods} 
          disabled={loading}
        >
          {loading ? 'Loading...' : 'Manage Payment Methods'}
        </Button>
      </CardContent>
    </Card>
  )
}





