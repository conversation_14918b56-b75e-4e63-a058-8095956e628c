import { CitationValidationService } from "@/lib/services/cite-check/CitationValidationService";
import { CITATION_TEST_CASES, CitationTestCase } from "@/lib/services/cite-check/test-cases";
import { Logger } from "@/lib/utils/Logger";
import { auth } from "@/app/(auth)/auth";
import { notFound } from "next/navigation";

export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface TestResult {
  testCase: CitationTestCase;
  claudeResult: {
    status: "Valid" | "Need Review";
    details: string;
    success: boolean;
  };
  perplexityResult: {
    status: "Valid" | "Need Review";
    details: string;
    success: boolean;
  };
}

export default async function CitationTestPage() {
  // Check if user is authenticated and is an admin
  const session = await auth();
  if (!session?.user || !(session.user as any).isAdmin) {
    return notFound();
  }
  
  const results = await runCitationValidationTests();
  console.log(results)
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Citation Validation Test Results</h1>
      
      <div className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">Summary</h2>
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <div className="text-3xl font-bold">{results.length}</div>
            <div className="text-sm text-gray-500">Total Test Cases</div>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <div className="text-3xl font-bold text-blue-600">
              {results.filter(r => r.claudeResult.success).length} 
              <span className="text-lg">
                ({((results.filter(r => r.claudeResult.success).length / results.length) * 100).toFixed(1)}%)
              </span>
            </div>
            <div className="text-sm text-gray-500">Claude Success Rate</div>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <div className="text-3xl font-bold text-purple-600">
              {results.filter(r => r.perplexityResult.success).length}
              <span className="text-lg">
                ({((results.filter(r => r.perplexityResult.success).length / results.length) * 100).toFixed(1)}%)
              </span>
            </div>
            <div className="text-sm text-gray-500">Perplexity Success Rate</div>
          </div>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
          <thead>
            <tr className="bg-gray-100 dark:bg-gray-800">
              <th className="py-3 px-4 text-left border-b">Citation</th>
              <th className="py-3 px-4 text-left border-b">Expected</th>
              <th className="py-3 px-4 text-left border-b">Type</th>
              <th className="py-3 px-4 text-left border-b">Notes</th>
              <th className="py-3 px-4 text-left border-b">Claude Result</th>
              <th className="py-3 px-4 text-left border-b">Perplexity Result</th>
            </tr>
          </thead>
          <tbody>
            {results.map((result, index) => (
              <tr key={index} className="border-b border-gray-200 dark:border-gray-700">
                <td className="py-3 px-4">{result.testCase.citation}</td>
                <td className="py-3 px-4">{result.testCase.validity}</td>
                <td className="py-3 px-4">{result.testCase.type}</td>
                <td className="py-3 px-4">{result.testCase.notes}</td>
                <td className={`py-3 px-4 ${result.claudeResult.success ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'}`}>
                  <div className="font-semibold">{result.claudeResult.status}</div>
                  <div 
                    className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer relative group"
                    title="Click to expand"
                  >
                    <div className="line-clamp-2">{result.claudeResult.details}</div>
                    <div className="hidden group-hover:block absolute left-0 top-full z-10 bg-white dark:bg-gray-800 p-4 rounded shadow-lg border border-gray-200 dark:border-gray-700 w-[400px] max-h-[300px] overflow-y-auto">
                      {result.claudeResult.details}
                    </div>
                  </div>
                </td>
                <td className={`py-3 px-4 ${result.perplexityResult.success ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'}`}>
                  <div className="font-semibold">{result.perplexityResult.status}</div>
                  <div 
                    className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer relative group"
                    title="Click to expand"
                  >
                    <div className="line-clamp-2">{result.perplexityResult.details}</div>
                    <div className="hidden group-hover:block absolute left-0 top-full z-10 bg-white dark:bg-gray-800 p-4 rounded shadow-lg border border-gray-200 dark:border-gray-700 w-[400px] max-h-[300px] overflow-y-auto">
                      {result.perplexityResult.details}
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

async function runCitationValidationTests(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  
  // Create a simple conversation context for testing
  const conversationSummary = "Discussion about important legal precedents and their implications in modern law.";
  const userQuery = "What are some landmark cases in American law and their significance?";
  
  console.log("Starting citation validation tests...");
  
  // Process test cases in batches
  const BATCH_SIZE = 4; // Smaller batch size for web context
  const batches = createBatches(CITATION_TEST_CASES, BATCH_SIZE);
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i+1}/${batches.length} with ${batch.length} citations`);
    
    // Create a generic response text that includes all citations in the batch
    const responseText = createBatchResponseText(batch);
    
    try {
      // Test with Claude
      const claudeResult = await CitationValidationService.validateCitations(
        conversationSummary,
        userQuery,
        responseText
      );
      
      // Test with Perplexity
      const perplexityResult = await CitationValidationService.validateCitationsWithPerplexity(
        conversationSummary,
        userQuery,
        responseText
      );
      
      // Process results for each citation in the batch
      for (const testCase of batch) {
        // Find the corresponding citation in the results
        const claudeCitation = findCitationInResults(claudeResult.citations, testCase.citation);
        const perplexityCitation = findCitationInResults(perplexityResult.citations, testCase.citation);
        
        // Determine if the validation was successful
        const claudeSuccess = 
          (testCase.validity === "Valid" && claudeCitation?.status === "Valid") ||
          (testCase.validity === "Invalid" && claudeCitation?.status === "Need Review");
          
        const perplexitySuccess = 
          (testCase.validity === "Valid" && perplexityCitation?.status === "Valid") ||
          (testCase.validity === "Invalid" && perplexityCitation?.status === "Need Review");
        
        results.push({
          testCase,
          claudeResult: {
            status: claudeCitation?.status || "Need Review",
            details: claudeCitation?.details || "No details provided",
            success: claudeSuccess
          },
          perplexityResult: {
            status: perplexityCitation?.status || "Need Review",
            details: perplexityCitation?.details || "No details provided",
            success: perplexitySuccess
          }
        });
      }
      
    } catch (error) {
      Logger.error(`Error testing batch ${i+1}:`, error);
      
      // Add failed tests to results
      for (const testCase of batch) {
        results.push({
          testCase,
          claudeResult: {
            status: "Need Review",
            details: `Test error: ${(error as Error).message}`,
            success: false
          },
          perplexityResult: {
            status: "Need Review",
            details: `Test error: ${(error as Error).message}`,
            success: false
          }
        });
      }
    }
    
    // Add a delay between batches to avoid rate limiting
    if (i < batches.length - 1) {
      console.log("Waiting between batches...");
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  return results;
}

// Helper function to create batches from an array
function createBatches<T>(array: T[], batchSize: number): T[][] {
  const batches: T[][] = [];
  for (let i = 0; i < array.length; i += batchSize) {
    batches.push(array.slice(i, i + batchSize));
  }
  return batches;
}

// Helper function to create a generic response text containing all citations in a batch
function createBatchResponseText(batch: CitationTestCase[]): string {
  let responseText = "Here are several landmark legal cases that have shaped American jurisprudence:\n\n";
  
  batch.forEach((testCase, index) => {
    responseText += `${index + 1}. ${testCase.citation}\n`;
    responseText += "   This case established important legal principles that continue to influence modern law.\n\n";
  });
  
  responseText += "\nThese cases represent foundational precedents in their respective areas of law. They demonstrate how the judicial system has evolved over time and continues to shape our understanding of legal rights and responsibilities.";
  
  return responseText;
}

// Helper function to find a citation in the results array
function findCitationInResults(citations: any[], citationText: string): any {
  // First try exact match
  let match = citations.find(c => c.citation === citationText);
  
  // If no exact match, try to find a citation that contains the key parts
  if (!match) {
    const caseName = citationText.split(',')[0].trim();
    const reporter = citationText.match(/\d+\s+[A-Za-z\.]+\s+\d+/)?.[0];
    
    match = citations.find(c => 
      c.citation.includes(caseName) && 
      (reporter ? c.citation.includes(reporter) : true)
    );
  }
  
  return match;
}


