
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { document, documentFolder } from "@/lib/db/schema";
import { and, desc, eq, isNull, ne, sql,   isNotNull} from "drizzle-orm";
import { NextResponse } from "next/server";

// check duplicate folder name
export async function POST(request: Request) {
  const { name, parentId, folderId } = await request.json()

  const session = await auth()
  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const existFolers = await db.select().from(documentFolder).where(
    and(
      eq(documentFolder.userId, session.user.id),
      eq(documentFolder.name, name),
      eq(documentFolder.parentFolderId, parentId),
      eq(documentFolder.status, 'NORMAL'),
      folderId ? ne(documentFolder.id, folderId) : undefined
    ))

  if (existFolers.length > 0) {
    return NextResponse.json({ duplicate: true })
  }

  return NextResponse.json({ duplicate: false })
}
