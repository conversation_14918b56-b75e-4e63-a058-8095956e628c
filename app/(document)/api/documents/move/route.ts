import { auth } from "@/app/(auth)/auth";
import { document, documentFolder } from "@/lib/db/schema";
import { db } from "@/lib/db";
import { NextResponse } from "next/server";
import { eq, inArray, and } from "drizzle-orm";

export async function POST(request: Request) {
  const body = await request.json()
  const { documentIds, folderId } = body

  const session = await auth()
  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  if (!folderId || !documentIds || documentIds?.length === 0) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 });
  }

  const documents = await db.select().from(document).where(
    and(
      inArray(document.id, documentIds),
      eq(document.uploadedBy, session.user.id)
    )
  )

  if (documents.length !== documentIds.length) {
    return NextResponse.json({ error: "Some documents are not found" }, { status: 404 });
  }

  const folder = await db.select().from(documentFolder).where(
    and(
      eq(documentFolder.id, folderId),
      eq(documentFolder.status, 'NORMAL'),
      eq(documentFolder.userId, session.user.id)
    )
  )

  if (folder.length === 0) {
    return NextResponse.json({ error: "Target folder not found, folder is not exist" }, { status: 404 });
  }

  await db.update(document).set({
    folderId: folderId,
  }).where(
    inArray(document.id, documentIds)
  ).returning({
    id: document.id,
  })

  return NextResponse.json({ success: true })
}
