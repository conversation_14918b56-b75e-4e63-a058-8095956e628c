import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { inArray, and, eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { document, artifact } from "@/lib/db/schema";
import { getSignedUrlToDownload } from "@/lib/utils/s3Object";
import { recordDocumentAudit } from "@/lib/services/docummentAudit";

export async function POST(request: Request) {
  const body = await request.json()
  const session = await auth()

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { documentIds } = body

  const documents = await db.select(
    {
      document,
      artifact,
    }
  ).from(document)
  .leftJoin(artifact, eq(document.artifactId, artifact.id))
  .where(
    and(
      inArray(document.id, documentIds),
      eq(document.uploadedBy, session.user.id),
      eq(document.artifactStatus, 'AVAILABLE')
    )
  )

  if (documents.filter(doc => doc.artifact?.storageKey).length === 0) {
    return NextResponse.json({ error: "No documents found" }, { status: 404 });
  }

  const result = await Promise.all(documents.filter(doc => doc.artifact?.storageKey).map(async (doc) => {
    const signedUrl = await getSignedUrlToDownload({
      key: doc.artifact?.storageKey ?? '',
      bucket: 'iqidis-artifact',
      mimeType: doc.artifact?.mime || 'application/pdf',
    })
    return {
      document: doc.document,
      downloadUrl: signedUrl,
      artifact: doc.artifact,
    }
  }))

  // Audit log for all downloads
  await recordDocumentAudit({
    entries: result.map(item => ({
      documentId: item.document.id,
      documentUrl: item.downloadUrl,
      fileSize: item.artifact?.sizeByte,
    })),
    additionalInfo: {
      userAgent: request.headers.get('user-agent'),
      ipAddress: request.headers.get('x-forwarded-for'),
    },
    userId: session.user.id,
    targetId: session.user.id, // or another appropriate target
    action: "DOWNLOAD",
  })

  return NextResponse.json({
    documents: result.map(({ artifact, ...rest }) => rest),
  })
}