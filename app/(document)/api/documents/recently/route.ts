import { getRecentlyDocumentsByUser, recordDocumentAudit } from "@/lib/services/docummentAudit";
import { auth } from "@/app/(auth)/auth";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const session = await auth();
  const { searchParams } = new URL(request.url);
  const fileName = searchParams.get("fileName") ?? undefined;

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const userId = session.user.id;
  const limit = parseInt(searchParams.get("limit") || "200");
  const offset = parseInt(searchParams.get("offset") || "0");

  const { documents, documentActionsMap } = await getRecentlyDocumentsByUser({
    userId,
    limit,
    offset,
    fileName,
  });
  return NextResponse.json({
    documents,
    documentActionsMap,
  });
}

// record document chat log
export async function POST(request: Request) {
  const session = await auth();
  const body = await request.json();
  const { documents, additionalInfo } = body;

  if (!session?.user || !session.user.id || documents.filter((doc: any) => doc._attachment_id).length === 0) {
    return NextResponse.json({}, { status: 200 });
  }
  const extraInfo = {
    userAgent: request.headers.get('user-agent'),
    ipAddress: request.headers.get('x-forwarded-for'),
  }
  await recordDocumentAudit({
    entries: documents.filter((doc: any) => doc._attachment_id).map((doc: any) => ({
      documentId: doc._attachment_id,
      targetId: doc._attachment_id,
      additionalInfo: {
        documentUrl: doc.url,
        ...additionalInfo,
      },
    })),
    additionalInfo: extraInfo,
    userId: session.user.id,
    result: 'SUCCESS',
    action: 'CHAT',
    errorMessage: '',
  })
  return NextResponse.json({}, { status: 200 });
}