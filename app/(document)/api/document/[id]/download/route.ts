import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { NextResponse } from "next/server";
import { eq, and } from "drizzle-orm";
import { artifact, document } from "@/lib/db/schema";
import { getSignedUrlToDownload } from "@/lib/utils/s3Object";
import { AuditAction, recordDocumentAudit } from "@/lib/services/docummentAudit";

type Params = Promise<{ id: string }>

export async function GET(request: Request, { params }: { params: Params }) {
  const { id } = await params
  const session = await auth()

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const recordInfo = {
    documentId: id,
    userId: session.user.id,
    targetId: id,
    additionalInfo: {
      userAgent: request.headers.get('user-agent'),
      ipAddress: request.headers.get('x-forwarded-for'),
    },
    action: 'DOWNLOAD' as AuditAction,
  }
  
  try {
    const result = await db.select({
      document,
      artifact,
    }).from(document).leftJoin(artifact, eq(document.artifactId, artifact.id)).where(
      and(
        eq(document.id, id),
        eq(document.uploadedBy, session.user.id),
        eq(document.artifactStatus, 'AVAILABLE')
      )
    ).limit(1)

    const data = result[0]
    if (result.length === 0 || !data.document || !data.artifact) {
      await recordDocumentAudit({
        entries: [
          {
            documentId: id,
            additionalInfo: {
              userAgent: request.headers.get('user-agent'),
              ipAddress: request.headers.get('x-forwarded-for'),
            },
          }
        ],
        userId: session.user.id,
        targetId: id,
        result: 'FAILED',
        errorMessage: 'Document not found',
      })
      return NextResponse.json({ error: "Document not found" }, { status: 404 });
    }
  
    const signedUrl = await getSignedUrlToDownload({
      key: data.artifact.storageKey,
      bucket: 'iqidis-artifact',
      mimeType: data.document.mime || 'application/pdf',
      otherOptions: {
        ResponseContentDisposition: `attachment; filename="${data.document.originalName}"`,
      }
    })
  
    if (signedUrl) {
      await recordDocumentAudit({
        ...recordInfo,
        entries: [
          {
            documentId: id,
            documentUrl: signedUrl,
            additionalInfo: {
              userAgent: request.headers.get('user-agent'),
              ipAddress: request.headers.get('x-forwarded-for'),
            },
          }
        ],
      })
    }
  
    return NextResponse.json({ 
      document: data.document,
      downloadUrl: signedUrl,
    });
  } catch (error) {
    await recordDocumentAudit({
      ...recordInfo,
      entries: [
        {
          documentId: id,
          additionalInfo: {
            userAgent: request.headers.get('user-agent'),
            ipAddress: request.headers.get('x-forwarded-for'),
          },
        }
      ],
      result: 'FAILED',
      errorMessage: 'Internal server error',
    })
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
