import { PendoProvider } from "@/app/pendo-provider";
import { SentryProvider } from "@/app/sentry-provider";
import { UserProvider } from '@/contexts/UserContext';
import { auth } from "../../(auth)/auth";
import { ConditionalSidebar } from "@/componentsV2/conditional-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";


export const experimental_ppr = true;

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  return (
    <UserProvider user={session?.user as any}>
      <PendoProvider user={session?.user}>
        <SentryProvider user={session?.user}>
          <SidebarProvider defaultOpen={false}>
            <div className="flex flex-1 overflow-hidden h-screen w-screen chat-background-light dark:chat-background-dark">
              <ConditionalSidebar user={session?.user as any} />
              <SidebarInset className="flex flex-1 overflow-hidden">
                {children}
              </SidebarInset>
            </div>
          </SidebarProvider>
        </SentryProvider>
      </PendoProvider>
    </UserProvider >
  );
}
