import { auth } from "@/app/(auth)/auth"
import { getArtifactByDocumentId } from "@/lib/db/queries"
import { AuditAction, recordDocumentAudit } from "@/lib/services/docummentAudit"
import { redirect } from "next/navigation"
import { headers } from "next/headers"
import { FileView } from "@/componentsV2/library/FileView"

export default async function LibraryPage({ params }: { params: Promise<{ document_id: string }> }) {
  const session = await auth()
  const headersList = await headers()

  if (!session || !session.user?.id) {
    redirect('/login')
  }

  const { document_id } = await params
  const { success, document, message } = await getArtifactByDocumentId(document_id, session.user.id)

  if (!success) {
    await recordDocumentAudit({
      entries: [
        {
          documentId: document_id,
          additionalInfo: {
            userAgent: headersList.get('user-agent') || '',
            ipAddress: headersList.get('x-forwarded-for') || 'unknown',
          },
        }
      ],
      userId: session.user.id,
      targetId: document_id,
      action: 'VIEW' as AuditAction,
      result: 'FAILED',
      errorMessage: message,
    })
    return <div>Error: {message}</div>
  }

  await recordDocumentAudit({
    entries: [
      {
        documentId: document_id,
        documentUrl: document?.downloadUrl || '',
        additionalInfo: {
          userAgent: headersList.get('user-agent') || '',
          ipAddress: headersList.get('x-forwarded-for') || 'unknown',
        },
      }
    ],
    userId: session.user.id,
    targetId: document_id,
    action: 'VIEW' as AuditAction,
  })

  return <FileView mime={document?.mime} downloadUrl={document?.downloadUrl} />
}