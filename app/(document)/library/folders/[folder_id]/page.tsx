import { FolderDetail } from '@/componentsV2/library/FolderDetail'
import { CommonHeader } from '@/componentsV2/common-header'

export default async function FolderPage({ params }: { params: Promise<{ folder_id: string }> }) {
  const { folder_id } = await params;

  return (
    <div className="h-full flex flex-col chat-background-light dark:chat-background-dark">
      <CommonHeader />
      <FolderDetail folderId={folder_id} />
    </div>
  );
}
