'use client';

import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import { useEffect } from 'react';

interface PHProviderProps {
  children: React.ReactNode;
}

export function PHProvider({ children }: PHProviderProps) {
  useEffect(() => {
    posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY as string, {
      api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST as string,
      capture_pageview: false
    });
  }, []);

  return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
}
