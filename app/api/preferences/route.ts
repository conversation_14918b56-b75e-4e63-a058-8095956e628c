import { auth } from '@/app/(auth)/auth';
import { db } from '@/lib/db';
import { userPreferences } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { user } from '@/lib/db/schema';

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const data = await req.json();
    console.log("Saving preferences:", data); // Add logging
    
    // First try to update existing preferences
    const result = await db
      .update(userPreferences)
      .set({
        ...data,
        updatedAt: new Date() // Ensure updatedAt is set
      })
      .where(eq(userPreferences.userId, session.user.id))
      .returning();

    // If no rows were updated, insert new preferences
    if (result.length === 0) {
      console.log("No existing preferences found, creating new record");
      await db
        .insert(userPreferences)
        .values({
          userId: session.user.id,
          ...data,
          updatedAt: new Date()
        });
    }

    // Also update relevant fields in the user table
    if (data.fullName || data.firmName || data.email) {
      const updateData: any = {};
      
      if (data.fullName) {
        const nameParts = data.fullName.split(' ');
        if (nameParts.length > 0) {
          updateData.firstname = nameParts[0];
          if (nameParts.length > 1) {
            updateData.lastname = nameParts.slice(1).join(' ');
          }
        }
      }
      
      if (data.firmName) {
        updateData.company = data.firmName;
      }
      
      // Only update if we have fields to update
      if (Object.keys(updateData).length > 0) {
        console.log("Updating user table with:", updateData);
        await db
          .update(user)
          .set(updateData)
          .where(eq(user.id, session.user.id));
      }
    }

    return new Response('Preferences updated', { status: 200 });
  } catch (error) {
    console.error('Error updating preferences:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// Add a GET endpoint to fetch preferences
export async function GET(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    console.log("Fetching preferences for user:", session.user.id);
    
    const preferences = await db
      .select()
      .from(userPreferences)
      .where(eq(userPreferences.userId, session.user.id))
      .limit(1);
    
    console.log("Found preferences:", preferences.length > 0 ? "yes" : "no");
    
    if (preferences.length === 0) {
      return new Response(JSON.stringify(null), { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return new Response(JSON.stringify(preferences[0]), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching preferences:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
