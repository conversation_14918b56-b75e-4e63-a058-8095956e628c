import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { user, subscription, plan } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { auth } from "@/app/(auth)/auth";
import { Logger } from '@/lib/utils/Logger';
import { EmailService } from "@/lib/email/service";
import { featureFlagsIntegration } from "@sentry/nextjs";

export async function POST(request: Request) {
  try {
    const session = await auth();
   
    if (!session?.user || !(session.user as any).isAdmin) {
      return new Response("Unauthorized", { status: 401 });
    }
    
    const { userId } = await request.json();
    
    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }
    
    // Get the premium plan
    const [premiumPlan] = await db
      .select()
      .from(plan)
      .where(eq(plan.name, "premium"))
      .limit(1);
    
    if (!premiumPlan) {
      return NextResponse.json({ error: "Premium plan not found" }, { status: 404 });
    }
    
    // Calculate trial end date (30 days from now)
    const trialEndsAt = new Date();
    trialEndsAt.setDate(trialEndsAt.getDate() + 30);
    
    // Update user's subscription tier
    await db
      .update(user)
      .set({
        subscriptionTier: "premium",
        nextSubscriptionTier: "free",
      })
      .where(eq(user.id, userId));
    
    // Get current active subscription
    const [activeSubscription] = await db
      .select()
      .from(subscription)
      .where(and(eq(subscription.userId, userId), eq(subscription.status, "active")));
    
    // If there's an active subscription, update it
    if (activeSubscription) {
      await db
        .update(subscription)
        .set({
          planId: premiumPlan.id,
          stripeSubscriptionStatus: "trialing",
          status: "active",
          autoRenew: false,
          updatedAt: new Date(),
          isAdminManaged: false,
          trialEndsAt: trialEndsAt,
          isTrialUsed: true,
          extendedTrial: true
        })
        .where(eq(subscription.id, activeSubscription.id));
    } else {
      // Create a new subscription
      await db.insert(subscription).values({
        userId,
        planId: premiumPlan.id,
        stripeSubscriptionStatus: "trialing",
        status: "active",
        startDate: new Date(),
        autoRenew: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        isAdminManaged: false,
        trialEndsAt: trialEndsAt,
        isTrialUsed: true,
        extendedTrial: true
      });
    }
    
    // Get user email to send confirmation
    const [userRecord] = await db.select().from(user).where(eq(user.id, userId));
    
    // Send confirmation email
    if (userRecord?.email) {
      await EmailService.sendTrialActivationEmail(userRecord.email);
    }
    
    Logger.info(`Admin activated 30-day trial for user ${userId}`);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error activating 30-day trial:", error);
    return NextResponse.json(
      { error: "Failed to activate trial" },
      { status: 500 }
    );
  }
}





