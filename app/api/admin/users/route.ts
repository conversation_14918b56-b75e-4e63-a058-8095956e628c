import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { getUser, createUser, deleteUser, getAllUsers } from "@/lib/db/queries";
import { db } from "@/lib/db";
import { user, userPreferences, subscription } from "@/lib/db/schema";
import { eq, and, desc } from "drizzle-orm";
import { hashSync, genSaltSync } from "bcrypt-ts";
import { generateUUID } from "@/lib/utils";
import jwt from 'jsonwebtoken';
import { EmailService } from '@/lib/email/service';


export async function GET() {
  const session = await auth();

  if (!session?.user || !(session.user as any).isAdmin) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    // Get all users
    const users = await db
      .select({
        id: user.id,
        email: user.email,
        isAdmin: user.isAdmin,
        subscriptionTier: user.subscriptionTier,
        heardFrom: user.heardFrom,
      })
      .from(user);

    // Get all active subscriptions in a single query
    const activeSubscriptions = await db
      .select({
        userId: subscription.userId,
        isAdminManaged: subscription.isAdminManaged,
        createdAt: subscription.createdAt,
        updatedAt: subscription.updatedAt,
        trialEndsAt: subscription.trialEndsAt,
        stripeSubscriptionStatus: subscription.stripeSubscriptionStatus
      })
      .from(subscription)
      .where(eq(subscription.status, "active"))
      .orderBy(desc(subscription.createdAt));

    // Create a map of userId to latest subscription
    const latestSubscriptionMap = new Map();
    activeSubscriptions.forEach(sub => {
      if (!latestSubscriptionMap.has(sub.userId) || 
          sub.createdAt > latestSubscriptionMap.get(sub.userId).createdAt) {
        latestSubscriptionMap.set(sub.userId, sub);
      }
    });

    // Combine user data with subscription data
    const usersWithSubscriptions = users.map(user => ({
      ...user,
      isAdminManaged: latestSubscriptionMap.get(user.id)?.isAdminManaged || false,
      subscriptionUpdatedAt: latestSubscriptionMap.get(user.id)?.updatedAt,
      trialEndsAt: latestSubscriptionMap.get(user.id)?.trialEndsAt,
      stripeSubscriptionStatus: latestSubscriptionMap.get(user.id)?.stripeSubscriptionStatus
    }));

    console.log("Users with subscription data:", usersWithSubscriptions);

    return NextResponse.json(usersWithSubscriptions);
  } catch (error) {
    console.error("Failed to fetch users:", error);
    return new Response("Failed to fetch users", { status: 500 });
  }
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user || !(session.user as any).isAdmin) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const { email, password, isAdmin, sendEmail } = await request.json();

    // Check if user already exists
    const existingUser = await getUser(email);
    if (existingUser.length > 0) {
      return new Response("User already exists", { status: 400 });
    }

    const referralCode = await generateUUID();

    // Create new user with email already verified
    const users = await createUser(
      email,
      referralCode,
      password,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      isAdmin
    );

    // Update the user to mark email as verified since it's admin-created
    if (users && users.length > 0) {
      await db
        .update(user)
        .set({ isEmailVerified: true })
        .where(eq(user.id, users[0].id));

      // Create initial preferences if needed
      await db
        .insert(userPreferences)
        .values({
          userId: users[0].id,
          email: email,
        })
        .onConflictDoNothing();
      
      if (sendEmail) {
        const token = jwt.sign({ 
            email,
            type: 'password_reset',
            exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24)
        },
          process.env.FORGET_PASSWORD_TOKEN_SECRET!
        );
      
        // Generate reset link and send email
        const resetLink = new URL(
          `/forgot-password/reset?email=${email}&token=${token}`,
          `${process.env.NODE_ENV === 'production' ? 'https' : 'http'}://${request.headers.get('host')}`
        ).toString();
        await EmailService.sendInviteEmail(email, resetLink);
      }
    }

    return new Response("User created successfully", { status: 201 });
  } catch (error) {
    return new Response("Failed to create user", { status: 500 });
  }
}

export async function DELETE(request: Request) {
  const session = await auth();

  if (!session?.user || !(session.user as any).isAdmin) {
    return new Response("Unauthorized", { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");

  if (!id) {
    return new Response("Missing user ID", { status: 400 });
  }

  try {
    await deleteUser(id);
    return new Response("User deleted successfully", { status: 200 });
  } catch (error) {
    return new Response("Failed to delete user", { status: 500 });
  }
}
