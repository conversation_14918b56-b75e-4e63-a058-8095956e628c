import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { user, subscription, plan } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { auth } from "@/app/(auth)/auth";
import { Logger } from '@/lib/utils/Logger';

export async function POST(request: Request) {
  try {
    const session = await auth();
   
     if (!session?.user || !(session.user as any).isAdmin) {
       return new Response("Unauthorized", { status: 401 });
     }
    
    const { userId, isPremium } = await request.json();
    
    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }
    
    // Get the premium plan
    const [premiumPlan] = await db
      .select()
      .from(plan)
      .where(eq(plan.name, isPremium ? "premium" : "free"))
      .limit(1);
    
    if (!premiumPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }
    
    // Update user's subscription tier
    await db
      .update(user)
      .set({
        subscriptionTier: isPremium ? "premium" : "free",
        nextSubscriptionTier: null,
      })
      .where(eq(user.id, userId));
    
    // Get current active subscription
    const [activeSubscription] = await db
      .select()
      .from(subscription)
      .where(and(eq(subscription.userId, userId), eq(subscription.status, "active")));
    
    // If there's an active subscription, update it
    if (activeSubscription) {
      await db
        .update(subscription)
        .set({
          planId: premiumPlan.id,
          status: "active",
          autoRenew: true,
          updatedAt: new Date(),
          isTrialUsed:true,
          // When upgrading to premium, set isAdminManaged to true
          // When downgrading to free, preserve the current isAdminManaged value
          ...(isPremium ? { isAdminManaged: true } : {isAdminManaged:false}),
          // Clear Stripe fields if downgrading to free
          ...(isPremium ? {} : {
            stripeSubscriptionId: null,
            stripeCustomerId: null,
            stripePriceId: null,
            stripeSubscriptionItemId: null,
            stripeSubscriptionStatus: null
          }),
          // When downgrading to free, set extendedTrial(30 day trial) to false
          ...(isPremium ? {} : {extendedTrial: false})
        })
        .where(eq(subscription.id, activeSubscription.id));
    } else {
      // Create a new subscription
      await db.insert(subscription).values({
        userId,
        planId: premiumPlan.id,
        status: "active",
        isTrialUsed: true,
        startDate: new Date(),
        autoRenew: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        isAdminManaged: isPremium ? true : false, 
      });
    }
    
    Logger.info(`Admin toggled user ${userId} premium status to ${isPremium}`);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error toggling premium status:", error);
    return NextResponse.json(
      { error: "Failed to update user subscription" },
      { status: 500 }
    );
  }
}



