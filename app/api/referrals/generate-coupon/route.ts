import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { db } from '@/lib/db';
import { user as userTable } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { stripe } from '@/lib/stripe';
import { Logger } from '@/lib/utils/Logger';

export async function POST(req: Request) {
  try {
    const userSession = await auth();
    
    if (!userSession?.user) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }
    
    const { amount } = await req.json();
    
    if (!amount || amount <= 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid credit amount' 
      }, { status: 400 });
    }
    
    // Get user details
    const [user] = await db
      .select()
      .from(userTable)
      .where(eq(userTable.email, userSession.user.email as string))
      .limit(1);
    
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'User not found' 
      }, { status: 404 });
    }
    
    // Verify user has enough credits
    if (user.totalReferralCredits < amount) {
      return NextResponse.json({ 
        success: false, 
        error: 'Insufficient credits' 
      }, { status: 400 });
    }
    
    // Create a coupon in Stripe
    const coupon = await stripe.coupons.create({
      amount_off: amount,
      currency: 'usd',
      duration: 'once',
      name: `Referral Credit - ${user.email}`,
      max_redemptions: 1,
    });
    
    // Create a promotion code for this coupon
    const promotionCode = await stripe.promotionCodes.create({
      coupon: coupon.id,
      code: `REF-${user.id.substring(0, 6)}-${Math.floor(Math.random() * 10000)}`,
      customer: user.stripeCustomerId || undefined,
      max_redemptions: 1,
      expires_at: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30 days
    });
    
    Logger.info(`Generated coupon code for user ${user.id}: ${promotionCode.code}`);
    
    return NextResponse.json({ 
      success: true, 
      couponCode: promotionCode.code 
    });
    
  } catch (error) {
    Logger.error(`Error generating coupon: ${error}`);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to generate coupon' 
    }, { status: 500 });
  }
}