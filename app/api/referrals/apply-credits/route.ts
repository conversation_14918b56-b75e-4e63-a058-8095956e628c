import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { db } from '@/lib/db';
import { user as userTable, referralCredit } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { stripe } from '@/lib/stripe';
import { Logger } from '@/lib/utils/Logger';

export async function POST(req: Request) {
  try {
    const userSession = await auth();
    
    if (!userSession?.user) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get user details
    const [user] = await db
      .select()
      .from(userTable)
      .where(eq(userTable.email, userSession.user.email as string))
      .limit(1);
    
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'User not found' 
      }, { status: 404 });
    }
    
    // Check if user has Stripe customer ID
    if (!user.stripeCustomerId) {
      return NextResponse.json({ 
        success: false, 
        error: 'No Stripe account found' 
      }, { status: 400 });
    }
    
    // Check if user has credits to apply
    if (user.totalReferralCredits <= 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'No credits available' 
      }, { status: 400 });
    }
    
    // Apply credits to Stripe balance
    await stripe.customers.createBalanceTransaction(
      user.stripeCustomerId,
      {
        amount: -user.totalReferralCredits, // Negative amount creates a credit
        currency: 'usd',
        description: 'Applied referral credits',
      }
    );
    
    // Mark all pending referral credits as applied
    await db
      .update(referralCredit)
      .set({ 
        appliedToStripe: true,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(referralCredit.referrerId, user.id),
          eq(referralCredit.appliedToStripe, false)
        )
      );
    
    // Reset user's total referral credits
    await db
      .update(userTable)
      .set({ totalReferralCredits: 0 })
      .where(eq(userTable.id, user.id));
    
    Logger.info(`Applied ${user.totalReferralCredits} credits to Stripe balance for user ${user.id}`);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Credits applied successfully' 
    });
    
  } catch (error) {
    Logger.error(`Error applying credits: ${error}`);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to apply credits' 
    }, { status: 500 });
  }
}