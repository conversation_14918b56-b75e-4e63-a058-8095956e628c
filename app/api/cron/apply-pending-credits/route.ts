import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { referralCredit, user } from "@/lib/db/schema";
import { eq, and, isNotNull } from "drizzle-orm";
import { Logger } from "@/lib/utils/Logger";
import { applyPendingReferralCredits } from "@/lib/db/queries";

// Force dynamic rendering for this route
export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
   

    Logger.info("Processing of pending referral credits");
    
    // Get all users with pending credits and valid Stripe customer IDs
    const usersWithPendingCredits = await db
      .select({
        id: user.id,
        stripeCustomerId: user.stripeCustomerId
      })
      .from(user)
      .innerJoin(
        referralCredit,
        and(
          eq(user.id, referralCredit.referrerId),
          eq(referralCredit.appliedToStripe, false)
        )
      )
      .where(isNotNull(user.stripeCustomerId))
      .groupBy(user.id, user.stripeCustomerId);
    
    let appliedCount = 0;
    let errorCount = 0;
    
    // Process each user's pending credits
    for (const userData of usersWithPendingCredits) {
      try {
        if (userData.stripeCustomerId) {
          await applyPendingReferralCredits(userData.id, userData.stripeCustomerId);
          appliedCount++;
          Logger.info(`Successfully applied pending credits for user ${userData.id}`);
        }
      } catch (error) {
        errorCount++;
        Logger.error(`Error processing credits for user ${userData.id}:`, error);
      }
    }
    
    Logger.info(`Completed processing of pending referral credits. Users processed: ${usersWithPendingCredits.length}, Successes: ${appliedCount}, Errors: ${errorCount}`);
    
    return NextResponse.json({ 
      success: true,
      stats: {
        usersProcessed: usersWithPendingCredits.length,
        successfullyProcessed: appliedCount,
        errors: errorCount
      }
    });
  } catch (error) {
    Logger.error("Error in pending credits cron job", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}













