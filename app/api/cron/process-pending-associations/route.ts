import { NextResponse } from "next/server";
import { processPendingDocumentAssociations } from "@/lib/db/queries";
import { Logger } from "@/lib/utils/Logger";

export const dynamic = "force-dynamic";
export const maxDuration = 300; // 5 minutes

export async function GET(request: Request) {
  try {
    // Verify this is a legitimate cron request
    // You might want to add authentication here
    const authHeader = request.headers.get("Authorization");
    if (!process.env.CRON_SECRET || authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    Logger.info("Starting processing of pending document associations");
    
    await processPendingDocumentAssociations();
    
    Logger.info("Completed processing of pending document associations");
    
    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error in pending associations cron job", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}