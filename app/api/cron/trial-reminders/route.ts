import { NextResponse } from "next/server";
import { sendExtendedTrialReminders } from "@/lib/tasks/trialReminders";
import { Logger } from "@/lib/utils/Logger";

export async function GET(request: Request) {
  try {
    // Verify this is a legitimate cron request
    const authHeader = request.headers.get("Authorization");
    if (!process.env.CRON_SECRET || authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    Logger.info("Starting extended trial reminder emails job");
    
    const reminderCount = await sendExtendedTrialReminders();
    
    Logger.info(`Completed sending extended trial reminder emails: ${reminderCount} emails sent`);
    
    return NextResponse.json({ 
      success: true,
      count: reminderCount
    });
  } catch (error) {
    Logger.error("Error in trial reminders cron job", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}