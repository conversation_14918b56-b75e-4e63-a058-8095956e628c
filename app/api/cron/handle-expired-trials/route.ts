import { NextResponse } from "next/server";
import { handleExpiredTrials } from "@/lib/tasks/handleExpiredTrials";
import { Logger } from "@/lib/utils/Logger";

export async function GET(request: Request) {
  try {
    // Verify this is a legitimate cron request
    const authHeader = request.headers.get("Authorization");
    if (!process.env.CRON_SECRET || authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    Logger.info("Starting expired trials handling job");
    
    const expiredCount = await handleExpiredTrials();
    
    Logger.info(`Completed handling expired trials: ${expiredCount} trials processed`);
    
    return NextResponse.json({ 
      success: true,
      count: expiredCount
    });
  } catch (error) {
    Logger.error("Error in expired trials cron job", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}