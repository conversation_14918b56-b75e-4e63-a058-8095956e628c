import { addReferralE<PERSON>ry, getUser, getUserByReferralCode, updateUserReferrerId } from "@/lib/db/queries";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const referralCode = body?.referralCode;
    const email = body?.email;
    if(referralCode) {
      const referralUserId = await getUserByReferralCode(referralCode);
      const [user] = await getUser(email);
      if (referralUserId && user) {
        const result = await updateUserReferrerId(user.id, referralUserId);   
        if(result.success) {
          await addReferralEntry(referralUserId, user.id);
          return NextResponse.json(true, { status: 200 });
        }
      }
      return NextResponse.json(false, { status: 200 });
    } else {
      return NextResponse.json(false, { status: 200 });
    }
  } catch (error) {
    console.error("Error fetching referral user:", error);
    return NextResponse.json(
      { message: "Failed to update referrer user" },
      { status: 500 }
    );
  }
}
