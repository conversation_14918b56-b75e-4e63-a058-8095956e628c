import { auth } from "@/app/(auth)/auth";
import { getReferralHistory } from "@/lib/db/queries";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await auth();

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const referrerId = session.user.id;
    const referrals = await getReferralHistory(referrerId);

    return NextResponse.json(referrals, { status: 200 });
  } catch (error) {
    console.error("Error fetching referral history:", error);
    return NextResponse.json(
      { message: "Failed to fetch referral history" },
      { status: 500 }
    );
  }
}
