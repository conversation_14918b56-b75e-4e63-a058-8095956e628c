import { getUserByReferralCode } from "@/lib/db/queries";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const referralCode = body?.referralCode;
    if(referralCode) {
      const referrerId = await getUserByReferralCode(referralCode);
      console.log("Referrer ID:", referrerId);
      // If referrerId is null or undefined, it means the referral code is invalid
      return NextResponse.json(referrerId ? false : true, { status: 200 });
    }
    return NextResponse.json(true, { status: 200 });
  } catch (error) {
    console.error("Error fetching referral user:", error);
    return NextResponse.json(
      { message: "Failed to fetch referral user" },
      { status: 500 }
    );
  }
}
