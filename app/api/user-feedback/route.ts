import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { feedback } from "@/lib/db/schema";
import { Logger } from "@/lib/utils/Logger";
import { desc } from "drizzle-orm";

export async function POST(request: Request) {
  try {
    // Authenticate the user
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const { featureType, rating, feedbackText, metadata } = await request.json();

    // Validate required fields
    if (!featureType || rating === undefined) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Insert feedback into database
    await db.insert(feedback).values({
      userId: session.user.id,
      userEmail: session.user.email || "",
      featureType,
      rating,
      feedbackText,
      metadata,
    });

    Logger.info("User feedback submitted", {
      userId: session.user.id,
      featureType,
      rating
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error submitting user feedback:", error);
    return NextResponse.json(
      { error: "Failed to submit feedback" },
      { status: 500 }
    );
  }
}

// GET handler to retrieve all feedback entries for admin dashboard
export async function GET() {
  try {
    // Authenticate the user and check admin status
    const session = await auth();
    if (!session?.user?.id || !(session.user as any).isAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Fetch all feedback entries ordered by creation date (newest first)
    const feedbackEntries = await db.select().from(feedback).orderBy(desc(feedback.createdAt));

    Logger.info("Admin retrieved feedback data", {
      userId: session.user.id,
      count: feedbackEntries.length
    });

    return NextResponse.json(feedbackEntries);
  } catch (error) {
    Logger.error("Error fetching feedback data:", error);
    return NextResponse.json(
      { error: "Failed to fetch feedback data" },
      { status: 500 }
    );
  }
}
