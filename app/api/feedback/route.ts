import { auth } from "@/app/(auth)/auth";
import { NextResponse } from "next/server";
import { EmailService } from "@/lib/email/service";
import { sendSlackFeedback } from "@/lib/analytics/slack";
import { Logger } from "@/lib/utils/Logger";
import { db } from "@/lib/db";
import { feedback } from "@/lib/db/schema";

const MAX_FILE_SIZE = 4.5 * 1024 * 1024; // 4.5MB
const MAX_TOTAL_SIZE = 4.5 * 1024 * 1024; // 4.5MB
const MAX_FILES = 5;

export async function POST(request: Request) {
  try {
    // Authenticate the request
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const type = formData.get('type') as string;
    const feedbackText = formData.get('feedback') as string;
    const chatUrl = formData.get('chatUrl') as string;
    
    // Validate required fields
    if (!type || !feedbackText) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Handle file uploads
    const files = formData.getAll('attachments') as File[];

    // Prepare attachments for email
    const emailAttachments = await Promise.all(
      files.map(async (file) => {
        const buffer = Buffer.from(await file.arrayBuffer());
        return {
          filename: file.name,
          content: buffer
        };
      })
    );

    // Store in feedback table
    try {
      await db.insert(feedback).values({
        userId: session.user.id as string,
        userEmail: session.user.email || "",
        featureType: type === 'problem' ? 'bug-report' : 'feature-request',
        rating: type === 'problem' ? 0 : 1,
        feedbackText,
        metadata: {
          chatUrl,
          attachmentCount: files.length,
          attachmentNames: files.map(file => file.name),
        },
      });
    } catch (error) {
      Logger.error('Failed to store feedback in database:', error);
    }

    // Send to Slack as well
    await sendSlackFeedback(
      type,
      feedbackText,
      session.user.email || 'No email provided',
      chatUrl,
      emailAttachments
    );
    // Send email notification with attachments
    const resp = await EmailService.sendFeedbackNotification({
      type,
      feedback: feedbackText,
      userEmail: session.user.email || 'No email provided',
      chatUrl,
      attachments: emailAttachments,
      createdAt: new Date().toISOString(),
    });
    if (!resp.success) {
      Logger.error('Failed to send feedback notification email:', resp.error);
      return NextResponse.json(
        { error: 'Failed to process feedback. Please try again later' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Feedback submitted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error processing feedback:', error);
    return NextResponse.json(
      { error: 'Failed to process feedback. Please try again later' },
      { status: 500 }
    );
  }
}
