import { NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { user } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { Logger } from "@/lib/utils/Logger";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's Stripe customer ID
    const [userData] = await db
      .select({ stripeCustomerId: user.stripeCustomerId })
      .from(user)
      .where(eq(user.id, session.user.id))
      .limit(1);

    if (!userData?.stripeCustomerId) {
      // User doesn't have a Stripe customer ID yet
      Logger.info(`User ${session.user.id} doesn't have a Stripe customer ID yet`);
      return NextResponse.json({ 
        balance: 0,
        credit: 0,
        hasStripeAccount: false
      });
    }

    // Get customer's balance
    const customer = await stripe.customers.retrieve(userData.stripeCustomerId);
    
    // Balance is in cents, negative means credit
    const balanceAmount = 'balance' in customer ? customer.balance || 0 : 0;
    
    // Convert to positive credit amount (if balance is negative)
    const creditAmount = balanceAmount < 0 ? -balanceAmount : 0;

    return NextResponse.json({ 
      balance: balanceAmount,
      credit: creditAmount,
      hasStripeAccount: true
    });
  } catch (error) {
    Logger.error("Error fetching Stripe balance:", error);
    return NextResponse.json({ error: "Failed to fetch balance" }, { status: 500 });
  }
}

