import { stripe } from '@/lib/stripe'
import { db } from '@/lib/db'
import { subscription } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'
import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth'
import { Logger } from '@/lib/utils/Logger'

export async function POST(req: Request) {
  try {
    // Verify user is authenticated
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { subscriptionId } = await req.json()

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    // Verify the subscription belongs to the current user
    const [userSubscription] = await db.select().from(subscription).where(eq(subscription.stripeSubscriptionId, subscriptionId)
    ).limit(1);

    // Get the user ID from the session
    const userId = session.user.id || (session.user as any).userId;

    if (!userSubscription || userSubscription.userId !== userId) {
      return NextResponse.json({ error: 'Subscription not found or does not belong to current user' }, { status: 403 });
    }

    // Cancel subscription at period end
    const canceled = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    })

    Logger.info(`Subscription ${subscriptionId} canceled at period end for user ${userId}`);

    return NextResponse.json({
      canceled: true,
      subscriptionId: subscriptionId,
      canceledAt: new Date().toISOString(),
      message: 'Your subscription has been canceled and will end at the current billing period'
    })
  } catch (error) {
    Logger.error('Error canceling subscription', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to cancel subscription'
    }, { status: 500 });
  }
}
