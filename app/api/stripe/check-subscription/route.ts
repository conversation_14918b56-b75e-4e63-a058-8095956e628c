import { NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { db } from '@/lib/db';
import { subscription } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { auth } from '@/app/(auth)/auth';
import { Logger } from '@/lib/utils/Logger';

export async function GET(req: Request) {
  try {
    // Get session ID from URL
    const url = new URL(req.url);
    const sessionId = url.searchParams.get('session_id');
    
    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }
    
    // Verify user is authenticated
    const userSession = await auth();
    if (!userSession?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Fetch the checkout session from Stripe
    const checkoutSession = await stripe.checkout.sessions.retrieve(sessionId);
    
    // Verify the session belongs to this user (using metadata)
    if (checkoutSession.metadata?.userId !== userSession.user.id) {
      return NextResponse.json({ error: 'Session does not belong to current user' }, { status: 403 });
    }
    
    // Check if subscription exists in our database
    let subscriptionData = null;
    
    if (checkoutSession.subscription) {
      const [userSubscription] = await db
        .select()
        .from(subscription)
        .where(eq(subscription.stripeSubscriptionId, checkoutSession.subscription as string))
        .limit(1);
      
      if (userSubscription) {
        subscriptionData = {
          status: userSubscription.status,
          stripeStatus: userSubscription.stripeSubscriptionStatus,
          planId: userSubscription.planId
        };
      }
    }
    
    return NextResponse.json({
      checkoutStatus: checkoutSession.status,
      subscription: subscriptionData,
      // Include any other relevant data from the checkout session
      paymentStatus: checkoutSession.payment_status,
      customerId: checkoutSession.customer
    });
    
  } catch (error) {
    Logger.error('Error checking subscription status:', error);
    return NextResponse.json({ error: 'Failed to check subscription status' }, { status: 500 });
  }
}