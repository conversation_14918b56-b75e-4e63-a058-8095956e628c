import { NextResponse } from "next/server";
import { getUser } from "@/lib/db/queries";
export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    if (!email ) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // User verification
    const [user] = await getUser(email);
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json( user?.heardFrom === 'google-login' ? false: true, { status: 200 });
  } catch (error) {
    console.error('Password reset error:', error);
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
