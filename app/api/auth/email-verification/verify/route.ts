import { NextResponse } from "next/server";
import { getUser, updateUserEmailVerification } from "@/lib/db/queries";
import jwt from 'jsonwebtoken';
import { EmailService } from '@/lib/email/service';
import { db } from "@/lib/db";
import { userPreferences } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function POST(req: Request) {
  try {
    const { email, token } = await req.json();

    if (!email || !token) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    try {
        const payload = jwt.verify(token, process.env.FORGET_PASSWORD_TOKEN_SECRET!) as {
        email: string;
        type: string;
        };

        // Validate token is for email verification and matches the email
        if (payload.type !== 'email_verification' || payload.email !== email) {
        return NextResponse.json({ error: "Invalid email link" }, { status: 400 });
        }
    } catch (error) {
        // Token verification failed (expired or invalid)
        return NextResponse.json({ error: "Invalid or expired email verification link" }, { status: 400 });
    }
    // User verification
    const users = await getUser(email);
    if (users.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    await updateUserEmailVerification(email);

    // Get user preferences to access user data using standard Drizzle query
    const userPrefsResults = await db
      .select()
      .from(userPreferences)
      .where(eq(userPreferences.email, email))
      .limit(1);

    const userPrefs = userPrefsResults.length > 0 ? userPrefsResults[0] : null;

    if (userPrefs) {
      // Update the email verification tag in Mailchimp
      await EmailService.updateEmailVerificationTag(email);
    }

    return NextResponse.json({ message: "Email verified successfully" }, { status: 200 });
  } catch (error) {
    console.error('Email verify error:', error);
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
