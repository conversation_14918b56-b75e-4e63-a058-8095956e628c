import { NextResponse } from "next/server";
import { getUser } from '@/lib/db/queries';
import { EmailService } from '@/lib/email/service';
import jwt from 'jsonwebtoken';

export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // Check if user exists
    const users = await getUser(email);
    if (users.length === 0) {
      // For security reasons, we still return success even if the email doesn't exist
      return NextResponse.json({ 
        message: "If an account exists with this email, you will not receive email verify instructions." 
      }, { status: 200 });
    }

    // Generate reset token using JWT
    const token = jwt.sign(
        { 
        email,
        type: 'email_verification',
        },
        process.env.FORGET_PASSWORD_TOKEN_SECRET!
    );
    
    // Generate verification link and send email
    const verificationLink = new URL(
      `/email-verification/verify?email=${email}&token=${token}`,
      `${process.env.NODE_ENV === 'production' ? 'https' : 'http'}://${req.headers.get('host')}`
    ).toString();
    const { success, error } = await EmailService.sendEmailVerification(email, verificationLink);

    if (!success) {
      console.error('Failed to send email verification email:', error);
      return NextResponse.json({ error: "Failed to send email verification instructions" }, { status: 500 });
    }

    return NextResponse.json({ 
      message: "If an account exists with this email, you will receive email verify instructions." 
    }, { status: 200 });
  } catch (error) {
    console.error('Email verification request error:', error);
    return NextResponse.json({ error: "An unexpected error occurred" }, { status: 500 });
  }
}
