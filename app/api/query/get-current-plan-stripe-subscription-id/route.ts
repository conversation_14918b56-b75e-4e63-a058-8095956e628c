import { Logger } from '@/lib/utils/Logger'
import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth'
import { db } from '@/lib/db'
import { user, subscription, plan } from '@/lib/db/schema'
import { eq, and } from 'drizzle-orm'
import { getUser } from '@/lib/db/queries'


export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    const users = await getUser(session.user.email as string)

    if (users.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const userId = users[0].id;
    const [userPlanSubscription] = await db
      .select()
      .from(subscription)
      .where(
        (and(
          eq(subscription.status, 'active'),
          eq(subscription.userId, userId)
        ))
      )
      .limit(1)

    if (!userPlanSubscription) {
      return NextResponse.json(
        { error: 'User plan and subscription not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(userPlanSubscription.stripeSubscriptionId)

  }
  catch (error) {
    Logger.error('Failed to fetch current plan and  subscription:', error)
    return NextResponse.json(
      { error: 'Failed to fetch  current plan and  subscription' },
      { status: 500 }
    )
  }
}