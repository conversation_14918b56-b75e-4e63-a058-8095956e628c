
import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth';
import { getUserActiveSubscription} from '@/lib/db/queries';


export async function GET(req: Request) {

    const userSession = await auth();

    if (!userSession?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const [activeSubscription] = await getUserActiveSubscription(userSession.user.id as string);
   

    if (!activeSubscription) {
      return NextResponse.json({ error: 'No active subscription found' }, { status: 404 });
    }
      
    return NextResponse.json(activeSubscription)

}
