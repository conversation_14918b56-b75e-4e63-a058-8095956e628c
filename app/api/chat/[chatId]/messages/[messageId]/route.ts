import { NextRequest } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { deleteMessageById, getMessageById } from "@/lib/db/queries";

// Hardcoded list of allowed chat IDs for admin-only testing
const _HARDCODED_CHAT_LIST = [
	"a1b2c3d4-e5f6-7890-abcd-ef1234567890", // Main markdown testing chat
	"markdown-test-chat-1",
	"markdown-test-chat-2",
	"markdown-test-chat-3",
];

// DELETE - Delete a specific message
export async function DELETE(
	_request: NextRequest,
	{ params }: { params: Promise<{ chatId: string; messageId: string }> },
) {
	try {
		const session = await auth();
		if (!session?.user?.isAdmin) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { chatId, messageId } = await params;

		// Check if chatId is in the hardcoded list
		if (!_HARDCODED_CHAT_LIST.includes(chatId)) {
			return new Response("Unauthorized", { status: 401 });
		}

		// Verify that the message belongs to the specified chat
		const [existingMessage] = await getMessageById({ id: messageId });
		if (!existingMessage || existingMessage.chatId !== chatId) {
			return new Response("Message not found or does not belong to this chat", {
				status: 404,
			});
		}

		await deleteMessageById({ id: messageId });

		return Response.json({ success: true });
	} catch (error) {
		console.error("Error deleting message:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
}
