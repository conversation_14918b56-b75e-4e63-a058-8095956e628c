import { NextRequest } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { deleteMessageById } from "@/lib/db/queries";

// DELETE - Delete a specific message
export async function DELETE(
	_request: NextRequest,
	{ params }: { params: Promise<{ chatId: string; messageId: string }> },
) {
	try {
		const session = await auth();
		if (!session?.user?.isAdmin) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { messageId } = await params;
		await deleteMessageById({ id: messageId });

		return Response.json({ success: true });
	} catch (error) {
		console.error("Error deleting message:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
}
