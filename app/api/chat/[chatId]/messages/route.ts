import { NextRequest } from "next/server";
import { auth } from "@/app/(auth)/auth";
import {
	deleteMessagesByChatId,
	getMessagesWithAttachmentsByChatId,
	saveMessages,
} from "@/lib/db/queries";

// Hardcoded list of allowed chat IDs for admin-only testing
const _HARDCODED_CHAT_LIST = [
	"markdown-test-chat-1",
	"markdown-test-chat-2",
	"markdown-test-chat-3",
];

// GET - Fetch messages for a chat
export async function GET(
	_request: NextRequest,
	{ params }: { params: Promise<{ chatId: string }> },
) {
	try {
		const session = await auth();
		if (!session?.user?.isAdmin) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { chatId } = await params;

		// Check if chatId is in the hardcoded list
		if (!_HARDCODED_CHAT_LIST.includes(chatId)) {
			return new Response("Unauthorized", { status: 401 });
		}

		const messages = await getMessagesWithAttachmentsByChatId({ id: chatId });

		return Response.json(messages);
	} catch (error) {
		console.error("Error fetching messages:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
}

// POST - Add a new message
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ chatId: string }> },
) {
	try {
		const session = await auth();
		if (!session?.user?.isAdmin) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { chatId } = await params;

		// Check if chatId is in the hardcoded list
		if (!_HARDCODED_CHAT_LIST.includes(chatId)) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { role, content } = await request.json();

		if (!role || !content) {
			return new Response("Role and content are required", { status: 400 });
		}

		if (role !== "user" && role !== "assistant") {
			return new Response("Role must be 'user' or 'assistant'", {
				status: 400,
			});
		}

		const newMessage = {
			id: crypto.randomUUID(),
			chatId,
			role,
			content,
			createdAt: new Date(),
			metadata: undefined, // Optional field
		};

		await saveMessages({ messages: [newMessage] });

		return Response.json({ success: true, message: newMessage });
	} catch (error) {
		console.error("Error adding message:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
}

// DELETE - Clear all messages for the chat
export async function DELETE(
	_request: NextRequest,
	{ params }: { params: Promise<{ chatId: string }> },
) {
	try {
		const session = await auth();
		if (!session?.user?.isAdmin) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { chatId } = await params;

		// Check if chatId is in the hardcoded list
		if (!_HARDCODED_CHAT_LIST.includes(chatId)) {
			return new Response("Unauthorized", { status: 401 });
		}

		await deleteMessagesByChatId({ chatId });

		return Response.json({ success: true });
	} catch (error) {
		console.error("Error clearing messages:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
}
