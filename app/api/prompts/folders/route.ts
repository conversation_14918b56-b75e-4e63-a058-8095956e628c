import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import {
  getPromptFolders,
  getSystemPromptFolders,
  getUserPromptFolders,
  createPromptFolder,
  updatePromptFolder,
  deletePromptFolder,
} from "@/lib/db/queries";
import { Logger } from "@/lib/utils/Logger";

/**
 * GET handler for retrieving prompt folders
 * Query params:
 * - type: "all" | "system" | "user" (default: "all")
 */
export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") || "all";

    let folders;
    switch (type) {
      case "system":
        folders = await getSystemPromptFolders();
        break;
      case "user":
        folders = await getUserPromptFolders(session.user.id);
        break;
      case "all":
      default:
        folders = await getPromptFolders(session.user.id);
        break;
    }

    return NextResponse.json(folders);
  } catch (error) {
    Logger.error("Error fetching prompt folders:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * POST handler for creating a new prompt folder
 * Body: { name: string }
 */
export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { name } = await request.json();
    if (!name) {
      return new Response("Name is required", { status: 400 });
    }

    try {
      const folder = await createPromptFolder({
        name,
        userId: session.user.id,
        isSystem: false, // Only admins can create system folders
      });

      return NextResponse.json(folder, { status: 201 });
    } catch (error: any) {
      // Check for specific error types
      if (error.message === "DUPLICATE_FOLDER_NAME") {
        return new Response("A folder with this name already exists. Please choose a different name", {
          status: 409 // Conflict status code
        });
      } else if (error.message === "USER_NOT_FOUND") {
        Logger.error(`Session user ID ${session.user.id} not found in database`);
        return new Response("User account issue. Please try logging out and back in.", {
          status: 400
        });
      } else if (error.code === '23503' && error.constraint_name === 'prompt_folders_user_id_User_id_fk') {
        // This is the foreign key constraint violation
        Logger.error(`Foreign key constraint violation for user ID ${session.user.id}`);
        return new Response("User account issue. Please try logging out and back in.", {
          status: 400
        });
      }

      // Re-throw other errors to be caught by the outer catch block
      throw error;
    }
  } catch (error) {
    Logger.error("Error creating prompt folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * PUT handler for updating a prompt folder
 * Body: { id: string, name: string }
 */
export async function PUT(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { id, name } = await request.json();
    if (!id || !name) {
      return new Response("ID and name are required", { status: 400 });
    }

    const folder = await updatePromptFolder({
      id,
      name,
      userId: session.user.id,
    });

    if (!folder) {
      return new Response("Folder not found or you don't have permission to update it", { status: 404 });
    }

    return NextResponse.json(folder);
  } catch (error) {
    Logger.error("Error updating prompt folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * DELETE handler for deleting a prompt folder
 * Query params:
 * - id: string (folder ID)
 */
export async function DELETE(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    if (!id) {
      return new Response("Folder ID is required", { status: 400 });
    }

    const folder = await deletePromptFolder({
      id,
      userId: session.user.id,
    });

    if (!folder) {
      return new Response("Folder not found or you don't have permission to delete it", { status: 404 });
    }

    return new Response("Folder deleted successfully", { status: 200 });
  } catch (error) {
    Logger.error("Error deleting prompt folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
