import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db/db";
import { promptFiles } from "@/lib/db/schema";
import { normalizeMimeType } from "@/lib/utils";
import { Logger } from "@/lib/utils/Logger";
import { createPrompt, sourceDocumentExists } from "@/lib/db/queries";
import { Readable } from "stream";


export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new Response("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;
    const { title, content, folderId, isFavorite, attachments } = await request.json();

    if (!title || !content) {
					return new Response("Title and content are required", {
						status: 400,
					});
				}

    // Debug logging
    Logger.info("Saving prompt with attachments", { 
      hasAttachments: attachments && attachments.length > 0,
      attachmentCount: attachments?.length || 0,
      firstAttachment: attachments?.[0]
    });

    

     // Create the prompt
      let prompt;
      try {
        prompt = await createPrompt({
          title,
          content,
          folderId, // This is now optional
          userId: userId as string, // Pass the userId for default folder creation
          isFavorite,
        });
      } catch (error) {
        if (error instanceof Error && error.message === "DUPLICATE_PROMPT_TITLE") {
          return new Response("A prompt with this title already exists in this folder. Please choose a different name.", { status: 409 });
        }
        throw error; // Re-throw other errors
      }
      
    // Process attachments if any
    if (attachments && attachments.length > 0) {
      await Promise.all(
        attachments.map(async (attachment: any) => {
          try {
            let documentId = attachment.document_id || attachment.id; // Use document_id or id if available
            // Validate document_id
            if (!documentId) {
              Logger.warn("Missing document_id in attachment", { attachment });
              return;
            }

            // Check if source document exists
            const documentExists = await sourceDocumentExists({ documentId: documentId });
            if (!documentExists) {
              Logger.warn("Source document not found", { documentId: documentId });
              return;
            }
            
            // Create prompt file record in database using the existing table
            Logger.info("attachment", attachment);
            await db.insert(promptFiles).values({
              promptId: prompt.id,
              fileName: attachment.name,
              fileUrl: attachment.url,
              fileType: normalizeMimeType(attachment.contentType),
              fileSize: attachment.size || null,
              uploadedAt: new Date(),
            });
            
            Logger.info("Created prompt file", {
													promptId: prompt.id,
													fileName: attachment.name,
													sourceDocumentId: documentId,
												});
          } catch (error) {
            Logger.error("Error creating prompt file", { 
              error, 
              documentId: attachment.document_id || attachment.id,
              promptId: prompt.id
            });
          }
        })
      );
    }

    return Response.json({ success: true, promptId: prompt.id });
  } catch (error) {
    Logger.error("Error saving prompt with attachments", { error });
    return new Response("Internal Server Error", { status: 500 });
  }
}













