import { NextResponse } from 'next/server';
import { PromptArchitectService } from '@/lib/services/rag/PromptArchitectService';
import { Logger } from '@/lib/utils/Logger';
import { auth } from '@/app/(auth)/auth';

export async function POST(req: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { promptGoal, textReferences, fileNames = [] } = body;

    // Validate required fields
    if (!promptGoal) {
      return NextResponse.json({ error: 'Prompt goal is required' }, { status: 400 });
    }

    // Get conversation history from the request
    const conversationHistory = body.conversationHistory || '';

    // Generate the refined prompt
    const refinedPrompt = await PromptArchitectService.generateRefinedPrompt(
      promptGoal,
      textReferences || '',
      conversationHistory
    );

    // Return the refined prompt
    return NextResponse.json({ refinedPrompt });
  } catch (error) {
    Logger.error('Error refining prompt:', error);
    return NextResponse.json(
      { error: 'Failed to refine prompt' },
      { status: 500 }
    );
  }
}
