import { NextRequest, NextResponse } from "next/server";
import { OpenAI } from "openai";

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

    const response = await openai.audio.transcriptions.create({
      file: file as any, // 'file' must be a Blob/File in Node 18+ runtime
      model: "whisper-1",
    });

    return NextResponse.json({ text: response.text });
  } catch (error: any) {
    console.error("Whisper error:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
