import { NextRequest, NextResponse } from "next/server";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { db } from "@/lib/db";
import { sourceDocuments } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { Logger } from "@/lib/utils/Logger";

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get("id");

    if (!fileId) {
      return NextResponse.json(
        { error: "File ID is required" },
        { status: 400 }
      );
    }

    // Get file details from database
    const document = await db
      .select()
      .from(sourceDocuments)
      .where(eq(sourceDocuments.id, fileId))
      .limit(1)
      .execute()
      .then((rows) => rows[0]);

    if (!document) {
      return NextResponse.json({ error: "File not found" }, { status: 404 });
    }

    // Check if it's a Vercel Blob URL
    if (
      document.url.includes("vercel-storage.com") ||
      document.url.includes("blob.vercel-storage.com")
    ) {
      // For Vercel Blob, we can return the URL directly if it's public
      // Or generate a signed URL if needed
      return NextResponse.json({ url: document.url });
    }

    // Extract the correct bucket name from the URL for S3
    let bucketName = "";
    let key = "";

    try {
      // Parse the document URL
      const urlObj = new URL(document.url);

      // Extract bucket name from hostname (e.g., "iqidis-ai-prime-uploads-dev.s3.us-east-1.amazonaws.com")
      const hostnameParts = urlObj.hostname.split(".");
      if (hostnameParts[1] === "s3") {
        bucketName = hostnameParts[0];
      } else {
        // Use environment variable as fallback
        bucketName = process.env.AWS_UPLOAD_BUCKET_NAME || "";
      }

      // Extract key from pathname (e.g., "/uploads/5d0c2480-e1b5-4292-ab3e-721a41794b45.jpg")
      key = urlObj.pathname.replace(/^\/+/, "");

      Logger.info("Generating signed URL", {
        bucketName,
        key,
        originalUrl: document.url,
      });
    } catch (error) {
      Logger.error("Error parsing document URL", { url: document.url, error });
      return NextResponse.json(
        { error: "Invalid document URL format" },
        { status: 400 }
      );
    }

    // Create command to get the object
    const commandInput = {
      Bucket: bucketName,
      Key: key,
      // ResponseContentType: document.mime,
      ResponseContentType: 'application/octet-stream',
      ResponseContentDisposition: 'inline',
    }
    if (document.filename) {
      const extension = document.filename.split('.').pop()?.toLowerCase();
      const mimeTypes: Record<string, string> = {
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'txt': 'text/plain',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt': 'application/vnd.ms-powerpoint',
        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
      };
      commandInput.ResponseContentType = mimeTypes[extension || ''] || 'application/octet-stream';
    }
    
    const command = new GetObjectCommand(commandInput);

    // Generate signed URL (valid for 15 minutes)
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 300 });

    return NextResponse.json({ url: signedUrl, mime: commandInput.ResponseContentType });
  } catch (error) {
    Logger.error("Error generating signed URL", error);
    return NextResponse.json(
      { error: "Failed to generate file access URL" },
      { status: 500 }
    );
  }
}
