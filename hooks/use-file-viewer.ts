import { useState, useCallback } from "react";
import { toast } from "sonner";
import { Logger } from "@/lib/utils/Logger";

export function useFileViewer() {
  const [isLoading, setIsLoading] = useState(false);

  const viewFile = useCallback(async (fileId: string, url: string) => {
    setIsLoading(true);

    try {
      Logger.info("Viewing file", { fileId });

      // Check if URL is a protected URL (S3 or other private storage)
      const isProtectedUrl =
        url.includes("s3.amazonaws.com") || url.includes("amazonaws.com");

      // Check if it's a Vercel Blob URL
      const isVercelBlobUrl =
        url.includes("blob.vercel-storage.com") ||
        url.includes("vercel-storage.com");

      if (isProtectedUrl) {
        // Get a signed URL for protected S3 files
        const response = await fetch(`/api/files/view?id=${fileId}`);

        if (!response.ok) {
          throw new Error("Failed to get file access");
        }

        const data = await response.json();
        window.open(`/library/artifact/view?url=${encodeURIComponent(data.url)}&mime=${encodeURIComponent(data.mime)}`, "_blank");
      } else if (isVercelBlobUrl) {
        // For Vercel Blob URLs, they might be public or need special handling
        // If they're public, open directly; if not, try to get a signed URL
        try {
          // First try direct access
          window.open(url, "_blank");
        } catch (error) {
          // If direct access fails, try to get a signed URL
          const response = await fetch(`/api/files/view?id=${fileId}`);

          if (!response.ok) {
            throw new Error("Failed to get file access");
          }

          const data = await response.json();
          window.open(data.url, "_blank");
        }
      } else {
        // For public URLs, open directly
        window.open(url, "_blank");
      }
    } catch (error) {
      Logger.error("Error viewing file", error);
      toast.error("Could not open file. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  return { viewFile, isLoading };
}
