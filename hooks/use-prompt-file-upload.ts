import { useState, useCallback } from "react";
import { generateUUID, validatePromptFileType } from "@/lib/utils";
import { toast } from "sonner";
import { Logger } from "@/lib/utils/Logger";
import { genSignature } from "@/lib/utils/auth";

// This hook provides file upload functionality that exactly matches
// the implementation used by the chat component
export function usePromptFileUpload(chatId: string) {
  const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);
  const [uploadedFiles, setUploadedFiles] = useState<Array<any>>([]);

  const FILE_SIZE_LIMIT = 50 * 1024 * 1024;

  const uploadFile = async (file: File) => {
    try {
      Logger.info("Prompt Architect: Starting upload", {
        name: file.name,
        size: file.size,
        type: file.type,
        chatId,
      });

      // Validate file type first
      const validation = validatePromptFileType(file);
      if (!validation.isValid) {
        toast.error(validation.error || "Unsupported file type");
        Logger.warn("Prompt Architect: File type validation failed", {
          fileName: file.name,
          fileType: file.type,
          error: validation.error,
        });
        return undefined;
      }

      // Choose upload method based on file size
      if (file.size <= FILE_SIZE_LIMIT) {
        return await uploadFileClientSide(file);
      } else {
        toast.error("File size exceeds current limit of 50 MB");
        return undefined;
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      toast.error(`Upload failed: ${errorMessage}`);
      Logger.error("Prompt Architect: Upload error", {
        error,
        fileName: file.name,
        fileType: file.type,
      });
      return undefined;
    }
  };

  const uploadFileClientSide = async (file: File) => {
    Logger.info("Prompt Architect: Using client-side upload", { name: file.name });

    try {
      // Generate document ID on the client side - this is critical for proper file association
      const documentId = generateUUID();

      // Prepare for S3 upload
      const { timestamp, signature } = genSignature();

      // Request presigned URL from API
      const prepareResponse = await fetch(`/api/files/prepare`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: signature,
          "x-requested-time": timestamp.toString(),
        },
        body: JSON.stringify({
          files: [
            {
              mime: file.type,
              metadata: {
                name: file.name,
                size: file.size,
              },
              multipart: file.size > 10 * 1024 * 1024, // Using multipart for files > 10MB
            },
          ],
          channel: "core",
          partitionId: "core",
          chatId,
        }),
      });

      if (!prepareResponse.ok) {
        throw new Error(`Failed to prepare upload: ${prepareResponse.status}`);
      }

      const responseData = await prepareResponse.json();
      Logger.debug("Prepare response:", responseData);

      // Handle different response formats
      // The API might return data directly or nested in a data property
      let data;

      if (responseData.data) {
        // If response has a data property, use it
        data = responseData.data;
      } else if (Array.isArray(responseData)) {
        // If response is directly an array
        data = responseData;
      } else {
        // If response is an object with direct properties
        data = [responseData];
      }

      if (!data || !Array.isArray(data) || data.length === 0) {
        throw new Error("Invalid response format from prepare endpoint");
      }

      Logger.debug("Parsed data for upload:", data[0]);

      // Check if multipart upload is needed
      if (data[0].multipart) {
        const { documentId, bucket, key, uploadId } = data[0];

        // Upload process for multipart uploads
        const CHUNK_SIZE = 9 * 1024 * 1024;
        const chunks = Math.ceil(file.size / CHUNK_SIZE);
        const parts = new Array(chunks);

        // Create an array of upload functions - one for each part
        const uploadFunctions = Array.from({ length: chunks }, (_, i) => {
          const partNumber = i + 1;

          return async () => {
            // Get presigned URL for this part
            const partResponse = await fetch("/api/files/upload-part", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ bucket, key, uploadId, partNumber }),
            });

            if (!partResponse.ok) {
              throw new Error(`Failed to get part URL: ${partResponse.status}`);
            }

            const { url: partUrl } = await partResponse.json();

            // Prepare the chunk
            const start = (partNumber - 1) * CHUNK_SIZE;
            const end = Math.min(start + CHUNK_SIZE, file.size);
            const chunk = file.slice(start, end);

            // Upload the chunk
            const uploadResponse = await fetch(partUrl, {
              method: "PUT",
              body: chunk,
            });

            if (!uploadResponse.ok) {
              throw new Error(
                `Failed to upload part: ${uploadResponse.status}`
              );
            }

            const etag = uploadResponse.headers.get("ETag");
            if (!etag) {
              throw new Error("No ETag received for uploaded part");
            }

            return { ETag: etag, PartNumber: partNumber };
          };
        });

        // Execute uploads with concurrency control
        const MAX_CONCURRENT_UPLOADS = 3;
        const executeWithConcurrency = async (
          funcs: any[],
          maxConcurrent: number
        ) => {
          const results = [];
          for (let i = 0; i < funcs.length; i += maxConcurrent) {
            const batch = funcs.slice(i, i + maxConcurrent);
            const batchResults = await Promise.all(batch.map((f) => f()));
            results.push(...batchResults);
          }
          return results;
        };

        const uploadResults = await executeWithConcurrency(
          uploadFunctions,
          MAX_CONCURRENT_UPLOADS
        );

        // Complete the multipart upload
        const completeResponse = await fetch("/api/files/complete-multipart", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            bucket,
            key,
            uploadId,
            parts: uploadResults,
          }),
        });

        if (!completeResponse.ok) {
          throw new Error(
            `Failed to complete upload: ${completeResponse.status}`
          );
        }

        const { url: finalUrl } = await completeResponse.json();

        toast.success(`Document Processed for Analysis`);

        const result = {
          url: finalUrl,
          name: file.name, // Use the original file name instead of extracting from key
          contentType: file.type,
          document_id: documentId,
          uploaded: true,
        };

        Logger.info("Prompt Architect: Multipart upload completed", {
          fileName: file.name,
          fileType: file.type,
          documentId,
        });

        return result;
      } else {
        // Standard upload with presigned URL
        const { url: uploadUrl, documentId } = data[0];

        // Upload directly to S3 using presigned URL
        const uploadResponse = await fetch(uploadUrl, {
          method: "PUT",
          body: file,
          headers: {
            "Content-Type": file.type,
          },
        });

        if (!uploadResponse.ok) {
          throw new Error(`Failed to upload to S3: ${uploadResponse.status}`);
        }

        toast.success(`Document Processed for Analysis`);

        // Return the file data in the format expected by the chat component
        const baseUrl = uploadUrl.split("?")[0];
        const result = {
          url: baseUrl,
          name: file.name, // Use the original file name instead of extracting from URL
          contentType: file.type,
          document_id: documentId,
          uploaded: true,
        };

        Logger.info("Prompt Architect: Standard upload completed", {
          fileName: file.name,
          fileType: file.type,
          documentId,
        });

        return result;
      }
    } catch (error) {
      Logger.error("Prompt Architect: Client-side upload failed", error);
      throw Error("Please try a different file, or contact support.");
    }
  };

  const handleFileUpload = useCallback(
    async (files: File[]) => {
      Logger.info("Prompt Architect: Starting file upload process");

      // Validate all files first
      const validFiles: File[] = [];
      const invalidFiles: string[] = [];

      for (const file of files) {
        const validation = validatePromptFileType(file);
        if (validation.isValid) {
          validFiles.push(file);
        } else {
          invalidFiles.push(`${file.name}: ${validation.error}`);
        }
      }

      // Show errors for invalid files
      if (invalidFiles.length > 0) {
        invalidFiles.forEach(error => toast.error(error));
      }

      // Only proceed with valid files
      if (validFiles.length === 0) {
        Logger.warn("Prompt Architect: No valid files to upload");
        return [];
      }

      setUploadQueue(validFiles.map((file) => file.name));

      try {
        Logger.info("Prompt Architect: Initiating upload promises");
        const uploadPromises = validFiles.map((file) => uploadFile(file));
        const uploadedAttachments = await Promise.all(uploadPromises);
        Logger.info(
          "Prompt Architect: All uploads completed",
          uploadedAttachments
        );

        const successfullyUploadedAttachments = uploadedAttachments.filter(
          (attachment) => attachment !== undefined
        ) as Array<any>;

        setUploadedFiles((currentAttachments) => [
          ...currentAttachments,
          ...successfullyUploadedAttachments,
        ]);
        return successfullyUploadedAttachments;
      } catch (error) {
        Logger.error("Prompt Architect: Upload process failed", error);
        toast.error("Error uploading files");
        return [];
      } finally {
        Logger.info("Prompt Architect: Upload process finished");
        setUploadQueue([]);
      }
    },
    [chatId]
  );

  const clearUploadedFiles = useCallback(() => {
    setUploadedFiles([]);
  }, []);

  const removeUploadedFile = useCallback((fileToRemove: any) => {
    setUploadedFiles((currentFiles) =>
      currentFiles.filter((file) => file.url !== fileToRemove.url)
    );
  }, []);

  return {
    uploadQueue,
    uploadedFiles,
    handleFileUpload,
    clearUploadedFiles,
    removeUploadedFile,
  };
}
