import { useEffect, useState, useRef } from 'react';
import { Logger } from '@/lib/utils/Logger';
import { ExtendedMessage } from '@/lib/types';

export function useAutoResume({
  chatId,
  messages,
  isAutoResumeEnabled,
  setIsThinking,
  setMessages,
  isCurrentlyThinking = false,
  setIsGotMessageContent: setHideThinkingFlag,
}: {
  chatId: string;
  messages: ExtendedMessage[];
  isAutoResumeEnabled: boolean;
  setIsThinking: (isThinking: boolean) => void;
  setMessages: (messages: ExtendedMessage[] | ((prev: ExtendedMessage[]) => ExtendedMessage[])) => void;
  isCurrentlyThinking?: boolean;
  setIsGotMessageContent: (hideThinking: boolean) => void;
}) {
  const [isPolling, setIsPolling] = useState(false);
  const [enableResumeThinking, setEnableResumeThinking] = useState(false);
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pollCountRef = useRef(0);
  const noProcessingCountRef = useRef(0);
  const isThinkingForResumeRef = useRef(false);
  
  // Create a ref to track the latest value of isCurrentlyThinking without causing re-renders.
  const isCurrentlyThinkingRef = useRef(isCurrentlyThinking);
  useEffect(() => {
    isCurrentlyThinkingRef.current = isCurrentlyThinking;
  }, [isCurrentlyThinking]);

  const MAX_POLLS = 40; // ~5 minutes with 7.5s interval
  const MIN_POLLS = 2; // Minimum polls before giving up if no processing detected

  // Add a utility function to manage both thinking states together
  const updateThinkingStates = (isThinking: boolean) => {
    setIsThinking(isThinking);
    setHideThinkingFlag(!isThinking); // Inverse relationship
    setEnableResumeThinking(isThinking);
  };

  // Check if the last message is from the user and we're not currently thinking
  const shouldAttemptResume = () => {
    if (!messages.length) return false;
    // Use the ref here to prevent dependency issues
    if (isCurrentlyThinkingRef.current || isThinkingForResumeRef.current) return false;
    const lastMessage = messages[messages.length - 1];
    return (lastMessage.role === 'user') || (lastMessage.role === 'assistant' && lastMessage.content === '');
  };

  // Get the latest user message
  const getLatestUserMessage = () => {
    if (!messages.length) return null;

    // Find the last user message
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === 'user') {
        return messages[i];
      }
    }
    return null;
  };

  // Poll the API to check for a response
  const pollForResponse = async () => {
    try {
      // Get the latest user message
      const latestUserMessage = getLatestUserMessage();

      if (!latestUserMessage) {
        Logger.warn('Auto-resume: No user message found to resume from');
        setIsPolling(false);
        updateThinkingStates(false);
        isThinkingForResumeRef.current = false;
        return;
      }

      // Use the POST endpoint with the user message timestamp
      const response = await fetch(`/api/chat/resume`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatId,
          userMessageTimestamp: latestUserMessage.createdAt,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to poll for response: ${response.status}`);
      }

      const data = await response.json();

      // If we have a response, add it to the messages
      if (data.status === 'completed' && data.message) {
        Logger.info('Auto-resume: Found completed message', { chatId });

        // Add the assistant message to the chat if it doesn't already exist
        setMessages((prevMessages) => {
          const messageExists = prevMessages.some(msg => msg.id === data.message.id);
          return messageExists ? prevMessages : [...prevMessages, data.message];
        });

        // Stop polling and thinking
        setIsPolling(false);
        updateThinkingStates(false);
        isThinkingForResumeRef.current = false;
        return;
      }

      // If the stream failed or timed out, stop polling
      if (data.status === 'failed' || data.status === 'timeout') {
        Logger.info('Auto-resume: Stream failed or timed out', {
          chatId,
          status: data.status
        });
        setIsPolling(false);
        updateThinkingStates(false);
        isThinkingForResumeRef.current = false;
        return;
      }

      // Handle no pending streams or no processing
      if (data.status === 'no_pending_streams') {
        noProcessingCountRef.current += 1;
        Logger.info('Auto-resume: No pending streams', {
          chatId,
          noProcessingCount: noProcessingCountRef.current
        });

        // If we've had MIN_POLLS consecutive "no processing" responses, stop
        if (noProcessingCountRef.current >= MIN_POLLS) {
          Logger.info('Auto-resume: Stopping after MIN_POLLS with no processing', { chatId });
          setIsPolling(false);
          updateThinkingStates(false);
          isThinkingForResumeRef.current = false;
          return;
        }
      } else if (data.status === 'processing') {
        // Reset no processing count when we detect processing
        noProcessingCountRef.current = 0;

        // Start thinking animation if not already started
        if (!isThinkingForResumeRef.current) {
          Logger.info('Auto-resume: Starting thinking animation', { chatId });
          isThinkingForResumeRef.current = true;
          updateThinkingStates(true);
        }
      }

      // Increment poll count
      pollCountRef.current += 1;

      if (pollCountRef.current >= MAX_POLLS) {
        Logger.info('Auto-resume: Max polls reached', { chatId });
        setIsPolling(false);
        updateThinkingStates(false);
        isThinkingForResumeRef.current = false;
        return;
      }

      // Schedule next poll
      pollingTimeoutRef.current = setTimeout(pollForResponse, 7500);
    } catch (error) {
      Logger.error('Auto-resume: Error polling for response', { error, chatId });
      setIsPolling(false);
      updateThinkingStates(false);
      isThinkingForResumeRef.current = false;
    }
  };

  // Start polling when the component mounts if needed
  useEffect(() => {
    // We use the shouldAttemptResume function which now internally uses the ref
    if (isAutoResumeEnabled && shouldAttemptResume() && !isPolling) {
      Logger.info('Auto-resume: Starting polling', { chatId });
      setIsPolling(true);
      pollCountRef.current = 0;
      noProcessingCountRef.current = 0;
      // Don't start thinking animation immediately - wait for 'processing' status
      pollForResponse();
    }

    // Cleanup function
    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
        pollingTimeoutRef.current = null;
      }
    };
  }, [chatId, messages, isAutoResumeEnabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
      }
      setIsPolling(false);
      isThinkingForResumeRef.current = false;
    };
  }, []);

  return { isPolling, enableResumeThinking };
}
