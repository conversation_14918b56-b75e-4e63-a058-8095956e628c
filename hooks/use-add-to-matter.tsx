import { useState, useCallback } from "react";
import { toast } from "sonner";
import { ChatWithTags as Chat } from "@/lib/db/schema";

interface Matter {
  id: string;
  name: string;
  chats: Chat[];
}

interface UseAddToMatterProps {
  onAddToMatter?: (matterId: string, chatId: string) => Promise<void>;
}

export function useAddToMatter({ onAddToMatter }: UseAddToMatterProps = {}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const openDialog = useCallback((chat: Chat) => {
    setCurrentChat(chat);
    setIsDialogOpen(true);
  }, []);

  const closeDialog = useCallback(() => {
    setIsDialogOpen(false);
    setCurrentChat(null);
  }, []);

  const addChatToMatter = useCallback(async (matter: Matter, chatId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Add chat to matter using the folder name (not ID)
      const addResponse = await fetch("/api/chat-org/folders/chats", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chatId, folderName: matter.name }),
      });
      
      if (!addResponse.ok) {
        throw new Error('Failed to add chat to matter');
      }

      return true;
    } catch (error) {
      console.error('Error adding chat to matter:', error);
      toast.error('Failed to add chat to matter');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleAddToMatter = useCallback(async (matter: Matter) => {
    if (!currentChat) return;
    
    const success = await addChatToMatter(matter, currentChat.id);
    
    if (success) {
      if (onAddToMatter) {
        await onAddToMatter(matter.id, currentChat.id);
      }
      toast.success(`Chat added to "${matter.name}" successfully`);
      closeDialog();
    }
  }, [currentChat, addChatToMatter, onAddToMatter, closeDialog]);

  return {
    isDialogOpen,
    currentChat,
    isLoading,
    openDialog,
    closeDialog,
    handleAddToMatter
  };
}