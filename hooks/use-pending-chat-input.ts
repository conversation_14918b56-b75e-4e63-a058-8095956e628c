"use client";

import { useEffect } from 'react';
import { Logger } from '@/lib/utils/Logger';

interface PendingFile {
  fileUrl?: string;
  url?: string;
  fileName?: string;
  name?: string;
  fileType?: string;
  contentType?: string;
  document_id?: string;
  id?: string;
  sourceDocumentId?: string;
  uploaded?: boolean;
  messageId?: string;
  [key: string]: any; // Allow for other properties
}

/**
 * Hook to check for pending chat input in localStorage and set it in the chat input field
 * Also handles any pending files from prompts
 * @param setInput Function to set the input value
 */
export function usePendingChatInput(setInput: (value: string) => void) {
  useEffect(() => {
    // Check if there's a pending chat input in localStorage
    const pendingInput = localStorage.getItem('pendingChatInput');

    if (pendingInput) {
      // Set the input value
      setInput(pendingInput);

      // Clear the pending input from localStorage
      localStorage.removeItem('pendingChatInput');
    }

    // Expose a function to handle prompt files
    if (typeof window !== 'undefined') {
      (window as any).__CHAT_HANDLE_PROMPT_FILES__ = () => {
        // Check if there are pending files from a prompt
        const pendingFilesJson = localStorage.getItem('pendingChatFiles');
        if (pendingFilesJson) {
          try {
            const pendingFiles = JSON.parse(pendingFilesJson);
            Logger.info('Found pending files from prompt:', pendingFiles);

            // Get the pending message ID from localStorage
            const pendingMessageId = localStorage.getItem('pendingMessageId');
            Logger.info('Found pending message ID for document association:', pendingMessageId);

            // Validate that all files have proper URLs and document_id
            const validFiles = pendingFiles.filter((file: PendingFile) => {
              // Check for URL in various possible locations
              const fileUrl = file.fileUrl || file.url;
              const isValidUrl = fileUrl &&
                typeof fileUrl === 'string' &&
                !fileUrl.startsWith('blob:');

              // Check for document_id in various possible locations
              let documentId = file.document_id;

              // If no document_id, try to use id field
              if (!documentId && file.id) {
                documentId = file.id;
                file.document_id = documentId;
                Logger.info('Using id field as document_id:', { id: documentId });
              }

              // If no document_id, try to use sourceDocumentId field
              if (!documentId && file.sourceDocumentId) {
                documentId = file.sourceDocumentId;
                file.document_id = documentId;
                Logger.info('Using sourceDocumentId field as document_id:', { id: documentId });
              }

              const hasDocumentId = !!documentId;

              if (!isValidUrl) {
                Logger.warn('Skipping file with invalid URL:', file);
              }

              if (!hasDocumentId) {
                Logger.warn('File missing document_id and no alternative found:', file);
              }

              return isValidUrl && hasDocumentId;
            });

            if (validFiles.length > 0) {
              // Ensure all files have the EXACT format the chat component expects
              // This format must match what's used in multimodal-input.tsx
              const formattedFiles = validFiles.map((file: PendingFile) => ({
                url: file.fileUrl || file.url,
                name: file.fileName || file.name,
                contentType: file.fileType || file.contentType || 'application/octet-stream',
                document_id: file.document_id,
                uploaded: true,
                _attachment_id: file.document_id,
                // Add the message ID to each file if available
                ...(pendingMessageId ? { messageId: pendingMessageId } : {})
              }));

              Logger.info('Formatted files for chat component:', formattedFiles);

              // Trigger file handling in the chat component via custom event
              const fileEvent = new CustomEvent('prompt-files-available', {
                detail: {
                  files: formattedFiles,
                  messageId: pendingMessageId,
                  isFromPlaybook: true // Add this flag to indicate files are from playbook
                }
              });
              window.dispatchEvent(fileEvent);

              // If we have a pending message ID, make a direct API call to associate documents
              // This is a backup in case the event handler in multimodal-input fails
              if (pendingMessageId) {
                Logger.info('Making direct API call to associate documents with message:', {
                  messageId: pendingMessageId,
                  documentIds: formattedFiles.map((f: { document_id: string }) => f.document_id)
                });

                // Make a direct API call to associate documents with the message
                fetch('/api/message-documents', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    messageId: pendingMessageId,
                    documentIds: formattedFiles.map((f: { document_id: string }) => f.document_id)
                  })
                })
                .then(response => {
                  if (response.ok) {
                    Logger.info('Successfully associated documents with message via direct API call:', pendingMessageId);
                  } else {
                    Logger.error('Failed to associate documents with message via direct API call:', pendingMessageId);

                    // Try one more time with a delay
                    setTimeout(() => {
                      fetch('/api/message-documents', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                          messageId: pendingMessageId,
                          documentIds: formattedFiles.map((f: { document_id: string }) => f.document_id)
                        })
                      })
                      .then(retryResponse => {
                        if (retryResponse.ok) {
                          Logger.info('Successfully associated documents with message on retry:', pendingMessageId);
                        } else {
                          Logger.error('Failed to associate documents with message on retry:', pendingMessageId);
                        }
                      })
                      .catch(retryError => {
                        Logger.error('Error retrying document association:', retryError);
                      });
                    }, 1000);
                  }
                })
                .catch(error => {
                  Logger.error('Error associating documents with message:', error);
                });

                // DO NOT remove pendingMessageId here - it will be used by multimodal-input
              }

              // Check if we should auto-submit after loading files
              const shouldAutoSubmit = localStorage.getItem('autoSubmitAfterFiles') === 'true';
              if (shouldAutoSubmit && (window as any).__CHAT_SUBMIT_FORM__) {
                Logger.info('Auto-submitting form after loading files');
                setTimeout(() => {
                  try {
                    (window as any).__CHAT_SUBMIT_FORM__();
                  } catch (submitError) {
                    Logger.error('Error auto-submitting form:', submitError);
                  }
                }, 500); // Increased timeout for better reliability
              }

              // Clear the auto-submit flag
              localStorage.removeItem('autoSubmitAfterFiles');
            } else {
              Logger.warn('No valid files found in pendingChatFiles');
            }

            // DO NOT clear pendingChatFiles here - we'll keep it for the multimodal-input component
            // The multimodal-input component will clear it after processing
          } catch (error) {
            Logger.error('Error parsing pending files:', error);
            // Only remove if there was an error parsing
            localStorage.removeItem('pendingChatFiles');
          }
        }
      };

      // Clean up when component unmounts
      return () => {
        delete (window as any).__CHAT_HANDLE_PROMPT_FILES__;
      };
    }
  }, [setInput]);
}
