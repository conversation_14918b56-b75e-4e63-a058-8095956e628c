import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN || "",

  // Note: if you want to override the automatic release value, do not set a
  // `release` value here - use the environment variable `SENTRY_RELEASE`, so
  // that it will also get attached to your source maps


  // Add optional integrations for additional features
  integrations: [
    // // Core integrations
    // Sentry.replayIntegration(),
    // Sentry.httpClientIntegration(),
    // // Additional recommended integrations
    // Sentry.browserTracingIntegration(),
    // Sentry.browserProfilingIntegration(),
    // Sentry.breadcrumbsIntegration(),
    // Sentry.dedupeIntegration(),
    // Sentry.linkedErrorsIntegration(),
  ],

  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
  tracesSampleRate: 1,
  // Define how likely Replay events are sampled.
  // This sets the sample rate to be 10%. You may want this to be 100% while
  // in development and sample at a lower rate in production
  replaysSessionSampleRate: 0.1,

  // Define how likely Replay events are sampled when an error occurs.
  replaysOnErrorSampleRate: 0.6,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,

  // This option is required for capturing headers and cookies
  sendDefaultPii: false,
});

// Add this function to set user information
export const identifyUser = (user: {
  id?: string;
  email?: string;
  username?: string;
  ip_address?: string;
  [key: string]: any; // For custom attributes
}) => {
  Sentry.setUser(user);
};

// This export will instrument router navigations, and is only relevant if you enable tracing.
// `captureRouterTransitionStart` is available from SDK version 9.12.0 onwards
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart
